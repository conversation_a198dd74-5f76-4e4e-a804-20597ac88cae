from django.contrib import admin
from django.contrib.auth.models import Group
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.forms import ReadOnlyPass<PERSON><PERSON>ashField
from django.core.exceptions import ValidationError
from phonenumber_field.formfields import PhoneNumber<PERSON>ield
from django.contrib.auth import get_user_model
from ..models import User, PartnerProfile, UserProfile, Role, Permission, UserRole
from .forms import *


class UserCreationForm(forms.ModelForm):
    """A form for creating new users. Includes all the required
    fields, plus a repeated password."""

    phone = PhoneNumberField()
    is_partner = forms.BooleanField(
        label="Partner", widget=forms.CheckboxInput, initial=True, required=False
    )
    password1 = forms.CharField(label="Password", widget=forms.PasswordInput)
    password2 = forms.CharField(
        label="Password confirmation", widget=forms.PasswordInput
    )

    class Meta:
        model = get_user_model()
        fields = ["name", "phone", "email", "is_partner"]

    def clean_password2(self):
        # Check that the two password entries match
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")
        if password1 and password2 and password1 != password2:
            raise ValidationError("Passwords don't match")
        return password2

    def save(self, commit=True):
        # Save the provided password in hashed format
        user = super().save(commit=False)
        if commit:
            user.set_password(self.cleaned_data["password1"])
            user.save()
            if self.cleaned_data.get("is_partner"):
                PartnerProfile.objects.create(user=user)
            UserProfile.objects.create(user=user)
        return user


class UserChangeForm(forms.ModelForm):
    """A form for updating users. Includes all the fields on
    the user, but replaces the password field with admin's
    disabled password hash display field.
    """

    phone = PhoneNumberField()
    is_partner = forms.BooleanField(required=False)
    password = ReadOnlyPasswordHashField()

    class Meta:
        model = User
        fields = ["name", "phone", "email", "is_partner", "is_active", "is_admin"]


class UserProfileInline(admin.TabularInline):
    model = UserProfile
    extra = 0


class PartnerProfileInline(admin.TabularInline):
    model = PartnerProfile
    fk_name = "user"  # Specify which ForeignKey to use
    extra = 0


class UserRoleInline(admin.TabularInline):
    model = UserRole
    extra = 1


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    form = UserChangeForm
    add_form = UserCreationForm

    list_display = ["name", "email", "phone", "is_admin", "is_partner", "updated_at"]
    list_filter = ["is_admin", "is_active", "is_partner", "updated_at"]

    # Search by phone number, name, and email
    search_fields = ["phone", "name", "email"]

    ordering = ["-updated_at"]

    fieldsets = [
        (None, {"fields": ["phone", "password"]}),
        ("Personal info", {"fields": ["name", "email"]}),
        ("Permissions", {"fields": ["is_admin", "is_active", "is_partner"]}),
        ("Important dates", {"fields": ["created_at", "updated_at"]}),
    ]

    add_fieldsets = [
        (
            None,
            {
                "classes": ["wide"],
                "fields": [
                    "name",
                    "phone",
                    "email",
                    "password1",
                    "password2",
                    "is_partner",
                ],
            },
        ),
    ]

    readonly_fields = ["created_at", "updated_at"]
    inlines = [UserProfileInline, PartnerProfileInline]

    def get_inlines(self, request, obj=None):
        if obj and obj.is_partner:
            return [UserProfileInline, PartnerProfileInline, UserRoleInline]
        return [UserProfileInline, UserRoleInline]


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ["name", "description"]
    search_fields = ["name", "description"]
    filter_horizontal = ["permissions"]


@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    list_display = ["name", "description"]
    search_fields = ["name", "description"]


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ["user", "role", "property_id"]
    list_filter = ["role"]
    search_fields = ["user__name", "user__email", "role__name"]


admin.site.unregister(Group)
