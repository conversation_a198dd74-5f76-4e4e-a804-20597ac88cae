from django.apps import AppConfig
import importlib

class PaymentsConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "payments"
    verbose_name = "Payment Processing"

    def ready(self):
        # Import signal handlers to register them
        try:
            importlib.import_module(f"{self.name}.signals")
        except ModuleNotFoundError as e:
            if e.name == f"{self.name}.signals":
                return
            raise