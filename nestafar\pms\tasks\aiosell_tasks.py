"""
Celery tasks for AioSell integration.

This module contains asynchronous tasks for syncing PMS data to AioSell
using their rate restrictions and inventory restrictions APIs.
"""

import logging
from celery import shared_task
from django.utils import timezone
from datetime import datetime, timedelta
from typing import Dict, List
from django.db import transaction

from ..models import (
    Calendar,
    RoomType,
    RatePlan,
    RoomBlock,
    HotelOTAIntegration,
    RoomBlockSyncLog,
    AvailabilityLog,
)
from stay.models import Property
from ..services import get_aiosell_service, AioSellAPIError

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def sync_calendar_to_aiosell(
    self, calendar_id: str, action: str, calendar_data: Dict = None
):
    """Sync a calendar entry (or its deletion) to AioSell using the rate restrictions API.

    Ensures all DB reads are contained within an atomic transaction so any partial
    related work is rolled back if an exception occurs. Enriched structured logging
    supplies identifying context (calendar_id, hotel_id, room_type_id, rate_plan_id)
    and stack traces on failure while preserving existing retry semantics.
    """
    context_base = {
        "calendar_id": calendar_id,
        "action": action,
    }
    try:
        logger.info("Starting calendar sync task", extra=context_base)

        with transaction.atomic():
            if action == "delete" and calendar_data:
                # Deleted calendar entry path
                ctx = {**context_base}
                try:
                    hotel_id = calendar_data.get("hotel_id")
                    room_type_id = calendar_data.get("room_type_id")
                    rate_plan_id = calendar_data.get("rate_plan_id")
                    ctx.update(
                        {
                            "hotel_id": hotel_id,
                            "room_type_id": room_type_id,
                            "rate_plan_id": rate_plan_id,
                        }
                    )
                    hotel = Property.objects.get(id=hotel_id)
                    room_type = RoomType.objects.get(id=room_type_id)
                    rate_plan = RatePlan.objects.get(id=rate_plan_id)
                except (
                    Property.DoesNotExist,
                    RoomType.DoesNotExist,
                    RatePlan.DoesNotExist,
                ):
                    logger.exception(
                        "Calendar delete sync skipped: related object not found",
                        extra=ctx,
                    )
                    return
                try:
                    from django.utils.dateparse import parse_date

                    entry_date = parse_date(calendar_data.get("date"))
                    if not entry_date:
                        raise ValueError(f"Invalid date '{calendar_data.get('date')}'")
                except Exception:
                    logger.exception(
                        "Calendar delete sync skipped: invalid date", extra=ctx
                    )
                    return

                aiosell_service = get_aiosell_service(hotel)
                if not aiosell_service:
                    logger.warning(
                        "No AioSell service available for hotel",
                        extra=ctx | {"hotel_id": str(hotel.id)},
                    )
                    return

                # Push "no restrictions" to clear any existing restrictions
                restrictions = {
                    "stop_sell": False,
                    "minimum_stay": 1,
                    "close_on_arrival": False,
                    "close_on_departure": False,
                }

                result = aiosell_service.push_rate_restrictions(
                    room_type=room_type,
                    rate_plan=rate_plan,
                    start_date=entry_date,
                    end_date=entry_date,
                    restrictions=restrictions,
                )
                logger.info(
                    "Calendar delete sync completed successfully",
                    extra={**ctx, "status": "success", "result": result},
                )
            else:
                # Existing calendar entry path
                try:
                    calendar_entry = Calendar.objects.select_related(
                        "room_type", "rate_plan", "room_type__hotel"
                    ).get(id=calendar_id)
                except Calendar.DoesNotExist:
                    logger.exception(
                        "Calendar sync skipped: calendar entry not found",
                        extra=context_base,
                    )
                    return

                hotel = calendar_entry.room_type.hotel
                ctx = {
                    **context_base,
                    "hotel_id": str(hotel.id),
                    "room_type_id": str(calendar_entry.room_type.id),
                    "rate_plan_id": (
                        str(calendar_entry.rate_plan.id)
                        if calendar_entry.rate_plan
                        else None
                    ),
                }
                aiosell_service = get_aiosell_service(hotel)
                if not aiosell_service:
                    logger.warning("No AioSell service available for hotel", extra=ctx)
                    return

                result = aiosell_service.sync_calendar_entry(calendar_entry)
                logger.info(
                    "Calendar sync completed successfully",
                    extra={**ctx, "status": "success", "result": result},
                )

    except Exception as exc:  # noqa: BLE001
        # Log full stack with context
        logger.exception("Calendar sync failed", extra={**context_base})

        # Retry with exponential backoff (1, 2, 4 minutes)
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 60
            logger.info(
                "Retrying calendar sync",
                extra={
                    **context_base,
                    "retry_delay": retry_delay,
                    "attempt": self.request.retries + 1,
                },
            )
            raise self.retry(countdown=retry_delay, exc=exc)
        else:
            logger.error(
                "Calendar sync failed after max retries",
                extra={**context_base, "max_retries": self.max_retries},
            )
            raise


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def sync_room_block_to_aiosell(
    self,
    room_block_id: str,
    sync_log_id: str,
    action: str,
    room_block_data: Dict = None,
):
    """
    Sync room block to AioSell using inventory restrictions API.

    Args:
        room_block_id: UUID of the room block
        sync_log_id: UUID of the sync log entry
        action: Action type ('create', 'update', 'delete')
        room_block_data: Room block data for deleted blocks
    """
    sync_log = None

    try:
        logger.info(
            f"Starting room block sync task: {room_block_id} (action: {action})"
        )

        # Get sync log
        try:
            sync_log = RoomBlockSyncLog.objects.get(id=sync_log_id)
            sync_log.sync_status = "in_progress"
            sync_log.save(update_fields=["sync_status"])
        except RoomBlockSyncLog.DoesNotExist:
            logger.error(f"Sync log {sync_log_id} not found")
            return

        hotel = sync_log.hotel
        aiosell_service = get_aiosell_service(hotel)
        if not aiosell_service:
            if sync_log:
                sync_log.sync_status = "failed"
                sync_log.error_message = (
                    f"No AioSell service available for hotel {hotel.id}"
                )
                sync_log.save(update_fields=["sync_status", "error_message"])
            return

        if action == "delete" and room_block_data:
            # Handle deleted room block using stored data
            from stay.models import Room

            room = None
            if room_block_data.get("room_id"):
                try:
                    room = Room.objects.get(id=room_block_data["room_id"])
                except Room.DoesNotExist:
                    pass

            if not room:
                logger.error(f"Cannot sync deleted room block - room not found")
                if sync_log:
                    sync_log.sync_status = "failed"
                    sync_log.error_message = "Room not found for deleted block"
                    sync_log.save(update_fields=["sync_status", "error_message"])
                return

            # Create temporary room block object for sync
            from django.utils.dateparse import parse_datetime

            blocked_from = parse_datetime(room_block_data["blocked_from"])
            if not blocked_from:
                logger.error(
                    f"Invalid blocked_from datetime: {room_block_data['blocked_from']}"
                )
                if sync_log:
                    sync_log.sync_status = "failed"
                    sync_log.error_message = "Invalid blocked_from datetime format"
                    sync_log.save(update_fields=["sync_status", "error_message"])
                return

            blocked_until = None
            if room_block_data.get("blocked_until"):
                blocked_until = parse_datetime(room_block_data["blocked_until"])
                if not blocked_until:
                    logger.error(
                        f"Invalid blocked_until datetime: {room_block_data['blocked_until']}"
                    )
                    if sync_log:
                        sync_log.sync_status = "failed"
                        sync_log.error_message = "Invalid blocked_until datetime format"
                        sync_log.save(update_fields=["sync_status", "error_message"])
                    return

            temp_block = RoomBlock(
                id=room_block_id,
                hotel=hotel,
                room=room,
                blocked_from=blocked_from,
                blocked_until=blocked_until,
                is_active=False,  # Mark as inactive for deletion
                reason=room_block_data.get("reason"),
            )

            result = aiosell_service.sync_room_block(temp_block, action="delete")

        else:
            # Handle existing room block
            try:
                room_block = RoomBlock.objects.select_related("hotel", "room").get(
                    id=room_block_id
                )
            except RoomBlock.DoesNotExist:
                logger.error(f"Room block {room_block_id} not found")
                if sync_log:
                    sync_log.sync_status = "failed"
                    sync_log.error_message = "Room block not found"
                    sync_log.save(update_fields=["sync_status", "error_message"])
                return

            result = aiosell_service.sync_room_block(room_block, action)

        # Update sync log with success
        if sync_log:
            sync_log.sync_status = "success"
            sync_log.response_data = result
            sync_log.synced_at = timezone.now()
            sync_log.save(update_fields=["sync_status", "response_data", "synced_at"])

        logger.info(f"Room block sync completed successfully: {result}")

    except AioSellAPIError as exc:
        logger.error(f"AioSell API error in room block sync: {str(exc)}")

        if sync_log:
            sync_log.sync_status = "failed"
            sync_log.error_message = str(exc)
            sync_log.retry_count += 1
            sync_log.save(update_fields=["sync_status", "error_message", "retry_count"])

        # Retry for API errors
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 60
            logger.info(f"Retrying room block sync in {retry_delay} seconds")
            raise self.retry(countdown=retry_delay, exc=exc)
        else:
            logger.error(f"Room block sync failed after {self.max_retries} retries")
            raise

    except Exception as exc:
        logger.error(f"Room block sync failed: {str(exc)}")

        if sync_log:
            sync_log.sync_status = "failed"
            sync_log.error_message = str(exc)
            sync_log.retry_count += 1
            sync_log.save(update_fields=["sync_status", "error_message", "retry_count"])

        # Retry for other errors
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 60
            raise self.retry(countdown=retry_delay, exc=exc)
        else:
            logger.error(f"Room block sync failed after {self.max_retries} retries")
            raise


@shared_task(bind=True, max_retries=2, default_retry_delay=600)
def sync_room_type_to_aiosell(self, room_type_id: str, action: str):
    """
    Sync room type changes to AioSell.

    This may trigger inventory restrictions updates for all rooms of this type.

    Args:
        room_type_id: UUID of the room type
        action: Action type ('create', 'update')
    """
    try:
        logger.info(f"Starting room type sync task: {room_type_id} (action: {action})")

        try:
            room_type = RoomType.objects.select_related("hotel").get(id=room_type_id)
        except RoomType.DoesNotExist:
            logger.error(f"Room type {room_type_id} not found")
            return

        hotel = room_type.hotel
        aiosell_service = get_aiosell_service(hotel)
        if not aiosell_service:
            logger.warning(f"No AioSell service available for hotel {hotel.id}")
            return

        # For room type changes, we might need to update existing restrictions
        # Get current active calendar entries for this room type
        active_entries = Calendar.objects.filter(
            room_type=room_type, date__gte=timezone.now().date()
        ).select_related("rate_plan")

        if active_entries.exists():
            # Sync active calendar entries to apply current restrictions
            for entry in active_entries[:10]:  # Limit to prevent overwhelming
                try:
                    aiosell_service.sync_calendar_entry(entry)
                    logger.debug(
                        f"Synced calendar entry {entry.id} for room type update"
                    )
                except Exception as e:
                    logger.warning(
                        f"Failed to sync calendar entry {entry.id}: {str(e)}"
                    )

        logger.info(f"Room type sync completed for {room_type.name}")

    except Exception as exc:
        logger.error(f"Room type sync failed: {str(exc)}")

        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300  # 5, 10 minutes
            raise self.retry(countdown=retry_delay, exc=exc)
        else:
            logger.error(f"Room type sync failed after {self.max_retries} retries")
            raise


@shared_task(bind=True, max_retries=2, default_retry_delay=600)
def sync_rate_plan_to_aiosell(self, rate_plan_id: str, action: str):
    """
    Sync rate plan changes to AioSell.

    This may trigger rate restrictions updates for all calendar entries using this rate plan.

    Args:
        rate_plan_id: UUID of the rate plan
        action: Action type ('create', 'update')
    """
    try:
        logger.info(f"Starting rate plan sync task: {rate_plan_id} (action: {action})")

        try:
            rate_plan = RatePlan.objects.select_related(
                "room_type", "room_type__hotel"
            ).get(id=rate_plan_id)
        except RatePlan.DoesNotExist:
            logger.error(f"Rate plan {rate_plan_id} not found")
            return

        hotel = rate_plan.room_type.hotel
        aiosell_service = get_aiosell_service(hotel)
        if not aiosell_service:
            logger.warning(f"No AioSell service available for hotel {hotel.id}")
            return

        try:
            result = aiosell_service.sync_rate_plan(rate_plan, action)
            logger.info(f"Rate plan sync completed for {rate_plan.name}: {result}")

        except Exception as e:
            logger.error(f"Failed to sync rate plan range {rate_plan.id}: {str(e)}")
            logger.info("Falling back to individual calendar entry sync")

            upper_bound = rate_plan.valid_to or (
                timezone.now().date() + timedelta(days=180)
            )  # 6 months
            active_entries_qs = Calendar.objects.filter(
                rate_plan=rate_plan,
                date__gte=timezone.now().date(),
                date__lte=upper_bound,
            ).select_related("room_type")
            if active_entries_qs.exists():
                for entry in active_entries_qs[:10]:  # Limit to prevent overwhelming
                    try:
                        aiosell_service.sync_calendar_entry(entry)
                        logger.debug(
                            f"Synced calendar entry {entry.id} for rate plan fallback"
                        )
                    except Exception as entry_error:
                        logger.warning(
                            f"Failed to sync calendar entry {entry.id}: {str(entry_error)}"
                        )
            else:
                logger.warning(
                    f"No calendar entries found for rate plan {rate_plan.id} fallback"
                )

        logger.info(f"Rate plan sync completed for {rate_plan.name}")

    except Exception as exc:
        logger.error(f"Rate plan sync failed: {str(exc)}")

        if getattr(self.request, "retries", 0) < self.max_retries:
            retry_delay = (2 ** getattr(self.request, "retries", 0)) * 300
            raise self.retry(countdown=retry_delay, exc=exc)
        else:
            logger.error(f"Rate plan sync failed after {self.max_retries} retries")
            raise


@shared_task(bind=True, max_retries=2, default_retry_delay=900)
def sync_hotel_initial_data_to_aiosell(self, hotel_id: str, integration_id: str):
    """
    Perform initial sync of hotel data to AioSell when integration is first set up.

    This syncs room types, rate plans, and current calendar entries.

    Args:
        hotel_id: UUID of the hotel
        integration_id: UUID of the AioSell integration
    """
    try:
        logger.info(f"Starting initial hotel data sync for hotel {hotel_id}")

        try:
            hotel = Property.objects.get(id=hotel_id)
            integration = HotelOTAIntegration.objects.get(id=integration_id)
        except (Property.DoesNotExist, HotelOTAIntegration.DoesNotExist) as e:
            logger.error(f"Hotel or integration not found: {str(e)}")
            return

        aiosell_service = get_aiosell_service(hotel)
        if not aiosell_service:
            logger.warning(f"No AioSell service available for hotel {hotel.id}")
            return

        # Test connection first
        connection_test = aiosell_service.test_connection()
        if not connection_test.get("success"):
            logger.error(f"AioSell connection test failed: {connection_test}")
            return

        # Sync current calendar entries (next 30 days)
        start_date = timezone.now().date()
        end_date = start_date + timedelta(days=30)

        calendar_entries = Calendar.objects.filter(
            room_type__hotel=hotel, date__range=(start_date, end_date)
        ).select_related("room_type", "rate_plan")

        sync_count = 0
        error_count = 0

        # Group entries for bulk sync
        if calendar_entries.exists():
            try:
                results = aiosell_service.bulk_sync_calendar_entries(
                    list(calendar_entries)
                )

                for result in results:
                    if "error" in result:
                        error_count += 1
                        logger.warning(f"Calendar sync error: {result}")
                    else:
                        sync_count += 1

            except Exception as e:
                logger.error(f"Bulk calendar sync failed: {str(e)}")
                error_count += len(calendar_entries)

        # Sync active room blocks
        active_blocks = RoomBlock.objects.filter(
            hotel=hotel, is_active=True, blocked_until__gte=timezone.now()
        ).select_related("room")

        for block in active_blocks:
            try:
                aiosell_service.sync_room_block(block, action="create")
                sync_count += 1
            except Exception as e:
                logger.warning(f"Room block sync failed for block {block.id}: {str(e)}")
                error_count += 1

        logger.info(
            f"Initial hotel data sync completed for {hotel.name}. "
            f"Synced: {sync_count}, Errors: {error_count}"
        )

        # Log the sync completion
        AvailabilityLog.objects.create(
            hotel=hotel,
            room_type=hotel.room_types.first() if hotel.room_types.exists() else None,
            ota_platform=integration.ota_platform,
            date=timezone.now().date(),
            available_rooms=sync_count,
            sync_status="success" if error_count == 0 else "partial",
            synced_at=timezone.now(),
        )

    except Exception as exc:
        logger.error(f"Initial hotel data sync failed: {str(exc)}")

        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 600  # 10, 20 minutes
            raise self.retry(countdown=retry_delay, exc=exc)
        else:
            logger.error(
                f"Initial hotel data sync failed after {self.max_retries} retries"
            )
            raise


@shared_task(bind=True, max_retries=2, default_retry_delay=600)
def sync_hotel_configuration_update(self, hotel_id: str, config_changes: List[str]):
    """
    Sync hotel data after configuration changes.

    Args:
        hotel_id: UUID of the hotel
        config_changes: List of configuration keys that changed
    """
    try:
        logger.info(f"Starting configuration update sync for hotel {hotel_id}")

        try:
            hotel = Property.objects.get(id=hotel_id)
        except Property.DoesNotExist:
            logger.error(f"Hotel {hotel_id} not found")
            return

        aiosell_service = get_aiosell_service(hotel)
        if not aiosell_service:
            logger.warning(f"No AioSell service available for hotel {hotel.id}")
            return

        # Test connection after config change
        connection_test = aiosell_service.test_connection()
        if not connection_test.get("success"):
            logger.error(
                f"AioSell connection test failed after config update: {connection_test}"
            )
            return

        # If room_mapping or rate_plans changed, re-sync recent calendar entries
        if "room_mapping" in config_changes or "rate_plans" in config_changes:
            recent_entries = Calendar.objects.filter(
                room_type__hotel=hotel,
                date__gte=timezone.now().date(),
                date__lte=timezone.now().date() + timedelta(days=7),
            ).select_related("room_type", "rate_plan")

            for entry in recent_entries:
                try:
                    aiosell_service.sync_calendar_entry(entry)
                except Exception as e:
                    logger.warning(
                        f"Failed to re-sync calendar entry {entry.id}: {str(e)}"
                    )

        logger.info(f"Configuration update sync completed for hotel {hotel.name}")

    except Exception as exc:
        logger.error(f"Configuration update sync failed: {str(exc)}")

        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300
            raise self.retry(countdown=retry_delay, exc=exc)
        else:
            logger.error(
                f"Configuration update sync failed after {self.max_retries} retries"
            )
            raise


@shared_task
def bulk_sync_calendar_entries_to_aiosell(hotel_id: str, date_range_days: int = 30):
    """
    Bulk sync calendar entries for a hotel.

    Args:
        hotel_id: UUID of the hotel
        date_range_days: Number of days from today to sync
    """
    try:
        logger.info(f"Starting bulk calendar sync for hotel {hotel_id}")

        hotel = Property.objects.get(id=hotel_id)
        aiosell_service = get_aiosell_service(hotel)

        if not aiosell_service:
            logger.warning(f"No AioSell service available for hotel {hotel.id}")
            return

        start_date = timezone.now().date()
        end_date = start_date + timedelta(days=date_range_days)

        calendar_entries = Calendar.objects.filter(
            room_type__hotel=hotel, date__range=(start_date, end_date)
        ).select_related("room_type", "rate_plan")

        if calendar_entries.exists():
            results = aiosell_service.bulk_sync_calendar_entries(list(calendar_entries))

            success_count = sum(1 for r in results if "error" not in r)
            error_count = len(results) - success_count

            logger.info(
                f"Bulk calendar sync completed for {hotel.name}. "
                f"Success: {success_count}, Errors: {error_count}"
            )
        else:
            logger.info(f"No calendar entries found for hotel {hotel.name}")

    except Exception as e:
        logger.error(f"Bulk calendar sync failed for hotel {hotel_id}: {str(e)}")


@shared_task
def retry_failed_aiosell_syncs():
    """
    Retry failed AioSell sync operations.

    This task runs periodically to retry failed syncs that haven't exceeded max retries.
    """
    try:
        logger.info("Starting retry of failed AioSell syncs")

        # Get failed sync logs that can be retried
        failed_syncs = RoomBlockSyncLog.objects.filter(
            sync_status="failed",
            retry_count__lt=3,
            created_at__gte=timezone.now() - timedelta(hours=24),
            ota_platform__name="aiosell",
        ).select_related("hotel", "room_block")

        retry_count = 0

        for sync_log in failed_syncs:
            try:
                if sync_log.room_block:
                    # Retry existing room block sync
                    sync_room_block_to_aiosell.delay(
                        room_block_id=str(sync_log.room_block.id),
                        sync_log_id=str(sync_log.id),
                        action=sync_log.action,
                    )
                    retry_count += 1
                elif sync_log.action == "delete" and sync_log.request_data:
                    # Retry deleted room block sync
                    deleted_block_id = sync_log.request_data.get(
                        "deleted_room_block_id"
                    )
                    if not deleted_block_id:
                        logger.warning(
                            f"No deleted_room_block_id in request_data for sync log {sync_log.id}"
                        )
                        continue

                    sync_room_block_to_aiosell.delay(
                        room_block_id=deleted_block_id,
                        sync_log_id=str(sync_log.id),
                        action="delete",
                        room_block_data=sync_log.request_data,
                    )
                    retry_count += 1

            except Exception as e:
                logger.warning(
                    f"Failed to queue retry for sync log {sync_log.id}: {str(e)}"
                )

        logger.info(f"Queued {retry_count} sync retries")

    except Exception as e:
        logger.error(f"Failed to retry failed syncs: {str(e)}")


@shared_task
def cleanup_old_sync_logs():
    """
    Clean up old sync logs to prevent database bloat.

    Removes successful sync logs older than 30 days and failed logs older than 7 days.
    """
    try:
        logger.info("Starting cleanup of old sync logs")

        # Clean successful logs older than 30 days
        old_success = RoomBlockSyncLog.objects.filter(
            sync_status="success", created_at__lt=timezone.now() - timedelta(days=30)
        )
        success_count = old_success.count()
        old_success.delete()

        # Clean failed logs older than 7 days
        old_failed = RoomBlockSyncLog.objects.filter(
            sync_status="failed", created_at__lt=timezone.now() - timedelta(days=7)
        )
        failed_count = old_failed.count()
        old_failed.delete()

        logger.info(
            f"Cleaned up {success_count} successful and {failed_count} failed sync logs"
        )

    except Exception as e:
        logger.error(f"Failed to cleanup sync logs: {str(e)}")


# Maintenance task to sync availability
@shared_task
def sync_availability_to_aiosell(
    hotel_id: str, date_from: str = None, date_to: str = None
):
    """
    Sync availability data to AioSell for a specific date range.

    Args:
        hotel_id: UUID of the hotel
        date_from: Start date (ISO format)
        date_to: End date (ISO format)
    """
    try:
        logger.info(f"Starting availability sync for hotel {hotel_id}")

        hotel = Property.objects.get(id=hotel_id)
        aiosell_service = get_aiosell_service(hotel)

        if not aiosell_service:
            logger.warning(f"No AioSell service available for hotel {hotel.id}")
            return

        # Parse dates
        if date_from:
            start_date = datetime.fromisoformat(date_from).date()
        else:
            start_date = timezone.now().date()

        if date_to:
            end_date = datetime.fromisoformat(date_to).date()
        else:
            end_date = start_date + timedelta(days=30)

        # Get calendar entries for the date range
        calendar_entries = Calendar.objects.filter(
            room_type__hotel=hotel, date__range=(start_date, end_date)
        ).select_related("room_type", "rate_plan")

        if calendar_entries.exists():
            results = aiosell_service.bulk_sync_calendar_entries(list(calendar_entries))

            success_count = sum(1 for r in results if "error" not in r)
            error_count = len(results) - success_count

            logger.info(
                f"Availability sync completed for {hotel.name}. "
                f"Date range: {start_date} to {end_date}. "
                f"Success: {success_count}, Errors: {error_count}"
            )

            # Log the sync
            try:
                integration = HotelOTAIntegration.objects.get(
                    hotel=hotel, ota_platform__name="aiosell", is_active=True
                )
                ota_platform_id = integration.ota_platform.id
            except HotelOTAIntegration.DoesNotExist:
                logger.warning(
                    f"No active AioSell integration found for hotel {hotel.id}"
                )
                return
            except HotelOTAIntegration.MultipleObjectsReturned:
                logger.warning(
                    f"Multiple active AioSell integrations found for hotel {hotel.id}"
                )
                # Use the first one or handle accordingly
                integration = HotelOTAIntegration.objects.filter(
                    hotel=hotel, ota_platform__name="aiosell", is_active=True
                ).first()
                ota_platform_id = integration.ota_platform.id

            AvailabilityLog.objects.create(
                hotel=hotel,
                room_type=(
                    hotel.room_types.first() if hotel.room_types.exists() else None
                ),
                ota_platform_id=ota_platform_id,
                date=timezone.now().date(),
                available_rooms=success_count,
                sync_status="success" if error_count == 0 else "partial",
                synced_at=timezone.now(),
            )
        else:
            logger.info(
                f"No calendar entries found for hotel {hotel.name} in date range"
            )

    except Exception as e:
        logger.error(f"Availability sync failed for hotel {hotel_id}: {str(e)}")


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def async_push_inventory_to_aiosell(
    self,
    hotel_id: str,
    room_inventories: List[Dict],
    start_date: str,
    end_date: str,
    user_id: str = None
):
    """
    Asynchronously push inventory updates to AioSell.
    
    Args:
        hotel_id: Property/Hotel UUID
        room_inventories: List of room inventory data
        start_date: Start date string (YYYY-MM-DD)
        end_date: End date string (YYYY-MM-DD)
        user_id: Optional user ID for audit logging
    """
    context_base = {
        "hotel_id": hotel_id,
        "room_count": len(room_inventories),
        "start_date": start_date,
        "end_date": end_date,
        "user_id": user_id,
    }
    
    try:
        logger.info("Starting async inventory push to AioSell", extra=context_base)
        
        # Get hotel object
        try:
            hotel = Property.objects.get(id=hotel_id)
        except Property.DoesNotExist:
            logger.error(f"Hotel {hotel_id} not found", extra=context_base)
            raise
        
        # Parse dates
        from datetime import datetime
        start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
        
        # Initialize AioSell service
        aiosell_service = get_aiosell_service(hotel)
        if not aiosell_service:
            logger.error(f"AioSell integration not configured for hotel {hotel_id}", extra=context_base)
            raise Exception(f"AioSell integration not configured for hotel {hotel.name}")
        
        # Push inventory update
        result = aiosell_service.push_inventory_update(
            room_inventories=room_inventories,
            start_date=start_date_obj,
            end_date=end_date_obj
        )
        
        # Log successful sync
        logger.info(
            f"Successfully pushed inventory to AioSell for hotel {hotel.name}",
            extra={
                **context_base,
                "aiosell_response": result
            }
        )
        
        # Create audit log entry
        AvailabilityLog.objects.create(
            hotel=hotel,
            sync_type="inventory_push",
            success=True,
            message=f"Inventory pushed to AioSell for {len(room_inventories)} room types from {start_date} to {end_date}",
            details={
                "room_inventories": room_inventories,
                "start_date": start_date,
                "end_date": end_date,
                "aiosell_response": result
            }
        )
        
        return {
            "success": True,
            "message": f"Inventory successfully pushed for {len(room_inventories)} room types",
            "aiosell_response": result
        }
        
    except AioSellAPIError as e:
        logger.error(
            f"AioSell API error during inventory push: {str(e)}",
            extra=context_base,
            exc_info=True
        )
        
        # Create audit log entry for failure
        try:
            hotel = Property.objects.get(id=hotel_id)
            AvailabilityLog.objects.create(
                hotel=hotel,
                sync_type="inventory_push",
                success=False,
                message=f"AioSell inventory push failed: {str(e)}",
                details={
                    "room_inventories": room_inventories,
                    "start_date": start_date,
                    "end_date": end_date,
                    "error": str(e)
                }
            )
        except Exception:
            pass  # Don't fail the task if logging fails
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying inventory push task (attempt {self.request.retries + 1}/{self.max_retries})", extra=context_base)
            raise self.retry(countdown=300 * (2 ** self.request.retries))
        else:
            logger.error(f"Max retries exceeded for inventory push to AioSell", extra=context_base)
            raise
    
    except Exception as e:
        logger.error(
            f"Unexpected error during inventory push: {str(e)}",
            extra=context_base,
            exc_info=True
        )
        
        # Create audit log entry for failure
        try:
            hotel = Property.objects.get(id=hotel_id)
            AvailabilityLog.objects.create(
                hotel=hotel,
                sync_type="inventory_push",
                success=False,
                message=f"Inventory push failed: {str(e)}",
                details={
                    "room_inventories": room_inventories,
                    "start_date": start_date,
                    "end_date": end_date,
                    "error": str(e)
                }
            )
        except Exception:
            pass  # Don't fail the task if logging fails
        
        # Retry with exponential backoff for temporary errors
        if self.request.retries < self.max_retries and "integration not configured" not in str(e).lower():
            logger.info(f"Retrying inventory push task (attempt {self.request.retries + 1}/{self.max_retries})", extra=context_base)
            raise self.retry(countdown=300 * (2 ** self.request.retries))
        else:
            logger.error(f"Not retrying inventory push task", extra=context_base)
            raise


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def async_bulk_inventory_sync(
    self,
    hotel_id: str,
    room_type_ids: List[str],
    start_date: str,
    end_date: str,
    date_specific_inventory: Dict = None,
    user_id: str = None
):
    """
    Asynchronously perform bulk inventory synchronization to AioSell.
    
    Args:
        hotel_id: Property/Hotel UUID
        room_type_ids: List of RoomType UUIDs
        start_date: Start date string (YYYY-MM-DD)
        end_date: End date string (YYYY-MM-DD)
        date_specific_inventory: Optional date-specific inventory overrides
        user_id: Optional user ID for audit logging
    """
    context_base = {
        "hotel_id": hotel_id,
        "room_type_count": len(room_type_ids),
        "start_date": start_date,
        "end_date": end_date,
        "user_id": user_id,
    }
    
    try:
        logger.info("Starting async bulk inventory sync to AioSell", extra=context_base)
        
        # Get hotel object
        try:
            hotel = Property.objects.get(id=hotel_id)
        except Property.DoesNotExist:
            logger.error(f"Hotel {hotel_id} not found", extra=context_base)
            raise
        
        # Parse dates
        from datetime import datetime
        start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
        
        # Initialize AioSell service
        aiosell_service = get_aiosell_service(hotel)
        if not aiosell_service:
            logger.error(f"AioSell integration not configured for hotel {hotel_id}", extra=context_base)
            raise Exception(f"AioSell integration not configured for hotel {hotel.name}")
        
        # Perform bulk inventory sync
        result = aiosell_service.bulk_inventory_update(
            room_type_ids=room_type_ids,
            start_date=start_date_obj,
            end_date=end_date_obj,
            date_specific_inventory=date_specific_inventory or {}
        )
        
        # Log successful sync
        logger.info(
            f"Successfully completed bulk inventory sync to AioSell for hotel {hotel.name}",
            extra={
                **context_base,
                "aiosell_response": result
            }
        )
        
        # Create audit log entry
        AvailabilityLog.objects.create(
            hotel=hotel,
            sync_type="bulk_inventory_sync",
            success=True,
            message=f"Bulk inventory sync completed for {len(room_type_ids)} room types from {start_date} to {end_date}",
            details={
                "room_type_ids": room_type_ids,
                "start_date": start_date,
                "end_date": end_date,
                "date_specific_inventory": date_specific_inventory,
                "aiosell_response": result
            }
        )
        
        return {
            "success": True,
            "message": f"Bulk inventory sync completed for {len(room_type_ids)} room types",
            "aiosell_response": result
        }
        
    except AioSellAPIError as e:
        logger.error(
            f"AioSell API error during bulk inventory sync: {str(e)}",
            extra=context_base,
            exc_info=True
        )
        
        # Create audit log entry for failure
        try:
            hotel = Property.objects.get(id=hotel_id)
            AvailabilityLog.objects.create(
                hotel=hotel,
                sync_type="bulk_inventory_sync",
                success=False,
                message=f"Bulk inventory sync failed: {str(e)}",
                details={
                    "room_type_ids": room_type_ids,
                    "start_date": start_date,
                    "end_date": end_date,
                    "date_specific_inventory": date_specific_inventory,
                    "error": str(e)
                }
            )
        except Exception:
            pass  # Don't fail the task if logging fails
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying bulk inventory sync task (attempt {self.request.retries + 1}/{self.max_retries})", extra=context_base)
            raise self.retry(countdown=300 * (2 ** self.request.retries))
        else:
            logger.error(f"Max retries exceeded for bulk inventory sync to AioSell", extra=context_base)
            raise
    
    except Exception as e:
        logger.error(
            f"Unexpected error during bulk inventory sync: {str(e)}",
            extra=context_base,
            exc_info=True
        )
        
        # Create audit log entry for failure
        try:
            hotel = Property.objects.get(id=hotel_id)
            AvailabilityLog.objects.create(
                hotel=hotel,
                sync_type="bulk_inventory_sync",
                success=False,
                message=f"Bulk inventory sync failed: {str(e)}",
                details={
                    "room_type_ids": room_type_ids,
                    "start_date": start_date,
                    "end_date": end_date,
                    "date_specific_inventory": date_specific_inventory,
                    "error": str(e)
                }
            )
        except Exception:
            pass  # Don't fail the task if logging fails
        
        # Retry with exponential backoff for temporary errors
        if self.request.retries < self.max_retries and "integration not configured" not in str(e).lower():
            logger.info(f"Retrying bulk inventory sync task (attempt {self.request.retries + 1}/{self.max_retries})", extra=context_base)
            raise self.retry(countdown=300 * (2 ** self.request.retries))
        else:
            logger.error(f"Not retrying bulk inventory sync task", extra=context_base)
            raise
