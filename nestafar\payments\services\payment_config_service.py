"""
Payment Configuration Service

Centralizes payment-related configuration management and validation.
"""

from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
import logging
import ipaddress

logger = logging.getLogger(__name__)


class PaymentConfigService:
    """
    Service for managing payment configuration and settings.

    This service provides a centralized way to access and validate
    payment-related configuration from Django settings.
    """

    def __init__(self):
        self._razorpay_settings = getattr(settings, "RAZORPAY_SETTINGS", {})
        self._validate_configuration()

    def _validate_configuration(self):
        """Validate required configuration settings"""
        required_settings = ["KEY_ID", "KEY_SECRET", "WEBHOOK_SECRET"]

        for setting in required_settings:
            if not self._razorpay_settings.get(setting):
                logger.warning(f"Razorpay {setting} is not configured")
                if not settings.DEBUG:
                    raise ImproperlyConfigured(
                        f"Razorpay {setting} must be configured for production"
                    )

    @property
    def razorpay_key_id(self):
        """Get Razorpay API Key ID"""
        return self._razorpay_settings.get("KEY_ID", "")

    @property
    def razorpay_key_secret(self):
        """Get Razorpay API Key Secret"""
        return self._razorpay_settings.get("KEY_SECRET", "")

    @property
    def razorpay_webhook_secret(self):
        """Get Razorpay Webhook Secret"""
        return self._razorpay_settings.get("WEBHOOK_SECRET", "")

    @property
    def is_test_mode(self):
        """Check if running in test mode"""
        return self._razorpay_settings.get("ENVIRONMENT", "test") == "test"

    @property
    def base_url(self):
        """Get Razorpay API base URL"""
        env = "test" if self.is_test_mode else "live"
        return self._razorpay_settings.get("BASE_URL", {}).get(
            env, "https://api.razorpay.com/v1"
        )

    @property
    def webhook_url(self):
        """Get webhook URL path"""
        return self._razorpay_settings.get(
            "WEBHOOK_URL", "/payments/webhooks/razorpay/"
        )

    @property
    def payment_link_expire_hours(self):
        """Get payment link expiry hours"""
        return self._razorpay_settings.get("PAYMENT_LINK_SETTINGS", {}).get(
            "expire_by_hours", 24
        )

    @property
    def default_platform_commission_rate(self):
        """Get default platform commission rate as a percentage (e.g., 5.00 means 5%)"""
        return self._razorpay_settings.get("COMMISSION_SETTINGS", {}).get(
            "default_platform_rate", 5.00
        )

    @property
    def min_payment_amount(self):
        """Get minimum payment amount in rupees"""
        return self._razorpay_settings.get("AMOUNT_LIMITS", {}).get(
            "min_payment_amount", 1.00
        )

    @property
    def max_payment_amount(self):
        """Get maximum payment amount in rupees"""
        return self._razorpay_settings.get("AMOUNT_LIMITS", {}).get(
            "max_payment_amount", 100000.00
        )

    @property
    def api_timeout(self):
        """Get API timeout in seconds"""
        return self._razorpay_settings.get("API_SETTINGS", {}).get("timeout", 30)

    @property
    def max_retries(self):
        """Get maximum API retry attempts"""
        return self._razorpay_settings.get("API_SETTINGS", {}).get("max_retries", 3)

    @property
    def retry_delay(self):
        """Get retry delay in seconds"""
        return self._razorpay_settings.get("API_SETTINGS", {}).get("retry_delay", 5)

    @property
    def webhook_max_retry_attempts(self):
        """Get maximum webhook retry attempts"""
        return self._razorpay_settings.get("WEBHOOK_SETTINGS", {}).get(
            "max_retry_attempts", 3
        )

    @property
    def webhook_signature_verification(self):
        """Check if webhook signature verification is enabled"""
        return self._razorpay_settings.get("WEBHOOK_SETTINGS", {}).get(
            "signature_verification", True
        )

    @property
    def log_api_requests(self):
        """Check if API request logging is enabled"""
        return self._razorpay_settings.get("LOGGING", {}).get("log_api_requests", True)

    @property
    def log_webhook_events(self):
        """Check if webhook event logging is enabled"""
        return self._razorpay_settings.get("LOGGING", {}).get(
            "log_webhook_events", True
        )

    def get_auth_tuple(self):
        """Get authentication tuple for Razorpay API"""
        if not self.razorpay_key_id or not self.razorpay_key_secret:
            raise ImproperlyConfigured(
                "Cannot create auth tuple: Razorpay credentials are not configured"
            )
        return (self.razorpay_key_id, self.razorpay_key_secret)
    def get_payment_link_config(self):
        """Get payment link configuration"""
        return self._razorpay_settings.get("PAYMENT_LINK_SETTINGS", {})

    def get_route_config(self):
        """Get Route (split payments) configuration"""
        return self._razorpay_settings.get("ROUTE_SETTINGS", {})

    def validate_amount(self, amount):
        """Validate payment amount against configured limits"""
        if amount < self.min_payment_amount:
            raise ValueError(f"Amount must be at least ₹{self.min_payment_amount}")
        if amount > self.max_payment_amount:
            raise ValueError(f"Amount cannot exceed ₹{self.max_payment_amount}")
        return True

    def get_webhook_ip_whitelist(self):
        """Get webhook IP whitelist"""
        return getattr(settings, "PAYMENT_SECURITY_SETTINGS", {}).get(
            "webhook_ip_whitelist", []
        )

    def _get_parsed_webhook_whitelist(self):
        """
        Parse and cache webhook whitelist entries into ipaddress network objects.

        Returns a list of ipaddress.IPv4Network/IPv6Network objects. Invalid
        entries are skipped with a warning.
        """
        if hasattr(self, "_parsed_webhook_whitelist") and self._parsed_webhook_whitelist is not None:
            return self._parsed_webhook_whitelist

        parsed = []
        raw_list = self.get_webhook_ip_whitelist() or []
        for entry in raw_list:
            try:
                # ip_network accepts both single addresses and CIDR ranges
                net = ipaddress.ip_network(entry, strict=False)
                parsed.append(net)
            except Exception as e:
                logger.warning(f"Invalid webhook whitelist entry '{entry}': {e}")

        # Cache parsed list on the instance for performance
        self._parsed_webhook_whitelist = parsed
        return parsed

    def should_verify_webhook_ip(self):
        """Check if webhook IP verification is enabled"""
        return bool(self.get_webhook_ip_whitelist())

    def is_webhook_ip_allowed(self, ip_address):
        """Check if IP address is allowed for webhooks"""
        if not self.should_verify_webhook_ip():
            return True

        try:
            ip = ipaddress.ip_address(ip_address)
        except Exception as e:
            logger.warning(f"Invalid IP address provided for whitelist check: '{ip_address}': {e}")
            return False

        # Normalize IPv4-mapped IPv6 addresses to IPv4 for accurate comparisons
        if isinstance(ip, ipaddress.IPv6Address) and getattr(ip, 'ipv4_mapped', None):
            ip = ip.ipv4_mapped

        parsed_networks = self._get_parsed_webhook_whitelist()
        for net in parsed_networks:
            try:
                # If network is IPv6 but contains IPv4-mapped addresses, handle accordingly
                if isinstance(net, ipaddress.IPv6Network) and isinstance(ip, ipaddress.IPv4Address):
                    # convert IPv4 to IPv4-mapped IPv6 and test containment
                    mapped = ipaddress.IPv6Address('::ffff:' + str(ip))
                    if mapped in net:
                        return True
                else:
                    if ip in net:
                        return True
            except Exception:
                # Defensive: skip any problematic network entries
                continue

        return False


# Global configuration instance
payment_config = PaymentConfigService()
