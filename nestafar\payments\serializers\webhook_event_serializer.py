"""
Webhook Event Serializers

Serializers for webhook event processing and responses.
"""

from rest_framework import serializers
from django.utils import timezone

from ..models import PaymentWebhookEvent
from ..constants import WebhookEventType
from ..utils.razorpay_helpers import is_webhook_event_supported, get_webhook_entity_type


class WebhookEventSerializer(serializers.Serializer):
    """Serializer for incoming webhook events"""
    
    event = serializers.CharField()
    payload = serializers.JSONField()
    created_at = serializers.IntegerField(required=False)  # Unix timestamp from Razorpay
    
    def validate_event(self, value):
        """Validate webhook event type"""
        if not is_webhook_event_supported(value):
            raise serializers.ValidationError(f"Unsupported webhook event: {value}")
        return value
    
    def validate_payload(self, value):
        """Validate webhook payload structure"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("Payload must be a dictionary")
        
        # Check for required payload structure
        entity_type = get_webhook_entity_type(value)
        if not entity_type:
            raise serializers.ValidationError("Invalid payload structure - no entity found")
        
        return value


class WebhookEventResponseSerializer(serializers.ModelSerializer):
    """Serializer for webhook event responses"""
    
    entity_type = serializers.SerializerMethodField()
    entity_id = serializers.SerializerMethodField()
    processing_status = serializers.SerializerMethodField()
    
    class Meta:
        model = PaymentWebhookEvent
        fields = [
            'id', 'event_id', 'event_type', 'entity_type', 'entity_id',
            'processed', 'processing_status', 'processing_attempts',
            'signature_verified', 'received_at', 'processed_at',
            'processing_error', 'payment_intent'
        ]
        read_only_fields = ['id', 'received_at']
    
    def get_entity_type(self, obj):
        """Get entity type from webhook data"""
        return obj.entity_type
    
    def get_entity_id(self, obj):
        """Get entity ID from webhook data"""
        return obj.entity_id
    
    def get_processing_status(self, obj):
        """Get processing status description"""
        if obj.processed:
            return "Processed Successfully"
        elif obj.processing_error:
            return f"Failed: {obj.processing_error}"
        elif obj.processing_attempts > 0:
            return f"Retrying (Attempt {obj.processing_attempts})"
        else:
            return "Pending Processing"


class WebhookProcessingResultSerializer(serializers.Serializer):
    """Serializer for webhook processing results"""
    
    event_id = serializers.CharField()
    event_type = serializers.CharField()
    processed = serializers.BooleanField()
    processing_time_ms = serializers.IntegerField()
    
    # Payment intent details (if applicable)
    payment_intent_id = serializers.UUIDField(allow_null=True)
    payment_status_updated = serializers.BooleanField(default=False)
    transfers_created = serializers.IntegerField(default=0)
    
    # Error details (if failed)
    error_message = serializers.CharField(allow_null=True)
    retry_scheduled = serializers.BooleanField(default=False)
    
    # Actions taken
    actions_performed = serializers.ListField(
        child=serializers.CharField(),
        default=list
    )


class WebhookSignatureVerificationSerializer(serializers.Serializer):
    """Serializer for webhook signature verification"""
    
    signature = serializers.CharField()
    payload = serializers.CharField()  # Raw payload string
    
    def validate_signature(self, value):
        """Validate signature format"""
        if not value:
            raise serializers.ValidationError("Signature is required")
        return value
    
    def validate_payload(self, value):
        """Validate payload is not empty"""
        if not value:
            raise serializers.ValidationError("Payload is required")
        return value


class WebhookRetrySerializer(serializers.Serializer):
    """Serializer for webhook retry requests"""
    
    event_ids = serializers.ListField(
        child=serializers.CharField(),
        min_length=1,
        max_length=10  # Limit batch size
    )
    force_retry = serializers.BooleanField(default=False)
    
    def validate_event_ids(self, value):
        """Perform lightweight validation for event IDs.

        This avoids expensive DB lookups in the serializer and only
        enforces format constraints such as non-empty strings, maximum
        per-item length and duplicate detection. The existence check
        is moved to the view layer where a single bulk query can be
        used to fetch all matching events.
        """
        if not isinstance(value, (list, tuple)):
            raise serializers.ValidationError("event_ids must be a list")

        if len(value) == 0:
            raise serializers.ValidationError("event_ids cannot be empty")

        # Check duplicates
        if len(set(value)) != len(value):
            raise serializers.ValidationError("Duplicate event IDs are not allowed")

        # Per-item basic checks
        cleaned = []
        for item in value:
            if not item or not isinstance(item, str):
                raise serializers.ValidationError("Each event_id must be a non-empty string")
            if len(item) > 255:
                raise serializers.ValidationError("Each event_id must be at most 255 characters")
            cleaned.append(item)

        return cleaned


class WebhookStatsSerializer(serializers.Serializer):
    """Serializer for webhook statistics"""
    
    total_events = serializers.IntegerField()
    processed_events = serializers.IntegerField()
    failed_events = serializers.IntegerField()
    pending_events = serializers.IntegerField()
    
    # Processing rates
    success_rate = serializers.FloatField()
    average_processing_time_ms = serializers.FloatField()
    
    # Event type breakdown
    event_type_stats = serializers.DictField()
    
    # Recent activity
    events_last_24h = serializers.IntegerField()
    events_last_hour = serializers.IntegerField()
    
    # Error summary
    common_errors = serializers.ListField(
        child=serializers.DictField(),
        max_length=5
    )


class WebhookEventListSerializer(serializers.ModelSerializer):
    """Serializer for listing webhook events"""
    
    processing_status = serializers.SerializerMethodField()
    time_since_received = serializers.SerializerMethodField()
    
    class Meta:
        model = PaymentWebhookEvent
        fields = [
            'id', 'event_id', 'event_type', 'entity_type', 'entity_id',
            'processed', 'processing_status', 'processing_attempts',
            'signature_verified', 'received_at', 'time_since_received',
            'payment_intent'
        ]
    
    def get_processing_status(self, obj):
        """Get processing status description"""
        if obj.processed:
            return "success"
        elif obj.processing_error:
            return "failed"
        elif obj.processing_attempts > 0:
            return "retrying"
        else:
            return "pending"
    
    def get_time_since_received(self, obj):
        """Get human-readable time since received"""
        now = timezone.now()
        diff = now - obj.received_at
        
        if diff.days > 0:
            return f"{diff.days} days ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hours ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minutes ago"
        else:
            return "Just now"


class WebhookEventDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed webhook event information"""
    
    entity_data = serializers.SerializerMethodField()
    processing_history = serializers.SerializerMethodField()
    related_payment_intent = serializers.SerializerMethodField()
    
    class Meta:
        model = PaymentWebhookEvent
        fields = [
            'id', 'event_id', 'event_type', 'entity_type', 'entity_id',
            'raw_payload', 'headers', 'entity_data',
            'processed', 'processed_at', 'processing_attempts',
            'processing_error', 'last_error_at',
            'signature_verified', 'signature', 'source_ip',
            'received_at', 'created_at', 'updated_at',
            'payment_intent', 'related_payment_intent',
            'processing_history'
        ]
    
    def get_entity_data(self, obj):
        """Extract entity data from raw payload"""
        return obj.get_entity_data()
    
    def get_processing_history(self, obj):
        """Get processing attempt history from related WebhookProcessingAttempt entries"""
        history = []
        try:
            attempts = obj.processing_attempts.all().order_by('-attempted_at')
            for att in attempts:
                history.append({
                    'attempt': att.attempt_number,
                    'status': att.status,
                    'timestamp': att.attempted_at,
                    'error': att.error_message,
                })
        except Exception:
            # Fallback: return the existing single-entry style for compatibility
            if getattr(obj, 'processing_attempts_count', 0) > 0:
                history.append({
                    'attempt': obj.processing_attempts_count,
                    'status': 'success' if obj.processed else 'failed',
                    'timestamp': obj.processed_at or obj.last_error_at,
                    'error': obj.processing_error if not obj.processed else None
                })
        return history
    
    def get_related_payment_intent(self, obj):
        """Get related payment intent details"""
        if obj.payment_intent:
            return {
                'id': obj.payment_intent.id,
                'reference_number': obj.payment_intent.reference_number,
                'status': obj.payment_intent.status,
                'total_amount': obj.payment_intent.total_amount
            }
        return None
