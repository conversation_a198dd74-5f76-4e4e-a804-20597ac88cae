from django.contrib import admin
from .models import (
    OTAPlatform,
    HotelOTAIntegration,
    RoomType,
    RatePlan,
    Calendar,
    AvailabilityLog,
    RoomBlock,
    RoomBlockSyncLog,
)


@admin.register(OTAPlatform)
class OTAPlatformAdmin(admin.ModelAdmin):
    list_display = ("name", "is_active")
    search_fields = ("name",)


@admin.register(HotelOTAIntegration)
class HotelOTAIntegrationAdmin(admin.ModelAdmin):
    list_display = ("hotel", "ota_platform", "is_active", "created_at")
    list_select_related = ("hotel", "ota_platform")
    autocomplete_fields = ("hotel", "ota_platform")
    search_fields = ("hotel__name", "ota_platform__name")
    list_filter = ("is_active", "ota_platform", "hotel")
    date_hierarchy = "created_at"
    ordering = ("-created_at",)
    show_full_result_count = False


@admin.register(RoomType)
class RoomTypeAdmin(admin.ModelAdmin):
    list_display = ("hotel", "name", "max_occupancy", "get_rooms_count", "created_at")
    list_select_related = ("hotel",)
    autocomplete_fields = ("hotel",)
    search_fields = ("name", "hotel__name")
    list_filter = ("hotel", "max_occupancy")
    readonly_fields = ("created_at", "updated_at", "get_rooms_list")
    
    def get_rooms_count(self, obj):
        """Display count of rooms for this room type"""
        return obj.rooms.count()
    get_rooms_count.short_description = "Number of Rooms"
    
    def get_rooms_list(self, obj):
        """Display list of room numbers for this room type"""
        rooms = obj.rooms.all().order_by('room_no')
        if rooms:
            room_numbers = [room.room_no for room in rooms]
            return ", ".join(room_numbers)
        return "No rooms"
    get_rooms_list.short_description = "Room Numbers"
    
    fieldsets = (
        (None, {
            'fields': ('hotel', 'name', 'description', 'max_occupancy', 'amenities')
        }),
        ('Room Information', {
            'fields': ('get_rooms_list',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(RatePlan)
class RatePlanAdmin(admin.ModelAdmin):
    list_display = (
        "room_type",
        "name",
        "base_rate",
        "valid_from",
        "valid_to",
        "is_active",
    )
    list_select_related = ("room_type", "room_type__hotel")
    autocomplete_fields = ("room_type",)
    search_fields = ("name", "room_type__name", "room_type__hotel__name")
    list_filter = ("is_active", "room_type")
    date_hierarchy = "valid_from"


@admin.register(Calendar)
class CalendarAdmin(admin.ModelAdmin):
    list_display = ("room_type", "date", "available_rooms", "daily_rate")


@admin.register(RoomBlock)
class RoomBlockAdmin(admin.ModelAdmin):
    list_display = (
        "hotel",
        "room",
        "reservation",
        "is_active",
        "blocked_from",
        "blocked_until",
        "reason",
    )
    list_select_related = ("hotel", "room", "reservation")
    autocomplete_fields = ("hotel", "room", "reservation")
    search_fields = ("hotel__name", "room__room_no", "reservation__id")
    list_filter = ("is_active", "reason", "hotel")
    date_hierarchy = "blocked_from"
    readonly_fields = ("id",)


@admin.register(RoomBlockSyncLog)
class RoomBlockSyncLogAdmin(admin.ModelAdmin):
    list_display = (
        "room_block",
        "hotel",
        "ota_platform",
        "action",
        "sync_status",
        "retry_count",
        "created_at",
        "synced_at",
    )
    list_select_related = ("room_block", "hotel", "ota_platform")
    autocomplete_fields = ("room_block", "hotel", "ota_platform")
    search_fields = ("room_block__hotel__name", "hotel__name", "ota_platform__name")
    list_filter = ("sync_status", "action", "ota_platform", "hotel")
    date_hierarchy = "created_at"
    show_full_result_count = False
    ordering = ("-created_at",)
    actions = None
    readonly_fields = (
        "id",
        "room_block",
        "hotel",
        "ota_platform",
        "action",
        "sync_status",
        "retry_count",
        "max_retries",
        "error_message",
        "request_data",
        "response_data",
        "created_at",
        "updated_at",
        "synced_at",
    )

    fieldsets = (
        (
            "Basic Information",
            {"fields": ("room_block", "hotel", "ota_platform", "action")},
        ),
        (
            "Sync Status",
            {"fields": ("sync_status", "retry_count", "max_retries", "error_message")},
        ),
        (
            "Data",
            {"fields": ("request_data", "response_data"), "classes": ("collapse",)},
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "synced_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def has_add_permission(self, request):
        # Prevent manual creation of sync logs
        return False

    def has_change_permission(self, request, obj=None):
        # Logs should be immutable
        return False

    def has_delete_permission(self, request, obj=None):
        # Allow deletion for superusers and when part of cascade operations
        # This handles the case where reservations are being deleted and need to cascade
        if request.user.is_superuser:
            return True
            
        # If this is a bulk operation or cascade delete from admin, allow it
        # Check if user has reservation delete permissions (indicates cascade context)
        if request.user.has_perm('booking.delete_reservation'):
            return True
            
        # For individual log deletion, restrict access
        if obj is not None:
            return False
            
        return False


@admin.register(AvailabilityLog)
class AvailabilityLogAdmin(admin.ModelAdmin):
    list_display = (
        "hotel",
        "room_type",
        "ota_platform",
        "date",
        "available_rooms",
        "sync_status",
    )
    list_select_related = ("hotel", "room_type", "ota_platform")
    autocomplete_fields = ("hotel", "room_type", "ota_platform")
    search_fields = ("hotel__name", "room_type__name", "ota_platform__name")
    list_filter = ("sync_status", "ota_platform", "hotel")
    date_hierarchy = "date"
    show_full_result_count = False
    ordering = ("-date",)
    readonly_fields = ("id", "created_at", "synced_at")

    fieldsets = (
        (
            "Basic Information",
            {
                "fields": (
                    "hotel",
                    "room_type",
                    "ota_platform",
                    "date",
                    "available_rooms",
                )
            },
        ),
        ("Sync Status", {"fields": ("sync_status", "synced_at")}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )
