from django.core.management.base import BaseCommand
from django.conf import settings
from notification.channel.whatsapp import WhatsAppChannel
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Test templates that need to be created in WhatsApp Business Manager - shows what needs to be configured"

    def add_arguments(self, parser):
        parser.add_argument(
            "--phone",
            type=str,
            required=True,
            help="Phone number to test (with country code, e.g., +2347088212727)",
        )
        parser.add_argument(
            "--template",
            type=str,
            help="Specific template category to test (e.g., USER_ORDER_ACCEPTED)",
        )
        parser.add_argument(
            "--debug", action="store_true", help="Enable detailed debug output"
        )
        parser.add_argument(
            "--test-payment-templates",
            action="store_true",
            help="Test payment-specific templates"
        )

    def handle(self, *args, **options):
        phone = options["phone"]
        specific_template = options.get("template")
        debug_mode = options.get("debug", False)
        test_payment_templates = options.get("test_payment_templates", False)

        # If testing payment templates, run payment-specific tests
        if test_payment_templates:
            self._test_payment_templates(phone)
            return

        self.stdout.write(
            "🔧 Testing Templates That Need WhatsApp Business Manager Setup\n"
        )
        self.stdout.write(
            "⚠️  These templates are NOT YET configured in WhatsApp Business Manager\n"
        )
        self.stdout.write("📋 Use this to verify which templates need to be created\n")

        try:
            # Initialize WhatsApp channel
            whatsapp = WhatsAppChannel()

            if debug_mode:
                self.stdout.write("🔍 Debug mode enabled\n")
                self.stdout.write(f"📞 Target phone: {phone}")
                self.stdout.write(f"📱 Phone Number ID: {whatsapp.phone_number_id}")
                self.stdout.write("")

            # Define test templates that need to be created
            test_templates = self.get_templates_needing_setup()

            # Filter to specific template if requested
            if specific_template:
                if specific_template in test_templates:
                    test_templates = {
                        specific_template: test_templates[specific_template]
                    }
                else:
                    self.stdout.write(
                        f'❌ Template category "{specific_template}" not found'
                    )
                    self.stdout.write(
                        f'Available categories: {", ".join(test_templates.keys())}'
                    )
                    return

            successful_tests = 0
            failed_tests = 0

            self.stdout.write(
                f"🔄 Testing {len(test_templates)} templates needing setup...\n"
            )

            for category, template_data in test_templates.items():
                self.stdout.write(f"🔸 Testing {category}")

                # Extract parameters for the template
                kwargs = template_data["kwargs"]
                expected_params = template_data["expected_params"]

                if debug_mode:
                    self.stdout.write(
                        f"   📋 Template name: {whatsapp._get_template_name(category)}"
                    )
                    self.stdout.write(f"   📋 Expected parameters: {expected_params}")
                    self.stdout.write(f"   📋 Test parameters: {kwargs}")

                # Get template name and components
                template_name = whatsapp._get_template_name(category)
                components = whatsapp._build_template_components(category, **kwargs)

                # Format phone number for API
                formatted_phone = whatsapp.format_phone_number(phone)

                if debug_mode:
                    self.stdout.write(f"   📞 Formatted phone: {formatted_phone}")
                    self.stdout.write(f"   📋 Components: {components}")

                # Send template message directly (this will fail as expected)
                success, result = whatsapp.send_template_message_to_phone(
                    formatted_phone, template_name, components
                )

                if success:
                    successful_tests += 1
                    self.stdout.write(
                        f"   ✅ Template sent successfully! Message ID: {result}"
                    )
                    self.stdout.write(
                        f'   🎉 Template "{template_name}" exists and is approved!'
                    )
                else:
                    failed_tests += 1
                    self.stdout.write(f"   ❌ Template failed: {result}")
                    if (
                        "template does not exist" in str(result).lower()
                        or "template name" in str(result).lower()
                    ):
                        self.stdout.write(
                            f'   📋 Template "{template_name}" needs to be created in WhatsApp Business Manager'
                        )
                    elif "parameter" in str(result).lower():
                        self.stdout.write(
                            f'   📋 Template "{template_name}" exists but parameter count mismatch'
                        )
                    else:
                        self.stdout.write(
                            f'   📋 Template "{template_name}" may need approval or has other issues'
                        )

                if debug_mode:
                    self.stdout.write("")

                self.stdout.write("")  # Add spacing between tests

            # Summary
            self.stdout.write("📊 Test Summary:")
            self.stdout.write(f"   ✅ Working: {successful_tests} (already configured)")
            self.stdout.write(f"   ❌ Failed: {failed_tests} (need to be created)")
            self.stdout.write(f"   📋 Total: {successful_tests + failed_tests}")

            if successful_tests > 0:
                self.stdout.write("\n🎉 Some templates are already working!")
                self.stdout.write(
                    "   ✅ These templates are properly configured and approved"
                )

            if failed_tests > 0:
                self.stdout.write("\n🔧 Templates needing setup:")
                self.stdout.write(
                    "   📋 Create these templates in WhatsApp Business Manager"
                )
                self.stdout.write("   ⏰ Wait for approval (24-48 hours)")
                self.stdout.write(
                    "   📖 Check WHATSAPP_TEMPLATES.md for suggested template content"
                )
                self.stdout.write("\n📝 Next steps:")
                self.stdout.write(
                    "   1. Go to WhatsApp Business Manager → Message Templates"
                )
                self.stdout.write(
                    "   2. Create templates with exact parameter counts shown above"
                )
                self.stdout.write(
                    "   3. Use UTILITY category for transactional messages"
                )
                self.stdout.write("   4. Wait for approval (24-48 hours)")
                self.stdout.write("   5. Re-run this test to verify")

        except Exception as e:
            self.stdout.write(f"❌ Error: {str(e)}")
            import traceback

            if debug_mode:
                self.stdout.write(traceback.format_exc())

    def get_templates_needing_setup(self):
        """Define test data for templates that need to be created in WhatsApp Business Manager"""
        return {
            # Account management templates
            "ACCOUNT_ACTIVATED": {
                "template_name": "account_activated",
                "kwargs": {
                    "partner_name": "John Doe",
                    "account_id": "acc_1234567890123456",
                    "activation_date": "January 15, 2025",
                    "bank_account_details": "HDFC Bank ****1234",
                    "dashboard_url": "https://partner.nestafar.com",
                    "support_contact": "+91-**********"
                },
                "expected_params": 6,
                "whatsapp_template": """
Category: UTILITY
Language: English (en)

Header: None

Body:
🎉 *Account Activated!*

Hi {{1}}! 👋

Your Razorpay account has been successfully activated. You can now receive payments directly to your bank account.

✅ Account Status: *Active*
🆔 Account ID: {{2}}
📅 Activated: {{3}}
💳 Settlement: {{4}}
🔗 Dashboard: {{5}}

🚀 You're now ready to:
• Accept online payments
• Receive automatic settlements
• Track all transactions

📞 Need help? Contact: {{6}}

Thank you for choosing Nestafar! 🌟

_Powered by Nestafar_

Footer: None

Buttons:
- URL Button: "Open Dashboard" → {{5}}

Variables: 6
1. Partner name
2. Account ID
3. Activation date
4. Bank account details
5. Dashboard URL
6. Support contact
                """
            },
            "ACCOUNT_SUSPENDED": {
                "template_name": "account_suspended",
                "kwargs": {
                    "partner_name": "John Doe",
                    "account_id": "acc_1234567890123456",
                    "suspension_reason": "KYC documents pending",
                    "suspension_date": "January 15, 2025",
                    "appeal_process": "contact our support team",
                    "support_contact": "+91-**********"
                },
                "expected_params": 6,
                "whatsapp_template": """
Category: UTILITY
Language: English (en)

Header: None

Body:
⚠️ *Account Suspended*

Hi {{1}},

Your Razorpay account has been suspended.

🆔 Account ID: {{2}}
📅 Suspended: {{4}}
📋 Reason: {{3}}

⚡ *Impact:*
• Payment acceptance is paused
• Settlements are on hold
• Account access restricted

🔄 *Next Steps:*
• Review suspension reason
• {{5}}
• Provide required documentation

📞 Support: {{6}}
📧 Email: <EMAIL>

We're here to help resolve this quickly.

_Powered by Nestafar_

Footer: None

Buttons:
- Phone Button: "Call Support" → {{6}}

Variables: 6
1. Partner name
2. Account ID
3. Suspension reason
4. Suspension date
5. Appeal process
6. Support contact
                """
            },
            "ACCOUNT_UNDER_REVIEW": {
                "template_name": "account_under_review",
                "kwargs": {
                    "partner_name": "John Doe",
                    "account_id": "acc_1234567890123456",
                    "review_reason": "routine compliance check",
                    "estimated_duration": "1-2 business days",
                    "review_date": "January 15, 2025",
                    "support_contact": "+91-**********"
                },
                "expected_params": 6,
                "whatsapp_template": """
Category: UTILITY
Language: English (en)

Header: None

Body:
📋 *Account Under Review*

Hi {{1}}! 👋

Your Razorpay account is currently under review. This is a standard process to ensure account security.

🆔 Account ID: {{2}}
📅 Review Started: {{5}}
📝 Reason: {{3}}
⏱️ Estimated Duration: {{4}}

🔍 *During Review:*
• Account remains functional
• Payments can be accepted
• Settlements may be delayed

📧 We'll notify you once the review is complete
📞 Questions? Contact: {{6}}

Thank you for your patience! 🙏

_Powered by Nestafar_

Footer: None

Buttons: None

Variables: 6
1. Partner name
2. Account ID
3. Review reason
4. Estimated duration
5. Review date
6. Support contact
                """
            },
            "KYC_VERIFIED": {
                "template_name": "kyc_verified",
                "kwargs": {
                    "partner_name": "John Doe",
                    "document_type": "Aadhaar Card",
                    "verification_date": "January 15, 2025",
                    "account_status": "Active",
                    "dashboard_url": "https://partner.nestafar.com",
                    "next_steps": "Start accepting payments"
                },
                "expected_params": 6,
                "whatsapp_template": """
Category: UTILITY
Language: English (en)

Header: None

Body:
✅ *KYC Verified!*

Hi {{1}}! 🎉

Your KYC document has been successfully verified.

📄 Document: {{2}}
📅 Verified: {{3}}
✅ Status: *Approved*
🏦 Account: {{4}}

🚀 *You can now:*
• Accept payments without limits
• Receive instant settlements
• Access full account features

🔗 Manage Account: {{5}}
📈 Next: {{6}}

Welcome to the Nestafar partner network! 🌟

_Powered by Nestafar_

Footer: None

Buttons:
- URL Button: "Manage Account" → {{5}}

Variables: 6
1. Partner name
2. Document type
3. Verification date
4. Account status
5. Dashboard URL
6. Next steps
                """
            },
            "KYC_REJECTED": {
                "template_name": "kyc_rejected",
                "kwargs": {
                    "partner_name": "John Doe",
                    "document_type": "Aadhaar Card",
                    "rejection_reason": "Document image unclear",
                    "resubmission_url": "https://partner.nestafar.com/kyc",
                    "support_contact": "+91-**********",
                    "deadline": "7 days"
                },
                "expected_params": 6,
                "whatsapp_template": """
Category: UTILITY
Language: English (en)

Header: None

Body:
❌ *KYC Document Rejected*

Hi {{1}},

Your KYC document verification was unsuccessful.

📄 Document: {{2}}
❗ Reason: {{3}}
⏰ Action Required: Within {{6}}

🔄 *To Resolve:*
• Upload a clear, valid document
• Ensure all details are visible
• Match registered information

🔗 Resubmit: {{4}}
📞 Help: {{5}}

⚠️ Account limitations may apply until verification

_Powered by Nestafar_

Footer: None

Buttons:
- URL Button: "Resubmit Documents" → {{4}}
- Phone Button: "Get Help" → {{5}}

Variables: 6
1. Partner name
2. Document type
3. Rejection reason
4. Resubmission URL
5. Support contact
6. Deadline
                """
            },
            "KYC_UNDER_REVIEW": {
                "template_name": "kyc_under_review",
                "kwargs": {
                    "partner_name": "John Doe",
                    "document_type": "Aadhaar Card",
                    "submission_date": "January 15, 2025",
                    "estimated_duration": "24-48 hours",
                    "reference_id": "KYC123456",
                    "support_contact": "+91-**********"
                },
                "expected_params": 6,
                "whatsapp_template": """
Category: UTILITY
Language: English (en)

Header: None

Body:
🔍 *KYC Under Review*

Hi {{1}}! 👋

Your KYC document is being reviewed by our verification team.

📄 Document: {{2}}
📅 Submitted: {{3}}
🆔 Reference: {{5}}
⏱️ Review Time: {{4}}

📋 *Review Process:*
• Document authenticity check
• Information verification
• Compliance validation

📧 We'll notify you once complete
📞 Questions? Contact: {{6}}

Thank you for your patience! 🙏

_Powered by Nestafar_

Footer: None

Buttons: None

Variables: 6
1. Partner name
2. Document type
3. Submission date
4. Estimated duration
5. Reference ID
6. Support contact
                """
            },
            # Payment templates (existing ones)
            "PAYMENT_LINK_CREATED": {
                "template_name": "payment_link_created",
                "kwargs": {
                    "customer_name": "John Doe",
                    "context_type": "checkout payment",
                    "amount": "2500.00",
                    "property_name": "Sunrise Hotel",
                    "reference_number": "PAY123456",
                    "payment_url": "https://rzp.io/i/abcd1234"
                },
                "expected_params": 6,
                "whatsapp_template": """
Category: UTILITY
Language: English (en)

Body: 🏨 *Payment Request*

Hi {{1}}! 👋

Please complete your {{2}} payment:

💰 Amount: *₹{{3}}*
🏢 Property: {{4}}
📋 Reference: {{5}}

👆 Click to pay securely: {{6}}

💳 Safe & secure payment powered by Razorpay
🔒 Your payment details are protected

_Powered by Nestafar_

Buttons:
- URL Button: "Pay Now" → {{6}}

Variables: 6
                """
            },
        }
        return {
            # User order flow templates
            "USER_CHECKOUT": {
                "expected_params": 2,
                "kwargs": {"username": "John Doe", "property_name": "Grand Hotel"},
            },
            "USER_ORDER_ACCEPTED": {
                "expected_params": 6,
                "kwargs": {
                    "username": "John Doe",
                    "order_id": "ORD-12345",
                    "vendor_name": "Restaurant ABC",
                    "estimated_time": "30 minutes",
                    "total_amount": "₹250",
                    "vendor_contact": "+91**********",
                },
            },
            "USER_ORDER_CANCELLED": {
                "expected_params": 6,
                "kwargs": {
                    "username": "John Doe",
                    "order_id": "ORD-12345",
                    "service_type": "Food",
                    "reason": "Vendor unavailable",
                    "refund_amount": "₹250",
                    "additional_info": "Refund will be processed in 3-5 working days",
                },
            },
            "USER_ORDER_ONGOING": {
                "expected_params": 5,
                "kwargs": {
                    "username": "John Doe",
                    "order_id": "ORD-12345",
                    "vendor_name": "Restaurant ABC",
                    "estimated_time": "15 minutes",
                    "contact_number": "+91**********",
                },
            },
            "USER_ORDER_REJECTED": {
                "expected_params": 5,
                "kwargs": {
                    "username": "John Doe",
                    "order_id": "ORD-12345",
                    "service_type": "Food",
                    "rejection_reason": "Item out of stock",
                    "refund_amount": "₹250",
                },
            },
            # Critical missing templates for core business flows
            "PRECHECKIN_CREATED": {
                "expected_params": 5,
                "kwargs": {
                    "guest_name": "John Doe",
                    "property_owner_name": "Grand Hotel",
                    "expected_date": "15 Jan 2025",
                    "room_number": "101",
                    "precheckin_link": "https://app.nestafar.com/precheckin/abc123",
                },
            },
            "PRECHECKIN_CONFIRMED": {
                "expected_params": 4,
                "kwargs": {
                    "guest_name": "John Doe",
                    "property_owner_name": "Grand Hotel Manager",
                    "expected_date": "2025-01-15",
                    "room_number": "101",
                },
            },
            "PRECHECKIN_STATUS_CHANGED": {
                "expected_params": 4,
                "kwargs": {
                    "guest_name": "John Doe",
                    "property_owner_name": "Grand Hotel",
                    "status": "Confirmed",
                    "room_number": "101",
                },
            },
            "CHECKOUT_BILLS": {
                "expected_params": 4,
                "kwargs": {
                    "guest_name": "John Doe",
                    "property_name": "Grand Hotel",
                    "checkout_date": "2025-01-15",
                    "total_amount": "₹4500",
                },
            },
            "ORDER_CONFIRMED": {
                "expected_params": 6,
                "kwargs": {
                    "guest_name": "John Doe",
                    "service_type": "Food",
                    "order_id": "ORD-12345",
                    "order_items": "Chicken Biryani, Raita, Dessert",
                    "total_amount": "₹450",
                    "delivery_time": "30 minutes",
                },
            },
            "CHECKIN_SUCCESSFUL": {
                "expected_params": 4,
                "kwargs": {
                    "guest_name": "John Doe",
                    "property_name": "Grand Hotel",
                    "room_details": "Room 101 - Deluxe AC",
                    "checkout_date": "2025-01-18",
                },
            },
            "PRECHECKIN_CANCELLATION_WARNING": {
                "expected_params": 3,
                "kwargs": {
                    "guest_name": "John Doe",
                    "property_name": "Grand Hotel",
                    "hours_remaining": "6",
                },
            },
            "PRECHECKIN_REMINDER": {
                "expected_params": 4,
                "kwargs": {
                    "guest_name": "John Doe",
                    "property_name": "Grand Hotel",
                    "checkin_date": "2025-01-15",
                    "hours_remaining": "24",
                },
            },
            "ONBOARDING_REMINDER": {
                "expected_params": 4,
                "kwargs": {
                    "partner_name": "Hotel Owner",
                    "property_name": "Grand Hotel",
                    "missing_steps": [
                        "Property photos",
                        "Room photos",
                        "Services setup",
                    ],
                    "completion_percentage": "60",
                },
            },
            "ONBOARDING_COMPLETED": {
                "expected_params": 2,
                "kwargs": {
                    "partner_name": "Hotel Owner",
                    "property_name": "Grand Hotel",
                },
            },
            "SIGNUP_SUCCESSFUL": {
                "expected_params": 2,
                "kwargs": {
                    "partner_name": "Hotel Owner",
                    "property_name": "Grand Hotel",
                },
            },
            # External reservation alert for property staff
            "EXTERNAL_RESERVATION_ALERT": {
                "expected_params": 9,
                "kwargs": {
                    "guest_name": "John Smith",
                    "property_name": "Grand Hotel",
                    "checkin_date": "15 Dec 2025",
                    "checkout_date": "18 Dec 2025",
                    "guest_count": "2",
                    "room_type": "Deluxe Suite",
                    "booking_source": "Booking.com",
                    "total_amount": "15,500.00",
                    "external_booking_id": "BK123456789",
                },
            },
            # Partner order flow templates
            "PARTNER_ORDER_PLACED": {
                "expected_params": 4,
                "kwargs": {
                    "partner_name": "Restaurant ABC",
                    "order_id": "ORD-12345",
                    "room_no": "101",
                    "guest_name": "John Doe",
                },
            },
            "PARTNER_ORDER_ACCEPTED": {
                "expected_params": 4,
                "kwargs": {
                    "partner_name": "Restaurant ABC",
                    "order_id": "ORD-12345",
                    "room_no": "101",
                    "guest_name": "John Doe",
                },
            },
            "PARTNER_ORDER_ONGOING": {
                "expected_params": 5,
                "kwargs": {
                    "partner_name": "Restaurant ABC",
                    "order_id": "ORD-12345",
                    "room_no": "101",
                    "guest_name": "John Doe",
                    "estimated_completion": "15 minutes",
                },
            },
            "PARTNER_ORDER_COMPLETED": {
                "expected_params": 4,
                "kwargs": {
                    "partner_name": "Restaurant ABC",
                    "order_id": "ORD-12345",
                    "room_no": "101",
                    "guest_name": "John Doe",
                },
            },
            "PARTNER_ORDER_CANCELLED": {
                "expected_params": 4,
                "kwargs": {
                    "partner_name": "Restaurant ABC",
                    "order_id": "ORD-12345",
                    "room_no": "101",
                    "guest_name": "John Doe",
                },
            },
            "PARTNER_ORDER_REJECTED": {
                "expected_params": 7,
                "kwargs": {
                    "partner_name": "Restaurant ABC",
                    "order_id": "ORD-12345",
                    "room_no": "101",
                    "guest_name": "John Doe",
                    "rejection_reason": "Item out of stock",
                    "refund_amount": "₹250",
                    "dashboard_link": "https://partner.nestafar.com",
                },
            },
            # Service management templates
            "DINNER_REMINDER": {
                "expected_params": 2,
                "kwargs": {"guest_name": "John Doe", "property_name": "Grand Hotel"},
            },
            "VENDOR_ORDER_REMINDER": {
                "expected_params": 3,
                "kwargs": {
                    "vendor_name": "Restaurant ABC",
                    "order_id": "ORD-12345",
                    "minutes_pending": "15",
                },
            },
            "SERVICE_HIDDEN_NOTIFICATION": {
                "expected_params": 3,
                "kwargs": {
                    "guest_name": "John Doe",
                    "service_name": "Food Delivery",
                    "property_name": "Grand Hotel",
                },
            },
            "SERVICE_RESTORED_NOTIFICATION": {
                "expected_params": 3,
                "kwargs": {
                    "guest_name": "John Doe",
                    "service_name": "Food Delivery",
                    "property_name": "Grand Hotel",
                },
            },
            # Summary templates
            "DAILY_SUMMARY_GUEST": {
                "expected_params": 5,
                "kwargs": {
                    "username": "John Doe",
                    "total_orders": "3",
                    "total_spent": "₹750",
                    "property_name": "Grand Hotel",
                    "most_ordered_service": "Food Delivery",
                },
            },
            "DAILY_SUMMARY_PARTNER": {
                "expected_params": 4,
                "kwargs": {
                    "partner_name": "Restaurant ABC",
                    "total_orders": "12",
                    "total_revenue": "₹3000",
                    "property_name": "Grand Hotel",
                },
            },
            "WEEKLY_REPORT": {
                "expected_params": 10,
                "kwargs": {
                    "partner_name": "Restaurant ABC",
                    "property_name": "Grand Hotel",
                    "week_start": "2025-07-21",
                    "week_end": "2025-07-27",
                    "reservations": "45",
                    "occupancy_rate": "85",
                    "avg_orders": "2.3",
                    "gmv": "₹15000",
                    "commission": "₹1500",
                    "recommendations": "Great performance! Consider expanding menu.",
                },
            },
            # Job management templates
            "JOB_ASSIGNED": {
                "expected_params": 7,
                "kwargs": {
                    "staff_name": "John Smith",
                    "job_id": "JOB-12345",
                    "job_type": "housekeeping",
                    "room": "101",
                    "pickup_location": "Front Desk",
                    "dropoff_location": "Room 101",
                    "earnings": "₹150",
                },
            },
            "JOB_STATUS_CHANGED": {
                "expected_params": 3,
                "kwargs": {
                    "staff_name": "John Smith",
                    "job_id": "JOB-12345",
                    "new_status": "In Progress",
                },
            },
            "DELIVERY_ASSIGNED": {
                "expected_params": 5,
                "kwargs": {
                    "staff_name": "Mike Johnson",
                    "job_id": "DEL-12345",
                    "pickup_location": "Restaurant ABC, Main Street",
                    "dropoff_location": "Hotel Grand - Room 205",
                    "earnings": "₹80",
                },
            },
            # Payment system templates (NEW - Need to be created)
            "PAYMENT_LINK_CREATED": {
                "expected_params": 6,
                "kwargs": {
                    "customer_name": "John Doe",
                    "context_type": "checkout",
                    "amount": "2500.00",
                    "property_name": "Hotel Paradise",
                    "reference_number": "PAY20250908001",
                    "payment_url": "https://rzp.io/l/test123456",
                },
            },
            "PAYMENT_SUCCESS": {
                "expected_params": 6,
                "kwargs": {
                    "customer_name": "John Doe",
                    "amount": "2500.00",
                    "property_name": "Hotel Paradise",
                    "payment_method": "Card",
                    "reference_number": "PAY20250908001",
                    "payment_id": "pay_test123456",
                },
            },
            "PAYMENT_FAILED": {
                "expected_params": 6,
                "kwargs": {
                    "customer_name": "John Doe",
                    "amount": "2500.00",
                    "property_name": "Hotel Paradise",
                    "reference_number": "PAY20250908001",
                    "error_reason": "Insufficient funds",
                    "retry_url": "https://rzp.io/l/retry123456",
                },
            },
            "PAYMENT_REMINDER": {
                "expected_params": 6,
                "kwargs": {
                    "customer_name": "John Doe",
                    "amount": "2500.00",
                    "property_name": "Hotel Paradise",
                    "reference_number": "PAY20250908001",
                    "hours_remaining": "12",
                    "payment_url": "https://rzp.io/l/test123456",
                },
            },
            "TRANSFER_SUCCESS": {
                "expected_params": 6,
                "kwargs": {
                    "recipient_name": "Hotel Paradise",
                    "amount": "1900.00",
                    "payment_reference": "PAY20250908001",
                    "transfer_id": "trf_test123456",
                    "settlement_date": "09 Sep 2025",
                    "recipient_type": "Partner",
                },
            },
            "TRANSFER_FAILED": {
                "expected_params": 5,
                "kwargs": {
                    "recipient_name": "Hotel Paradise",
                    "amount": "1900.00",
                    "payment_reference": "PAY20250908001",
                    "recipient_type": "Partner",
                    "error_message": "Invalid account details",
                },
            },
        }

        return templates

    def _test_payment_templates(self, phone_number):
        """Test payment-specific templates including account management"""
        self.stdout.write(
            self.style.SUCCESS("🔧 Testing Account Management Templates\n")
        )
        
        account_templates = [
            "ACCOUNT_ACTIVATED",
            "ACCOUNT_SUSPENDED", 
            "ACCOUNT_UNDER_REVIEW",
            "KYC_VERIFIED",
            "KYC_REJECTED",
            "KYC_UNDER_REVIEW"
        ]
        
        templates_data = self.get_templates_needing_setup()
        working_count = 0
        failed_count = 0
        
        for template_category in account_templates:
            if template_category in templates_data:
                template_info = templates_data[template_category]
                
                self.stdout.write(f"\n{'='*60}")
                self.stdout.write(f"Testing: {template_category}")
                self.stdout.write(f"Template: {template_info['template_name']}")
                self.stdout.write(f"{'='*60}")
                
                # Test the template with WhatsApp API
                try:
                    from notification.channel.whatsapp import WhatsAppChannel
                    channel = WhatsAppChannel()
                    
                    # Try to send template message
                    self.stdout.write(f"\n🔄 Testing template: {template_info['template_name']}")
                    
                    success, result = channel.send_message_to_phone(
                        phone_number,
                        "Account notification test",
                        title="Test Notification",
                        category=template_category,
                        **template_info.get('kwargs', {})
                    )
                    
                    if success:
                        working_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(f"✅ Template '{template_info['template_name']}' is WORKING and APPROVED!")
                        )
                        self.stdout.write(f"📱 Message ID: {result}")
                    else:
                        failed_count += 1
                        self.stdout.write(
                            self.style.ERROR(f"❌ Template '{template_info['template_name']}' FAILED: {result}")
                        )
                        
                        # Display WhatsApp Business Manager template for failed ones
                        self.stdout.write(
                            self.style.WARNING("📋 WhatsApp Business Manager Template:")
                        )
                        self.stdout.write(template_info['whatsapp_template'])
                    
                except Exception as e:
                    failed_count += 1
                    self.stdout.write(
                        self.style.ERROR(f"❌ Error testing {template_category}: {e}")
                    )
        
        # Summary
        self.stdout.write(f"\n{'='*60}")
        self.stdout.write(
            self.style.SUCCESS("📊 Account Management Templates Test Summary:")
        )
        self.stdout.write(f"✅ Working (Approved): {working_count}")
        self.stdout.write(f"❌ Failed (Need Setup): {failed_count}")
        self.stdout.write(f"📋 Total Tested: {working_count + failed_count}")
        
        if working_count > 0:
            self.stdout.write(
                self.style.SUCCESS(f"\n🎉 {working_count} templates are ready for production use!")
            )
        
        if failed_count > 0:
            self.stdout.write(
                self.style.WARNING(
                    f"\n⚠️ {failed_count} templates need to be created in WhatsApp Business Manager"
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    "\n🎉 ALL ACCOUNT MANAGEMENT TEMPLATES ARE WORKING!"
                )
            )
        """Test payment-specific templates with comprehensive WhatsApp integration."""
        self.stdout.write("💳 Testing Payment System WhatsApp Templates")
        self.stdout.write("=" * 60)
        self.stdout.write("🔧 These templates need to be created in WhatsApp Business Manager\n")

        try:
            # Initialize WhatsApp channel
            whatsapp = WhatsAppChannel()
            formatted_phone = whatsapp.format_phone_number(phone_number)
            
            if not formatted_phone:
                self.stdout.write(f"❌ Invalid phone number: {phone_number}")
                return
            
            self.stdout.write(f"📞 Testing with phone number: {formatted_phone}")
            self.stdout.write(f"📱 Phone Number ID: {whatsapp.phone_number_id}\n")

            # Define payment template test data
            payment_templates = {
                "PAYMENT_LINK_CREATED": {
                    "expected_params": 6,
                    "kwargs": {
                        "customer_name": "John Doe",
                        "context_type": "checkout",
                        "amount": "2500.00",
                        "property_name": "Hotel Paradise",
                        "reference_number": "PAY20250908001",
                        "payment_url": "https://rzp.io/l/test123456",
                    },
                    "description": "Send payment link to customers for checkout or precheckin payments",
                },
                "PAYMENT_SUCCESS": {
                    "expected_params": 6,
                    "kwargs": {
                        "customer_name": "John Doe",
                        "amount": "2500.00",
                        "property_name": "Hotel Paradise",
                        "payment_method": "Card",
                        "reference_number": "PAY20250908001",
                        "payment_id": "pay_test123456",
                    },
                    "description": "Confirm successful payment to customers",
                },
                "PAYMENT_FAILED": {
                    "expected_params": 6,
                    "kwargs": {
                        "customer_name": "John Doe",
                        "amount": "2500.00",
                        "property_name": "Hotel Paradise",
                        "reference_number": "PAY20250908001",
                        "error_reason": "Insufficient funds",
                        "retry_url": "https://rzp.io/l/retry123456",
                    },
                    "description": "Notify customers about payment failures",
                },
                "PAYMENT_REMINDER": {
                    "expected_params": 6,
                    "kwargs": {
                        "customer_name": "John Doe",
                        "amount": "2500.00",
                        "property_name": "Hotel Paradise",
                        "reference_number": "PAY20250908001",
                        "hours_remaining": "12",
                        "payment_url": "https://rzp.io/l/test123456",
                    },
                    "description": "Remind customers about pending payments",
                },
                "TRANSFER_SUCCESS": {
                    "expected_params": 6,
                    "kwargs": {
                        "recipient_name": "Hotel Paradise",
                        "amount": "1900.00",
                        "payment_reference": "PAY20250908001",
                        "transfer_id": "trf_test123456",
                        "settlement_date": "09 Sep 2025",
                        "recipient_type": "Partner",
                    },
                    "description": "Notify partners about successful payment transfers",
                },
                "TRANSFER_FAILED": {
                    "expected_params": 6,
                    "kwargs": {
                        "recipient_name": "Hotel Paradise",
                        "amount": "1900.00",
                        "payment_reference": "PAY20250908001",
                        "recipient_type": "Partner",
                        "error_message": "Invalid account details",
                        "support_contact": "+91-**********",
                    },
                    "description": "Notify partners about failed payment transfers",
                },
            }

            successful_tests = 0
            failed_tests = 0

            # Test each payment template
            for category, template_data in payment_templates.items():
                self.stdout.write(f"🔸 Testing {category}")
                self.stdout.write(f"   📋 Purpose: {template_data['description']}")

                # Extract parameters for the template
                kwargs = template_data["kwargs"]
                expected_params = template_data["expected_params"]

                # Get template name and components
                template_name = whatsapp._get_template_name(category)
                if not template_name:
                    self.stdout.write(f"   ❌ No template mapping found for {category}")
                    failed_tests += 1
                    continue

                components = whatsapp._build_template_components(category, **kwargs)

                self.stdout.write(f"   📋 Template name: {template_name}")
                self.stdout.write(f"   📋 Expected parameters: {expected_params}")
                self.stdout.write(f"   📋 Generated components: {len(components)} component(s)")

                # Show parameter details
                for component in components:
                    if component["type"] == "body" and "parameters" in component:
                        self.stdout.write(f"   📋 Body parameters: {len(component['parameters'])} params")
                        for i, param in enumerate(component["parameters"]):
                            self.stdout.write(f"      {i+1}. {param['text']}")

                # Test the template (this will fail as expected since templates don't exist yet)
                success, result = whatsapp.send_template_message_to_phone(
                    formatted_phone, template_name, components
                )

                if success:
                    successful_tests += 1
                    self.stdout.write(f"   ✅ Template sent successfully! Message ID: {result}")
                    self.stdout.write(f'   🎉 Template "{template_name}" exists and is approved!')
                else:
                    failed_tests += 1
                    self.stdout.write(f"   ❌ Template failed: {result}")
                    if "template does not exist" in str(result).lower() or "template name" in str(result).lower():
                        self.stdout.write(f'   📋 Template "{template_name}" needs to be created in WhatsApp Business Manager')
                    elif "parameter" in str(result).lower():
                        self.stdout.write(f'   📋 Template "{template_name}" exists but parameter count mismatch')
                    else:
                        self.stdout.write(f'   📋 Template "{template_name}" may need approval or has other issues')

                self.stdout.write("")  # Add spacing between tests

            # Summary
            self.stdout.write("📊 Payment Templates Test Summary:")
            self.stdout.write(f"   ✅ Working: {successful_tests} (already configured)")
            self.stdout.write(f"   ❌ Failed: {failed_tests} (need to be created)")
            self.stdout.write(f"   📋 Total: {successful_tests + failed_tests}")

            if successful_tests > 0:
                self.stdout.write("\n🎉 Some payment templates are already working!")
                self.stdout.write("   ✅ These templates are properly configured and approved")

            if failed_tests > 0:
                self.stdout.write("\n🔧 Payment Templates needing setup:")
                self.stdout.write("   📋 Create these templates in WhatsApp Business Manager")
                self.stdout.write("   📝 Use the template content from WHATSAPP_TEMPLATES.md")
                self.stdout.write("   🏷️ Category: UTILITY (for transactional messages)")
                self.stdout.write("   ⏰ Wait for approval (24-48 hours)")
                self.stdout.write("\n📋 Template Creation Checklist:")
                for category, template_data in payment_templates.items():
                    template_name = whatsapp._get_template_name(category)
                    if template_name:
                        self.stdout.write(f"   □ {template_name} ({template_data['expected_params']} parameters)")

                self.stdout.write("\n📝 Next steps:")
                self.stdout.write("   1. Go to WhatsApp Business Manager → Message Templates")
                self.stdout.write("   2. Create templates with exact parameter counts shown above")
                self.stdout.write("   3. Use template content from WHATSAPP_TEMPLATES.md")
                self.stdout.write("   4. Set category to UTILITY for transactional messages")
                self.stdout.write("   5. Wait for approval (24-48 hours)")
                self.stdout.write("   6. Re-run this test to verify")

        except Exception as e:
            self.stdout.write(f"❌ Error testing payment templates: {str(e)}")
            import traceback
            self.stdout.write(traceback.format_exc())
