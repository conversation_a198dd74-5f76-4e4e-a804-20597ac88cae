# Stay Utils

This directory contains utility functions for the stay app, specifically for checkout functionality.

## Files

### order_utils.py
Contains functions for managing orders during checkout:
- `cancel_all_pending_orders_for_guests(guests)`: Cancels all pending orders for guests that are not in COMPLETED or REJECTED state.

### pdf_utils.py
Contains functions for generating PDF invoices:
- `generate_invoice_pdf(room, guests)`: Generates a comprehensive PDF invoice including:
  - Property and room information
  - Guest details
  - Stay charges calculation
  - All service orders breakdown
  - Total summary

### email_utils.py
Contains functions for sending invoice emails:
- `send_invoice_email(guest, room, pdf_buffer, property_name)`: Sends invoice PDF as email attachment to a single guest
- `send_invoice_to_all_guests(guests, room, pdf_buffer, property_name)`: Sends invoice emails to all guests with email addresses

### s3_utils.py
Contains functions for S3 file management:
- `upload_pdf_to_s3_and_get_signed_url(pdf_buffer, filename_prefix)`: Uploads PDF to S3 and returns a signed URL
- `generate_signed_url(file_path, expiration)`: Generates a signed URL for S3 objects
- `delete_pdf_from_s3(file_path)`: Deletes PDF files from S3

## Usage

These utilities are integrated into the `CheckoutView` in `stay/views/checkin.py`. When a checkout is performed:

1. All pending orders for guests are cancelled
2. A PDF invoice is generated with complete order breakdown
3. The PDF is uploaded to S3 and a signed URL is generated (fallback option)
4. The PDF is sent as an email attachment to all guests who have email addresses
5. Both email results and PDF URL are returned in the API response

## Dependencies

- `reportlab==4.0.7`: For PDF generation
- `django.core.mail`: For email functionality (built-in Django)
- `boto3`: For S3 operations (already included in requirements.txt)
- `django-storages`: For S3 storage backend (already configured)
- Email configuration in settings.py (SMTP settings)

## Email Configuration

The following environment variables need to be set for email functionality:
- `EMAIL_HOST`: SMTP server host (default: smtp.gmail.com)
- `EMAIL_PORT`: SMTP server port (default: 587)
- `EMAIL_HOST_USER`: SMTP username
- `EMAIL_HOST_PASSWORD`: SMTP password
- `DEFAULT_FROM_EMAIL`: Default sender email (default: <EMAIL>)
