from celery import shared_task
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True, name="booking.distribute_availability_after_booking")
def distribute_availability_after_booking(self, reservation_id: str):
    """Distribute updated availability after a reservation booking.

    Placeholder implementation: add real distribution logic (e.g. push to PMS/
    channel manager) here. Kept lightweight so import in booking.utils works.
    """
    try:
        logger.info(
            "distribute_availability_after_booking start",
            extra={"reservation_id": reservation_id},
        )
        # TODO: implement actual availability distribution logic.
        # e.g., call a service method: availability_service.distribute(reservation_id)
        return {"status": "ok", "reservation_id": reservation_id}
    except Exception as e:
        logger.error(
            "distribute_availability_after_booking failed",
            extra={"reservation_id": reservation_id, "error": str(e)},
        )
        raise


@shared_task(
    bind=True,
    name="booking.enqueue_precheckin_setup",
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_backoff_max=300,
    retry_kwargs={"max_retries": 5},
)
def enqueue_precheckin_setup(self, reservation_id: str):
    """Background task to perform precheckin + room block creation.

    Retries with exponential backoff up to 5 times (max ~5+ minutes) to handle
    transient DB/contention issues. Persistent failures are logged at error.
    """
    from booking.models import Reservation as _Reservation
    from booking.utils import trigger_precheckin_and_block

    try:
        res = _Reservation.objects.get(id=reservation_id)
    except _Reservation.DoesNotExist:
        logger.error(
            "enqueue_precheckin_setup reservation missing",
            extra={"reservation_id": reservation_id},
        )
        return
    try:
        trigger_precheckin_and_block(res)
        logger.info(
            "enqueue_precheckin_setup success", extra={"reservation_id": reservation_id}
        )
    except Exception as e:  # Will trigger autoretry
        logger.warning(
            "enqueue_precheckin_setup attempt failed",
            extra={
                "reservation_id": reservation_id,
                "error": str(e),
                "retry": self.request.retries,
            },
        )
        raise
