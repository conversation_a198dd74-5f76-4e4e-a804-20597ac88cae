from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status
from datetime import timed<PERSON>ta

from core.models import PartnerProfile
from stay.models import Property, Room, Guest
from service.models import ServicePartner
from stay.models import PropertyPartner
from service import service_factory
from geo.models import Location, State, City

User = get_user_model()


class ReportsViewSetTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()

        # Create test user and partner profile
        self.user = User.objects.create_user(
            phone="+1234567890",
            name="Test Partner",
        )

        # Create location data
        self.state = State.objects.create(name="Test State", code="TS")
        self.city = City.objects.create(name="Test City", state=self.state)
        self.location = Location.objects.create(
            name="Test Location", city=self.city, latitude=12.34, longitude=56.78
        )

        # Create property
        self.property = Property.objects.create(
            name="Test Property", location=self.location, rooms=10, avg_price=100.0
        )

        # Create partner profile
        self.partner_profile = PartnerProfile.objects.create(
            user=self.user, active_property=self.property
        )

        # Associate partner with property
        self.property.staffs.add(self.partner_profile)

        # Create rooms
        self.room1 = Room.objects.create(
            property=self.property, room_no="101", rate=100.0
        )
        self.room2 = Room.objects.create(
            property=self.property, room_no="102", rate=150.0
        )

        # Create guest user
        self.guest_user = User.objects.create_user(
            phone="+1987654321", name="Test Guest"
        )

        # Create guest
        self.guest = Guest.objects.create(
            user=self.guest_user,
            room=self.room1,
            checkin_key="test123",
            checked_in=True,
            check_in_date=timezone.now() - timedelta(days=2),
            check_out_date=timezone.now() + timedelta(days=1),
        )

        # Create service partner
        self.service_partner = ServicePartner.objects.create(
            name="Test Service Partner",
            location=self.location,
            type_of_service=1,  # Food
        )

        # Create property partner relationship
        self.property_partner = PropertyPartner.objects.create(
            property=self.property,
            partner=self.service_partner,
            commission=10.0,
            delivery_charges=5.0,
            name="Test Partnership",
        )

        # Authenticate user
        self.client.force_authenticate(user=self.user)

    def test_financial_report_json(self):
        """Test financial report generation in JSON format"""
        response = self.client.get(
            reverse("core:reports-list") + "?q=financial&out=json"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertIn("data", response.data)

        report_data = response.data["data"]
        self.assertEqual(report_data["report_type"], "financial")
        self.assertEqual(report_data["property_name"], "Test Property")
        self.assertIn("monthly_earnings", report_data)
        self.assertIn("commission_breakdown", report_data)
        self.assertIn("total_revenue_ytd", report_data)

    def test_vendor_report_json(self):
        """Test vendor report generation in JSON format"""
        response = self.client.get("/core/reports/?q=vendor&out=json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])

        report_data = response.data["data"]
        self.assertEqual(report_data["report_type"], "vendor")
        self.assertEqual(report_data["total_vendors"], 1)
        self.assertIn("vendors_by_service", report_data)
        self.assertIn("top_vendors", report_data)

    def test_guest_report_json(self):
        """Test guest report generation in JSON format"""
        response = self.client.get("/core/reports/?q=guest&out=json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])

        report_data = response.data["data"]
        self.assertEqual(report_data["report_type"], "guest")
        self.assertIn("stay_metrics", report_data)
        self.assertIn("top_guests", report_data)
        self.assertIn("total_unique_guests", report_data)

    def test_invalid_report_type(self):
        """Test invalid report type parameter"""
        response = self.client.get("/core/reports/?q=invalid&out=json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data["success"])
        self.assertIn("Invalid report type", response.data["message"])

    def test_invalid_output_format(self):
        """Test invalid output format parameter"""
        response = self.client.get("/core/reports/?q=financial&out=invalid")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data["success"])
        self.assertIn("Invalid output format", response.data["message"])

    def test_missing_report_type(self):
        """Test missing report type parameter"""
        response = self.client.get("/core/reports/?out=json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data["success"])
        self.assertIn("Invalid report type", response.data["message"])

    def test_unauthenticated_access(self):
        """Test unauthenticated access to reports"""
        self.client.force_authenticate(user=None)
        response = self.client.get("/core/reports/?q=financial&out=json")

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_non_partner_access(self):
        """Test non-partner user access to reports"""
        non_partner_user = User.objects.create_user(
            phone="+1111111111",
            name="Non Partner User",
        )
        self.client.force_authenticate(user=non_partner_user)

        response = self.client.get("/core/reports/?q=financial&out=json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_partner_without_property(self):
        """Test partner without active property"""
        # Create partner without property
        user_no_property = User.objects.create_user(
            phone="+2222222222",
            name="Partner No Property",
        )
        PartnerProfile.objects.create(user=user_no_property)

        self.client.force_authenticate(user=user_no_property)
        response = self.client.get("/core/reports/?q=financial&out=json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("No properties found", response.data["message"])

    def test_partner_property_access_validation(self):
        """Test that partner can only access their own property data"""
        # Create another property and partner
        other_property = Property.objects.create(
            name="Other Property", location=self.location, rooms=5, avg_price=200.0
        )

        other_user = User.objects.create_user(
            phone="+3333333333",
            name="Other Partner",
        )

        other_partner = PartnerProfile.objects.create(
            user=other_user, active_property=other_property
        )
        other_property.staffs.add(other_partner)

        # Test that first partner can't access other property data
        self.client.force_authenticate(user=self.user)
        response = self.client.get("/core/reports/?q=financial&out=json")

        # Should only see data from their own property
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        report_data = response.data["data"]
        self.assertEqual(report_data["property_name"], "Test Property")
        self.assertNotEqual(report_data["property_name"], "Other Property")

    def test_pdf_generation_without_reportlab(self):
        """Test PDF generation when reportlab is not available"""
        # This test would need to mock the import error
        # For now, we'll just test that the PDF endpoint exists
        response = self.client.get("/core/reports/?q=financial&out=pdf")

        # Should either return PDF or error about missing reportlab
        self.assertIn(
            response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        )

    def test_default_output_format(self):
        """Test default output format (should be JSON)"""
        response = self.client.get("/core/reports/?q=financial")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertIn("data", response.data)

    def test_financial_report_with_orders(self):
        """Test financial report with actual order data"""
        # Create a food service for orders (using concrete implementation)
        from service.subapps.food.models import FoodService, FoodOrder

        food_service = FoodService.objects.create(
            name="Test Food Service", partner=self.service_partner
        )

        # Create test order using concrete model
        order = FoodOrder.objects.create(
            guest=self.guest,
            service_partner=self.service_partner,
            service=food_service,
            status=5,  # COMPLETED status
            subtotal=50.0,
            commissions=5.0,
            total=55.0,
        )

        response = self.client.get("/core/reports/?q=financial&out=json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        report_data = response.data["data"]

        # Check that order data is reflected in the report
        self.assertGreater(report_data["total_revenue_ytd"], 0)
        self.assertGreater(report_data["total_orders_ytd"], 0)

    def test_guest_report_with_multiple_guests(self):
        """Test guest report with multiple guests"""
        # Create additional guest
        guest_user2 = User.objects.create_user(phone="+1555555555", name="Test Guest 2")

        guest2 = Guest.objects.create(
            user=guest_user2,
            room=self.room2,
            checkin_key="test456",
            checked_in=True,
            check_in_date=timezone.now() - timedelta(days=5),
            check_out_date=timezone.now() - timedelta(days=2),
        )

        response = self.client.get("/core/reports/?q=guest&out=json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        report_data = response.data["data"]

        # Should have data for multiple guests
        self.assertGreaterEqual(report_data["total_unique_guests"], 2)

    def test_vendor_report_with_multiple_service_types(self):
        """Test vendor report with multiple service types"""
        # Create additional service partners
        laundry_partner = ServicePartner.objects.create(
            name="Laundry Partner", location=self.location, type_of_service=2  # Laundry
        )

        transport_partner = ServicePartner.objects.create(
            name="Transport Partner",
            location=self.location,
            type_of_service=3,  # Transport
        )

        # Create property partner relationships
        PropertyPartner.objects.create(
            property=self.property,
            partner=laundry_partner,
            commission=15.0,
            name="Laundry Partnership",
        )

        PropertyPartner.objects.create(
            property=self.property,
            partner=transport_partner,
            commission=20.0,
            name="Transport Partnership",
        )

        response = self.client.get("/core/reports/?q=vendor&out=json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        report_data = response.data["data"]

        # Should have multiple vendors
        self.assertEqual(report_data["total_vendors"], 3)

        # Should have vendors by service breakdown
        vendors_by_service = report_data["vendors_by_service"]
        self.assertEqual(vendors_by_service["Food"], 1)
        self.assertEqual(vendors_by_service["Laundry"], 1)
        self.assertEqual(vendors_by_service["Transport"], 1)

    def test_report_data_filtering_by_date(self):
        """Test that reports properly filter data by year"""
        # Create old guest data (previous year)
        old_date = timezone.now().replace(year=timezone.now().year - 1)

        old_guest_user = User.objects.create_user(phone="+1666666666", name="Old Guest")

        old_guest = Guest.objects.create(
            user=old_guest_user,
            room=self.room1,
            checkin_key="old123",
            checked_in=True,
            checked_out=True,
            check_in_date=old_date,
            check_out_date=old_date + timedelta(days=1),
        )

        response = self.client.get("/core/reports/?q=guest&out=json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        report_data = response.data["data"]

        # Current year data should not include old guest in active metrics
        # (This depends on the specific implementation of date filtering)
        self.assertIsInstance(report_data["stay_metrics"], list)

    def test_empty_property_reports(self):
        """Test reports for property with no data"""
        # Create empty property
        empty_property = Property.objects.create(
            name="Empty Property", location=self.location, rooms=5, avg_price=100.0
        )

        empty_user = User.objects.create_user(
            phone="+1777777777",
            name="Empty Partner",
        )

        empty_partner = PartnerProfile.objects.create(
            user=empty_user, active_property=empty_property
        )
        empty_property.staffs.add(empty_partner)

        self.client.force_authenticate(user=empty_user)

        response = self.client.get("/core/reports/?q=financial&out=json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        report_data = response.data["data"]

        # Should handle empty data gracefully
        self.assertEqual(report_data["total_revenue_ytd"], "0.00")
        self.assertEqual(report_data["total_orders_ytd"], 0)
