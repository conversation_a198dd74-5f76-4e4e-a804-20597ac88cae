"""
Payment Exceptions

Custom exception classes for the payment system.
"""


class PaymentException(Exception):
    """Base exception for payment-related errors"""
    pass


class RazorpayException(PaymentException):
    """Exception raised for Razorpay API errors"""
    pass


class PaymentSplitException(PaymentException):
    """Exception raised during payment split calculations"""
    pass


class WebhookException(PaymentException):
    """Exception raised during webhook processing"""
    pass


class AccountCreationException(PaymentException):
    """Exception raised during Razorpay account creation"""
    pass


class KYCException(PaymentException):
    """Exception raised during KYC document processing"""
    pass


class BankAccountException(PaymentException):
    """Exception raised during bank account operations"""
    pass


class AccountVerificationException(PaymentException):
    """Exception raised during account verification"""
    pass


class PaymentValidationException(PaymentException):
    """Exception raised during payment validation"""
    pass


class PaymentProcessingException(PaymentException):
    """Exception raised during payment processing"""
    pass


class PaymentLinkException(PaymentException):
    """Exception raised during payment link operations"""
    pass


class TransferException(PaymentException):
    """Exception raised during transfer operations"""
    pass


class SignatureVerificationException(PaymentException):
    """Exception raised during signature verification"""
    pass


class ConfigurationException(PaymentException):
    """Exception raised due to configuration issues"""
    pass