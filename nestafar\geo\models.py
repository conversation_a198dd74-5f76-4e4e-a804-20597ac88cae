from django.db import models
import uuid
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.contrib.postgres.indexes import GinIndex

import logging

logger = logging.getLogger(__name__)


class Country(models.Model):
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=2, unique=True)
    phone_code = models.CharField(max_length=5, blank=True, null=True)

    class Meta:
        verbose_name_plural = "Countries"
        indexes = [models.Index(fields=["code"])]

    def __str__(self):
        return self.name


class State(models.Model):
    name = models.CharField(max_length=100)
    code = models.Char<PERSON>ield(max_length=2)
    country = models.ForeignKey(
        Country, on_delete=models.CASCADE, related_name="states"
    )

    class Meta:
        unique_together = ["country", "name"]
        indexes = [
            models.Index(fields=["code"]),
            models.Index(fields=["name"]),
        ]

    def __str__(self):
        return f"{self.name}, {self.country.code}"

    @classmethod
    def get_or_create_state(cls, country, name, code):
        """
        Get existing state or create new one, handling duplicates appropriately
        """
        try:
            # Try to get existing state by name and country
            return cls.objects.get(country=country, name=name)
        except cls.DoesNotExist:
            # Create new state if none exists
            return cls.objects.create(country=country, name=name, code=code)
        except cls.MultipleObjectsReturned:
            # If multiple entries exist, get the first one
            return cls.objects.filter(country=country, name=name).first()


class City(models.Model):
    name = models.CharField(max_length=100)
    state = models.ForeignKey(State, on_delete=models.CASCADE, related_name="cities")

    class Meta:
        verbose_name_plural = "Cities"
        unique_together = ["state", "name"]
        indexes = [models.Index(fields=["name"])]

    def __str__(self):
        return f"{self.name}, {self.state.code}"


class AdministrationArea(models.Model):
    name = models.CharField(max_length=100)
    city = models.ForeignKey(City, on_delete=models.CASCADE, related_name="areas")
    pincode = models.CharField(max_length=6)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name_plural = "Administration Areas"
        # Modified unique constraint to include name
        unique_together = ["city", "pincode", "name"]
        indexes = [
            models.Index(fields=["pincode"]),
            models.Index(fields=["name", "pincode"]),
        ]

    def __str__(self):
        return f"{self.name}, {self.pincode} ({self.city.name})"

    @classmethod
    def get_or_create_area(cls, city, pincode, name):
        """
        Get existing area or create new one, handling duplicates appropriately
        """
        try:
            # Try to get existing active area
            return cls.objects.get(
                city=city, pincode=pincode, name=name, is_active=True
            )
        except cls.DoesNotExist:
            # Create new area if none exists
            return cls.objects.create(city=city, pincode=pincode, name=name)
        except cls.MultipleObjectsReturned:
            # If multiple entries exist, get the most recently created one
            return cls.objects.filter(
                city=city, pincode=pincode, name=name, is_active=True
            ).latest("id")


class Location(models.Model):
    class LocationType(models.TextChoices):
        HOMESTAY = "HS", _("Home Stay")
        HOTEL = "HT", _("Hotel")
        TRANSPORT = "TR", _("Transport Hub")
        RESTAURANT = "RS", _("Restaurant")
        TOURIST = "TS", _("Tourist Spot")
        AREA = "AR", _("Area")
        OTHER = "OT", _("Other")

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    address = models.TextField()
    location_type = models.CharField(
        max_length=2, choices=LocationType.choices, default=LocationType.OTHER
    )
    latitude = models.DecimalField(
        max_digits=9, decimal_places=6, blank=True, null=True
    )
    longitude = models.DecimalField(
        max_digits=9, decimal_places=6, blank=True, null=True
    )
    place_id = models.CharField(
        max_length=255, default=None, blank=True, null=True
    )  # Google Maps place_id
    administrative_area = models.ForeignKey(
        AdministrationArea,
        on_delete=models.SET_NULL,
        null=True,
        related_name="locations",
    )
    timezone = models.CharField(
        max_length=50,
        default="Asia/Kolkata",  # Default timezone for India (IST)
        help_text=_("Timezone for the location (defaults to IST)"),
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_verified = models.BooleanField(default=False)

    class Meta:
        indexes = [
            models.Index(fields=["place_id"]),
            models.Index(fields=["location_type"]),
            models.Index(fields=["latitude", "longitude"]),
        ]

    def clean(self):
        if self.latitude is not None:
            self.latitude = round(self.latitude, 6)
            if not (-90 <= self.latitude <= 90):
                raise ValidationError({"latitude": "Invalid latitude value"})

        if self.longitude is not None:
            self.longitude = round(self.longitude, 6)
            if not (-180 <= self.longitude <= 180):
                raise ValidationError({"longitude": "Invalid longitude value"})

    def save(self, *args, **kwargs):
        if self.latitude is not None:
            self.latitude = round(self.latitude, 6)
        if self.longitude is not None:
            self.longitude = round(self.longitude, 6)
        super().save(*args, **kwargs)

    def ensure_coordinates(self):
        """
        Ensures the location has latitude and longitude coordinates.
        If coordinates are missing, attempts to fetch them using Google Maps API.
        Returns True if coordinates are available/fetched, False otherwise.
        """
        if self.latitude is not None and self.longitude is not None:
            return True

        if not self.address:
            return False

        try:
            from geo.services import GoogleMapsService

            maps_service = GoogleMapsService()

            # First try using place_id if available
            if self.place_id:
                place_details = maps_service.get_place_details(self.place_id)
                if place_details and "geometry" in place_details.get("result", {}):
                    geometry = place_details["result"]["geometry"]
                    self.latitude = geometry["location"]["lat"]
                    self.longitude = geometry["location"]["lng"]
                    self.save(update_fields=["latitude", "longitude"])
                    return True

            # If no place_id or place details fetch failed, try searching by address
            search_result = maps_service.search_place(
                "{0}, {1}, {2}".format(
                    self.address,
                    self.administrative_area.city.name,
                    self.administrative_area.pincode,
                )
            )
            if search_result and search_result.get("results"):
                first_result = search_result["results"][0]
                if "geometry" in first_result:
                    self.latitude = first_result["geometry"]["location"]["lat"]
                    self.longitude = first_result["geometry"]["location"]["lng"]
                    # If we found a place_id, save it for future use
                    if "place_id" in first_result:
                        self.place_id = first_result["place_id"]
                        self.save(update_fields=["latitude", "longitude", "place_id"])
                    else:
                        self.save(update_fields=["latitude", "longitude"])
                    return True

            return False
        except Exception as e:
            logger.error(f"Error ensuring coordinates: {str(e)}")
            return False

    def __str__(self):
        return f"{self.name} ({self.get_location_type_display()})"


class LocationMetadata(models.Model):
    class Rating(models.IntegerChoices):
        POOR = 1, _("Poor")
        FAIR = 2, _("Fair")
        GOOD = 3, _("Good")
        VERY_GOOD = 4, _("Very Good")
        EXCELLENT = 5, _("Excellent")

    location = models.OneToOneField(
        Location, on_delete=models.CASCADE, related_name="metadata"
    )
    overall_rating = models.PositiveSmallIntegerField(
        choices=Rating.choices, default=Rating.FAIR
    )
    weather_rating = models.PositiveSmallIntegerField(
        choices=Rating.choices, default=Rating.FAIR
    )
    air_quality_index = models.PositiveSmallIntegerField(
        choices=Rating.choices, default=Rating.FAIR
    )
    internet_speed = models.PositiveIntegerField(help_text="Speed in Mbps", null=True)
    accessibility_rating = models.PositiveSmallIntegerField(
        choices=Rating.choices, default=Rating.FAIR
    )
    safety_rating = models.PositiveSmallIntegerField(
        choices=Rating.choices, default=Rating.FAIR
    )
    healthcare_rating = models.PositiveSmallIntegerField(
        choices=Rating.choices, default=Rating.FAIR
    )
    primary_language = models.CharField(max_length=100, blank=True)
    additional_languages = models.JSONField(default=list)
    amenities = models.JSONField(default=dict)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Location metadata"

    def __str__(self):
        return f"Metadata for {self.location.name}"
