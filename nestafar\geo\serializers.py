from rest_framework.serializers import Model<PERSON>erializer, Char<PERSON>ield, ValidationError
from .models import *
from .services import LocationService


class CountrySerializer(ModelSerializer):
    id = CharField(max_length=None)

    class Meta:
        model = Country
        fields = "__all__"


class StateSerializer(ModelSerializer):
    id = CharField(max_length=None)
    country = CountrySerializer()

    class Meta:
        model = State
        fields = "__all__"


class CitySerializer(ModelSerializer):
    id = CharField(max_length=None)
    state = StateSerializer()

    class Meta:
        model = City
        fields = "__all__"


class AdministrationAreaSerializer(ModelSerializer):
    city = CitySerializer()
    id = CharField(max_length=None)

    class Meta:
        model = AdministrationArea
        fields = "__all__"


class LocationSerializer(ModelSerializer):
    class Meta:
        model = Location
        fields = "__all__"
        depth = 2


class LocationCreateOrManualSerializer(ModelSerializer):
    place_id = CharField(required=False, allow_null=True)
    administrative_area = CharField(required=False, write_only=True, allow_null=True)

    class Meta:
        model = Location
        fields = [
            "id",
            "place_id",
            "name",
            "description",
            "address",
            "location_type",
            "latitude",
            "longitude",
            "administrative_area",
        ]
        extra_kwargs = {
            "name": {"required": False},
            "description": {"required": False},
            "address": {"required": False},
            "location_type": {"required": False},
            "latitude": {"required": False},
            "longitude": {"required": False},
            "administrative_area": {"required": False},
        }

    def validate_administrative_area(self, value):
        if value:
            try:
                # Try to get the AdministrationArea object using the pincode
                admin_area = AdministrationArea.objects.filter(
                    pk=int(value), is_active=True
                ).first()

                if not admin_area:
                    raise ValidationError(
                        f"No active administrative area found with id {value}"
                    )

                # Return the actual AdministrationArea object
                return admin_area
            except (ValueError, TypeError):
                raise ValidationError("Invalid pincode format")
        return None

    def validate(self, attrs):
        place_id = attrs.get("place_id")

        # If place_id is provided, skip validation for other fields
        if place_id:
            return attrs

        # If place_id is not provided, enforce required fields except administrative_area
        required_fields = ["name", "address", "location_type"]
        missing_fields = [field for field in required_fields if not attrs.get(field)]

        if missing_fields:
            raise ValidationError(
                {
                    "error": f"The following fields are required when 'place_id' is not provided: {', '.join(missing_fields)}"
                }
            )

        return attrs

    def create(self, validated_data):
        place_id = validated_data.get("place_id")
        location_service = LocationService()

        # Check if location already exists
        existing_location = None
        if place_id:
            existing_location = Location.objects.filter(place_id=place_id).first()
        elif validated_data.get("latitude") and validated_data.get("longitude"):
            existing_location = Location.objects.filter(
                latitude=validated_data["latitude"],
                longitude=validated_data["longitude"],
            ).first()

        if existing_location:
            return existing_location

        # Handle verified location creation (using place_id)
        if place_id:
            location = location_service.get_or_create_location(place_id)
            if not location:
                raise ValidationError(
                    {"place_id": "Invalid or not found in Google Maps"}
                )
            return location

        location = Location.objects.create(**validated_data)
        return location


class LocationMetadataSerializer(ModelSerializer):
    location = LocationSerializer

    class Meta:
        model = LocationMetadata
        fields = "__all__"
        depth = 2
