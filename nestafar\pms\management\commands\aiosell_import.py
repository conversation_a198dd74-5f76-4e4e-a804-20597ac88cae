"""
Django management command for importing AioSell room and rate plan configurations.

This command imports AioSell room codes and rate plan codes into the PMS system,
creating RoomType, Room, and RatePlan instances based on AioSell naming conventions.
"""

import json
from decimal import Decimal
from datetime import timed<PERSON><PERSON>
from typing import Dict, List, Tuple, Optional

from django.core.management.base import BaseCommand, CommandError
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils import timezone

from stay.models import Property, Room
from pms.models import RoomType, RatePlan


class Command(BaseCommand):
    help = 'Import AioSell room and rate plan configurations into the PMS system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--hotel-id',
            type=str,
            required=True,
            help='Hotel/Property ID (UUID) to import configurations for'
        )
        
        parser.add_argument(
            '--input-file',
            type=str,
            help='JSON file containing room codes and rate plan codes'
        )
        
        parser.add_argument(
            '--rooms',
            type=str,
            help='Comma-separated list of room codes (e.g., "standard-room,balcony-room")'
        )
        
        parser.add_argument(
            '--rate-plans',
            type=str,
            help='Comma-separated list of rate plan codes'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Preview changes without committing to database'
        )
        
        parser.add_argument(
            '--default-rate',
            type=float,
            default=1000.0,
            help='Default base rate for rate plans (default: 1000.0)'
        )
        
        parser.add_argument(
            '--rooms-per-type',
            type=int,
            default=5,
            help='Number of rooms to create per room type (default: 5)'
        )
        
        parser.add_argument(
            '--rate-plan-days',
            type=int,
            default=365,
            help='Number of days for rate plan validity (default: 365)'
        )
        
        parser.add_argument(
            '--interactive',
            action='store_true',
            help='Interactive mode to input room codes and rate plans'
        )

    def handle(self, *args, **options):
        """Main command handler."""
        self.verbosity = options['verbosity']
        self.dry_run = options['dry_run']
        
        # Get hotel
        try:
            hotel = Property.objects.get(id=options['hotel_id'])
            self.stdout.write(f"Found hotel: {hotel.name} (ID: {hotel.id})")
        except (Property.DoesNotExist, ValueError, ValidationError):
            raise CommandError(f"Hotel with ID {options['hotel_id']} not found")
        
        # Get input data
        room_codes, rate_plan_codes = self._get_input_data(options)
        
        if not room_codes and not rate_plan_codes:
            raise CommandError("No room codes or rate plan codes provided")
        
        # Parse and validate data
        parsed_data = self._parse_aiosell_data(room_codes, rate_plan_codes)
        
        # Display preview
        self._display_preview(parsed_data, options)
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN - No changes will be made"))
            return
        
        # Confirm before proceeding (unless non-interactive)
        if options['interactive'] or self.verbosity >= 2:
            confirm = input("Proceed with import? (y/N): ")
            if confirm.lower() != 'y':
                self.stdout.write("Import cancelled")
                return
        
        # Import data
        with transaction.atomic():
            results = self._import_data(hotel, parsed_data, options)
            self._update_hotel_config(hotel, room_codes, rate_plan_codes)
        
        # Display results
        self._display_results(results)

    def _get_input_data(self, options) -> Tuple[List[str], List[str]]:
        """Get room codes and rate plan codes from various input sources."""
        room_codes = []
        rate_plan_codes = []
        
        # From JSON file
        if options['input_file']:
            try:
                with open(options['input_file'], 'r') as f:
                    data = json.load(f)
                    room_codes = data.get('room_codes', [])
                    rate_plan_codes = data.get('rate_plan_codes', [])
                    self.stdout.write(f"Loaded data from {options['input_file']}")
            except (FileNotFoundError, json.JSONDecodeError) as e:
                raise CommandError(f"Error reading input file: {e}")
        
        # From command arguments
        if options['rooms']:
            room_codes.extend([code.strip() for code in options['rooms'].split(',')])
        
        if options['rate_plans']:
            rate_plan_codes.extend([code.strip() for code in options['rate_plans'].split(',')])
        
        # Interactive mode
        if options['interactive']:
            self.stdout.write("Interactive mode - Enter room codes and rate plan codes")
            self.stdout.write("(Press Enter on empty line to finish each section)")
            
            self.stdout.write("\nEnter room codes:")
            while True:
                code = input("Room code: ").strip()
                if not code:
                    break
                room_codes.append(code)
            
            self.stdout.write("\nEnter rate plan codes:")
            while True:
                code = input("Rate plan code: ").strip()
                if not code:
                    break
                rate_plan_codes.append(code)
        
        # Remove duplicates while preserving order
        room_codes = list(dict.fromkeys(room_codes))
        rate_plan_codes = list(dict.fromkeys(rate_plan_codes))
        
        return room_codes, rate_plan_codes

    def _parse_aiosell_data(self, room_codes: List[str], rate_plan_codes: List[str]) -> Dict:
        """Parse AioSell codes and extract room types and rate plan information."""
        parsed_data = {
            'room_types': {},
            'rate_plans': {}
        }
        
        # Parse room codes
        for room_code in room_codes:
            room_type_name = self._parse_room_type_name(room_code)
            parsed_data['room_types'][room_code] = {
                'name': room_type_name,
                'code': room_code,
                'max_occupancy': self._get_default_occupancy(room_code)
            }
        
        # Parse rate plan codes
        for rate_plan_code in rate_plan_codes:
            parsed_plan = self._parse_rate_plan_code(rate_plan_code)
            if parsed_plan:
                room_code = parsed_plan['room_code']
                
                # Ensure room type exists
                if room_code not in parsed_data['room_types']:
                    room_type_name = self._parse_room_type_name(room_code)
                    parsed_data['room_types'][room_code] = {
                        'name': room_type_name,
                        'code': room_code,
                        'max_occupancy': self._get_default_occupancy(room_code)
                    }
                
                # Add rate plan
                if room_code not in parsed_data['rate_plans']:
                    parsed_data['rate_plans'][room_code] = []
                
                parsed_data['rate_plans'][room_code].append(parsed_plan)
        
        return parsed_data

    def _parse_room_type_name(self, room_code: str) -> str:
        """Convert room code to human-readable name."""
        # Replace hyphens and underscores with spaces, then title case
        name = room_code.replace('-', ' ').replace('_', ' ')
        return ' '.join(word.capitalize() for word in name.split())

    def _get_default_occupancy(self, room_code: str) -> int:
        """Get default occupancy based on room code."""
        room_code_lower = room_code.lower()
        if 'dormitory' in room_code_lower:
            return 8  # Dormitory rooms typically have higher occupancy
        elif 'suite' in room_code_lower:
            return 4  # Suites typically accommodate more guests
        elif 'family' in room_code_lower:
            return 6  # Family rooms
        else:
            return 2  # Standard rooms

    def _parse_rate_plan_code(self, rate_plan_code: str) -> Optional[Dict]:
        """Parse rate plan code to extract components."""
        # Expected format: room-code-occupancy-meal-plan
        # e.g., "standard-room-s-ep", "balcony-room-d-cp"
        
        parts = rate_plan_code.split('-')
        if len(parts) < 3:
            self.stdout.write(
                self.style.WARNING(f"Invalid rate plan code format: {rate_plan_code}")
            )
            return None
        
        # Extract occupancy and meal plan from last two parts
        occupancy_code = parts[-2].lower()
        meal_plan_code = parts[-1].lower()
        
        # Room code is everything except the last two parts
        room_code = '-'.join(parts[:-2])
        
        # Map occupancy codes
        occupancy_map = {
            's': ('Single', 1),
            'd': ('Double', 2),
            't': ('Triple', 3),
            'q': ('Quad', 4)
        }
        
        # Map meal plan codes
        meal_plan_map = {
            'ep': 'European Plan',
            'cp': 'Continental Plan',
            'map': 'Modified American Plan',
            'ap': 'American Plan',
            'bb': 'Bed & Breakfast',
            'hb': 'Half Board',
            'fb': 'Full Board'
        }
        
        occupancy_name, max_guests = occupancy_map.get(occupancy_code, ('Unknown', 2))
        meal_plan_name = meal_plan_map.get(meal_plan_code, meal_plan_code.upper())
        
        return {
            'code': rate_plan_code,
            'room_code': room_code,
            'occupancy_code': occupancy_code,
            'occupancy_name': occupancy_name,
            'meal_plan_code': meal_plan_code,
            'meal_plan_name': meal_plan_name,
            'max_guests': max_guests,
            'name': f"{occupancy_name} - {meal_plan_name}"
        }

    def _display_preview(self, parsed_data: Dict, options: Dict):
        """Display preview of what will be created."""
        self.stdout.write(self.style.SUCCESS("\n=== IMPORT PREVIEW ==="))
        
        # Room types
        self.stdout.write(f"\nRoom Types to create ({len(parsed_data['room_types'])}):")
        for room_code, room_data in parsed_data['room_types'].items():
            self.stdout.write(f"  • {room_data['name']} (code: {room_code}, max_occupancy: {room_data['max_occupancy']})")
            self.stdout.write(f"    → Will create {options['rooms_per_type']} rooms")
        
        # Rate plans
        total_rate_plans = sum(len(plans) for plans in parsed_data['rate_plans'].values())
        self.stdout.write(f"\nRate Plans to create ({total_rate_plans}):")
        for room_code, rate_plans in parsed_data['rate_plans'].items():
            room_name = parsed_data['room_types'][room_code]['name']
            self.stdout.write(f"  {room_name}:")
            for plan in rate_plans:
                self.stdout.write(f"    • {plan['name']} (code: {plan['code']})")
        
        self.stdout.write(f"\nDefault base rate: ${options['default_rate']}")
        self.stdout.write(f"Rate plan validity: {options['rate_plan_days']} days from today")

    def _import_data(self, hotel: Property, parsed_data: Dict, options: Dict) -> Dict:
        """Import the parsed data into the database."""
        results = {
            'room_types_created': 0,
            'room_types_updated': 0,
            'rooms_created': 0,
            'rate_plans_created': 0,
            'rate_plans_updated': 0,
            'errors': []
        }

        # Create/update room types and rooms
        for room_code, room_data in parsed_data['room_types'].items():
            try:
                room_type, created = RoomType.objects.get_or_create(
                    hotel=hotel,
                    name=room_data['name'],
                    defaults={
                        'max_occupancy': room_data['max_occupancy'],
                        'description': f"Room type imported from AioSell (code: {room_code})"
                    }
                )

                if created:
                    results['room_types_created'] += 1
                    self.stdout.write(f"Created room type: {room_type.name}")
                else:
                    results['room_types_updated'] += 1
                    self.stdout.write(f"Found existing room type: {room_type.name}")

                # Create rooms for this room type
                existing_rooms = Room.objects.filter(
                    property=hotel,
                    room_type=room_type
                ).count()

                rooms_to_create = max(0, options['rooms_per_type'] - existing_rooms)

                for i in range(rooms_to_create):
                    room_number = f"{room_code.upper()}-{existing_rooms + i + 1:03d}"

                    room = Room.objects.create(
                        property=hotel,
                        room_type=room_type,
                        room_no=room_number,
                        max_guests=room_data['max_occupancy'],
                        type_of_room=room_data['name']
                    )
                    results['rooms_created'] += 1

                if rooms_to_create > 0:
                    self.stdout.write(f"  Created {rooms_to_create} rooms for {room_type.name}")
                elif existing_rooms > 0:
                    self.stdout.write(f"  Found {existing_rooms} existing rooms for {room_type.name}")

            except Exception as e:
                error_msg = f"Error creating room type {room_data['name']}: {e}"
                results['errors'].append(error_msg)
                self.stdout.write(self.style.ERROR(error_msg))

        # Create/update rate plans
        today = timezone.now().date()
        valid_from = today
        valid_to = today + timedelta(days=options['rate_plan_days'])

        for room_code, rate_plans in parsed_data['rate_plans'].items():
            try:
                room_type = RoomType.objects.get(hotel=hotel, name=parsed_data['room_types'][room_code]['name'])

                for plan_data in rate_plans:
                    rate_plan, created = RatePlan.objects.get_or_create(
                        room_type=room_type,
                        name=plan_data['name'],
                        defaults={
                            'base_rate': Decimal(str(options['default_rate'])),
                            'valid_from': valid_from,
                            'valid_to': valid_to,
                            'min_stay_days': 1,
                            'is_active': True
                        }
                    )

                    if created:
                        results['rate_plans_created'] += 1
                        self.stdout.write(f"  Created rate plan: {rate_plan.name}")
                    else:
                        results['rate_plans_updated'] += 1
                        self.stdout.write(f"  Found existing rate plan: {rate_plan.name}")

            except RoomType.DoesNotExist:
                error_msg = f"Room type not found for rate plans: {room_code}"
                results['errors'].append(error_msg)
                self.stdout.write(self.style.ERROR(error_msg))
            except Exception as e:
                error_msg = f"Error creating rate plans for {room_code}: {e}"
                results['errors'].append(error_msg)
                self.stdout.write(self.style.ERROR(error_msg))

        return results

    def _update_hotel_config(self, hotel: Property, room_codes: List[str], rate_plan_codes: List[str]):
        """Update hotel's channel manager configuration with AioSell mappings."""
        channel_managers = hotel.channel_managers or {}

        if 'aiosell' not in channel_managers:
            channel_managers['aiosell'] = {}

        aiosell_config = channel_managers['aiosell']

        # Update room mapping
        if 'room_mapping' not in aiosell_config:
            aiosell_config['room_mapping'] = {}

        # Map room codes to room type names
        for room_code in room_codes:
            room_type_name = self._parse_room_type_name(room_code)
            try:
                room_type = RoomType.objects.get(hotel=hotel, name=room_type_name)
                aiosell_config['room_mapping'][room_type.name] = room_code
            except RoomType.DoesNotExist:
                pass

        # Update rate plans mapping
        if 'rate_plans' not in aiosell_config:
            aiosell_config['rate_plans'] = {}

        # Group rate plan codes by room code
        rate_plans_by_room = {}
        for rate_plan_code in rate_plan_codes:
            parsed_plan = self._parse_rate_plan_code(rate_plan_code)
            if parsed_plan:
                room_code = parsed_plan['room_code']
                if room_code not in rate_plans_by_room:
                    rate_plans_by_room[room_code] = []
                rate_plans_by_room[room_code].append(rate_plan_code)

        # Update configuration
        for room_code, plans in rate_plans_by_room.items():
            aiosell_config['rate_plans'][room_code] = plans

        # Store imported codes for reference
        aiosell_config['imported_room_codes'] = room_codes
        aiosell_config['imported_rate_plan_codes'] = rate_plan_codes
        aiosell_config['last_import'] = timezone.now().isoformat()

        # Save updated configuration
        hotel.channel_managers = channel_managers
        hotel.save(update_fields=['channel_managers'])

        self.stdout.write("Updated hotel's AioSell configuration")

    def _display_results(self, results: Dict):
        """Display import results."""
        self.stdout.write(self.style.SUCCESS("\n=== IMPORT RESULTS ==="))

        self.stdout.write(f"Room Types:")
        self.stdout.write(f"  Created: {results['room_types_created']}")
        self.stdout.write(f"  Updated: {results['room_types_updated']}")

        self.stdout.write(f"Rooms:")
        self.stdout.write(f"  Created: {results['rooms_created']}")

        self.stdout.write(f"Rate Plans:")
        self.stdout.write(f"  Created: {results['rate_plans_created']}")
        self.stdout.write(f"  Updated: {results['rate_plans_updated']}")

        if results['errors']:
            self.stdout.write(self.style.ERROR(f"\nErrors ({len(results['errors'])}):"))
            for error in results['errors']:
                self.stdout.write(self.style.ERROR(f"  • {error}"))
        else:
            self.stdout.write(self.style.SUCCESS("\nImport completed successfully with no errors!"))
