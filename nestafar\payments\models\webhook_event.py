"""
PaymentWebhookEvent Model

Tracks webhook events from Razorpay for audit and debugging purposes.
"""

import uuid
import logging
from collections.abc import Mapping
from django.db import models
from django.db.utils import DatabaseError, IntegrityError
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db import transaction
from payments.constants import WebhookEventType

logger = logging.getLogger(__name__)


class PaymentWebhookEvent(models.Model):
    """
    Webhook event tracking for Razorpay webhooks.

    This model stores all incoming webhook events for audit purposes,
    debugging, and ensuring idempotent processing.
    """

    # Primary identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    event_id = models.CharField(
        max_length=100, unique=True, help_text="Razorpay event ID"
    )

    # Event details
    event_type = models.CharField(
        max_length=50,
        choices=WebhookEventType.choices,
        help_text="Type of webhook event",
    )
    entity_type = models.CharField(
        max_length=50, help_text="Type of entity (payment, transfer, etc.)"
    )
    entity_id = models.CharField(max_length=100, help_text="ID of the entity")

    # Related payment intent (if applicable)
    payment_intent = models.ForeignKey(
        "PaymentIntent",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="webhook_events",
        help_text="Associated payment intent",
    )

    # Raw webhook data
    raw_payload = models.JSONField(help_text="Complete webhook payload from Razorpay")
    headers = models.JSONField(
        default=dict, help_text="HTTP headers from webhook request"
    )

    # Processing status
    processed = models.BooleanField(
        default=False, help_text="Whether this event has been processed"
    )
    processed_at = models.DateTimeField(
        null=True, blank=True, help_text="When this event was processed"
    )
    processing_attempts_count = models.PositiveIntegerField(
        default=0, help_text="Number of processing attempts"
    )

    # Error handling
    processing_error = models.TextField(
        null=True, blank=True, help_text="Error message if processing failed"
    )
    last_error_at = models.DateTimeField(
        null=True, blank=True, help_text="When last error occurred"
    )

    # Signature verification
    signature_verified = models.BooleanField(
        default=False, help_text="Whether webhook signature was verified"
    )
    signature = models.CharField(
        max_length=500,
        null=True,
        blank=True,
        help_text="Webhook signature from Razorpay",
    )

    # Audit trail
    received_at = models.DateTimeField(auto_now_add=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # IP tracking for security
    source_ip = models.GenericIPAddressField(
        null=True, blank=True, help_text="IP address of webhook source"
    )

    class Meta:
        db_table = "payments_webhook_event"
        indexes = [
            models.Index(fields=["event_id"]),
            models.Index(fields=["event_type", "processed"]),
            models.Index(fields=["entity_type", "entity_id"]),
            models.Index(fields=["payment_intent", "event_type"]),
            models.Index(fields=["processed", "received_at"]),
        ]
        ordering = ["-received_at"]

    def __str__(self):
        return f"Webhook {self.event_type} - {self.entity_id} ({'Processed' if self.processed else 'Pending'})"

    def mark_as_processed(self):
        """Mark webhook event as successfully processed"""
        # Record processing attempt as successful
        self.processing_attempts_count += 1
        self.processed = True
        self.processed_at = timezone.now()
        self.save()

        try:
            WebhookProcessingAttempt.objects.create(
                event=self,
                attempt_number=self.processing_attempts_count,
                status='processed',
                attempted_at=self.processed_at,
                error_message=None,
            )
        except Exception:
            # Non-fatal: log and continue
            logger.exception('Failed to record processing attempt for processed event')

    @transaction.atomic
    def mark_processing_error(self, error_message):
        """Mark webhook processing error"""
        # Increment attempt count and persist error
        self.processing_attempts_count += 1
        self.processing_error = error_message
        self.last_error_at = timezone.now()
        self.save()

        try:
            WebhookProcessingAttempt.objects.create(
                event=self,
                attempt_number=self.processing_attempts_count,
                status='failed',
                attempted_at=self.last_error_at,
                error_message=error_message,
            )
        except (IntegrityError, DatabaseError) as e:
            logger.exception(
                'Failed to record processing attempt for failed event %s: %s',
                self.event_id,
                str(e)
            )   
    def can_retry_processing(self):
        """Check if webhook processing can be retried"""
        from ..constants.payment_constants import MAX_WEBHOOK_RETRY_ATTEMPTS
        return (
            not self.processed and self.processing_attempts_count < MAX_WEBHOOK_RETRY_ATTEMPTS
        )

    def get_entity_data(self):
        """Extract entity data from raw payload"""
        try:
            # raw_payload should normally be a mapping (JSONField), but guard against unexpected types
            if isinstance(self.raw_payload, Mapping):
                payload = self.raw_payload.get("payload")
            else:
                payload = None
        except Exception:
            payload = None

        if isinstance(payload, Mapping):
            # Safely return the entity mapping or empty dict
            return payload.get(self.entity_type, {}) or {}

        # Non-mapping payloads are handled gracefully; log at debug level for troubleshooting
        logger.debug(
            "Webhook event %s has non-mapping payload: %r",
            getattr(self, 'id', None),
            self.raw_payload,
        )
        return {}

    @classmethod
    def validate_and_normalize_ids(cls, event_data, headers=None):
        """Validate and normalize event_id and event_type from webhook data"""
        # Try to get event ID from different sources
        event_id_raw = None
        
        # 1. Try top-level 'id' field (some webhook formats)
        if "id" in event_data:
            event_id_raw = event_data.get("id")
        
        # 2. Try headers for X-Razorpay-Event-Id
        elif headers and isinstance(headers, dict):
            event_id_raw = headers.get('X-Razorpay-Event-Id') or headers.get('x-razorpay-event-id')
        
        # 3. Generate a unique ID based on event data if not found
        if not event_id_raw:
            import hashlib
            import time
            # Create a unique identifier from timestamp and event data
            timestamp = int(time.time() * 1000)  # milliseconds
            data_hash = hashlib.md5(str(event_data).encode()).hexdigest()[:8]
            event_id_raw = f"evt_{timestamp}_{data_hash}"
        
        event_type_raw = event_data.get("event")

        event_id = str(event_id_raw).strip() if event_id_raw is not None else ""
        event_type = str(event_type_raw).strip() if event_type_raw is not None else ""

        if not event_id:
            raise ValidationError("Unable to determine event ID from webhook payload")
        if not event_type:
            raise ValidationError("Webhook payload missing required 'event' field")

        # Enforce that event_type matches model choices
        try:
            if hasattr(WebhookEventType, "values"):
                valid_events = set(WebhookEventType.values)
            else:
                # Handle the case where choices might be None
                choices = getattr(WebhookEventType, 'choices', None)
                if choices:
                    valid_events = {c[0] for c in choices}
                else:
                    # Fallback: use the constants from RazorpayWebhookEvents
                    from ..constants import RazorpayWebhookEvents
                    valid_events = set(RazorpayWebhookEvents.SUPPORTED_EVENTS)
        except Exception as e:
            # If there's any issue with choices, fallback to supported events
            from ..constants import RazorpayWebhookEvents
            valid_events = set(RazorpayWebhookEvents.SUPPORTED_EVENTS)
        
        if event_type not in valid_events:
            raise ValidationError(f"Unknown 'event' value: {event_type!r}")
        
        return event_id, event_type

    @classmethod
    def extract_entity_from_payload(cls, payload, event_type):
        """Extract entity_type and entity_obj from webhook payload"""
        if not payload or not isinstance(payload, dict):
            return "", None

        # Prefer deriving the payload key from the event_type (e.g. "payment.captured" -> "payment")
        entity_candidate = ""
        if event_type:
            entity_candidate = event_type.split(".")[0]

        entity_obj = None
        entity_type = ""

        # 1) Try payload.get(entity_candidate)
        if entity_candidate and isinstance(payload.get(entity_candidate), dict):
            entity_type = entity_candidate
            entity_obj = payload.get(entity_type)
        # 2) Try payload.get("entity")
        elif isinstance(payload.get("entity"), dict):
            entity_type = "entity"
            entity_obj = payload.get("entity")
        else:
            # 3) Fallback to the first key in payload
            try:
                first_key = next(iter(payload.keys()))
            except StopIteration:
                first_key = None

            if first_key is not None:
                entity_type = first_key
                entity_obj = payload.get(first_key)

        return entity_type, entity_obj

    @classmethod
    def extract_entity_id(cls, entity_obj):
        """Extract entity_id from entity_obj (dict or scalar)"""
        if entity_obj is None:
            return ""

        # If entity_obj is a dict, look for common id placements
        if isinstance(entity_obj, dict):
            maybe_id = None
            if "id" in entity_obj:
                maybe_id = entity_obj.get("id")
            elif (
                "entity" in entity_obj
                and isinstance(entity_obj["entity"], dict)
                and "id" in entity_obj["entity"]
            ):
                maybe_id = entity_obj["entity"].get("id")
            else:
                # Look one level deeper for any nested dict containing 'id'
                for v in entity_obj.values():
                    if isinstance(v, dict) and "id" in v:
                        maybe_id = v.get("id")
                        break

            if maybe_id is None:
                return ""
            else:
                try:
                    return str(maybe_id).strip()
                except (TypeError, ValueError):
                    return ""
        else:
            # Scalar values (string/int) — treat as id
            try:
                return str(entity_obj).strip()
            except (TypeError, ValueError):
                return ""

    @classmethod
    def associate_payment_intent(cls, event, entity_data):
        """Try to find and associate PaymentIntent with the webhook event"""
        if "order_id" in entity_data:
            try:
                from .payment_intent import PaymentIntent

                payment_intent = PaymentIntent.objects.get(
                    razorpay_order_id=entity_data["order_id"]
                )
                event.payment_intent = payment_intent
            except PaymentIntent.DoesNotExist:
                pass

    @classmethod
    def create_from_webhook(cls, event_data, headers=None, source_ip=None):
        """Create webhook event from incoming webhook data"""
        # Extract and validate critical fields
        event_id, event_type = cls.validate_and_normalize_ids(event_data, headers)
            
        # Check if event with this ID already exists (idempotency)
        existing_event = cls.objects.filter(event_id=event_id).first()
        if existing_event:
            logger.info(f"Webhook event {event_id} already exists, returning existing event")
            return existing_event

        # Determine entity_type and entity_id from payload
        payload = event_data.get("payload")
        entity_type, entity_obj = cls.extract_entity_from_payload(payload, event_type)
        entity_id = cls.extract_entity_id(entity_obj)
        
        # Create the event instance
        event = cls(
            event_id=event_id,
            event_type=event_type,
            entity_type=entity_type,
            entity_id=entity_id,
            raw_payload=event_data,
            headers=headers or {},
            source_ip=source_ip,
        )

        # Try to associate with PaymentIntent
        entity_data = event.get_entity_data()
        cls.associate_payment_intent(event, entity_data)

        event.save()
        return event


class WebhookProcessingAttempt(models.Model):
    """
    Records each processing attempt for a PaymentWebhookEvent.

    This provides an audit trail for retries, successes, and failures.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    event = models.ForeignKey(
        PaymentWebhookEvent,
        on_delete=models.CASCADE,
        related_name='processing_attempts',
        help_text='Associated webhook event',
    )
    attempt_number = models.PositiveIntegerField(help_text='Sequential attempt number')
    status = models.CharField(max_length=20, help_text='Status of this attempt')
    attempted_at = models.DateTimeField(help_text='When attempt occurred')
    error_message = models.TextField(null=True, blank=True, help_text='Error message if failed')

    class Meta:
        db_table = 'payments_webhook_processing_attempt'
        ordering = ['-attempted_at']

    def __str__(self):
        return f"WebhookAttempt {self.event.event_id} #{self.attempt_number} - {self.status}"
