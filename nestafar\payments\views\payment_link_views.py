"""
Payment Link Views

API views for payment link creation and management.
"""

import logging
from datetime import timed<PERSON><PERSON>
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone

from ..models import PaymentIntent
from ..serializers.payment_link_serializer import (
    CreatePaymentLinkSerializer,
    CancelPaymentLinkSerializer,
    ResendPaymentLinkSerializer
)
from ..services import RazorpayService, PaymentSplitService, PaymentNotificationService
from ..exceptions import PaymentException, PaymentLinkException
from ..utils.razorpay_helpers import sanitize_customer_data
from core.permissions import PartnerPermission

logger = logging.getLogger(__name__)


class PaymentLinkViewSet(viewsets.ViewSet):
    """
    ViewSet for payment link operations.
    
    Handles creation, management, and status tracking of payment links
    for both checkout and precheckin scenarios.
    """
    
    permission_classes = [IsAuthenticated, PartnerPermission]
    
    def create(self, request):
        """
        Create a new payment link.
        
        Creates a payment intent, calculates splits, generates Razorpay
        payment link, and sends notifications via SMS/WhatsApp.
        """
        serializer = CreatePaymentLinkSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        try:
            with transaction.atomic():
                # Get validated data
                data = serializer.validated_data
                
                # Get related objects
                from core.models import PartnerProfile
                partner = get_object_or_404(PartnerProfile, id=data['partner_id'])
                
                precheckin = None
                if data.get('precheckin_id'):
                    from booking.models import PreCheckin
                    precheckin = get_object_or_404(PreCheckin, id=data['precheckin_id'])
                
                guest = None
                if data.get('guest_id'):
                    from stay.models import Guest
                    guest = get_object_or_404(Guest, id=data['guest_id'])
                
                # Validate expire_hours
                raw_expire = data.get('expire_hours')
                if raw_expire is None:
                    return Response({'error': 'expire_hours is required'}, status=status.HTTP_400_BAD_REQUEST)

                try:
                    # Allow numeric strings or floats by casting to int safely
                    expire_hours = int(float(raw_expire))
                except (TypeError, ValueError):
                    return Response({'error': 'expire_hours must be a numeric value (hours)'}, status=status.HTTP_400_BAD_REQUEST)

                # Enforce sane bounds: minimum 1 hour, maximum 720 hours (30 days)
                # Assumption: product policy allows up to 30 days; adjust MAX_EXPIRE_HOURS if different.
                MAX_EXPIRE_HOURS = 720
                if expire_hours < 1:
                    return Response({'error': 'expire_hours must be at least 1 hour'}, status=status.HTTP_400_BAD_REQUEST)
                if expire_hours > MAX_EXPIRE_HOURS:
                    return Response({'error': f'expire_hours must be <= {MAX_EXPIRE_HOURS} hours'}, status=status.HTTP_400_BAD_REQUEST)

                expires_at = timezone.now() + timedelta(hours=expire_hours)
                
                # Create payment intent
                payment_intent = PaymentIntent.objects.create(
                    context=data['context'],
                    partner=partner,
                    precheckin=precheckin,
                    guest=guest,
                    total_amount=data['total_amount'],
                    expires_at=expires_at,
                    metadata=data.get('metadata', {}),
                    created_by=request.user
                )
                
                # Calculate payment splits
                split_service = PaymentSplitService()
                split_data = split_service.calculate_splits(payment_intent)
                
                # Save split details
                payment_intent.split_details = split_data
                payment_intent.save(update_fields=['split_details'])
                
                # Create split records
                splits = split_service.create_payment_splits(payment_intent, split_data)
                
                # Prepare customer data
                customer_data = sanitize_customer_data({
                    'name': data['customer_name'],
                    'email': data.get('customer_email', ''),
                    'contact': data['customer_phone']
                })
                
                # Create Razorpay payment link
                razorpay_service = RazorpayService()
                
                payment_link_data = razorpay_service.create_payment_link(
                    amount=payment_intent.total_amount,
                    description=data['description'],
                    customer_details=customer_data,
                    callback_url=data.get('callback_url'),
                    expire_by=payment_intent.expires_at,
                    notes={
                        'payment_intent_id': str(payment_intent.id),
                        'context': payment_intent.context,
                        'partner_id': str(partner.id),
                        'reference_number': payment_intent.reference_number,
                        **data.get('notes', {})
                    }
                )
                
                # Update payment intent with Razorpay details
                payment_intent.razorpay_payment_link_id = payment_link_data['id']
                payment_intent.save(update_fields=['razorpay_payment_link_id'])
                
                # Send notifications
                notification_service = PaymentNotificationService()
                notifications_sent = {}
                
                if data.get('send_sms', True):
                    try:
                        sms_result = notification_service.send_payment_link_sms(
                            payment_intent, payment_link_data['short_url']
                        )
                        notifications_sent['sms'] = sms_result
                    except Exception as e:
                        logger.error(f"Failed to send SMS for payment {payment_intent.id}: {str(e)}")
                        notifications_sent['sms'] = {'sent': False, 'error': str(e)}
                
                if data.get('send_whatsapp', True):
                    try:
                        whatsapp_result = notification_service.send_payment_link_whatsapp(
                            payment_intent, payment_link_data['short_url']
                        )
                        notifications_sent['whatsapp'] = whatsapp_result
                    except Exception as e:
                        logger.error(f"Failed to send WhatsApp for payment {payment_intent.id}: {str(e)}")
                        notifications_sent['whatsapp'] = {'sent': False, 'error': str(e)}
                
                # Build response
                # Determine if any notifications failed
                notification_warnings = False
                if notifications_sent:
                    for _k, _v in notifications_sent.items():
                        try:
                            if isinstance(_v, dict) and _v.get('sent') is False:
                                notification_warnings = True
                                break
                        except Exception:
                            continue

                response_data = {
                    'payment_intent_id': payment_intent.id,
                    'reference_number': payment_intent.reference_number,
                    'payment_link_id': payment_link_data['id'],
                    'payment_link_url': payment_link_data['short_url'],
                    'short_url': payment_link_data['short_url'],
                    'total_amount': payment_intent.total_amount,
                    'total_amount_display': f"₹{payment_intent.total_amount:,.2f}",
                    'description': data['description'],
                    'status': payment_intent.status,
                    'created_at': payment_intent.created_at,
                    'expires_at': payment_intent.expires_at,
                    'customer_name': data['customer_name'],
                    'customer_email': data.get('customer_email'),
                    'customer_phone': data['customer_phone'],
                    'notifications_sent': notifications_sent,
                    'notifications': notifications_sent,
                    'notification_warnings': notification_warnings,
                    'split_preview': {
                        'platform_commission': payment_intent.platform_commission,
                        'partner_amount': payment_intent.partner_amount,
                        'vendor_amount': payment_intent.vendor_amount,
                        'commission_rate': partner.platform_commission_rate
                    }
                }
                
                logger.info(f"Created payment link {payment_link_data['id']} for payment {payment_intent.id}")
                
                return Response(response_data, status=status.HTTP_201_CREATED)
                
        except PaymentLinkException as e:
            logger.error(f"Payment link creation failed: {str(e)}")
            return Response(
                {'error': f'Failed to create payment link: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error creating payment link: {str(e)}")
            return Response(
                {'error': 'Failed to create payment link'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'])
    def status(self, request, pk=None):
        """
        Get payment link status.
        
        Returns current status of the payment link including
        payment status and transfer information.
        """
        try:
            # Get payment intent by payment link ID
            payment_intent = get_object_or_404(
                PaymentIntent,
                razorpay_payment_link_id=pk
            )
            
            # Verify user has permission to resend notifications.
            # Support partner.users (m2m/one-to-many) or partner.user (single user) and
            # defensively handle missing partner or users attribute to avoid AttributeError.
            has_access = False
            partner = getattr(payment_intent, 'partner', None)
            if partner is not None:
                users_attr = getattr(partner, 'users', None)
                if users_attr is not None:
                    try:
                        # If users_attr is a manager/queryset
                        if hasattr(users_attr, 'filter'):
                            has_access = users_attr.filter(pk=request.user.pk).exists()
                        else:
                            # Fallback to membership check
                            has_access = request.user in users_attr
                    except Exception:
                        has_access = False
                else:
                    # Fallback to single-user attribute if present
                    partner_user = getattr(partner, 'user', None)
                    has_access = (partner_user == request.user)

            if not has_access and not request.user.is_staff:
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )
        
            # Get latest status from Razorpay
            razorpay_service = RazorpayService()
            link_data = razorpay_service.get_payment_link(pk)
            
            # Calculate transfer status
            splits = payment_intent.splits.all()
            transfers_status = {
                'total': splits.count(),
                'completed': splits.filter(status='processed').count(),
                'pending': splits.filter(status__in=['pending', 'processing']).count(),
                'failed': splits.filter(status='failed').count()
            }
            
            response_data = {
                'payment_link_id': pk,
                'payment_intent_id': payment_intent.id,
                'reference_number': payment_intent.reference_number,
                'status': payment_intent.status,
                'status_display': payment_intent.get_status_display(),
                'total_amount': payment_intent.total_amount,
                'total_amount_display': f"₹{payment_intent.total_amount:,.2f}",
                'paid_amount': payment_intent.total_amount if payment_intent.is_paid() else 0,
                'created_at': payment_intent.created_at,
                'expires_at': payment_intent.expires_at,
                'paid_at': payment_intent.paid_at,
                'payment_method': payment_intent.payment_method,
                'razorpay_payment_id': payment_intent.razorpay_payment_id,
                'customer_name': link_data.get('customer', {}).get('name', ''),
                'customer_email': link_data.get('customer', {}).get('email'),
                'customer_phone': link_data.get('customer', {}).get('contact'),
                'transfers_status': transfers_status
            }
            
            return Response(response_data)
            
        except PaymentIntent.DoesNotExist:
            return Response(
                {'error': 'Payment link not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error getting payment link status {pk}: {str(e)}")
            return Response(
                {'error': 'Failed to get payment link status'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel a payment link.
        
        Cancels the payment link in Razorpay and updates
        the local payment intent status.
        """
        serializer = CancelPaymentLinkSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        try:
            # Get payment intent
            payment_intent = get_object_or_404(
                PaymentIntent,
                razorpay_payment_link_id=pk
            )
            
            # Verify user has permission to cancel this payment link.
            # Use membership check and handle missing attrs defensively.
            has_access = False
            partner = getattr(payment_intent, 'partner', None)
            if partner is not None:
                users_attr = getattr(partner, 'users', None)
                if users_attr is not None:
                    try:
                        if hasattr(users_attr, 'filter'):
                            has_access = users_attr.filter(pk=request.user.pk).exists()
                        else:
                            has_access = request.user in users_attr
                    except Exception:
                        has_access = False
                else:
                    partner_user = getattr(partner, 'user', None)
                    has_access = (partner_user == request.user)

            if not has_access and not request.user.is_staff:
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            if payment_intent.is_paid():
                return Response(
                    {'error': 'Cannot cancel a paid payment link'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Cancel in Razorpay
            razorpay_service = RazorpayService()
            razorpay_service.cancel_payment_link(pk)
            
            # Update local status
            payment_intent.status = 'cancelled'
            payment_intent.save(update_fields=['status'])
            
            # Send notification if requested
            if serializer.validated_data.get('notify_customer', False):
                try:
                    notification_service = PaymentNotificationService()
                    notification_service.send_payment_cancelled_notification(
                        payment_intent,
                        reason=serializer.validated_data.get('reason', '')
                    )
                except Exception as e:
                    logger.error(f"Failed to send cancellation notification: {str(e)}")
            
            logger.info(f"Cancelled payment link {pk} for payment {payment_intent.id}")
            
            return Response({
                'message': 'Payment link cancelled successfully',
                'payment_intent_id': payment_intent.id,
                'status': payment_intent.status
            })
            
        except Exception as e:
            logger.error(f"Error cancelling payment link {pk}: {str(e)}")
            return Response(
                {'error': f'Failed to cancel payment link: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def resend(self, request, pk=None):
        """
        Resend payment link notifications.
        
        Resends SMS and/or WhatsApp notifications for the payment link.
        """
        serializer = ResendPaymentLinkSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        try:
            # Get payment intent
            payment_intent = get_object_or_404(
                PaymentIntent,
                razorpay_payment_link_id=pk
            )

            # Enforce object-level permissions (raises PermissionDenied if not allowed)
            self.check_object_permissions(request, payment_intent)

            if payment_intent.is_paid():
                return Response(
                    {'error': 'Cannot resend notifications for paid payment'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get payment link URL
            razorpay_service = RazorpayService()
            link_data = razorpay_service.get_payment_link(pk)
            payment_url = link_data.get('short_url', '')
            
            # Send notifications
            notification_service = PaymentNotificationService()
            notifications_sent = {}
            
            if serializer.validated_data.get('send_sms', True):
                try:
                    sms_result = notification_service.send_payment_link_sms(
                        payment_intent, 
                        payment_url,
                        custom_message=serializer.validated_data.get('custom_message')
                    )
                    notifications_sent['sms'] = sms_result
                except Exception as e:
                    logger.error(f"Failed to resend SMS: {str(e)}")
                    notifications_sent['sms'] = {'sent': False, 'error': str(e)}
            
            if serializer.validated_data.get('send_whatsapp', True):
                try:
                    whatsapp_result = notification_service.send_payment_link_whatsapp(
                        payment_intent, 
                        payment_url,
                        custom_message=serializer.validated_data.get('custom_message')
                    )
                    notifications_sent['whatsapp'] = whatsapp_result
                except Exception as e:
                    logger.error(f"Failed to resend WhatsApp: {str(e)}")
                    notifications_sent['whatsapp'] = {'sent': False, 'error': str(e)}
            
            return Response({
                'message': 'Notifications sent successfully',
                'payment_intent_id': payment_intent.id,
                'notifications_sent': notifications_sent
            })
            
        except Exception as e:
            logger.error(f"Error resending notifications for {pk}: {str(e)}")
            return Response(
                {'error': f'Failed to resend notifications: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
