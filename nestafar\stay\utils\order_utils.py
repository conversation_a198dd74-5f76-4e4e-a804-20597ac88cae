from django.db import transaction
from service import service_factory
from service.models.order import BaseOrder


def cancel_all_pending_orders_for_guests(guests):
    """
    Cancel all pending orders for the given guests.
    Only cancels orders that are not in COMPLETED or REJECTED state.
    
    Args:
        guests: QuerySet or list of Guest objects
        
    Returns:
        dict: Summary of cancelled orders by service type
    """
    cancelled_orders_summary = {}
    
    with transaction.atomic():
        for service_name, service_type in service_factory.url_mappings.items():
            order_model = service_factory.service_order_model.get(service_type)
            
            # Get all orders for these guests that are not completed or rejected
            pending_orders = order_model.objects.filter(
                guest__in=guests,
                status__in=[
                    BaseOrder.OrderStatus.PENDING,
                    BaseOrder.OrderStatus.ACCEPTED,
                    BaseOrder.OrderStatus.ONGOING,
                ]
            )
            
            # Update status to CANCELLED
            cancelled_count = pending_orders.update(status=BaseOrder.OrderStatus.CANCELLED)
            
            if cancelled_count > 0:
                cancelled_orders_summary[service_name] = cancelled_count
    
    return cancelled_orders_summary
