from functools import reduce
from rest_framework.views import APIView
from core.permissions import PropertyPermission, ServicePermission
from core.utils.sms import Messages, MessageTemplates
from rest_framework.permissions import IsAuthenticated
from nestafar.responses import SuccessResponse, BadRequestResponse
from service import service_factory
from django.db import transaction
from django.forms.models import model_to_dict


class OrderView(APIView):
    permission_classes = [PropertyPermission, IsAuthenticated, ServicePermission]

    def get(self, request, service_type=None):
        try:
            service_type = service_factory.url_mappings.get(service_type)
            if hasattr(request, "property"):
                orders = service_factory.service_order_model.get(
                    service_type
                ).objects.filter(guest__room__property=request.property)
                filter = service_factory.service_order_filter.get(service_type)
                objs = filter(queryset=orders, data=request.query_params).qs
                serializer = service_factory.service_order_serializer.get(service_type)(
                    objs, many=True
                )
                return SuccessResponse(data=serializer.data)
            else:
                return BadRequestResponse(message="Property not found")
        except Exception as e:
            return BadRequestResponse(message=str(e))

    def post(self, request, service_type=None):
        try:
            service_type = service_factory.url_mappings.get(service_type)
            cart_id = request.data.get("cart_id")
            order_params = request.data
            del order_params["cart_id"]
            cart = service_factory.service_cart_model.get(service_type).objects.get(
                id=cart_id
            )
            cart_items = service_factory.service_cart_item_model.get(
                service_type
            ).objects.filter(cart=cart)
            service_orders = []
            for item in cart_items:
                service = item.item.service
                existing_order = [
                    service_order
                    for service_order in service_orders
                    if service_order.service == service
                ]
                if len(existing_order) > 1:
                    raise Exception(
                        "Duplicate service orders cannot be created from cart"
                    )
                if existing_order:
                    with transaction.atomic():
                        service_order = existing_order[0]
                        service_factory.service_order_item_model.get(
                            service_type
                        ).objects.create(
                            order=service_order,
                            item=item.item,
                            quantity=item.quantity,
                            add_ons=item.add_ons,
                            price=item.price,
                        )
                        service_order.add_cart_item(item)
                else:
                    property_partner = item.item.service.partner.partner.filter(
                        property=request.property
                    ).get()
                    pickup_charges = property_partner.pickup_charges
                    delivery_charges = property_partner.delivery_charges
                    charges = pickup_charges + delivery_charges
                    with transaction.atomic():
                        service_order = service_factory.service_order_model.get(
                            service_type
                        )(
                            guest=cart.guest,
                            cart=cart,
                            service_partner=service.partner,
                            service=service,
                            charges=charges,
                            **order_params,
                        )
                        service_order.save()
                        extra_item_params = {
                            k: v
                            for k, v in model_to_dict(item).items()
                            if k
                            not in [
                                "id",
                                "cart",
                                "item",
                                "quantity",
                                "add_ons",
                                "price",
                                "name",
                                "ordered",
                            ]
                        }

                        service_order_item = (
                            service_factory.service_order_item_model.get(service_type)(
                                order=service_order,
                                item=item.item,
                                quantity=item.quantity,
                                add_ons=item.add_ons,
                                price=item.price,
                                **extra_item_params,
                            )
                        )
                        service_order_item.save()
                        service_order = service_order_item.add_item(item)
                        service_orders.append(service_order)

            # TODO: Add SMS as tasks for celery
            # Send SMS to guest with order total
            msg = Messages(cart.guest.user.phone.national_number)
            msg.send_bulk_sms(
                MessageTemplates.ORDER_CONFIRMATION.value,
                cart.guest.room.room_no,
                cart.total,
            )

            # Send SMS to staff with order details
            staff_nos = request.property.staffs.values_list("user__phone", flat=True)
            msg = Messages(
                reduce(
                    lambda x, y: f"{x},{y}", [no.national_number for no in staff_nos]
                )
            )
            order_total = reduce(
                lambda x, y: x + y,
                [service_order.total for service_order in service_orders],
            )
            msg.send_bulk_sms(
                MessageTemplates.ORDER_CONFIRMATION.value,
                cart.guest.room.room_no,
                order_total,
            )

            # Send SMS to service vendor with order details
            for service_order in service_orders:
                msg = Messages(
                    service_order.service_partner.phone_number.national_number
                )
                msg.send_bulk_sms(
                    MessageTemplates.ORDER_CONFIRMATION.value,
                    service_order.guest.room.property.name,
                    service_order.total,
                )

            return SuccessResponse(
                data={"orders": [service_order.id for service_order in service_orders]}
            )
        except Exception as e:
            return BadRequestResponse(message=str(e))

    def put(self, request, service_type=None):
        try:
            service_type = service_factory.url_mappings.get(service_type)
            order_id = request.data.get("order_id")
            order = service_factory.service_order_model.get(service_type).objects.get(
                id=order_id
            )
            action = request.data.get("action")
            order.status = order.OrderStatus[action]
            order.save()
            msg = Messages(order.cart.guest.user.phone.national_number)
            msg.send_bulk_sms(MessageTemplates.ORDER_UPDATE.value, action)
            return SuccessResponse(data={"order": order.id})
        except Exception as e:
            return BadRequestResponse(message=str(e))
