"""
Test cases for AioSell Inventory API endpoints.
"""

import json
from datetime import date, timedelta
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth.models import User
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch, MagicMock

from stay.models import Property, Room
from pms.models import RoomType, HotelOTAIntegration


class InventoryAPITestCase(TestCase):
    """Test case for inventory API endpoints."""

    def setUp(self):
        """Set up test data."""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test property
        self.property = Property.objects.create(
            name='Test Hotel',
            address='123 Test St',
            city='Test City',
            state='Test State',
            country='Test Country',
            rooms=10,
            owner=self.user
        )
        
        # Create room type
        self.room_type = RoomType.objects.create(
            hotel=self.property,
            name='Standard Room',
            description='A standard room',
            max_guests=2,
            area=25.0
        )
        
        # Create rooms
        for i in range(5):
            Room.objects.create(
                property=self.property,
                room_type=self.room_type,
                room_no=f'10{i}',
                max_guests=2,
                occupied=False,
                checked_in=False
            )
        
        # Create AioSell integration
        self.integration = HotelOTAIntegration.objects.create(
            hotel=self.property,
            ota_name='AioSell',
            api_key='test_api_key',
            secret_key='test_secret_key',
            endpoint_url='https://test.aiosell.com/api/v2/cm',
            is_active=True,
            configuration={
                'hotel_code': 'TEST_HOTEL',
                'identifier': 'test_identifier',
                'room_mapping': {
                    str(self.room_type.id): 'STANDARD'
                }
            }
        )
        
        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # Mock request property
        self.client.defaults['HTTP_X_PROPERTY_ID'] = str(self.property.id)

    def test_push_to_aiosell_success(self):
        """Test successful inventory push to AioSell."""
        url = reverse('pms-inventory-push-to-aiosell')
        
        data = {
            "room_inventories": [
                {"room_code": "STANDARD", "available": 3}
            ],
            "start_date": "2023-12-24",
            "end_date": "2023-12-30"
        }
        
        with patch('pms.services.aiosell.get_aiosell_service') as mock_get_service:
            mock_service = MagicMock()
            mock_service.push_inventory_update.return_value = {
                "success": True,
                "message": "Inventory updated successfully"
            }
            mock_get_service.return_value = mock_service
            
            response = self.client.post(url, data, format='json')
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn('message', response.data)
            self.assertEqual(response.data['data']['updated_rooms'], 1)
            mock_service.push_inventory_update.assert_called_once()

    def test_push_to_aiosell_validation_error(self):
        """Test validation error for invalid data."""
        url = reverse('pms-inventory-push-to-aiosell')
        
        data = {
            "room_inventories": [
                {"room_code": "", "available": -1}  # Invalid data
            ],
            "start_date": "2023-12-24",
            "end_date": "2023-12-30"
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('message', response.data)

    def test_push_to_aiosell_missing_data(self):
        """Test error for missing required data."""
        url = reverse('pms-inventory-push-to-aiosell')
        
        data = {
            "room_inventories": [],  # Empty
            "start_date": "2023-12-24"
            # Missing end_date
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('message', response.data)

    def test_push_to_aiosell_invalid_date_format(self):
        """Test error for invalid date format."""
        url = reverse('pms-inventory-push-to-aiosell')
        
        data = {
            "room_inventories": [
                {"room_code": "STANDARD", "available": 3}
            ],
            "start_date": "24-12-2023",  # Wrong format
            "end_date": "2023-12-30"
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Dates must be in YYYY-MM-DD format', response.data['message'])

    def test_push_to_aiosell_invalid_date_range(self):
        """Test error for invalid date range."""
        url = reverse('pms-inventory-push-to-aiosell')
        
        data = {
            "room_inventories": [
                {"room_code": "STANDARD", "available": 3}
            ],
            "start_date": "2023-12-30",
            "end_date": "2023-12-24"  # End before start
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('end_date must be on or after start_date', response.data['message'])

    def test_push_to_aiosell_past_date_validation(self):
        """Test error for past dates."""
        from datetime import date, timedelta
        
        url = reverse('pms-inventory-push-to-aiosell')
        
        yesterday = (date.today() - timedelta(days=1)).strftime("%Y-%m-%d")
        
        data = {
            "room_inventories": [
                {"room_code": "STANDARD", "available": 3}
            ],
            "start_date": yesterday,  # Yesterday (past date)
            "end_date": "2023-12-30"
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('start_date cannot be in the past', response.data['message'])

    def test_push_to_aiosell_today_date_allowed(self):
        """Test that today's date is allowed."""
        from datetime import date
        
        url = reverse('pms-inventory-push-to-aiosell')
        
        today = date.today().strftime("%Y-%m-%d")
        
        data = {
            "room_inventories": [
                {"room_code": "STANDARD", "available": 3}
            ],
            "start_date": today,  # Today should be allowed
            "end_date": today
        }
        
        with patch('pms.services.aiosell.get_aiosell_service') as mock_get_service:
            mock_service = MagicMock()
            mock_service.push_inventory_update.return_value = {
                "success": True,
                "message": "Inventory updated successfully"
            }
            mock_get_service.return_value = mock_service
            
            response = self.client.post(url, data, format='json')
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn('message', response.data)

    def test_bulk_sync_success(self):
        """Test successful bulk inventory sync."""
        url = reverse('pms-inventory-bulk-sync')
        
        data = {
            "room_type_ids": [str(self.room_type.id)],
            "start_date": "2023-12-24",
            "end_date": "2023-12-30"
        }
        
        with patch('pms.services.aiosell.get_aiosell_service') as mock_get_service:
            mock_service = MagicMock()
            mock_service.bulk_inventory_update.return_value = {
                "success": True,
                "message": "Bulk sync completed successfully"
            }
            mock_get_service.return_value = mock_service
            
            response = self.client.post(url, data, format='json')
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn('message', response.data)
            self.assertEqual(response.data['data']['updated_room_types'], 1)
            mock_service.bulk_inventory_update.assert_called_once()

    def test_aiosell_status_success(self):
        """Test AioSell status endpoint."""
        url = reverse('pms-inventory-aiosell-status')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertTrue(response.data['data']['configured'])
        self.assertEqual(response.data['data']['hotel_code'], 'TEST_HOTEL')
        self.assertEqual(response.data['data']['room_types_count'], 1)

    def test_push_to_aiosell_async(self):
        """Test async inventory push."""
        url = reverse('pms-inventory-push-to-aiosell')
        
        data = {
            "room_inventories": [
                {"room_code": "STANDARD", "available": 3}
            ],
            "start_date": "2023-12-24",
            "end_date": "2023-12-30"
        }
        
        with patch('pms.tasks.aiosell_tasks.async_push_inventory_to_aiosell.delay') as mock_task:
            mock_task.return_value = MagicMock(id='test-task-id')
            
            response = self.client.post(url + '?async=true', data, format='json')
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data['data']['task_id'], 'test-task-id')
            self.assertEqual(response.data['data']['status'], 'queued')
            mock_task.assert_called_once()

    def test_aiosell_not_configured(self):
        """Test error when AioSell is not configured."""
        # Delete the integration
        self.integration.delete()
        
        url = reverse('pms-inventory-push-to-aiosell')
        
        data = {
            "room_inventories": [
                {"room_code": "STANDARD", "available": 3}
            ],
            "start_date": "2023-12-24",
            "end_date": "2023-12-30"
        }
        
        with patch('pms.services.aiosell.get_aiosell_service') as mock_get_service:
            mock_get_service.return_value = None  # No service available
            
            response = self.client.post(url, data, format='json')
            
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertIn('AioSell integration not configured', response.data['message'])