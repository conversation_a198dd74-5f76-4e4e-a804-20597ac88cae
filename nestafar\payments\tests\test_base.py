"""
Base Test Classes for Payment System

Provides common test utilities, fixtures, and mock objects
for payment system testing.
"""

import uuid
import decimal
from decimal import Decimal
from unittest.mock import patch
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone

from core.models import PartnerProfile
from service.models import ServicePartner
from stay.models import Property, Room, Guest
from booking.models import <PERSON><PERSON><PERSON>ckin, PreCheckinGuest
from ..models import PaymentIntent, PaymentSplit, PaymentWebhookEvent
from ..constants import PaymentContext, PaymentStatus, TransferStatus
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


class PaymentTestBase(TestCase):
    """
    Base test class for payment system tests.
    
    Provides common fixtures and utilities for testing
    payment functionality.
    """
    
    def setUp(self):
        """Set up test fixtures"""
        # Create test users
        self.partner_user = User.objects.create_user(
            username='testpartner',
            email='<EMAIL>',
            name='Test Partner',
            phone_number='+919876543210'
        )
        
        self.guest_user = User.objects.create_user(
            username='testguest',
            email='<EMAIL>',
            name='Test Guest',
            phone_number='+************'
        )
        
        self.vendor_user = User.objects.create_user(
            username='testvendor',
            email='<EMAIL>',
            name='Test Vendor',
            phone_number='+************'
        )
        
        # Create partner profile
        self.partner_profile = PartnerProfile.objects.create(
            user=self.partner_user,
            platform_commission_rate=Decimal('5.00'),
            razorpay_linked_account_id='acc_test_partner123',
            razorpay_account_verified=True
        )
        
        # Create service partner (vendor)
        self.service_partner = ServicePartner.objects.create(
            name='Test Vendor Service',
            phone_number='+************',
            razorpay_linked_account_id='acc_test_vendor123',
            razorpay_account_verified=True,
            kyc_completed=True,
            account_status='active'
        )
        
        # Create property and room
        self.property = Property.objects.create(
            name='Test Property',
            partner_profile=self.partner_profile,
            address='Test Address',
            city='Test City',
            state='Test State',
            pincode='123456'
        )
        
        self.room = Room.objects.create(
            property=self.property,
            room_no='101',
            room_type='Standard',
            price_per_night=Decimal('1000.00')
        )
        
        # Create guest
        self.guest = Guest.objects.create(
            user=self.guest_user,
            room=self.room,
            checked_in=True,
            check_in_date=timezone.now()
        )
        
        # Create precheckin
        self.precheckin = PreCheckin.objects.create(
            property=self.property,
            expected_checkin=timezone.now() + timezone.timedelta(days=1),
            stay_duration=2,
            number_of_rooms=1,
            total_amount=Decimal('2000.00'),
            amount_paid=Decimal('0.00'),
            pending_balance=Decimal('2000.00'),
            payment_status='pending'
        )
        
        # Create precheckin guest
        self.precheckin_guest = PreCheckinGuest.objects.create(
            pre_checkin=self.precheckin,
            user=self.guest_user,
            is_primary=True
        )
    
    def create_payment_intent(self, context=PaymentContext.CHECKOUT, 
                            amount=Decimal('1000.00'), **kwargs):
        """Create a test payment intent"""
        # Validate mutually exclusive parameters
        if 'guest' in kwargs and 'precheckin' in kwargs:
            raise ValueError("Cannot specify both 'guest' and 'precheckin' parameters - they are mutually exclusive")
        
        # Validate context-specific field conflicts
        if context == PaymentContext.CHECKOUT and 'precheckin' in kwargs:
            raise ValueError("Cannot specify 'precheckin' when context is CHECKOUT")
        elif context == PaymentContext.PRECHECKIN and 'guest' in kwargs:
            raise ValueError("Cannot specify 'guest' when context is PRECHECKIN")
        
        defaults = {
            'context': context,
            'partner': self.partner_profile,
            'total_amount': amount,
            'expires_at': timezone.now() + timezone.timedelta(hours=24),
        }
        
        if context == PaymentContext.CHECKOUT:
            defaults['guest'] = self.guest
        elif context == PaymentContext.PRECHECKIN:
            defaults['precheckin'] = self.precheckin
        
        defaults.update(kwargs)
        return PaymentIntent.objects.create(**defaults)
    
    def create_payment_split(self, payment_intent, recipient_type='partner',
                           amount=Decimal('950.00'), **kwargs):
        """Create a test payment split"""
        defaults = {
            'payment_intent': payment_intent,
            'recipient_type': recipient_type,
            'recipient_id': str(self.partner_profile.id),
            'recipient_name': 'Test Recipient',
            'amount': amount,
            'status': TransferStatus.PENDING,
        }
        
        if recipient_type == 'partner':
            defaults['razorpay_account_id'] = self.partner_profile.razorpay_linked_account_id
        elif recipient_type == 'vendor':
            defaults['recipient_id'] = str(self.service_partner.id)
            defaults['razorpay_account_id'] = self.service_partner.razorpay_linked_account_id
        
        defaults.update(kwargs)
        return PaymentSplit.objects.create(**defaults)
    
    def create_webhook_event(self, event_type='payment.captured', 
                           entity_id='pay_test123', **kwargs):
        """Create a test webhook event"""
        defaults = {
            'event_id': f'event_{uuid.uuid4().hex[:12]}',
            'event_type': event_type,
            'entity_type': 'payment',
            'entity_id': entity_id,
            'raw_payload': {
                'event': event_type,
                'payload': {
                    'payment': {
                        'entity': {
                            'id': entity_id,
                            'status': 'captured',
                            'amount': 100000,  # 1000.00 in paise
                        }
                    }
                }
            },
            'signature_verified': True,
        }
        defaults.update(kwargs)
        return PaymentWebhookEvent.objects.create(**defaults)
    
    def mock_razorpay_response(self, method_name, return_value):
        """Mock Razorpay service method"""
        return patch(f'payments.services.razorpay_service.RazorpayService.{method_name}',
                    return_value=return_value)
    
    def mock_notification_service(self, method_name, return_value=None):
        """Mock notification service method"""
        if return_value is None:
            return_value = {'sent': True, 'message_id': 'test_msg_123'}
        
        return patch(f'payments.services.payment_notification_service.PaymentNotificationService.{method_name}',
                    return_value=return_value)


class PaymentAPITestBase(PaymentTestBase):
    """
    Base test class for payment API tests.
    
    Extends PaymentTestBase with API testing utilities.
    """
    
    def setUp(self):
        super().setUp()        
        
        self.client = APIClient()
        
        # Create JWT tokens for authentication
        self.partner_token = RefreshToken.for_user(self.partner_user).access_token
        self.guest_token = RefreshToken.for_user(self.guest_user).access_token
    
    def authenticate_as_partner(self):
        """Authenticate API client as partner"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.partner_token}')
    
    def authenticate_as_guest(self):
        """Authenticate API client as guest"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.guest_token}')
    
    def clear_authentication(self):
        """Clear API client authentication"""
        self.client.credentials()


class MockRazorpayService:
    """
    Mock Razorpay service for testing.
    
    Provides predictable responses for Razorpay API calls
    without making actual HTTP requests.
    """
    
    def __init__(self):
        self.payment_links = {}
        self.payments = {}
        self.transfers = {}
    
    def create_payment_link(self, amount, description, customer_details, **kwargs):
        """Mock payment link creation"""
        link_id = f'plink_test_{uuid.uuid4().hex[:12]}'
        # Normalize and validate amount
        try:
            amt = decimal.Decimal(str(amount))
        except (decimal.InvalidOperation, TypeError, ValueError) as e:
            raise ValueError(f"Invalid amount passed to mock create_payment_link: {amount}")

        if amt < 0:
            raise ValueError(f"Amount must be non-negative, got: {amount}")

        # Ensure two decimal places (rupees precision)
        amt = amt.quantize(decimal.Decimal('0.01'))

        payment_link = {
            'id': link_id,
            'short_url': f'https://rzp.io/l/{link_id}',
            'amount': int(amt * 100),  # Convert to paise
            'description': description,
            'customer': customer_details,
            'status': 'created',
            'created_at': int(timezone.now().timestamp()),
        }
        
        self.payment_links[link_id] = payment_link
        return payment_link
    
    def get_payment_link(self, link_id):
        """Mock payment link retrieval"""
        return self.payment_links.get(link_id, {})
    
    def cancel_payment_link(self, link_id):
        """Mock payment link cancellation"""
        if link_id in self.payment_links:
            self.payment_links[link_id]['status'] = 'cancelled'
        return {'status': 'cancelled'}
    
    def create_transfer(self, payment_id, transfers):
        """Mock transfer creation"""
        transfer_results = []
        
        for transfer in transfers:
            transfer_id = f'trf_test_{uuid.uuid4().hex[:12]}'
            
            transfer_result = {
                'id': transfer_id,
                'amount': transfer['amount'],
                'currency': transfer.get('currency', 'INR'),
                'account': transfer['account'],
                'status': 'processed',
                'created_at': int(timezone.now().timestamp()),
            }
            
            self.transfers[transfer_id] = transfer_result
            transfer_results.append(transfer_result)
        
        return transfer_results
    
    def get_payment(self, payment_id):
        """Mock payment retrieval"""
        return self.payments.get(payment_id, {
            'id': payment_id,
            'status': 'captured',
            'method': 'card',
            'amount': 100000,  # 1000.00 in paise
        })
    
    def verify_webhook_signature(self, payload, signature):
        """Mock webhook signature verification"""
        return True  # Always return True for testing


class PaymentTestMixin:
    """
    Mixin providing common payment test utilities.
    
    Can be used with any test class to add payment-specific
    assertion methods and utilities.
    """
    
    def assertPaymentIntentCreated(self, payment_intent, expected_amount, expected_context):
        """Assert payment intent was created correctly"""
        self.assertIsNotNone(payment_intent.id)
        self.assertEqual(payment_intent.total_amount, expected_amount)
        self.assertEqual(payment_intent.context, expected_context)
        self.assertEqual(payment_intent.status, PaymentStatus.PENDING)
        self.assertIsNotNone(payment_intent.reference_number)
    
    def assertPaymentSplitCalculated(self, payment_intent, expected_splits_count):
        """Assert payment splits were calculated correctly"""
        splits = payment_intent.splits.all()
        self.assertEqual(splits.count(), expected_splits_count)
        
        # Verify split amounts sum to total
        total_split_amount = sum(split.amount for split in splits)
        self.assertEqual(total_split_amount, payment_intent.total_amount)
    
    def assertNotificationSent(self, mock_notification, expected_calls=1):
        """Assert notification was sent"""
        self.assertEqual(mock_notification.call_count, expected_calls)
    
    def assertWebhookProcessed(self, webhook_event):
        """Assert webhook event was processed"""
        webhook_event.refresh_from_db()
        self.assertTrue(webhook_event.processed)
        self.assertIsNotNone(webhook_event.processed_at)
        self.assertIsNone(webhook_event.processing_error)
