from service.subapps.food.serializers import *
from service.subapps.laundry.serializers import *
from service.subapps.rental.serializers import *
from service.subapps.transport.serializers import *
from service.subapps.food.models import *
from service.subapps.laundry.models import *
from service.subapps.rental.models import *
from service.subapps.transport.models import *
from service.subapps.food.filters import *
from service.subapps.laundry.filters import *
from service.subapps.rental.filters import *
from service.subapps.transport.filters import *
from service.subapps.shop.serializers import *
from service.subapps.shop.models import *
from service.subapps.shop.filters import *
from service.subapps.tourism.serializers import *
from service.subapps.tourism.models import *
from service.subapps.tourism.filters import *
from service.models.service import ServicePartner

PartnerTypes = ServicePartner.PartnerTypes


url_mappings = {
    "food": PartnerTypes.FOOD,
    "transport": PartnerTypes.TRANSPORT,
    "rental": PartnerTypes.RENTAL,
    "laundry": PartnerTypes.LAUNDRY,
    "shop": PartnerTypes.SHOP,
    "tourism": PartnerTypes.TOURISM,
}

service_list_serializer = {
    PartnerTypes.FOOD: FoodServiceListSerializer,
    PartnerTypes.LAUNDRY: LaundryServiceListSerializer,
    PartnerTypes.RENTAL: RentalServiceSerializer,
    PartnerTypes.TRANSPORT: TransportServiceSerializer,
    PartnerTypes.SHOP: ShopServiceListSerializer,
    PartnerTypes.TOURISM: TourismServiceSerializer,
}

service_item_list_serializer = {
    PartnerTypes.FOOD: FoodServiceItemListSerializer,
    PartnerTypes.LAUNDRY: LaundryServiceItemListSerializer,
    PartnerTypes.RENTAL: RentalServiceItemSerializer,
    PartnerTypes.TRANSPORT: TransportServiceItemLocationSerializer,
    PartnerTypes.SHOP: ShopServiceItemListSerializer,
    PartnerTypes.TOURISM: TourismServiceItemLocationSerializer,
}

service_model = {
    PartnerTypes.FOOD: FoodService,
    PartnerTypes.LAUNDRY: LaundryService,
    PartnerTypes.RENTAL: RentalService,
    PartnerTypes.TRANSPORT: TransportService,
    PartnerTypes.SHOP: ShopService,
    PartnerTypes.TOURISM: TourismService,
}

service_item_model = {
    PartnerTypes.FOOD: FoodServiceItem,
    PartnerTypes.LAUNDRY: LaundryServiceItem,
    PartnerTypes.RENTAL: RentalServiceItem,
    PartnerTypes.TRANSPORT: TransportServiceItem,
    PartnerTypes.SHOP: ShopServiceItem,
    PartnerTypes.TOURISM: TourismServiceItem,
}
service_item_filter = {
    PartnerTypes.FOOD: FoodServiceItemFilter,
    PartnerTypes.LAUNDRY: LaundryItemFilterSet,
    PartnerTypes.RENTAL: RentalItemFilterSet,
    PartnerTypes.TRANSPORT: TransportItemFilterSet,
    PartnerTypes.SHOP: ShopServiceItemFilter,
    PartnerTypes.TOURISM: TourismItemFilterSet,
}

service_order_filter = {
    PartnerTypes.FOOD: FoodOrderFilter,
    PartnerTypes.LAUNDRY: LaundryOrderFilter,
    PartnerTypes.RENTAL: RentalOrderFilter,
    PartnerTypes.TRANSPORT: TransportOrderFilter,
    PartnerTypes.SHOP: ShopOrderFilter,
    PartnerTypes.TOURISM: TourismOrderFilter,
}

service_cart_filter = {
    PartnerTypes.FOOD: FoodCartFilter,
    PartnerTypes.LAUNDRY: LaundryCartFilter,
    PartnerTypes.RENTAL: RentalCartFilter,
    PartnerTypes.TRANSPORT: TransportCartFilter,
    PartnerTypes.SHOP: ShopCartFilter,
    PartnerTypes.TOURISM: TourismCartFilter,
}


service_item_create_serializer = {
    PartnerTypes.FOOD: FoodServiceItemCreateSerializer,
    PartnerTypes.LAUNDRY: LaundryServiceItemSerializer,
    PartnerTypes.RENTAL: RentalServiceItemSerializer,
    PartnerTypes.TRANSPORT: TransportServiceItemSerializer,
    PartnerTypes.SHOP: ShopServiceItemCreateSerializer,
    PartnerTypes.TOURISM: TourismServiceItemSerializer,
}

service_cart_model = {
    PartnerTypes.FOOD: FoodCart,
    PartnerTypes.LAUNDRY: LaundryCart,
    PartnerTypes.RENTAL: RentalCart,
    PartnerTypes.TRANSPORT: TransportCart,
    PartnerTypes.SHOP: ShopCart,
    PartnerTypes.TOURISM: TourismCart,
}

service_cart_serializer = {
    PartnerTypes.FOOD: FoodCartSerializer,
    PartnerTypes.LAUNDRY: LaundryCartSerializer,
    PartnerTypes.RENTAL: RentalCartSerializer,
    PartnerTypes.TRANSPORT: TransportCartSerializer,
    PartnerTypes.SHOP: ShopCartSerializer,
    PartnerTypes.TOURISM: TourismCartSerializer,
}

service_cart_item_serializer = {
    PartnerTypes.FOOD: FoodCartItemSerializer,
    PartnerTypes.LAUNDRY: LaundryCartItemListSerializer,
    PartnerTypes.RENTAL: RentalCartItemSerializer,
    PartnerTypes.TRANSPORT: TransportCartItemSerializer,
    PartnerTypes.SHOP: ShopCartItemSerializer,
    PartnerTypes.TOURISM: TourismCartItemSerializer,
}

service_cart_item_model = {
    PartnerTypes.FOOD: FoodCartItems,
    PartnerTypes.LAUNDRY: LaundryCartItems,
    PartnerTypes.RENTAL: RentalCartItems,
    PartnerTypes.TRANSPORT: TransportCartItems,
    PartnerTypes.SHOP: ShopCartItems,
    PartnerTypes.TOURISM: TourismCartItems,
}

service_order_model = {
    PartnerTypes.FOOD: FoodOrder,
    PartnerTypes.LAUNDRY: LaundryOrder,
    PartnerTypes.RENTAL: RentalOrder,
    PartnerTypes.TRANSPORT: TransportOrder,
    PartnerTypes.SHOP: ShopOrder,
    PartnerTypes.TOURISM: TourismOrder,
}

service_order_item_model = {
    PartnerTypes.FOOD: FoodOrderItem,
    PartnerTypes.LAUNDRY: LaundryOrderItem,
    PartnerTypes.RENTAL: RentalOrderItem,
    PartnerTypes.TRANSPORT: TransportOrderItem,
    PartnerTypes.SHOP: ShopOrderItem,
    PartnerTypes.TOURISM: TourismOrderItem,
}

service_order_serializer = {
    PartnerTypes.FOOD: FoodOrderSerializer,
    PartnerTypes.LAUNDRY: LaundryOrderSerializer,
    PartnerTypes.RENTAL: RentalOrderSerializer,
    PartnerTypes.TRANSPORT: TransportOrderSerializer,
    PartnerTypes.SHOP: ShopOrderSerializer,
    PartnerTypes.TOURISM: TourismOrderSerializer,
}
