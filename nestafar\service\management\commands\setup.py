from notification.models import Notification, NotificationCategory, NotificationChannel
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        try:
            Notification.objects.create(
                category=NotificationCategory.USER_CHECKIN_INITIATED.name,
                channel=NotificationChannel.MESSAGE.name,
                description="User checkin initiated",
                user_type=Notification.UserTypeOptions.USER,
            )
            Notification.objects.create(
                category=NotificationCategory.USER_CHECKIN.name,
                channel=NotificationChannel.PUSH.name,
                description="User checkin",
                user_type=Notification.UserTypeOptions.USER,
            )
            Notification.objects.create(
                category=NotificationCategory.USER_CHECKOUT.name,
                channel=NotificationChannel.PUSH.name,
                description="User checkout",
                user_type=Notification.UserTypeOptions.USER,
            )
            Notification.objects.create(
                category=NotificationCategory.USER_ORDER_ACCEPTED.name,
                channel=NotificationChannel.PUSH.name,
                description="User order accepted",
                user_type=Notification.UserTypeOptions.USER,
            )
            Notification.objects.create(
                category=NotificationCategory.USER_ORDER_CANCELLED.name,
                channel=NotificationChannel.PUSH.name,
                description="User order cancelled",
                user_type=Notification.UserTypeOptions.USER,
            )
            Notification.objects.create(
                category=NotificationCategory.USER_ORDER_COMPLETED.name,
                channel=NotificationChannel.PUSH.name,
                description="User order completed",
                user_type=Notification.UserTypeOptions.USER,
            )
            Notification.objects.create(
                category=NotificationCategory.PARTNER_ORDER_PLACED.name,
                channel=NotificationChannel.PUSH.name,
                description="Partner order placed",
                user_type=Notification.UserTypeOptions.PARTNER,
            )
        except Exception as e:
            print(e)
