# Generated by Django 4.2.7 on 2025-10-02 05:11

from django.db import migrations, models
import phonenumber_field.modelfields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Job',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('room', models.CharField(blank=True, help_text='Room number for housekeeping or in-property jobs', max_length=20, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], db_index=True, default='pending', max_length=20)),
                ('initiated_by', models.CharField(choices=[('auto_created', 'Auto Created'), ('guest_request', 'Guest Request'), ('owner_request', 'Owner/Partner Request')], default='auto_created', max_length=20)),
                ('type', models.Char<PERSON>ield(choices=[('delivery', 'Delivery'), ('housekeeping', 'Housekeeping')], db_index=True, max_length=20)),
                ('pickup_location', models.CharField(blank=True, max_length=255, null=True)),
                ('dropoff_location', models.CharField(blank=True, max_length=255, null=True)),
                ('earnings', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('title', models.CharField(blank=True, max_length=120, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('payload', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='JobStatusLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('previous_status', models.CharField(blank=True, choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], max_length=20, null=True)),
                ('new_status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], max_length=20)),
                ('note', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='ServicePartner',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('type_of_service', models.PositiveSmallIntegerField(choices=[(1, 'Food'), (2, 'Laundry'), (3, 'Transport'), (4, 'Rental'), (5, 'Others'), (6, 'Shop'), (7, 'Tourism')], default=5)),
                ('description', models.TextField(blank=True, null=True)),
                ('phone_number', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, null=True, region=None)),
                ('is_visible', models.BooleanField(default=True)),
                ('razorpay_linked_account_id', models.CharField(blank=True, help_text='Razorpay linked account identifier for vendor payouts', max_length=100, null=True)),
                ('razorpay_account_verified', models.BooleanField(default=False, help_text='Whether the Razorpay linked account is verified and active')),
                ('kyc_completed', models.BooleanField(default=False, help_text='Whether KYC verification is completed for compliance')),
                ('kyc_completed_at', models.DateTimeField(blank=True, help_text='Timestamp when KYC was completed', null=True)),
                ('account_status', models.CharField(choices=[('pending', 'Pending Verification'), ('verified', 'Verified'), ('suspended', 'Suspended'), ('rejected', 'Rejected')], db_index=True, default='pending', help_text='Current status of the vendor account', max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='Staff',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='StaffProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('role', models.CharField(choices=[('housekeeping', 'Housekeeping'), ('delivery', 'Delivery'), ('maintenance', 'Maintenance'), ('frontdesk', 'Front Desk')], max_length=32)),
                ('contact_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('alt_phone', models.CharField(blank=True, max_length=32, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_gig_worker', models.BooleanField(default=False, help_text='True for on-demand delivery workers')),
                ('earnings_rate', models.DecimalField(blank=True, decimal_places=2, help_text='Per-job or hourly rate', max_digits=10, null=True)),
                ('meta', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
