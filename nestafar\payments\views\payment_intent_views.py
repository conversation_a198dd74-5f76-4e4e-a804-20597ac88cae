"""
Payment Intent Views

API views for payment intent management and operations.
"""

import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone
from decimal import Decimal
from django.db.models import Sum

from ..models import PaymentIntent, PaymentSplit
from ..serializers.payment_intent_serializer import (
    PaymentIntentSerializer,
    CreatePaymentLinkSerializer,
    PaymentStatusSerializer,
    PaymentSplitSerializer
)
from ..services import RazorpayService, PaymentSplitService
from ..exceptions import PaymentException, RazorpayException
from core.permissions import PartnerPermission

logger = logging.getLogger(__name__)


class PaymentIntentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for PaymentIntent management.
    
    Provides CRUD operations for payment intents and related actions
    like creating payment links, checking status, and managing splits.
    """
    
    queryset = PaymentIntent.objects.all()
    serializer_class = PaymentIntentSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Filter queryset based on user permissions"""
        user = self.request.user
        
        if user.is_staff or user.is_superuser:
            # Admin users can see all payment intents
            return PaymentIntent.objects.all()
        elif hasattr(user, 'partner_profile'):
            # Partners can only see their own payment intents
            return PaymentIntent.objects.filter(partner=user.partner_profile)
        else:
            # Regular users can't access payment intents directly
            return PaymentIntent.objects.none()
    
    def get_permissions(self):
        """Get permissions based on action"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAuthenticated, PartnerPermission]
        else:
            permission_classes = [IsAuthenticated]
        
        return [permission() for permission in permission_classes]
    
    @action(detail=True, methods=['get'])
    def status(self, request, pk=None):
        """
        Get detailed payment status including splits and transfers.
        
        Returns comprehensive payment status information including
        split details and transfer status.
        """
        payment_intent = self.get_object()
        
        try:
            # Get split information
            splits = PaymentSplit.objects.filter(payment_intent=payment_intent)
            split_serializer = PaymentSplitSerializer(splits, many=True)
            
            # Calculate transfer statistics
            transfers_completed = splits.filter(status='processed').count()
            transfers_pending = splits.filter(status__in=['pending', 'processing']).count()
            transfers_failed = splits.filter(status='failed').count()
            
            # Build response data
            response_data = {
                'payment_id': payment_intent.id,
                'reference_number': payment_intent.reference_number,
                'status': payment_intent.status,
                'status_display': payment_intent.get_status_display(),
                'total_amount': payment_intent.total_amount,
                'total_amount_display': f"₹{payment_intent.total_amount:,.2f}",
                'payment_method': payment_intent.payment_method,
                'razorpay_payment_id': payment_intent.razorpay_payment_id,
                'paid_at': payment_intent.paid_at,
                'created_at': payment_intent.created_at,
                
                # Split information
                'platform_commission': payment_intent.platform_commission,
                'partner_amount': payment_intent.partner_amount,
                'vendor_amount': payment_intent.vendor_amount,
                
                # Transfer status
                'transfers_completed': transfers_completed,
                'transfers_pending': transfers_pending,
                'transfers_failed': transfers_failed,
                
                # Detailed splits
                'splits': split_serializer.data,
            }
            
            serializer = PaymentStatusSerializer(data=response_data)
            serializer.is_valid(raise_exception=True)
            
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error getting payment status for {pk}: {str(e)}")
            return Response(
                {'error': 'Failed to get payment status'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'])
    def splits(self, request, pk=None):
        """
        Get payment splits for a payment intent.
        
        Returns detailed information about how the payment
        amount is split between platform, partner, and vendors.
        """
        payment_intent = self.get_object()
        splits = PaymentSplit.objects.filter(payment_intent=payment_intent)
        serializer = PaymentSplitSerializer(splits, many=True)
        
        return Response({
            'payment_intent_id': payment_intent.id,
            'reference_number': payment_intent.reference_number,
            'total_amount': payment_intent.total_amount,
            'splits': serializer.data,
            'split_summary': {
                'platform_commission': payment_intent.platform_commission,
                'partner_amount': payment_intent.partner_amount,
                'vendor_amount': payment_intent.vendor_amount,
            }
        })
    
    @action(detail=True, methods=['post'])
    def calculate_splits(self, request, pk=None):
        """
        Recalculate payment splits for a payment intent.
        
        This action recalculates the payment splits based on
        current business rules and partner configuration.
        """
        payment_intent = self.get_object()
        
        if payment_intent.is_paid():
            return Response(
                {'error': 'Cannot recalculate splits for paid payments'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            with transaction.atomic():
                # Delete existing splits
                PaymentSplit.objects.filter(payment_intent=payment_intent).delete()
                
                # Recalculate splits
                split_service = PaymentSplitService()
                split_data = split_service.calculate_splits(payment_intent)
                
                # Create new splits
                splits = split_service.create_payment_splits(payment_intent, split_data)
                
                # Update payment intent with new amounts
                payment_intent.split_details = split_data
                payment_intent.save()
                
                # Serialize response
                split_serializer = PaymentSplitSerializer(splits, many=True)
                
                return Response({
                    'message': 'Splits recalculated successfully',
                    'payment_intent_id': payment_intent.id,
                    'splits': split_serializer.data,
                    'summary': split_data.get('summary', {})
                })
                
        except Exception as e:
            logger.error(f"Error recalculating splits for {pk}: {str(e)}")
            return Response(
                {'error': f'Failed to recalculate splits: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def retry_transfers(self, request, pk=None):
        """
        Retry failed transfers for a payment intent.
        
        This action retries any failed transfers for the payment,
        useful when transfers fail due to temporary issues.
        """
        payment_intent = self.get_object()
        
        if not payment_intent.is_paid():
            return Response(
                {'error': 'Payment must be completed before retrying transfers'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Get failed splits that can be retried
            failed_splits = PaymentSplit.objects.filter(
                payment_intent=payment_intent,
                status='failed'
            ).filter(retry_count__lt=3)  # Max 3 retry attempts
            
            if not failed_splits.exists():
                return Response({
                    'message': 'No failed transfers to retry',
                    'retried_count': 0
                })
            
            # Retry transfers
            razorpay_service = RazorpayService()
            retried_count = 0
            
            for split in failed_splits:
                try:
                    if split.get_transfer_payload():
                        # Create transfer via Razorpay
                        transfer_data = razorpay_service.create_transfer(
                            payment_intent.razorpay_payment_id,
                            [split.get_transfer_payload()]
                        )
                        if transfer_data and len(transfer_data) > 0:
                            transfer = transfer_data[0]
                            if transfer and 'id' in transfer:
                                split.mark_as_initiated(transfer['id'])
                                retried_count += 1
                            else:
                                logger.warning(f"Invalid transfer response for split {split.id}")
                            
                except Exception as e:
                    logger.error(f"Failed to retry transfer for split {split.id}: {str(e)}")
                    # mark_as_failed handles incrementing retry_count and persisting
                    split.mark_as_failed(error_message=str(e))
            
            return Response({
                'message': f'Retried {retried_count} transfers',
                'retried_count': retried_count,
                'total_failed': failed_splits.count()
            })
            
        except Exception as e:
            logger.error(f"Error retrying transfers for {pk}: {str(e)}")
            return Response(
                {'error': f'Failed to retry transfers: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """
        Get payment summary statistics for the current user.
        
        Returns summary statistics about payments for the
        authenticated user (partner or admin).
        """
        queryset = self.get_queryset()
        
        # Calculate statistics
        total_payments = queryset.count()
        completed_payments = queryset.filter(status='completed').count()
        pending_payments = queryset.filter(status='pending').count()
        failed_payments = queryset.filter(status='failed').count()
        
        # Calculate amounts using database aggregation for completed payments
        completed_qs = queryset.filter(status='completed')
        aggregates = completed_qs.aggregate(
            total_amount=Sum('total_amount'),
            total_commission=Sum('platform_commission')
        )
        total_amount = aggregates['total_amount'] or Decimal('0.00')
        total_commission = aggregates['total_commission'] or Decimal('0.00')
        
        return Response({
            'total_payments': total_payments,
            'completed_payments': completed_payments,
            'pending_payments': pending_payments,
            'failed_payments': failed_payments,
            'success_rate': (completed_payments / total_payments * 100) if total_payments > 0 else 0,
            'total_amount': total_amount,
            'total_commission': total_commission,
            'average_amount': (total_amount / completed_payments) if completed_payments > 0 else 0,
        })
