# ============================================================================
# RAZORPAY PAYMENT GATEWAY CONFIGURATION
# ============================================================================

# Razorpay API Credentials
# Get these from your Razorpay Dashboard: https://dashboard.razorpay.com/app/keys
RAZORPAY_KEY_ID=rzp_test_your_key_id_here
RAZORPAY_KEY_SECRET=your_key_secret_here

# Razorpay Environment
# Use 'test' for development/testing, 'live' for production
RAZORPAY_ENVIRONMENT=test

# Razorpay Webhook Secret
# Generate this from your Razorpay Dashboard webhook settings
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret_here

# Razorpay Webhook URL
# This should be the full URL where <PERSON><PERSON><PERSON><PERSON> will send webhooks
# Example (production): https://backend.nestafar.com/payments/webhooks/razorpay/
RAZORPAY_WEBHOOK_URL=https://{{host}}/payments/webhooks/razorpay/
# ============================================================================
# EXISTING ENVIRONMENT VARIABLES (for reference)
# ============================================================================

# Database Configuration
DATABASE_URL=your_database_url_here

# AWS ConfigurationAWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_STORAGE_BUCKET_NAME=your_bucket_name
AWS_S3_REGION_NAME=your_region

# Google Maps APIGOOGLE_MAPS_API_KEY=your_google_maps_api_key

Fast2SMS Configuration
# Fast2SMS Configuration
WhatsApp Configuration
# WhatsApp Configuration
# ============================================================================
# PAYMENT SECURITY NOTES
# ============================================================================

# IMPORTANT SECURITY NOTES:
# 1. Never commit actual credentials to version control
# 2. Use different credentials for test and production environments
# 3. Regularly rotate your API keys and webhook secrets
# 4. Monitor your Razorpay dashboard for suspicious activity
# 5. Enable webhook signature verification in production
# 6. Use HTTPS for all webhook URLs in production
