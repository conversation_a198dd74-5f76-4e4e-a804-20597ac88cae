from django.db import models
from service.models import (
    BaseService,
    BaseServiceItem,
    BaseOrder,
    BaseOrderItem,
    BaseCart,
    BaseCartItems,
)


class LaundryService(BaseService):
    def __str__(self):
        return self.name + " by " + self.partner.name


class LaundryServiceItem(BaseServiceItem):
    service = models.ForeignKey(
        LaundryService, on_delete=models.CASCADE, related_name="service_items"
    )

    def __str__(self):
        return self.name


class LaundryCart(BaseCart):
    def __str__(self):
        return (
            self.guest.user.name
            + " "
            + self.guest.room.property.name
            + " "
            + str(self.total)
        )


class LaundryCartItems(BaseCartItems):
    item = models.ForeignKey(
        LaundryServiceItem, on_delete=models.CASCADE, related_name="cart_items"
    )
    cart = models.ForeignKey(
        LaundryCart,
        on_delete=models.CASCADE,
        related_name="cart_items",
        null=True,
        blank=True,
    )

    class Meta:
        verbose_name_plural = "Laundry cart items"


class LaundryOrder(BaseOrder):
    cart = models.ForeignKey(
        LaundryCart, on_delete=models.CASCADE, related_name="orders"
    )
    service = models.ForeignKey(
        LaundryService, on_delete=models.CASCADE, related_name="orders"
    )

    def __str__(self):
        return self.guest.user.name + "_" + self.service.name


class LaundryOrderItem(BaseOrderItem):
    item = models.ForeignKey(
        LaundryServiceItem, on_delete=models.CASCADE, related_name="order_items"
    )
    order = models.ForeignKey(
        LaundryOrder, on_delete=models.CASCADE, related_name="order_items"
    )

    def __str__(self):
        return self.item.name + "_" + self.order.guest.user.name
