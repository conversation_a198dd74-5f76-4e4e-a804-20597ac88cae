# Generated by Django 4.2.7 on 2025-10-02 05:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('stay', '0001_initial'),
        ('booking', '0002_initial'),
        ('pms', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='roomtype',
            name='hotel',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_types', to='stay.property'),
        ),
        migrations.AddField(
            model_name='roomblocksynclog',
            name='hotel',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_block_sync_logs', to='stay.property'),
        ),
        migrations.AddField(
            model_name='roomblocksynclog',
            name='ota_platform',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_block_sync_logs', to='pms.otaplatform'),
        ),
        migrations.AddField(
            model_name='roomblocksynclog',
            name='room_block',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sync_logs', to='pms.roomblock'),
        ),
        migrations.AddField(
            model_name='roomblock',
            name='hotel',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_blocks', to='stay.property'),
        ),
        migrations.AddField(
            model_name='roomblock',
            name='reservation',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='room_blocks', to='booking.reservation'),
        ),
        migrations.AddField(
            model_name='roomblock',
            name='room',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blocks', to='stay.room'),
        ),
        migrations.AddField(
            model_name='rateplan',
            name='room_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rate_plans', to='pms.roomtype'),
        ),
        migrations.AddField(
            model_name='hotelotaintegration',
            name='hotel',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ota_integrations', to='stay.property'),
        ),
        migrations.AddField(
            model_name='hotelotaintegration',
            name='ota_platform',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='hotel_integrations', to='pms.otaplatform'),
        ),
        migrations.AddField(
            model_name='calendar',
            name='rate_plan',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='calendar_entries', to='pms.rateplan'),
        ),
        migrations.AddField(
            model_name='calendar',
            name='room_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calendar_entries', to='pms.roomtype'),
        ),
        migrations.AddField(
            model_name='availabilitylog',
            name='hotel',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_logs', to='stay.property'),
        ),
        migrations.AddField(
            model_name='availabilitylog',
            name='ota_platform',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_logs', to='pms.otaplatform'),
        ),
        migrations.AddField(
            model_name='availabilitylog',
            name='room_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_logs', to='pms.roomtype'),
        ),
        migrations.AlterUniqueTogether(
            name='roomtype',
            unique_together={('hotel', 'name')},
        ),
        migrations.AddIndex(
            model_name='roomblocksynclog',
            index=models.Index(fields=['hotel', 'sync_status', 'created_at'], name='pms_room_bl_hotel_i_9239b1_idx'),
        ),
        migrations.AddIndex(
            model_name='roomblocksynclog',
            index=models.Index(fields=['room_block', 'ota_platform'], name='pms_room_bl_room_bl_4c7eba_idx'),
        ),
        migrations.AddIndex(
            model_name='roomblocksynclog',
            index=models.Index(fields=['sync_status', 'retry_count'], name='pms_room_bl_sync_st_499b44_idx'),
        ),
        migrations.AddIndex(
            model_name='roomblock',
            index=models.Index(fields=['hotel', 'is_active'], name='pms_roomblo_hotel_i_efb1b1_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='roomblock',
            unique_together={('hotel', 'reservation', 'reason')},
        ),
        migrations.AlterUniqueTogether(
            name='hotelotaintegration',
            unique_together={('hotel', 'ota_platform')},
        ),
        migrations.AddIndex(
            model_name='calendar',
            index=models.Index(fields=['room_type', 'rate_plan', 'date'], name='pms_calenda_room_ty_4823d3_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='calendar',
            unique_together={('room_type', 'rate_plan', 'date')},
        ),
        migrations.AddIndex(
            model_name='availabilitylog',
            index=models.Index(fields=['hotel', 'sync_status', 'date'], name='pms_availab_hotel_i_f8efa2_idx'),
        ),
        migrations.AddIndex(
            model_name='availabilitylog',
            index=models.Index(fields=['created_at'], name='pms_availab_created_264234_idx'),
        ),
    ]
