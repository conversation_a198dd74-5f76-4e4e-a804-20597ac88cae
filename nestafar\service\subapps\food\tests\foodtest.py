from models import *
from rest_framework.test import APIClient
from django.urls import reverse
from core.models import User
import json
from django.contrib.auth import get_user_model


# Create a reusable client object


def create_user(context, username, password):
    client = APIClient()
    user = User.objects.get(username=username)
    login_response = client.post(
        reverse("login"), {"username": username, "password": password}
    )
    token = json.loads(login_response.content)["access_token"]
    client.credentials(HTTP_AUTHORIZATION="Bearer " + token)
    context.auth_client = client
    context.auth_user = user


def create_staff_user(username="staff_user", password="secret"):
    """
    Creates a test user object with staff privileges.
    """
    user = create_user(username, password)
    user.is_staff = True
    user.save()
    return user


def authenticate_user(client, user):
    """
    Authenticates a user with the test client.
    """
    client.force_authenticate(user=user)


def create_food_service(name="Test Service", partner=None):
    """
    Creates a test food service object.
    """
    if not partner:
        partner = ServicePartner.objects.create(
            name="Test Partner", type_of_service="FOOD"
        )
    service = FoodService.objects.create(name=name, partner=partner)
    return service


def create_food_service_item(service, name="Test Item", category="MCR", price=10.50):
    """
    Creates a test food service item object.
    """
    item = FoodServiceItem.objects.create(
        service=service, name=name, category=category, price=price
    )
    return item
