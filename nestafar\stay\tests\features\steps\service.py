from service.models import ServicePartner
from pytest_bdd import given, when, then, parsers
from geo.models import Location
from django.urls import reverse
from service.subapps.food.models import FoodService, FoodServiceItem
from service.subapps.rental.models import RentalService, RentalServiceItem
from service.subapps.laundry.models import LaundryService, LaundryServiceItem
from service.subapps.transport.models import TransportService, TransportServiceItem
import logging
import json
from stay.models import Property
from rest_framework.test import APIClient
from django.core.files.uploadedfile import SimpleUploadedFile

logger = logging.getLogger(__name__)

subapp_url_mapping = {
    "foodservice": ("food-service", "food-service-item"),
    "rentalservice": ("rental-service", "rental-service-item"),
    "laundryservice": ("laundry-service", "laundry-service-item"),
    "transportservice": ("transport-service", "transport-service-item"),
}


@given(parsers.parse("Setup Service Partners\n{table}"))
def setup_service_partners(context, table):
    context.populate_table_from_str(table)
    for row in context.table:
        location = Location.objects.get(name=row["location"])
        payload = {
            "name": row["name"],
            "location": location.id,
            "type_of_service": row["type"],
            "description": row["description"],
            "commission": row["commission"],
        }
        if row["delivery_charges"] != "NA":
            payload["delivery_charges"] = row["delivery_charges"]
        if row["pickup_charges"] != "NA":
            payload["pickup_charges"] = row["pickup_charges"]
        # ServicePartner.objects.create(
        #     name=row['name'],
        #     location_id=location.id,
        #     type=row['type'],
        #     description=row['description']
        # )
        response = context.auth_client.post(
            reverse("service:service-partner-list"), payload
        )
        assert response.status_code == 201


@given(parsers.parse("Setup Services\n{table}"))
def setup_services(context, table):
    context.populate_table_from_str(table)
    for row in context.table:
        partner = ServicePartner.objects.get(name=row["partner"])
        payload = {
            "name": row["name"],
            "partner": partner.id,
            "charges": row["charges"],
            "tax_rate": row["tax_rate"],
        }
        if row["service_type"] == "rentalservice":
            payload["period"] = 0
            payload["min_period"] = 1
            payload["max_period"] = 240
        if row["service_type"] == "transportservice":
            payload["surcharge"] = 0
        response = context.auth_client.post(
            reverse(
                f'service:{row["service_type"]}:{subapp_url_mapping[row["service_type"]][0]}-list'
            ),
            payload,
        )
        assert response.status_code == 201


service_model_mapping = {
    "foodservice": (FoodService, FoodServiceItem),
    "rentalservice": (RentalService, RentalServiceItem),
    "laundryservice": (LaundryService, LaundryServiceItem),
    "transportservice": (TransportService, TransportServiceItem),
}


@given(parsers.parse("Setup Service Items\n{table}"))
def setup_service_items(context, table):
    context.populate_table_from_str(table)
    payloads = []
    for row in context.table:
        service_model = service_model_mapping[row["service_type"]][0]
        service = service_model.objects.get(name=row["service"])
        addon = json.loads(row["addon"]) if row["addon"] else None
        # send json string
        if addon:
            addon = json.dumps(addon)
        payload = {
            "name": row["name"],
            "description": row["description"],
            "price": row["price"],
            # TODO: ADDON handling
            "service": service.id,
        }
        if addon:
            payload["addon"] = addon
        if service_model == FoodService:
            payload["vegetarian"] = row["vegetarian"]
        response = context.auth_client.post(
            reverse(
                f'service:{row["service_type"]}:{subapp_url_mapping[row["service_type"]][1]}-list'
            ),
            payload,
            # content_type='application/json'
        )
    logger.info(f"Response: {response.json()}")
    # logger.info(f'Response: {response.data}')
    assert response.status_code == 201


@given(parsers.parse("Setup Transport Service Items\n{table}"))
def setup_transport_service_items(context, table):
    context.populate_table_from_str(table)
    for row in context.table:
        service = TransportService.objects.get(name=row["service"])
        location_strs = row["service_areas"].split(",")
        location_ids = []
        for location_str in location_strs:
            location = Location.objects.get(name=location_str.strip())
            location_ids.append(location.id)
        payload = {
            "name": row["name"],
            "description": row["description"],
            "price": row["price"],
            "addon": json.loads(row["addon"]) if row["addon"] else None,
            "service": service.id,
            "service_areas": location_ids,
            "coverage_distance": row["coverage_distance"],
        }
        # logger.info(f'Payload: {payload}')
        response = context.auth_client.post(
            reverse(
                f'service:{row["service_type"]}:{subapp_url_mapping[row["service_type"]][1]}-list'
            ),
            payload,
            format="json",
            # content_type='application/json'
        )
        # logger.info(f'Response: {response.data}')
        assert response.status_code == 201


@then(parsers.parse("Validate Service Items\n{table}"))
def validate_service_items(context, table):
    context.populate_table_from_str(table)
    for row in context.table:
        service_model = service_model_mapping[row["service_type"]][0]
        service_item_model = service_model_mapping[row["service_type"]][1]
        service = service_model.objects.get(name=row["service"])
        service_item = service_item_model.objects.get(name=row["name"], service=service)
        assert service_item.service == service
        assert service_item.description == row["description"]
        assert service_item.price == float(row["price"])
        if row["addon"]:
            assert service_item.addon == json.loads(row["addon"])
        else:
            assert service_item.addon == None


catalog_sub_url_mapping = {
    "foodservice": "food",
    "rentalservice": "rental",
    "laundryservice": "laundry",
    "transportservice": "transport",
}


@given(parsers.parse("Validate Catalog for property {property_name}\n{table}"))
def validate_catalog(context, property_name, table):
    context.populate_table_from_str(table)
    catalogs = {}
    for fi in FoodServiceItem.objects.all():
        if not fi.image:
            fi.image = SimpleUploadedFile("food.jpg", b"", content_type="image/jpg")
            fi.save()
    for row in context.table:
        if row["service_type"] in catalogs:
            catalogs[row["service_type"]].append(row["name"])
        else:
            catalogs[row["service_type"]] = [row["name"]]
    for service_type, service_names in catalogs.items():
        response = context.auth_client.get(
            reverse(
                "service:catalog",
                kwargs={"service_type": catalog_sub_url_mapping[service_type]},
            )
        )
        logger.info(f"Response: {response.json()}")
        assert response.status_code == 200
        assert set(service_names) == set(
            [s["name"] for s in response.json()["data"][0]["catalog"]]
        )
