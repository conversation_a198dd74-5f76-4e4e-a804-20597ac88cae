from django.db import models
from service.models import *
from geo.models import Location
from django.core.exceptions import ValidationError


class RentalService(BaseService):
    class TypeOfRental(models.TextChoices):
        BIKE = "bike"
        CAR = "car"
        GEAR = "gear"
        GUIDE = "guide"

    class PeriodType(models.TextChoices):
        HOURLY = "hourly"
        DAILY = "daily"
        WEEKLY = "weekly"
        MONTHLY = "monthly"

    period = models.CharField(
        choices=PeriodType.choices, max_length=10, default=PeriodType.DAILY
    )
    min_period = models.IntegerField(default=0)
    max_period = models.IntegerField(default=100)
    type_of_rental = models.CharField(choices=TypeOfRental.choices, max_length=10)

    def save(self, *args, **kwargs):
        if self.period.lower() not in self.PeriodType.values:
            raise ValidationError("Incorrect rental period")
        if self.type_of_rental.lower() not in self.TypeOfRental.values:
            raise ValidationError("Incorrect rental type")
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name


class RentalServiceItem(BaseServiceItem):
    service = models.ForeignKey(
        RentalService, on_delete=models.CASCADE, related_name="service_items"
    )
    deposit = models.FloatField(default=0)
    extra_args = models.JSONField(null=True, blank=True)

    def __str__(self):
        return self.name


class RentalCart(BaseCart):
    def __str__(self):
        return (
            self.guest.user.name
            + " "
            + self.guest.room.property.name
            + " "
            + str(self.total)
        )


class RentalCartItems(BaseCartItems):
    cart = models.ForeignKey(
        RentalCart,
        on_delete=models.CASCADE,
        related_name="cart_items",
        null=True,
        blank=True,
    )
    item = models.ForeignKey(
        RentalServiceItem, on_delete=models.CASCADE, related_name="cart_items"
    )
    pickup_date = models.DateTimeField(blank=True, null=True)
    drop_date = models.DateTimeField(blank=True, null=True)
    pickup_location = models.ForeignKey(
        Location,
        on_delete=models.SET_NULL,
        related_name="rental_cart_pickup_locations",
        null=True,
    )
    no_of_periods = models.IntegerField(default=0)

    class Meta:
        verbose_name_plural = "Rental cart items"

    def add_item(self, commission, charges):
        self.name = self.item.name
        delta = self.drop_date - self.pickup_date
        period = self.item.service.period
        no_of_periods = 0
        if period.lower() == RentalService.PeriodType.HOURLY:
            no_of_periods = round(delta.days * 24 + delta.seconds / 3600)  # hours
        elif period.lower() == RentalService.PeriodType.MONTHLY:
            no_of_periods = round(delta.days / 30)  # monthly
        elif period.lower() == RentalService.PeriodType.DAILY:
            no_of_periods = round(delta.days)  # daily
        elif period.lower() == RentalService.PeriodType.WEEKLY:
            no_of_periods = round(delta.days / 7)  # weekly
        self.price = round(self.item.price * (1 + commission / 100))
        add_on_price = (
            sum(self.item.addon[add_on] for add_on in self.add_ons)
            if self.add_ons
            else 0
        )
        add_on_price += round(add_on_price * commission / 100)
        self.price += add_on_price
        self.no_of_periods = no_of_periods
        item_total = self.price * self.quantity * no_of_periods
        self.cart.subtotal += item_total
        self.cart.taxes += item_total * (self.item.service.tax_rate / 100)
        self.cart.taxes = round(self.cart.taxes, 2)
        self.cart.charges += charges
        self.cart.total = round(
            self.cart.subtotal + self.cart.taxes + self.cart.charges, 2
        )
        self.cart.save(update_fields=["subtotal", "taxes", "charges", "total"])
        self.save()
        return self

    def add_quantity(self, quantity):
        self.quantity += quantity
        quantity_total = self.price * quantity * self.no_of_periods
        self.cart.subtotal += quantity_total
        new_tax = self.cart.taxes + (
            quantity_total * (self.item.service.tax_rate / 100)
        )
        self.cart.apply_taxes(new_tax)
        self.save(update_fields=["quantity"])
        return self

    def remove_quantity(self, quantity):
        self.quantity -= quantity
        self.quantity = self.quantity if self.quantity > 0 else 0
        quantity_total = self.price * quantity * self.no_of_periods
        self.cart.subtotal -= quantity_total
        new_tax = self.cart.taxes - (
            quantity_total * (self.item.service.tax_rate / 100)
        )
        self.cart.apply_taxes(new_tax)
        self.save(update_fields=["quantity"])
        return self


class RentalOrder(BaseOrder):
    cart = models.ForeignKey(
        RentalCart, on_delete=models.CASCADE, related_name="orders"
    )
    service = models.ForeignKey(
        RentalService, on_delete=models.CASCADE, related_name="orders"
    )

    def __str__(self):
        return self.guest.user.name + "_" + self.service.name


class RentalOrderItem(BaseOrderItem):
    item = models.ForeignKey(
        RentalServiceItem, on_delete=models.CASCADE, related_name="order_items"
    )
    order = models.ForeignKey(
        RentalOrder, on_delete=models.CASCADE, related_name="order_items"
    )
    pickup_date = models.DateTimeField(blank=True, null=True)
    drop_date = models.DateTimeField(blank=True, null=True)
    no_of_periods = models.IntegerField(default=0)
    pickup_location = models.ForeignKey(
        Location,
        on_delete=models.SET_NULL,
        related_name="rental_order_pickup_locations",
        null=True,
    )

    def __str__(self):
        return (
            self.item.name
            + "_"
            + self.order.guest.user.name
            + "_"
            + self.order.service.name
        )

    def add_item(self, cart_item):
        assert issubclass(
            type(cart_item), RentalCartItems
        ), "Only Rental Cart Items are accepted"
        no_of_periods = cart_item.no_of_periods
        service_price = cart_item.item.price * no_of_periods
        service_addons_price = (
            sum([cart_item.item.addon[addon] for addon in cart_item.add_ons])
            * no_of_periods
        )
        catalog_price = cart_item.price * no_of_periods
        tax_rate = cart_item.item.service.tax_rate

        self.order.commissions = round(
            (catalog_price - (service_price + service_addons_price))
            * cart_item.quantity,
            2,
        )
        self.order.subtotal += round(
            (service_price + service_addons_price) * cart_item.quantity, 2
        )
        self.order.taxes = round(self.order.subtotal * (tax_rate / 100), 2)
        self.order.total = round(
            self.order.subtotal + self.order.taxes + self.order.charges, 2
        )
        self.order.save(
            update_fields=["commissions", "subtotal", "taxes", "charges", "total"]
        )
        return self.order
