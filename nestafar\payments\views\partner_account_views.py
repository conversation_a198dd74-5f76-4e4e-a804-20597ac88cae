"""
Partner Account Management Views

API views for managing partner <PERSON><PERSON>pay accounts, bank details,
KYC documents, and verification status.
"""

import logging
from rest_framework import status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON>PartPars<PERSON>, FormParser
from django.shortcuts import get_object_or_404
from django.db import transaction

from ..models import (
    PartnerRazorpayAccount,
    PartnerBankAccount,
    PartnerKYCDocument
)
from ..serializers.partner_account_serializer import (
    PartnerRazorpayAccountSerializer,
    CreatePartnerAccountSerializer,
    PartnerBankAccountSerializer,
    AddBankAccountSerializer,
    PartnerKYCDocumentSerializer,
    UploadKYCDocumentSerializer,
    AccountVerificationLogSerializer,
    AccountStatusSerializer,
    CompleteOnboardingSerializer,
)
from ..services.razorpay_account_service import RazorpayAccountService
from ..exceptions import (
    AccountCreationException,
    <PERSON>YCEx<PERSON>,
    Bank<PERSON>ccountException,
    RazorpayException
)
from core.permissions import PartnerPermission

logger = logging.getLogger(__name__)


class PartnerAccountViewSet(ModelViewSet):
    """ViewSet for managing partner Razorpay accounts"""

    serializer_class = PartnerRazorpayAccountSerializer
    permission_classes = [permissions.IsAuthenticated, PartnerPermission]
    http_method_names = ['get', 'post']  # Restrict to only needed methods

    def get_queryset(self):
        """Return accounts for the authenticated partner only"""
        return PartnerRazorpayAccount.objects.filter(
            partner__user=self.request.user
        ).select_related('partner__user')

    def get_object(self):
        """Get or create partner account"""
        try:
            return self.get_queryset().get()
        except PartnerRazorpayAccount.DoesNotExist:
            # Return None if account doesn't exist - will be handled in create action
            return None

    @action(detail=False, methods=['post'], url_path='create-account')
    def create_account(self, request):
        """Create a new Razorpay account for the partner. Optionally auto-run stakeholder creation and product config request."""
        try:
            # Check if account already exists
            existing_account = self.get_queryset().first()
            if existing_account:
                return Response(
                    {'error': 'Partner account already exists'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate input data
            serializer = CreatePartnerAccountSerializer(data=request.data)
            if not serializer.is_valid():
                logger.warning({
                    'event': 'partner_account_create_validation_failed',
                    'user_id': getattr(request.user, 'id', None),
                    'errors': serializer.errors
                })
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            # Create partner account
            with transaction.atomic():
                partner_account = PartnerRazorpayAccount.objects.create(
                    partner=request.user.partner_profile,
                    business_name=serializer.validated_data['business_name'],
                    business_type=serializer.validated_data['business_type'],
                    contact_name=serializer.validated_data['contact_name'],
                    contact_email=serializer.validated_data['contact_email'],
                    contact_phone=serializer.validated_data['contact_phone'],
                    address_line1=serializer.validated_data['address_line1'],
                    address_line2=serializer.validated_data.get('address_line2', ''),
                    city=serializer.validated_data['city'],
                    state=serializer.validated_data['state'],
                    postal_code=serializer.validated_data['postal_code'],
                    country=serializer.validated_data.get('country', 'IN'),
                )

                # Create Razorpay account (Step 1)
                account_service = RazorpayAccountService()
                razorpay_response = account_service.create_partner_account(
                    partner_account,
                    category=serializer.validated_data.get('category'),
                    subcategory=serializer.validated_data.get('subcategory')
                )

                # Optionally auto-run Steps 2 and 3
                if serializer.validated_data.get('auto_onboard'):
                    try:
                        account_service.create_stakeholder(partner_account)
                        account_service.request_product_configuration(partner_account)
                    except RazorpayException as e:
                        logger.error(f"Auto-onboard post account creation failed: {e}")
                        raise

                # Return created account
                response_serializer = PartnerRazorpayAccountSerializer(partner_account)
                return Response(
                    {
                        'message': 'Partner account created successfully',
                        'account': response_serializer.data,
                        'razorpay_account_id': razorpay_response.get('id')
                    },
                    status=status.HTTP_201_CREATED
                )

        except AccountCreationException as e:
            logger.error(f"Account creation failed for partner {request.user.id}: {str(e)}")
            return Response(
                {'error': f'Account creation failed: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error creating account for partner {request.user.id}: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'], url_path='complete-onboarding')
    def complete_onboarding(self, request):
        """Create account if needed and complete onboarding by adding bank account (runs Steps 1-4)."""
        try:
            payload_serializer = CompleteOnboardingSerializer(data=request.data)
            if not payload_serializer.is_valid():
                return Response(payload_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            account_payload = payload_serializer.validated_data.get('account') or {}
            bank_payload = payload_serializer.validated_data['bank']

            with transaction.atomic():
                partner_account = self.get_queryset().first()
                account_service = RazorpayAccountService()

                # Step 1: Create account if missing
                if not partner_account:
                    if not account_payload:
                        return Response(
                            {'error': 'account payload is required when account does not exist'},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                    partner_account = PartnerRazorpayAccount.objects.create(
                        partner=request.user.partner_profile,
                        business_name=account_payload['business_name'],
                        business_type=account_payload['business_type'],
                        contact_name=account_payload['contact_name'],
                        contact_email=account_payload['contact_email'],
                        contact_phone=account_payload['contact_phone'],
                        address_line1=account_payload['address_line1'],
                        address_line2=account_payload.get('address_line2', ''),
                        city=account_payload['city'],
                        state=account_payload['state'],
                        postal_code=account_payload['postal_code'],
                        country=account_payload.get('country', 'IN'),
                    )
                    account_service.create_partner_account(
                        partner_account,
                        category=account_payload.get('category'),
                        subcategory=account_payload.get('subcategory')
                    )

                # Steps 2-4: Add bank account (handles stakeholder + product config)
                bank_account = account_service.add_bank_account(partner_account, bank_payload)

            return Response(
                {
                    'message': 'Onboarding completed successfully',
                    'account': PartnerRazorpayAccountSerializer(partner_account).data,
                    'bank_account': PartnerBankAccountSerializer(bank_account).data,
                },
                status=status.HTTP_201_CREATED
            )
        except RazorpayException as e:
            logger.error(f"Complete onboarding failed for partner {request.user.id}: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Unexpected error completing onboarding for partner {request.user.id}: {str(e)}")
            return Response({'error': 'Unexpected error'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def status(self, request):
        """Get comprehensive account status"""
        try:
            partner_account = self.get_queryset().first()
            if not partner_account:
                return Response(
                    {'error': 'Partner account not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get status from Razorpay
            account_service = RazorpayAccountService()
            status_data = account_service.get_account_status(partner_account)

            # Get additional statistics
            bank_accounts = partner_account.bank_accounts.all()
            kyc_documents = partner_account.kyc_documents.all()
            recent_activities = partner_account.verification_logs.all()[:10]

            response_data = {
                **status_data,
                'business_name': partner_account.business_name,
                'business_type': partner_account.business_type,
                'contact_email': partner_account.contact_email,
                'total_bank_accounts': bank_accounts.count(),
                'verified_bank_accounts': bank_accounts.filter(verification_status='verified').count(),
                'total_kyc_documents': kyc_documents.count(),
                'verified_kyc_documents': kyc_documents.filter(verification_status='verified').count(),
                'recent_activities': AccountVerificationLogSerializer(recent_activities, many=True).data,
                'last_error': partner_account.last_error,
                'error_count': partner_account.error_count
            }

            serializer = AccountStatusSerializer(response_data)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error fetching account status for partner {request.user.id}: {str(e)}") 
            return Response(
                {'error': 'Failed to fetch account status'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'], url_path='verification-logs')
    def verification_logs(self, request):
        """Get account verification activity logs"""
        try:
            partner_account = self.get_queryset().first()
            if not partner_account:
                return Response(
                    {'error': 'Partner account not found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            logs = partner_account.verification_logs.all()

            # Apply pagination
            page = self.paginate_queryset(logs)
            if page is not None:
                serializer = AccountVerificationLogSerializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = AccountVerificationLogSerializer(logs, many=True)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error fetching verification logs for partner {request.user.id}: {str(e)}")
            return Response(
                {'error': 'Failed to fetch verification logs'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class BankAccountViewSet(ModelViewSet):
    """ViewSet for managing partner bank accounts"""

    serializer_class = PartnerBankAccountSerializer
    permission_classes = [permissions.IsAuthenticated, PartnerPermission]
    http_method_names = ['get', 'post']  # Restrict to only needed methods

    def get_queryset(self):
        """Return bank accounts for the authenticated partner only"""
        return PartnerBankAccount.objects.filter(
            razorpay_account__partner__user=self.request.user
        ).select_related('razorpay_account__partner__user')

    def get_partner_account(self):
        """Get partner's Razorpay account"""
        return get_object_or_404(
            PartnerRazorpayAccount,
            partner__user=self.request.user
        )

    def create(self, request, *args, **kwargs):
        """Add a new bank account"""
        try:
            partner_account = self.get_partner_account()

            # Validate input data
            serializer = AddBankAccountSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            # Check if bank account already exists
            account_number = serializer.validated_data['account_number']
            existing_account = partner_account.bank_accounts.filter(
                account_number=account_number
            ).first()

            if existing_account:
                return Response(
                    {'error': f'Bank account {account_number[-4:]} already exists for this partner'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Add bank account (atomic to rollback on Razorpay failure)
            with transaction.atomic():
                account_service = RazorpayAccountService()
                bank_account = account_service.add_bank_account(
                    partner_account,
                    serializer.validated_data
                )

            # Return created bank account
            response_serializer = PartnerBankAccountSerializer(bank_account)
            return Response(
                {
                    'message': 'Bank account added successfully',
                    'bank_account': response_serializer.data
                },
                status=status.HTTP_201_CREATED
            )

        except BankAccountException as e:
            logger.error(f"Bank account creation failed for partner {request.user.id}: {str(e)}")
            return Response(
                {'error': f'Bank account creation failed: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error adding bank account for partner {request.user.id}: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """Initiate bank account verification"""
        try:
            bank_account = self.get_object()

            # Initiate verification (atomic to rollback any partial DB updates on failure)
            with transaction.atomic():
                account_service = RazorpayAccountService()
                verification_response = account_service.verify_bank_account(bank_account)

            return Response(
                {
                    'message': 'Bank account verification initiated',
                    'verification_response': verification_response
                }
            )

        except RazorpayException as e:
            logger.error(f"Bank account verification failed: {str(e)}")
            return Response(
                {'error': f'Verification failed: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error during bank account verification: {str(e)}")
            return Response(
                {'error': 'An unexpected error occurred'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def set_primary(self, request, pk=None):
        """Set bank account as primary"""
        try:
            bank_account = self.get_object()

            # Update primary status
            with transaction.atomic():
                # Lock all bank accounts for this razorpay_account (including the target)
                qs = PartnerBankAccount.objects.select_for_update().filter(
                    razorpay_account=bank_account.razorpay_account
                )

                # Clear primary flag on all other accounts while rows are locked
                qs.exclude(pk=bank_account.pk).update(is_primary=False)

                # Re-fetch the target row under the same lock and set it primary
                target = qs.get(pk=bank_account.pk)
                if not target.is_primary:
                    target.is_primary = True
                    target.save(update_fields=['is_primary'])

            serializer = PartnerBankAccountSerializer(bank_account)
            return Response(
                {
                    'message': 'Bank account set as primary',
                    'bank_account': serializer.data
                }
            )

        except Exception as e:
            logger.error(f"Error setting primary bank account: {str(e)}")
            return Response(
                {'error': 'Failed to set primary bank account'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class KYCDocumentViewSet(ModelViewSet):
    """ViewSet for managing KYC documents"""

    serializer_class = PartnerKYCDocumentSerializer
    permission_classes = [permissions.IsAuthenticated, PartnerPermission]
    parser_classes = [MultiPartParser, FormParser]

    def get_queryset(self):
        """Return KYC documents for the authenticated partner only"""
        return PartnerKYCDocument.objects.filter(
            razorpay_account__partner__user=self.request.user
        ).select_related('razorpay_account__partner__user')

    def get_partner_account(self):
        """Get partner's Razorpay account"""
        return get_object_or_404(
            PartnerRazorpayAccount,
            partner__user=self.request.user
        )

    def create(self, request, *args, **kwargs):
        """Upload a new KYC document"""
        try:
            partner_account = self.get_partner_account()

            # Validate input data
            serializer = UploadKYCDocumentSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            # Check if document type already exists
            document_type = serializer.validated_data['document_type']
            existing_doc = partner_account.kyc_documents.filter(
                document_type=document_type
            ).first()

            if existing_doc:
                return Response(
                    {'error': f'{document_type} document already uploaded'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Pre-process and validate uploaded file
            uploaded_file = serializer.validated_data['document_file']
            # Enforce max file size (10 MB) and allowed MIME types
            MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
            ALLOWED_CONTENT_TYPES = ('image/jpeg', 'image/png', 'application/pdf')

            file_size = getattr(uploaded_file, 'size', None)
            content_type = getattr(uploaded_file, 'content_type', None)

            if file_size is not None and file_size > MAX_FILE_SIZE:
                return Response(
                    {'error': f'File size exceeds the maximum allowed size of 10MB'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if content_type is None or content_type not in ALLOWED_CONTENT_TYPES:
                return Response(
                    {'error': 'Unsupported file type. Allowed types: image/jpeg, image/png, application/pdf'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Upload document atomically (rollback on Razorpay failure)
            with transaction.atomic():
                account_service = RazorpayAccountService()
                kyc_document = account_service.upload_kyc_document(
                    partner_account,
                    serializer.validated_data['document_type'],
                    uploaded_file,
                    serializer.validated_data['document_number']
                )

            # Return uploaded document
            response_serializer = PartnerKYCDocumentSerializer(kyc_document)
            return Response(
                {
                    'message': 'KYC document uploaded successfully',
                    'document': response_serializer.data
                },
                status=status.HTTP_201_CREATED
            )

        except KYCException as e:
            logger.error(f"KYC document upload failed for partner {request.user.id}: {str(e)}")
            return Response(
                {'error': f'Document upload failed: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error uploading KYC document for partner {request.user.id}: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def replace(self, request, pk=None):
        """Replace an existing KYC document"""
        try:
            old_document = self.get_object()

            # Validate input data
            serializer = UploadKYCDocumentSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            # Ensure document type matches
            if serializer.validated_data['document_type'] != old_document.document_type:
                return Response(
                    {'error': 'Document type cannot be changed when replacing'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Pre-process and validate uploaded file
            uploaded_file = serializer.validated_data['document_file']
            MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
            ALLOWED_CONTENT_TYPES = ('image/jpeg', 'image/png', 'application/pdf')

            file_size = getattr(uploaded_file, 'size', None)
            content_type = getattr(uploaded_file, 'content_type', None)

            if file_size is not None and file_size > MAX_FILE_SIZE:
                return Response(
                    {'error': f'File size exceeds the maximum allowed size of 10MB'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if content_type is None or content_type not in ALLOWED_CONTENT_TYPES:
                return Response(
                    {'error': 'Unsupported file type. Allowed types: image/jpeg, image/png, application/pdf'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Upload new document atomically and delete old one inside same transaction
            with transaction.atomic():
                account_service = RazorpayAccountService()
                new_document = account_service.upload_kyc_document(
                    old_document.razorpay_account,
                    serializer.validated_data['document_type'],
                    uploaded_file,
                    serializer.validated_data['document_number']
                )
                # Delete old document
                old_document.delete()

            # Return new document
            response_serializer = PartnerKYCDocumentSerializer(new_document)
            return Response(
                {
                    'message': 'KYC document replaced successfully',
                    'document': response_serializer.data
                }
            )

        except Exception as e:
            logger.error(f"Error replacing KYC document: {str(e)}")
            return Response(
                {'error': 'Failed to replace document'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
