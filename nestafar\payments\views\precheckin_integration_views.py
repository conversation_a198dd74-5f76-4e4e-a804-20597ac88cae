"""
Precheckin Integration Views

API views for integrating payment processing into the precheckin flow.
"""

import logging
import decimal
from ..models import PaymentIntent, PaymentSplit
from ..constants import PaymentContext
from decimal import Decimal, InvalidOperation
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.core.exceptions import PermissionDenied

from booking.models import PreCheckin
from ..services.precheckin_payment_service import PrecheckinPaymentService
from ..exceptions import PaymentException
from core.permissions import PartnerPermission, check_precheckin_access

logger = logging.getLogger(__name__)


class CreatePrecheckinPaymentView(APIView):
    """
    Create payment link for precheckin upfront payment.

    This endpoint is called when a precheckin requires upfront payment.
    """

    permission_classes = [IsAuthenticated, PartnerPermission]

    def post(self, request, precheckin_id):
        """
        Create precheckin payment link.

        Args:
            precheckin_id: PreCheckin UUID

        Request body:
            {
                "upfront_amount": 1000.00,  # Optional: custom upfront amount
                "upfront_percentage": 20.0,  # Optional: percentage of total
                "description": "Custom description",  # Optional
                "expire_hours": 72  # Optional: payment link expiry
            }
        """
        try:
            # Get precheckin
            precheckin = get_object_or_404(PreCheckin, id=precheckin_id)

            # Permission check
            if not check_precheckin_access(request.user, precheckin):
                return Response(
                    {"error": "Permission denied"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Check if precheckin is eligible for payment
            if precheckin.status in ["cancelled", "checked_out"]:
                return Response(
                    {"error": "Precheckin is not eligible for payment"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Initialize precheckin payment service
            precheckin_service = PrecheckinPaymentService()

            # Calculate or get custom upfront amount
            custom_amount = request.data.get("upfront_amount")
            upfront_percentage = request.data.get("upfront_percentage")

            if custom_amount:
                try:
                    upfront_amount = Decimal(str(custom_amount))
                except (ValueError, decimal.InvalidOperation) as ex:
                    logger.warning(f"Invalid upfront_amount provided: %r", custom_amount)
                    return Response(
                        {"error": f"Invalid upfront_amount: {str(ex)}"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            else:
                # Validate upfront_percentage before converting
                if upfront_percentage is not None:
                    if not isinstance(upfront_percentage, (str, int, float, Decimal)):
                        return Response(
                            {
                                "error": "upfront_percentage must be a number or numeric string"
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                    try:
                        percentage = Decimal(str(upfront_percentage))
                    except (ValueError, InvalidOperation) as ex:
                        logger.warning(f"Invalid upfront_percentage provided: %r", upfront_percentage)
                        return Response(
                            {"error": f"Invalid upfront_percentage: {str(ex)}"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                else:
                    percentage = None

                upfront_amount = precheckin_service.calculate_upfront_amount(
                    precheckin, percentage
                )

            # Validate amount
            if upfront_amount <= 0:
                return Response(
                    {"error": "Upfront amount must be greater than zero"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            total_amount = Decimal(str(precheckin.total_amount))

            # Check if payment already exists
            existing_payment = PaymentIntent.objects.filter(
                precheckin=precheckin,
                total_amount=upfront_amount,
                context=PaymentContext.PRECHECKIN,
                status__in=["pending", "completed"],
            ).first()

            if existing_payment:
                return Response(
                    {
                        "error": "Payment already exists for this precheckin",
                        "existing_payment_id": existing_payment.id,
                        "payment_status": existing_payment.status,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if upfront_amount > total_amount:
                return Response(
                    {"error": "Upfront amount cannot exceed total amount"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get custom description
            description = request.data.get("description")

            # Create payment link
            payment_data = precheckin_service.create_precheckin_payment_link(
                precheckin=precheckin,
                upfront_amount=upfront_amount,
                partner=request.user.partner_profile,
                description=description,
            )

            # Build response
            response_data = {
                "payment_link": payment_data,
                "precheckin_info": {
                    "id": str(precheckin.id),
                    "property_name": precheckin.property.name,
                    "expected_checkin": precheckin.expected_checkin.isoformat(),
                    "stay_duration": precheckin.stay_duration,
                    "number_of_rooms": precheckin.number_of_rooms,
                    "total_amount": float(precheckin.total_amount),
                    "amount_paid": float(precheckin.amount_paid),
                    "pending_balance": float(precheckin.pending_balance),
                    "payment_status": precheckin.payment_status,
                },
                "upfront_details": {
                    "upfront_amount": float(upfront_amount),
                    "percentage_of_total": float(
                        (upfront_amount / total_amount * 100).quantize(Decimal("0.01"))
                    ),
                    "remaining_balance": float(total_amount - upfront_amount),
                },
            }

            return Response(response_data, status=status.HTTP_201_CREATED)

        except PaymentException as e:
            logger.error(f"Payment error for precheckin {precheckin_id}: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Unexpected error creating precheckin payment: {str(e)}")
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PrecheckinPaymentStatusView(APIView):
    """
    Check payment status for precheckin.

    This endpoint allows checking precheckin payment status.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, precheckin_id):
        """
        Get precheckin payment status.

        Args:
            precheckin_id: PreCheckin UUID
        """
        try:
            # Get precheckin
            precheckin = get_object_or_404(PreCheckin, id=precheckin_id)

            # Permission check
            if not check_precheckin_access(request.user, precheckin):
                return Response(
                    {"error": "Permission denied"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Find precheckin payment intents
            # Find precheckin payment intents
            payment_intents = PaymentIntent.objects.filter(
                precheckin=precheckin, context=PaymentContext.PRECHECKIN
            ).order_by("-created_at")

            payment_status = {
                "precheckin_id": str(precheckin.id),
                "precheckin_status": precheckin.status,
                "payment_status": precheckin.payment_status,
                "total_amount": float(precheckin.total_amount),
                "amount_paid": float(precheckin.amount_paid),
                "pending_balance": float(precheckin.pending_balance),
                "has_pending_payment": False,
                "has_completed_payment": False,
                "payments": [],
            }

            for payment_intent in payment_intents:
                payment_info = {
                    "payment_id": str(payment_intent.id),
                    "reference_number": payment_intent.reference_number,
                    "amount": float(payment_intent.total_amount),
                    "status": payment_intent.status,
                    "created_at": payment_intent.created_at.isoformat() if payment_intent.created_at else None,
                    "paid_at": payment_intent.paid_at.isoformat() if payment_intent.paid_at else None,
                    "expires_at": payment_intent.expires_at.isoformat() if payment_intent.expires_at else None,
                }

                payment_status["payments"].append(payment_info)

                if payment_intent.status == "pending":
                    payment_status["has_pending_payment"] = True
                elif payment_intent.is_paid():
                    payment_status["has_completed_payment"] = True

            return Response(payment_status)

        except Exception as e:
            logger.error(f"Error getting precheckin payment status: {str(e)}")
            return Response(
                {"error": "Failed to get payment status"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class SendPrecheckinPaymentReminderView(APIView):
    """
    Send payment reminder for pending precheckin.

    This endpoint allows partners to send payment reminders
    to guests with pending precheckin payments.
    """

    permission_classes = [IsAuthenticated, PartnerPermission]

    def post(self, request, precheckin_id):
        """
        Send payment reminder for precheckin.

        Args:
            precheckin_id: PreCheckin UUID
        """
        try:
            # Get precheckin
            precheckin = get_object_or_404(PreCheckin, id=precheckin_id)
            # Permission check
            if not check_precheckin_access(request.user, precheckin):
                return Response(
                    {"error": "Permission denied"},
                    status=status.HTTP_403_FORBIDDEN,
                )
            if precheckin.payment_status == "completed":
                return Response(
                    {"error": "Precheckin payment is already completed"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Send reminder
            precheckin_service = PrecheckinPaymentService()
            result = precheckin_service.send_precheckin_payment_reminder(precheckin)

            if result.get("sent", False):
                return Response(
                    {
                        "message": "Payment reminder sent successfully",
                        "precheckin_id": str(precheckin.id),
                        "reminder_result": result,            
                        "precheckin_status": precheckin.status,
                        "payment_status": precheckin.payment_status,
                    }
                )
            else:
                return Response(
                    {
                        "error": "Failed to send payment reminder",
                        "details": result.get("error", "Unknown error"),
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except Exception as e:
            logger.error(f"Error sending precheckin payment reminder: {str(e)}")
            return Response(
                {"error": "Failed to send payment reminder"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class CalculateUpfrontAmountView(APIView):
    """
    Calculate upfront amount for precheckin without creating payment.

    This endpoint allows partners to see upfront amount calculations
    before creating the actual payment link.
    """

    permission_classes = [IsAuthenticated, PartnerPermission]

    def post(self, request, precheckin_id):
        """
        Calculate upfront amount for precheckin.

        Args:
            precheckin_id: PreCheckin UUID

        Request body:
            {
                "upfront_percentage": 20.0  # Optional: percentage of total
            }
        """
        try:
            # Get precheckin
            precheckin = get_object_or_404(PreCheckin, id=precheckin_id)

            # Check permissions using centralized helper
            if not check_precheckin_access(request.user, precheckin):
                return Response({"error": "Permission denied"}, status=status.HTTP_403_FORBIDDEN)

            # Calculate upfront amount
            precheckin_service = PrecheckinPaymentService()

            upfront_percentage = request.data.get("upfront_percentage")
            if upfront_percentage is not None:
                if not isinstance(upfront_percentage, (str, int, float, Decimal)):
                    return Response(
                        {
                            "error": "upfront_percentage must be a number or numeric string"
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                try:
                    percentage = Decimal(str(upfront_percentage))
                except (InvalidOperation, TypeError, ValueError) as ex:
                    return Response(
                        {"error": f"Invalid upfront_percentage: {str(ex)}"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            else:
                percentage = None

            upfront_amount = precheckin_service.calculate_upfront_amount(
                precheckin, percentage
            )

            total_amount = Decimal(str(precheckin.total_amount))
            actual_percentage = (
                (upfront_amount / total_amount * 100).quantize(Decimal("0.01"))
                if total_amount > 0
                else Decimal("0")
            )

            return Response(
                {
                    "precheckin_id": str(precheckin.id),
                    "total_amount": float(total_amount),
                    "upfront_amount": float(upfront_amount),
                    "upfront_percentage": float(actual_percentage),
                    "remaining_balance": float(total_amount - upfront_amount),
                    "current_paid": float(precheckin.amount_paid),
                    "current_pending": float(precheckin.pending_balance),
                }
            )

        except Exception as e:
            logger.error(f"Error calculating upfront amount: {str(e)}")
            return Response(
                {"error": "Failed to calculate upfront amount"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PrecheckinPaymentHistoryView(APIView):
    """
    Get payment history for a precheckin.

    This endpoint provides detailed payment history including
    all payment attempts and their outcomes.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, precheckin_id):
        """
        Get payment history for precheckin.

        Args:
            precheckin_id: PreCheckin UUID
        """
        try:
            # Get precheckin
            precheckin = get_object_or_404(PreCheckin, id=precheckin_id)

            # Check permissions
            # Permission check
            if not check_precheckin_access(request.user, precheckin):
                return Response(
                    {"error": "Permission denied"},
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Get payment history

            payment_intents = PaymentIntent.objects.filter(
                precheckin=precheckin, context=PaymentContext.PRECHECKIN
            ).order_by("-created_at")

            payment_history = []

            for payment_intent in payment_intents:
                # Get splits for this payment
                splits = PaymentSplit.objects.filter(payment_intent=payment_intent)

                payment_info = {
                    "payment_id": str(payment_intent.id),
                    "reference_number": payment_intent.reference_number,
                    "amount": float(payment_intent.total_amount),
                    "status": payment_intent.status,
                    "payment_method": payment_intent.payment_method,
                    "created_at": payment_intent.created_at.isoformat() if payment_intent.created_at else None,
                    "paid_at": payment_intent.paid_at.isoformat() if payment_intent.paid_at else None,
                    "expires_at": payment_intent.expires_at.isoformat() if payment_intent.expires_at else None,
                    "razorpay_payment_id": payment_intent.razorpay_payment_id,
                    "splits": [
                        {
                            "recipient_type": split.recipient_type,
                            "recipient_name": split.recipient_name,
                            "amount": float(split.amount),
                            "status": split.status,
                        }
                        for split in splits
                    ],
                }

                payment_history.append(payment_info)

            return Response(
                {
                    "precheckin_id": str(precheckin.id),
                    "payment_history": payment_history,
                    "summary": {
                        "total_payments": len(payment_history),
                        "completed_payments": len(
                            [p for p in payment_history if p["status"] == "completed"]
                        ),
                        "pending_payments": len(
                            [p for p in payment_history if p["status"] == "pending"]
                        ),
                        "failed_payments": len(
                            [p for p in payment_history if p["status"] == "failed"]
                        ),
                    },
                }
            )

        except Exception as e:
            logger.error(f"Error getting precheckin payment history: {str(e)}")
            return Response(
                {"error": "Failed to get payment history"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
