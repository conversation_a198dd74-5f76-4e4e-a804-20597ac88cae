"""
Base Channel Manager interface for handling reservations from OTA sources.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Tuple, Optional
from rest_framework.response import Response
from django.http import HttpRequest
import logging
import json
from rest_framework import status

logger = logging.getLogger(__name__)


class BaseChannelManager(ABC):
    """
    Abstract base class for Channel Manager adapters.

    This class defines the standard interface that all channel manager
    implementations must follow for handling reservations from different
    OTA (Online Travel Agency) sources.
    """

    def __init__(self, channel_name: str):
        """
        Initialize the channel manager.

        Args:
            channel_name: Name of the channel manager (e.g., 'aiosell', 'booking_com')
        """
        self.channel_name = channel_name

    @abstractmethod
    def validate_webhook_data(self, data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Validate incoming webhook data.

        Args:
            data: Raw webhook data dictionary

        Returns:
            Tuple of (is_valid, error_message)
        """
        pass

    @abstractmethod
    def parse_guest_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse guest information from webhook data.

        Args:
            data: Raw webhook data dictionary

        Returns:
            Standardized guest data dictionary with keys:
            - first_name, last_name, email, phone, address
        """
        pass

    @abstractmethod
    def parse_room_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse room details from webhook data.

        Args:
            data: Raw webhook data dictionary

        Returns:
            Standardized room data dictionary with keys:
            - rooms_data, total_guests
        """
        pass

    @abstractmethod
    def create_reservation(self, data: Dict[str, Any]) -> Response:
        """
        Create a new reservation from webhook data.

        Args:
            data: Validated webhook data dictionary

        Returns:
            Django REST framework Response object
        """
        pass

    @abstractmethod
    def modify_reservation(self, data: Dict[str, Any]) -> Response:
        """
        Modify an existing reservation from webhook data.

        Args:
            data: Validated webhook data dictionary

        Returns:
            Django REST framework Response object
        """
        pass

    @abstractmethod
    def cancel_reservation(self, data: Dict[str, Any]) -> Response:
        """
        Cancel an existing reservation from webhook data.

        Args:
            data: Validated webhook data dictionary

        Returns:
            Django REST framework Response object
        """
        pass

    @abstractmethod
    def update_inventory(self, data: Dict[str, Any]) -> Response:
        """
        Update room inventory from webhook data.

        Args:
            data: Validated webhook data dictionary

        Returns:
            Django REST framework Response object
        """
        pass

    def process_webhook(self, request: HttpRequest) -> Response:
        """
        Main entry point for processing webhook requests.

        This method handles the common workflow:
        1. Parse and validate the request
        2. Route to appropriate handler based on action
        3. Return standardized response

        Args:
            request: Django HTTP request object

        Returns:
            Django REST framework Response object
        """
        try:
            # Parse JSON data

            if not request.body:
                return Response(
                    {"success": False, "message": "Empty request body"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                data = json.loads(request.body)
            except json.JSONDecodeError:
                return Response(
                    {"success": False, "message": "Invalid JSON format"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate webhook data
            is_valid, error_message = self.validate_webhook_data(data)
            if not is_valid:
                return Response(
                    {"success": False, "message": error_message},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Route to appropriate handler
            action = data.get("action", "").lower()

            if action == "book":
                return self.create_reservation(data)
            elif action == "modify":
                return self.modify_reservation(data)
            elif action == "cancel":
                return self.cancel_reservation(data)
            elif action == "inventory":
                return self.update_inventory(data)
            else:
                return Response(
                    {
                        "success": False,
                        "message": f"Invalid action: {action}. Valid actions are: book, modify, cancel, inventory",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        except Exception as e:

            logger.error(f"{self.channel_name} webhook error: {str(e)}", exc_info=True)
            return Response(
                {"success": False, "message": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
