import csv
import phonenumbers
from django.core.management import BaseCommand
from geo.models import Location, AdministrationArea, Country, State, City
from service.models import ServicePartner
from service.subapps.laundry.models import LaundryService


class Command(BaseCommand):
    help = "Loads laundry vendor data from a CSV file"

    def add_arguments(self, parser):
        parser.add_argument("filename", type=str, help="Path to the CSV file")

    def handle(self, *args, **options):
        filename = options["filename"]
        missing_fields = []

        with open(filename, "r", encoding="utf-8") as csvfile:
            reader = csv.DictReader(csvfile)

            for row in reader:
                # Check if all required fields are present
                required_fields = [
                    "location_name",
                    "location_address",
                    "country",
                    "state",
                    "city",
                    "name",
                    "phone_number",
                    "service",
                    "service_charges",
                ]
                missing = [field for field in required_fields if field not in row]
                if missing:
                    missing_fields.extend(missing)
                    continue

                # Create Location
                location_data = {
                    "name": row["location_name"],
                    "description": row.get("location_description", ""),
                    "address": row["location_address"],
                }
                country, _ = Country.objects.get_or_create(name=row["country"])
                state, _ = State.objects.get_or_create(
                    name=row["state"], country=country
                )
                city, _ = City.objects.get_or_create(name=row["city"], state=state)
                administrative_area, _ = AdministrationArea.objects.get_or_create(
                    pincode=row.get("postal_code", ""), city=city
                )
                location = Location.objects.create(
                    **location_data, administrative_area=administrative_area
                )

                # Create Service Partner
                partner_data = {
                    "name": row["name"],
                    "location": location,
                    "type_of_service": ServicePartner.PartnerTypes.LAUNDRY,
                    "phone_number": phonenumbers.parse(
                        row.get("phone_number", ""), None
                    ),
                }
                partner = ServicePartner.objects.create(**partner_data)

                # Create Service
                service_data = {
                    "name": row["service"],
                    "partner": partner,
                    "charges": float(row["service_charges"]),
                    "tax_rate": float(row.get("service_tax_rate", 0.0)),
                }
                service = LaundryService.objects.create(**service_data)

                print(f"Successfully created laundry vendor: {row['name']}")

        if missing_fields:
            print("Missing fields in CSV:", ", ".join(missing_fields))
        else:
            self.stdout.write(
                self.style.SUCCESS(f"Laundry vendor data loaded from {filename}")
            )
