from rest_framework import viewsets
from rest_framework.exceptions import ValidationError
from rest_framework.decorators import action
from nestafar.responses import SuccessResponse, BadRequestResponse, NotFoundResponse, CreateResponse
from django.db import transaction, models, IntegrityError
import logging
from datetime import timedelta, datetime, date
from django.utils import timezone
from core.permissions import PropertyPermission
from stay.models import Property, Room
from ..models import RatePlan, RoomType, Calendar, HotelOTAIntegration, AvailabilityLog
from pms.tasks.aiosell_tasks import bulk_sync_calendar_entries_to_aiosell
from ..serializers import RatePlanSerializer, RoomTypeSerializer, RoomTypeCreateSerializer
from django.db.models import Avg, Sum
from ..services.aiosell import get_aiosell_service, AioSellAPIError
from django.core.exceptions import ValidationError as DjangoValidationError

logger = logging.getLogger(__name__)


class RoomTypeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing room types for the authenticated property.

    GET /pms/roomtypes/ -> List all room types
    POST /pms/roomtypes/ -> Create new room type with associated rooms and rate plan
    GET /pms/roomtypes/{id}/ -> Retrieve specific room type
    PUT/PATCH /pms/roomtypes/{id}/ -> Update room type
    DELETE /pms/roomtypes/{id}/ -> Delete room type
    """

    permission_classes = [PropertyPermission]

    def get_queryset(self):
        return RoomType.objects.filter(hotel=self.request.property).order_by(
            "created_at"
        )

    def get_serializer_class(self):
        if self.action == 'create':
            return RoomTypeCreateSerializer
        return RoomTypeSerializer

    def get_serializer_context(self):
        ctx = super().get_serializer_context()
        if getattr(self.request, "property", None) is not None:
            ctx["hotel"] = self.request.property
        return ctx
    
    def create(self, request, *args, **kwargs):
        """Create a new room type with associated rooms and rate plan."""
        logger.info(f"Creating room type for property {getattr(request, 'property', None)}")
        
        try:
            # Validate request data
            serializer = self.get_serializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"Room type creation validation failed: {serializer.errors}")
                return BadRequestResponse(
                    data=serializer.errors,
                    message="Invalid data provided for room type creation."
                )
            
            # Check for existing room type with same name
            existing_room_type = RoomType.objects.filter(
                hotel=request.property,
                name=serializer.validated_data.get('name')
            ).first()
            
            if existing_room_type:
                logger.warning(f"Room type with name '{serializer.validated_data.get('name')}' already exists")
                return BadRequestResponse(
                    message=f"Room type with name '{serializer.validated_data.get('name')}' already exists for this property."
                )
            
            # Create room type with transaction safety
            with transaction.atomic():
                room_type = serializer.save()
                logger.info(f"Successfully created room type '{room_type.name}' with ID {room_type.id}")
                
                # Prepare response data
                response_data = {
                    "room_type": RoomTypeSerializer(room_type, context=self.get_serializer_context()).data,
                    "rooms_created": [],
                    "rate_plan": None
                }
                
                # Format room data for response
                if hasattr(room_type, '_created_rooms'):
                    response_data["rooms_created"] = [
                        {
                            "id": str(room.id),
                            "room_no": room.room_no,
                            "max_guests": room.max_guests,
                            "property": str(room.property.id)
                        } for room in room_type._created_rooms
                    ]
                    logger.info(f"Created {len(room_type._created_rooms)} rooms")
                
                if hasattr(room_type, '_created_rate_plan'):
                    response_data["rate_plan"] = {
                        "id": str(room_type._created_rate_plan.id),
                        "name": room_type._created_rate_plan.name,
                        "base_rate": str(room_type._created_rate_plan.base_rate),
                        "valid_from": room_type._created_rate_plan.valid_from.isoformat(),
                        "valid_to": room_type._created_rate_plan.valid_to.isoformat()
                    }
                    logger.info(f"Created rate plan '{room_type._created_rate_plan.name}'")
                
                # Update property room count if needed
                if hasattr(room_type, '_created_rooms'):
                    property_obj = request.property
                    property_obj.rooms += len(room_type._created_rooms)
                    property_obj.save()
                    logger.info(f"Updated property room count to {property_obj.rooms}")
                
                return CreateResponse(
                    data=response_data,
                    message=f"Room type '{room_type.name}' created successfully with {len(getattr(room_type, '_created_rooms', []))} rooms."
                )
        
        except IntegrityError as e:
            logger.error(f"Database integrity error during room type creation: {str(e)}")
            return BadRequestResponse(
                message="Failed to create room type due to data conflicts. Please check for duplicate room numbers."
            )
        
        except ValidationError as e:
            logger.error(f"Validation error during room type creation: {str(e)}")
            return BadRequestResponse(
                message=str(e)
            )
            
        except Exception as e:
            logger.error(f"Unexpected error creating room type: {str(e)}", exc_info=True)
            return BadRequestResponse(
                message=str(e)
            )


class RatePlanViewSet(viewsets.ModelViewSet):
    queryset = RatePlan.objects.all()
    serializer_class = RatePlanSerializer
    permission_classes = [PropertyPermission]

    MAX_CALENDAR_DAYS = (
        365  # inclusive maximum span that can be auto-generated (365 days = 1 year)
    )

    def get_queryset(self):
        return RatePlan.objects.filter(room_type__hotel=self.request.property)

    def get_serializer_context(
        self,
    ):  # ensure 'hotel' is available for HotelContextMixin
        ctx = super().get_serializer_context()
        if getattr(self.request, "property", None) is not None:
            ctx["hotel"] = self.request.property
        return ctx

    @transaction.atomic
    def perform_create(self, serializer):
        valid_from = serializer.validated_data.get("valid_from")
        valid_to = serializer.validated_data.get("valid_to")
        if valid_from is None or valid_to is None:
            raise ValidationError(
                {"non_field_errors": "valid_from and valid_to are required."}
            )
        # Fields are DateField -> compare directly; if ever DateTimeField, ensure both same awareness
        if isinstance(valid_from, datetime) and isinstance(valid_to, datetime):
            if (valid_from.tzinfo is None) != (valid_to.tzinfo is None):
                raise ValidationError(
                    {
                        "non_field_errors": "Both dates must be either timezone-aware or naive."
                    }
                )
        if valid_to < valid_from:
            raise ValidationError(
                {"valid_to": "valid_to must be on or after valid_from."}
            )
        # Prevent past date creation
        today = timezone.localdate()
        if valid_from < today:
            raise ValidationError({"valid_from": "valid_from cannot be in the past."})
        if valid_to < today:
            raise ValidationError({"valid_to": "valid_to cannot be in the past."})
        day_span = (valid_to - valid_from).days + 1  # inclusive
        if day_span > self.MAX_CALENDAR_DAYS:
            raise ValidationError(
                {
                    "valid_to": f"Validity period spans {day_span} days which exceeds limit of {self.MAX_CALENDAR_DAYS} days."
                }
            )

        # Save the rate plan - the signal will handle AioSell sync using the new date range approach
        rate_plan = serializer.save()

        # Create calendar entries for internal PMS use (availability tracking, reporting, etc.)
        # These are created after rate plan save to ensure referential integrity
        room_type = rate_plan.room_type
        current = rate_plan.valid_from
        while current <= rate_plan.valid_to:
            available_rooms = room_type.rooms.all().count()
            Calendar.objects.get_or_create(
                room_type=room_type,
                rate_plan=rate_plan,
                date=current,
                defaults={
                    "daily_rate": rate_plan.base_rate, 
                    "available_rooms": available_rooms
                },
            )
            current += timedelta(days=1)

        # Note: AioSell sync is handled by the post_save signal using the new date range approach
        # No need for separate bulk sync trigger

    @transaction.atomic
    def perform_update(self, serializer):
        rate_plan = self.get_object()
        old_valid_from = rate_plan.valid_from
        old_valid_to = rate_plan.valid_to

        # Get new values from serializer
        new_valid_from = serializer.validated_data.get("valid_from", old_valid_from)
        new_valid_to = serializer.validated_data.get("valid_to", old_valid_to)

        # Validate new date range
        if new_valid_from is None or new_valid_to is None:
            raise ValidationError(
                {"non_field_errors": "valid_from and valid_to are required."}
            )
        if new_valid_to < new_valid_from:
            raise ValidationError(
                {"valid_to": "valid_to must be on or after valid_from."}
            )
        if isinstance(new_valid_from, datetime) and isinstance(new_valid_to, datetime):
            if (new_valid_from.tzinfo is None) != (new_valid_to.tzinfo is None):
                raise ValidationError(
                    {
                        "non_field_errors": "Both dates must be either timezone-aware or naive."
                    }
                )
        # Prevent past dates on update
        today = timezone.localdate()
        if new_valid_from < today:
            raise ValidationError({"valid_from": "valid_from cannot be in the past."})
        if new_valid_to < today:
            raise ValidationError({"valid_to": "valid_to cannot be in the past."})

        # Check span limit
        day_span = (new_valid_to - new_valid_from).days + 1  # inclusive
        if day_span > self.MAX_CALENDAR_DAYS:
            raise ValidationError(
                {
                    "valid_to": f"Validity period spans {day_span} days which exceeds limit of {self.MAX_CALENDAR_DAYS} days."
                }
            )

        # Save the updated rate plan
        updated_rate_plan = serializer.save()
        room_type = updated_rate_plan.room_type

        # Handle calendar adjustments only if dates changed
        if old_valid_from != new_valid_from or old_valid_to != new_valid_to:
            # Remove calendar entries that are outside the new date range
            Calendar.objects.filter(
                room_type=room_type, rate_plan=rate_plan, date__lt=new_valid_from
            ).delete()

            # Add new calendar entries for extended date range
            current = new_valid_from
            while current <= new_valid_to:
                # Ensure a calendar entry exists for this specific (room_type, rate_plan, date)
                cal = (
                    Calendar.objects.select_for_update()
                    .filter(
                        room_type=room_type, rate_plan=updated_rate_plan, date=current
                    )
                    .first()
                )

                if cal is None:
                    # Create fresh entry for new date for this rate plan (do not overwrite other plans)
                    Calendar.objects.create(
                        room_type=room_type,
                        date=current,
                        rate_plan=updated_rate_plan,
                        daily_rate=updated_rate_plan.base_rate,
                    )
                else:
                    # Update existing entry's daily rate if needed
                    if cal.daily_rate != updated_rate_plan.base_rate:
                        cal.daily_rate = updated_rate_plan.base_rate
                        cal.save(update_fields=["daily_rate"])

                current = current + timedelta(days=1)

        # Update base_rate for existing calendar entries if base_rate changed
        # Update base_rate for existing calendar entries if base_rate changed
        new_base_rate = updated_rate_plan.base_rate
        if new_base_rate != old_base_rate:
            Calendar.objects.filter(
                room_type=room_type, rate_plan=updated_rate_plan
            ).update(daily_rate=updated_rate_plan.base_rate)
        # Trigger async inventory sync (limited date range)
        hotel_id = str(room_type.hotel_id)
        transaction.on_commit(
            lambda: bulk_sync_calendar_entries_to_aiosell.delay(
                hotel_id=hotel_id, date_range_days=30
            )
        )


class AvailabilityViewSet(viewsets.ViewSet):
    permission_classes = [PropertyPermission]

    def list(self, request):
        # Aggregate availability by room type and date with room type details
        room_types = RoomType.objects.filter(hotel=request.property)
        entries = (
            Calendar.objects.filter(room_type__in=room_types)
            .select_related("room_type")
            .values(
                "room_type",
                "room_type__name",
                "room_type__max_occupancy",
                "room_type__description",
                "room_type__amenities",
                "date",
            )
            .annotate(
                total_available_rooms=Sum("available_rooms"),
                daily_rate=Avg("daily_rate"),  # Use average if multiple rates exist
            )
            .order_by("date", "room_type")
        )

        results = [
            {
                "room_type_id": str(entry["room_type"]),
                "room_type_name": entry["room_type__name"],
                "max_occupancy": entry["room_type__max_occupancy"],
                "description": entry["room_type__description"],
                "amenities": entry["room_type__amenities"],
                "date": entry["date"],
                "available_rooms": entry["total_available_rooms"] or 0,
                "daily_rate": entry["daily_rate"],
            }
            for entry in entries
        ]
        return SuccessResponse(data={"results": results})

    @action(methods=["post"], detail=False)
    @transaction.atomic
    def distribute(self, request):
        # Distribute availability evenly across OTAs (placeholder)
        if not hasattr(request, "property") or request.property is None:
            return NotFoundResponse(message="Property not found")
        property_obj: Property = request.property
        # Fetch active integrations once
        integrations = list(
            HotelOTAIntegration.objects.filter(hotel=property_obj, is_active=True)
        )
        if not integrations:
            return SuccessResponse(
                message="No active OTA integrations to distribute availability."
            )

        today = timezone.now().date()
        # Aggregate availability per room_type for today (extend logic as needed for date ranges)
        aggregates = (
            Calendar.objects.filter(room_type__hotel=property_obj, date=today)
            .values("room_type")
            .annotate(total=models.Sum("available_rooms"))
        )

        to_create = []
        for agg in aggregates:
            total_available = agg["total"] or 0
            room_type_id = agg["room_type"]

            # Distribute rooms proportionally (or based on OTA-specific allocation strategy)
            rooms_per_ota = total_available // len(integrations) if integrations else 0
            remainder = total_available % len(integrations) if integrations else 0

            for integ in integrations:
                # Give one extra room to the first OTAs if there's a remainder
                allocated_rooms = rooms_per_ota + (1 if remainder > 0 else 0)
                remainder = max(0, remainder - 1)

                to_create.append(
                    AvailabilityLog(
                        hotel=property_obj,
                        room_type_id=room_type_id,
                        ota_platform=integ.ota_platform,
                        date=today,
                        available_rooms=allocated_rooms,
                        sync_status="pending",
                    )
                )

        if to_create:
            AvailabilityLog.objects.bulk_create(to_create)
        else:
            return SuccessResponse(
                message="No availability entries to distribute today."
            )
        return SuccessResponse(
            message=f"Availability distribution initiated for {property_obj.name}."
        )


class PolicyViewSet(viewsets.ViewSet):
    permission_classes = [PropertyPermission]

    def retrieve(self, request, pk=None):
        if not hasattr(request, "property") or request.property is None:
            return NotFoundResponse(message="Property not found")
        # Source: Property.policies (default: empty dict)
        policies = getattr(request.property, "policies", {}) or {}
        return SuccessResponse(data={"policies": policies})

    def update(self, request, pk=None):
        if not hasattr(request, "property") or request.property is None:
            return NotFoundResponse(message="Property not found")

        prop: Property = request.property
        policies = request.data.get("policies", {})

        # Validate policies is a dictionary
        if not isinstance(policies, dict):
            return BadRequestResponse(message="Policies must be a dictionary")

        prop.policies = policies
        prop.save(
            update_fields=["policies"]
        )  # Save the updated policies to the database
        return SuccessResponse(message="Policies updated successfully.")


class InventoryViewSet(viewsets.ViewSet):
    """
    ViewSet for managing inventory operations with AioSell integration.
    
    Provides endpoints for:
    - Direct inventory updates to AioSell
    - Bulk inventory synchronization
    - Validation of hotel configuration
    """
    permission_classes = [PropertyPermission]

    @action(methods=["post"], detail=False, url_path="push-to-aiosell")
    def push_to_aiosell(self, request):
        """
        Push inventory updates directly to AioSell.
        
        Expected payload format:
        {
            "room_inventories": [
                {"room_code": "SUITE", "available": 3},
                {"room_code": "STANDARD", "available": 5}
            ],
            "start_date": "2023-12-24",
            "end_date": "2023-12-30"
        }
        """
        if not hasattr(request, "property") or request.property is None:
            return NotFoundResponse(message="Property not found")

        try:
            # Validate request data
            room_inventories = request.data.get("room_inventories", [])
            start_date_str = request.data.get("start_date")
            end_date_str = request.data.get("end_date")

            if not room_inventories:
                return BadRequestResponse(message="room_inventories is required and cannot be empty")

            if not start_date_str or not end_date_str:
                return BadRequestResponse(message="start_date and end_date are required")

            # Parse dates
            try:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                return BadRequestResponse(message="Dates must be in YYYY-MM-DD format")

            # Validate date range - allow today or future dates
            today = timezone.localdate()
            if start_date < today:
                return BadRequestResponse(message="start_date cannot be in the past")
            if end_date < today:
                return BadRequestResponse(message="end_date cannot be in the past")
            if end_date < start_date:
                return BadRequestResponse(message="end_date must be on or after start_date")

            # Check if property has AioSell integration
            from ..services.aiosell import get_aiosell_service
            from ..tasks.aiosell_tasks import async_push_inventory_to_aiosell
            
            aiosell_service = get_aiosell_service(hotel=request.property)
            if not aiosell_service:
                return BadRequestResponse(message="AioSell integration not configured for this property")

            # Validate room inventory data format
            for room_inv in room_inventories:
                if not isinstance(room_inv.get("room_code"), str) or not room_inv.get("room_code").strip():
                    return BadRequestResponse(message="Each room inventory must have a valid room_code")
                
                if not isinstance(room_inv.get("available"), int) or room_inv.get("available") < 0:
                    return BadRequestResponse(message=f"Invalid available count for room {room_inv.get('room_code')}")

            # Check if async processing is requested
            use_async = request.query_params.get("async", "false").lower() == "true"
            
            if use_async:
                # Queue task for async processing
                task = async_push_inventory_to_aiosell.delay(
                    hotel_id=str(request.property.id),
                    room_inventories=room_inventories,
                    start_date=start_date_str,
                    end_date=end_date_str,
                    user_id=str(request.user.id) if hasattr(request, 'user') and request.user.is_authenticated else None
                )
                
                logger.info(f"Queued async inventory push task {task.id} for property {request.property.name}")
                
                return SuccessResponse(
                    data={
                        "task_id": task.id,
                        "status": "queued",
                        "updated_rooms": len(room_inventories),
                        "date_range": {
                            "start": start_date_str,
                            "end": end_date_str
                        }
                    },
                    message=f"Inventory push queued for async processing. Task ID: {task.id}"
                )
            else:
                # Process synchronously
                result = aiosell_service.push_inventory_update(
                    room_inventories=room_inventories,
                    start_date=start_date,
                    end_date=end_date
                )

                logger.info(f"Successfully pushed inventory to AioSell for property {request.property.name}")

                return SuccessResponse(
                    data={
                        "aiosell_response": result,
                        "updated_rooms": len(room_inventories),
                        "date_range": {
                            "start": start_date_str,
                            "end": end_date_str
                        }
                    },
                    message=f"Inventory successfully pushed to AioSell for {len(room_inventories)} room types"
                )

        except ValidationError as e:
            logger.error(f"Validation error during inventory push: {str(e)}")
            return BadRequestResponse(message=str(e))
            
        except Exception as e:
            logger.error(f"Unexpected error during inventory push: {str(e)}", exc_info=True)
            return BadRequestResponse(message=f"Failed to push inventory to AioSell: {str(e)}")

    @action(methods=["post"], detail=False, url_path="bulk-sync")
    def bulk_sync(self, request):
        """
        Bulk synchronize inventory for multiple room types.
        
        Expected payload format:
        {
            "room_type_ids": ["uuid1", "uuid2", "uuid3"],
            "start_date": "2023-12-24",
            "end_date": "2023-12-30",
            "date_specific_inventory": {
                "2023-12-25": {"uuid1": 2, "uuid2": 4}
            }
        }
        """
        if not hasattr(request, "property") or request.property is None:
            return NotFoundResponse(message="Property not found")

        try:
            # Validate request data
            room_type_ids = request.data.get("room_type_ids", [])
            start_date_str = request.data.get("start_date")
            end_date_str = request.data.get("end_date")
            date_specific_inventory = request.data.get("date_specific_inventory", {})

            if not room_type_ids:
                return BadRequestResponse(message="room_type_ids is required and cannot be empty")

            if not start_date_str or not end_date_str:
                return BadRequestResponse(message="start_date and end_date are required")

            # Parse dates
            try:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                return BadRequestResponse(message="Dates must be in YYYY-MM-DD format")

            # Validate date range - allow today or future dates
            today = timezone.localdate()
            if start_date < today:
                return BadRequestResponse(message="start_date cannot be in the past")
            if end_date < today:
                return BadRequestResponse(message="end_date cannot be in the past")
            if end_date < start_date:
                return BadRequestResponse(message="end_date must be on or after start_date")

            # Check if property has AioSell integration
            from ..services.aiosell import get_aiosell_service
            from ..tasks.aiosell_tasks import async_bulk_inventory_sync
            
            aiosell_service = get_aiosell_service(hotel=request.property)
            if not aiosell_service:
                return BadRequestResponse(message="AioSell integration not configured for this property")

            # Check if async processing is requested
            use_async = request.query_params.get("async", "false").lower() == "true"
            
            if use_async:
                # Queue task for async processing
                task = async_bulk_inventory_sync.delay(
                    hotel_id=str(request.property.id),
                    room_type_ids=room_type_ids,
                    start_date=start_date_str,
                    end_date=end_date_str,
                    date_specific_inventory=date_specific_inventory,
                    user_id=str(request.user.id) if hasattr(request, 'user') and request.user.is_authenticated else None
                )
                
                logger.info(f"Queued async bulk inventory sync task {task.id} for property {request.property.name}")
                
                return SuccessResponse(
                    data={
                        "task_id": task.id,
                        "status": "queued",
                        "updated_room_types": len(room_type_ids),
                        "date_range": {
                            "start": start_date_str,
                            "end": end_date_str
                        }
                    },
                    message=f"Bulk inventory sync queued for async processing. Task ID: {task.id}"
                )
            else:
                # Process synchronously
                result = aiosell_service.bulk_inventory_update(
                    room_type_ids=room_type_ids,
                    start_date=start_date,
                    end_date=end_date,
                    date_specific_inventory=date_specific_inventory
                )

                logger.info(f"Successfully bulk synced inventory to AioSell for property {request.property.name}")

                return SuccessResponse(
                    data={
                        "aiosell_response": result,
                        "updated_room_types": len(room_type_ids),
                        "date_range": {
                            "start": start_date_str,
                            "end": end_date_str
                        }
                    },
                    message=f"Bulk inventory sync completed for {len(room_type_ids)} room types"
                )

        except ValidationError as e:
            logger.error(f"Validation error during bulk sync: {str(e)}")
            return BadRequestResponse(message=str(e))
            
        except Exception as e:
            logger.error(f"Unexpected error during bulk sync: {str(e)}", exc_info=True)
            return BadRequestResponse(message=f"Failed to sync inventory to AioSell: {str(e)}")

    @action(methods=["get"], detail=False, url_path="aiosell-status")
    def aiosell_status(self, request):
        """
        Check AioSell integration status and configuration for the property.
        """
        if not hasattr(request, "property") or request.property is None:
            return NotFoundResponse(message="Property not found")

        try:
            from ..services.aiosell import get_aiosell_service
            
            # Try to initialize AioSell service
            aiosell_service = get_aiosell_service(hotel=request.property)
            if not aiosell_service:
                return BadRequestResponse(
                    data={
                        "configured": False,
                        "error": "AioSell integration not found or not active"
                    },
                    message="AioSell integration not properly configured"
                )
                
            # Get integration details
            integration_details = {
                "configured": True,
                "hotel_code": aiosell_service._get_hotel_code(),
                "identifier": aiosell_service._get_identifier(),
                "base_url": aiosell_service.base_url,
                "room_types_count": RoomType.objects.filter(hotel=request.property).count(),
                "total_rooms": Room.objects.filter(property=request.property).count()
            }
            
            # Check room mappings
            room_types = RoomType.objects.filter(hotel=request.property)
            room_mappings = []
            for room_type in room_types:
                try:
                    room_code = aiosell_service._get_room_code_mapping(room_type=room_type)
                    room_mappings.append({
                        "room_type_id": str(room_type.id),
                        "room_type_name": room_type.name,
                        "room_code": room_code,
                        "rooms_count": room_type.rooms.count()
                    })
                except Exception as e:
                    room_mappings.append({
                        "room_type_id": str(room_type.id),
                        "room_type_name": room_type.name,
                        "room_code": None,
                        "error": str(e)
                    })
            
            integration_details["room_mappings"] = room_mappings
            
            return SuccessResponse(
                data=integration_details,
                message="AioSell integration status retrieved successfully"
            )
                
        except Exception as e:
            logger.error(f"Error checking AioSell status: {str(e)}", exc_info=True)
            return BadRequestResponse(message=f"Failed to check AioSell status: {str(e)}")


class AioSellViewSet(viewsets.ViewSet):
    """
    ViewSet for AioSell integration API endpoints.

    Provides the three main AioSell integration endpoints:
    - Rates Update: Push room rates and rate plans to AioSell
    - Inventory Restrictions: Push room-specific booking restrictions to AioSell
    - Rate Restrictions: Push rate-specific booking restrictions to AioSell
    """
    permission_classes = [PropertyPermission]

    def _get_aiosell_service(self, request):
        """Helper method to get AioSell service with error handling."""
        if not hasattr(request, "property") or request.property is None:
            raise ValidationError("Property not found")

        aiosell_service = get_aiosell_service(hotel=request.property)
        if not aiosell_service:
            raise ValidationError("AioSell integration not configured for this property")

        return aiosell_service

    def _parse_date(self, date_str: str, field_name: str) -> date:
        """Helper method to parse date strings with validation."""
        try:
            return datetime.strptime(date_str, "%Y-%m-%d").date()
        except ValueError:
            raise ValidationError(f"Invalid date format for {field_name}. Expected YYYY-MM-DD")

    @action(methods=["post"], detail=False, url_path="rates-update")
    def rates_update(self, request):
        """
        Push room rates and rate plans to AioSell.

        Expected payload format:
        {
            "start_date": "2023-02-22",
            "end_date": "2023-02-24",
            "rate_updates": [
                {
                    "room_code": "EXECUTIVE",
                    "rate": 1749.0,
                    "rateplan_code": "EXECUTIVE-S-101"
                },
                {
                    "room_code": "EXECUTIVE",
                    "rate": 1849.0,
                    "rateplan_code": "EXECUTIVE-D-101"
                }
            ],
            "to_channels": ["agoda", "booking.com"]  // Optional
        }
        """
        try:
            # Get AioSell service
            aiosell_service = self._get_aiosell_service(request)

            # Validate required fields
            rate_updates = request.data.get("rate_updates", [])
            if not rate_updates:
                return BadRequestResponse(message="rate_updates is required and cannot be empty")

            start_date_str = request.data.get("start_date")
            end_date_str = request.data.get("end_date")

            if not start_date_str or not end_date_str:
                return BadRequestResponse(message="start_date and end_date are required")

            # Parse dates
            start_date = self._parse_date(start_date_str, "start_date")
            end_date = self._parse_date(end_date_str, "end_date")

            # Validate date range
            if end_date < start_date:
                return BadRequestResponse(message="end_date must be on or after start_date")

            # Optional channels
            to_channels = request.data.get("to_channels")

            # Push rates update to AioSell
            result = aiosell_service.push_rates_update(
                rate_updates=rate_updates,
                start_date=start_date,
                end_date=end_date,
                to_channels=to_channels
            )

            logger.info(f"Successfully pushed rates update to AioSell for property {request.property.name}")

            return SuccessResponse(
                data={
                    "aiosell_response": result,
                    "updated_rates": len(rate_updates),
                    "date_range": {
                        "start": start_date_str,
                        "end": end_date_str
                    },
                    "channels": to_channels or aiosell_service._get_target_channels()
                },
                message=f"Rates successfully pushed to AioSell for {len(rate_updates)} rate entries"
            )

        except ValidationError as e:
            logger.error(f"Validation error during rates update: {str(e)}")
            return BadRequestResponse(message=str(e))

        except AioSellAPIError as e:
            logger.error(f"AioSell API error during rates update: {str(e)}")
            return BadRequestResponse(message=f"AioSell API error: {str(e)}")

        except Exception as e:
            logger.error(f"Unexpected error during rates update: {str(e)}", exc_info=True)
            return BadRequestResponse(message=f"Failed to push rates to AioSell: {str(e)}")

    @action(methods=["post"], detail=False, url_path="inventory-restrictions")
    def inventory_restrictions(self, request):
        """
        Push room-specific booking restrictions to AioSell.

        Expected payload format:
        {
            "start_date": "2023-01-24",
            "end_date": "2023-01-26",
            "room_restrictions": [
                {
                    "room_code": "SUITE",
                    "restrictions": {
                        "stop_sell": false,
                        "exact_stay_arrival": null,
                        "maximum_stay_arrival": null,
                        "minimum_advance_reservation": null,
                        "minimum_stay": 1,
                        "close_on_arrival": false,
                        "minimum_stay_arrival": null,
                        "maximum_stay": null,
                        "maximum_advance_reservation": null,
                        "close_on_departure": false
                    }
                }
            ],
            "to_channels": ["agoda", "booking.com"]  // Optional
        }
        """
        try:
            # Get AioSell service
            aiosell_service = self._get_aiosell_service(request)

            # Validate required fields
            room_restrictions = request.data.get("room_restrictions", [])
            if not room_restrictions:
                return BadRequestResponse(message="room_restrictions is required and cannot be empty")

            start_date_str = request.data.get("start_date")
            end_date_str = request.data.get("end_date")

            if not start_date_str or not end_date_str:
                return BadRequestResponse(message="start_date and end_date are required")

            # Parse dates
            start_date = self._parse_date(start_date_str, "start_date")
            end_date = self._parse_date(end_date_str, "end_date")

            # Validate date range
            if end_date < start_date:
                return BadRequestResponse(message="end_date must be on or after start_date")

            # Optional channels
            to_channels = request.data.get("to_channels")

            # Push inventory restrictions to AioSell
            result = aiosell_service.push_inventory_restrictions_update(
                room_restrictions=room_restrictions,
                start_date=start_date,
                end_date=end_date,
                to_channels=to_channels
            )

            logger.info(f"Successfully pushed inventory restrictions to AioSell for property {request.property.name}")

            return SuccessResponse(
                data={
                    "aiosell_response": result,
                    "updated_rooms": len(room_restrictions),
                    "date_range": {
                        "start": start_date_str,
                        "end": end_date_str
                    },
                    "channels": to_channels or aiosell_service._get_target_channels()
                },
                message=f"Inventory restrictions successfully pushed to AioSell for {len(room_restrictions)} rooms"
            )

        except ValidationError as e:
            logger.error(f"Validation error during inventory restrictions update: {str(e)}")
            return BadRequestResponse(message=str(e))

        except AioSellAPIError as e:
            logger.error(f"AioSell API error during inventory restrictions update: {str(e)}")
            return BadRequestResponse(message=f"AioSell API error: {str(e)}")

        except Exception as e:
            logger.error(f"Unexpected error during inventory restrictions update: {str(e)}", exc_info=True)
            return BadRequestResponse(message=f"Failed to push inventory restrictions to AioSell: {str(e)}")

    @action(methods=["post"], detail=False, url_path="rate-restrictions")
    def rate_restrictions(self, request):
        """
        Push rate-specific booking restrictions to AioSell.

        Expected payload format:
        {
            "start_date": "2023-02-22",
            "end_date": "2023-02-24",
            "rate_restrictions": [
                {
                    "room_code": "EXECUTIVE",
                    "rateplan_code": "EXECUTIVE-S-101",
                    "restrictions": {
                        "stop_sell": false,
                        "exact_stay_arrival": null,
                        "maximum_stay_arrival": null,
                        "minimum_advance_reservation": null,
                        "minimum_stay": 1,
                        "close_on_arrival": false,
                        "maximum_stay": null,
                        "maximum_advance_reservation": null,
                        "close_on_departure": false
                    }
                }
            ],
            "to_channels": ["agoda", "booking.com"]  // Optional
        }
        """
        try:
            # Get AioSell service
            aiosell_service = self._get_aiosell_service(request)

            # Validate required fields
            rate_restrictions = request.data.get("rate_restrictions", [])
            if not rate_restrictions:
                return BadRequestResponse(message="rate_restrictions is required and cannot be empty")

            start_date_str = request.data.get("start_date")
            end_date_str = request.data.get("end_date")

            if not start_date_str or not end_date_str:
                return BadRequestResponse(message="start_date and end_date are required")

            # Parse dates
            start_date = self._parse_date(start_date_str, "start_date")
            end_date = self._parse_date(end_date_str, "end_date")

            # Validate date range
            if end_date < start_date:
                return BadRequestResponse(message="end_date must be on or after start_date")

            # Optional channels
            to_channels = request.data.get("to_channels")

            # Push rate restrictions to AioSell
            result = aiosell_service.push_rate_restrictions_update(
                rate_restrictions=rate_restrictions,
                start_date=start_date,
                end_date=end_date,
                to_channels=to_channels
            )

            logger.info(f"Successfully pushed rate restrictions to AioSell for property {request.property.name}")

            return SuccessResponse(
                data={
                    "aiosell_response": result,
                    "updated_rate_plans": len(rate_restrictions),
                    "date_range": {
                        "start": start_date_str,
                        "end": end_date_str
                    },
                    "channels": to_channels or aiosell_service._get_target_channels()
                },
                message=f"Rate restrictions successfully pushed to AioSell for {len(rate_restrictions)} rate plans"
            )

        except ValidationError as e:
            logger.error(f"Validation error during rate restrictions update: {str(e)}")
            return BadRequestResponse(message=str(e))

        except AioSellAPIError as e:
            logger.error(f"AioSell API error during rate restrictions update: {str(e)}")
            return BadRequestResponse(message=f"AioSell API error: {str(e)}")

        except Exception as e:
            logger.error(f"Unexpected error during rate restrictions update: {str(e)}", exc_info=True)
            return BadRequestResponse(message=f"Failed to push rate restrictions to AioSell: {str(e)}")
