from .models import *
from django_filters import rest_framework as django_filters
from service.filters import BaseCartFilter


class LaundryItemFilterSet(django_filters.FilterSet):
    class Meta:
        model = LaundryServiceItem
        fields = ["service", "price", "is_active", "rating"]


class LaundryOrderFilter(django_filters.FilterSet):
    class Meta:
        model = LaundryOrder
        fields = ["status", "guest", "cart"]


class LaundryCartFilter(BaseCartFilter):
    class Meta:
        model = LaundryCart
        fields = ["status", "guest"]
