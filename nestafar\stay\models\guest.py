import uuid
from django.db import models
from .room import Room
from core.models import User
from .property import Property


class Guest(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="guest")
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name="guest")
    group_id = models.CharField(max_length=100, blank=True, null=True)
    checkin_key = models.CharField(max_length=100)
    checked_in = models.BooleanField(default=False)
    check_in_date = models.DateTimeField(blank=True, null=True)
    checked_out = models.BooleanField(default=False)
    payment_completed = models.BooleanField(default=False)
    check_out_date = models.DateTimeField(blank=True, null=True)
    total_orders = models.IntegerField(default=0, blank=True, null=True)
    total_spends = models.FloatField(default=0, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        # Handle cases where room filed is not available
        prop = getattr(self.room, "property", None) if getattr(self, 'room', None) else None
        property_name = getattr(prop, "name", "") if prop else ""

        user_name = getattr(self.user, "name", None) if getattr(self, 'user', None) else None
        if not user_name and getattr(self, 'user', None) is not None:
            # Fallback to stringifying the user object (id or username)
            try:
                user_name = str(self.user)
            except Exception:
                user_name = ""

        user_name = user_name or ""

        return f"{user_name}{('-' + property_name) if property_name else ''}"


class GuestReview(models.Model):
    """Guest reviews for properties"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    guest = models.ForeignKey(
        "stay.Guest", on_delete=models.CASCADE, related_name="reviews"
    )
    property = models.ForeignKey(
        Property, on_delete=models.CASCADE, related_name="guest_reviews"
    )

    # Review data
    rating = models.IntegerField(choices=[(i, i) for i in range(1, 6)])  # 1-5 stars
    review_text = models.TextField(blank=True)

    # Service ratings
    service_rating = models.IntegerField(
        choices=[(i, i) for i in range(1, 6)], null=True, blank=True
    )
    cleanliness_rating = models.IntegerField(
        choices=[(i, i) for i in range(1, 6)], null=True, blank=True
    )
    location_rating = models.IntegerField(
        choices=[(i, i) for i in range(1, 6)], null=True, blank=True
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        guest_name = (
            getattr(self.guest.user, "name", "Unknown")
            if self.guest.user
            else "Unknown"
        )
        return f"Review by {guest_name} - {self.rating} stars"
