"""
Unit tests for AioSell integration services, tasks, and utilities.
"""

import json
import unittest
import unittest.mock as mock
from datetime import date, datetime, timedelta
from decimal import Decimal

from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError

from pms.models import (
    OTAPlatform,
    HotelOTAIntegration,
    RoomType,
    RatePlan,
    Calendar,
    RoomBlock,
    RoomBlockSyncLog,
)
from stay.models import Property, Room
from core.models import User
from booking.models import Reservation

from pms.services.aiosell import AioSellService, AioSellAPIError, get_aiosell_service
from pms.utils.aiosell_mapper import AioSellDataMapper, get_data_mapper


class AioSellServiceTestCase(TestCase):
    """Test cases for AioSell service class."""

    def setUp(self):
        """Set up test data."""
        # Create test hotel
        self.hotel = Property.objects.create(
            name="Test Hotel",
            rooms=10,
            channel_managers={
                "aiosell": {
                    "enabled": True,
                    "config": {
                        "sync_enabled": True,
                        "target_channels": ["agoda", "booking.com"],
                        "room_mapping": {"101": "DELUXE_101", "Deluxe Room": "DELUXE"},
                        "rate_plans": {"DELUXE": ["DELUXE-S-101", "DELUXE-D-101"]},
                    },
                }
            },
        )

        # Create OTA platform
        self.ota_platform = OTAPlatform.objects.create(
            name="aiosell",
            api_endpoint="https://live.aiosell.com/api/v2/cm",
            is_active=True,
        )

        # Create hotel integration
        self.integration = HotelOTAIntegration.objects.create(
            hotel=self.hotel,
            ota_platform=self.ota_platform,
            external_hotel_id="TEST-HOTEL-001",
            credentials={"identifier": "test_token_123", "api_key": "test_key_456"},
            is_active=True,
        )

        # Create room type and rate plan
        self.room_type = RoomType.objects.create(
            hotel=self.hotel, name="Deluxe Room", max_occupancy=2
        )

        self.rate_plan = RatePlan.objects.create(
            room_type=self.room_type,
            name="Standard Rate",
            base_rate=Decimal("100.00"),
            valid_from=date.today(),
            valid_to=date.today() + timedelta(days=365),
        )

        # Create test room
        self.room = Room.objects.create(
            property=self.hotel, room_no="101", max_guests=2
        )

        # Create test user for reservations
        self.user = User.objects.create(username="testuser", email="<EMAIL>")

    def test_aiosell_service_initialization(self):
        """Test AioSell service initialization."""
        service = AioSellService(self.integration)

        self.assertEqual(service.hotel, self.hotel)
        self.assertEqual(service.integration, self.integration)
        self.assertEqual(service.base_url, "https://live.aiosell.com/api/v2/cm")
        self.assertTrue("identifier" in service.credentials)

    def test_aiosell_service_invalid_credentials(self):
        """Test AioSell service with invalid credentials."""
        invalid_integration = HotelOTAIntegration.objects.create(
            hotel=self.hotel,
            ota_platform=self.ota_platform,
            external_hotel_id="INVALID-HOTEL",
            credentials={},  # No API token
            is_active=True,
        )

        with self.assertRaises(ValidationError):
            AioSellService(invalid_integration)

    def test_get_hotel_code(self):
        """Test hotel code retrieval."""
        service = AioSellService(self.integration)
        hotel_code = service._get_hotel_code()
        self.assertEqual(hotel_code, "TEST-HOTEL-001")

    def test_get_target_channels(self):
        """Test target channels retrieval."""
        service = AioSellService(self.integration)
        channels = service._get_target_channels()
        self.assertEqual(channels, ["agoda", "booking.com"])

    def test_room_code_mapping(self):
        """Test room code mapping."""
        service = AioSellService(self.integration)

        # Test room-specific mapping
        room_code = service._get_room_code_mapping(room=self.room)
        self.assertEqual(room_code, "DELUXE_101")

        # Test room type mapping
        room_code = service._get_room_code_mapping(room_type=self.room_type)
        self.assertEqual(room_code, "DELUXE")

    def test_rate_plan_codes(self):
        """Test rate plan code generation."""
        service = AioSellService(self.integration)

        # Test configured rate plans
        rate_codes = service._get_rate_plan_codes("DELUXE", self.rate_plan)
        self.assertEqual(rate_codes, ["DELUXE-S-101", "DELUXE-D-101"])

        # Test default rate plans
        rate_codes = service._get_rate_plan_codes("UNKNOWN", self.rate_plan)
        self.assertIn("UNKNOWN-STANDARD_RATE", rate_codes[0])

    @mock.patch("pms.services.aiosell.requests.post")
    def test_api_request_success(self, mock_post):
        """Test successful API request."""
        # Mock successful response
        mock_response = mock.Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "message": "Rates Updated Successfully",
        }
        mock_response.elapsed.total_seconds.return_value = 0.5
        mock_post.return_value = mock_response

        service = AioSellService(self.integration)

        test_data = {
            "hotelCode": "TEST-HOTEL-001",
            "toChannels": ["agoda"],
            "updates": [],
        }

        result = service._make_api_request("update-rates/TEST-HOTEL-001", test_data)

        self.assertTrue(result["success"])
        self.assertEqual(result["message"], "Rates Updated Successfully")

        # Verify request was made with correct parameters
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        self.assertIn("json", kwargs)
        self.assertEqual(kwargs["json"], test_data)

    @mock.patch("pms.services.aiosell.requests.post")
    def test_api_request_failure(self, mock_post):
        """Test API request failure."""
        # Mock failed response
        mock_response = mock.Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "success": False,
            "message": "Invalid hotel code",
        }
        mock_post.return_value = mock_response

        service = AioSellService(self.integration)

        test_data = {
            "hotelCode": "INVALID-HOTEL",
            "toChannels": ["agoda"],
            "updates": [],
        }

        with self.assertRaises(AioSellAPIError):
            service._make_api_request("update-rates/INVALID-HOTEL", test_data)

    @mock.patch("pms.services.aiosell.requests.post")
    def test_push_rate_restrictions(self, mock_post):
        """Test pushing rate restrictions."""
        # Mock successful response
        mock_response = mock.Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "message": "Rates Updated Successfully",
        }
        mock_response.elapsed.total_seconds.return_value = 0.5
        mock_post.return_value = mock_response

        service = AioSellService(self.integration)

        restrictions = {
            "stop_sell": False,
            "minimum_stay": 2,
            "close_on_arrival": False,
        }

        result = service.push_rate_restrictions(
            room_type=self.room_type,
            rate_plan=self.rate_plan,
            start_date=date.today(),
            end_date=date.today() + timedelta(days=1),
            restrictions=restrictions,
        )

        self.assertTrue(result["success"])

        # Verify API call structure
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        payload = kwargs["json"]

        self.assertEqual(payload["hotelCode"], "TEST-HOTEL-001")
        self.assertEqual(payload["toChannels"], ["agoda", "booking.com"])
        self.assertEqual(len(payload["updates"]), 1)

        update = payload["updates"][0]
        self.assertEqual(update["startDate"], date.today().isoformat())
        self.assertTrue(len(update["rates"]) > 0)

        rate = update["rates"][0]
        self.assertEqual(rate["roomCode"], "DELUXE")
        self.assertFalse(rate["restrictions"]["stopSell"])
        self.assertEqual(rate["restrictions"]["minimumStay"], 2)

    @mock.patch("pms.services.aiosell.requests.post")
    def test_push_inventory_restrictions(self, mock_post):
        """Test pushing inventory restrictions."""
        # Mock successful response
        mock_response = mock.Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "message": "Inventory Updated Successfully",
        }
        mock_response.elapsed.total_seconds.return_value = 0.5
        mock_post.return_value = mock_response

        service = AioSellService(self.integration)

        restrictions = {"stop_sell": True, "minimum_stay": 1}

        result = service.push_inventory_restrictions(
            room_type=self.room_type,
            start_date=date.today(),
            end_date=date.today(),
            restrictions=restrictions,
        )

        self.assertTrue(result["success"])

        # Verify API call structure
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        payload = kwargs["json"]

        self.assertEqual(payload["hotelCode"], "TEST-HOTEL-001")
        self.assertEqual(len(payload["updates"]), 1)

        update = payload["updates"][0]
        self.assertEqual(len(update["rooms"]), 1)

        room = update["rooms"][0]
        self.assertEqual(room["roomCode"], "DELUXE")
        self.assertTrue(room["restrictions"]["stopSell"])

    def test_sync_room_block(self):
        """Test room block synchronization."""
        # Create a reservation and room block
        reservation = Reservation.objects.create(
            user=self.user,
            property=self.hotel,
            check_in=timezone.now(),
            check_out=timezone.now() + timedelta(days=2),
            guests=2,
            total=200.0,
            status="confirmed",
        )

        room_block = RoomBlock.objects.create(
            hotel=self.hotel,
            room=self.room,
            reservation=reservation,
            blocked_from=timezone.now(),
            blocked_until=timezone.now() + timedelta(days=2),
            is_active=True,
            reason="reservation",
        )

        with mock.patch.object(
            AioSellService, "push_inventory_restrictions"
        ) as mock_push:
            mock_push.return_value = {"success": True}

            service = AioSellService(self.integration)
            result = service.sync_room_block(room_block, "create")

            self.assertTrue(result["success"])
            mock_push.assert_called_once()

            # Verify restrictions were set correctly
            args, kwargs = mock_push.call_args
            restrictions = kwargs["restrictions"]
            self.assertTrue(restrictions["stop_sell"])

    def test_get_aiosell_service_function(self):
        """Test get_aiosell_service utility function."""
        service = get_aiosell_service(self.hotel)
        self.assertIsInstance(service, AioSellService)
        self.assertEqual(service.hotel, self.hotel)

        # Test with hotel without integration
        hotel_without_integration = Property.objects.create(
            name="Hotel Without Integration", rooms=5
        )

        service = get_aiosell_service(hotel_without_integration)
        self.assertIsNone(service)


class AioSellDataMapperTestCase(TestCase):
    """Test cases for AioSell data mapper."""

    def setUp(self):
        """Set up test data."""
        # Create test hotel with configuration
        self.hotel = Property.objects.create(
            name="Test Hotel",
            rooms=10,
            channel_managers={
                "aiosell": {
                    "enabled": True,
                    "config": {
                        "target_channels": ["agoda", "booking.com"],
                        "room_mapping": {
                            "101": "DELUXE_101",
                            "Executive Room": "EXECUTIVE",
                        },
                        "rate_plans": {
                            "EXECUTIVE": ["EXECUTIVE-S-101", "EXECUTIVE-D-101"]
                        },
                    },
                }
            },
        )

        # Create room type and rate plan
        self.room_type = RoomType.objects.create(
            hotel=self.hotel, name="Executive Room", max_occupancy=2
        )

        self.rate_plan = RatePlan.objects.create(
            room_type=self.room_type,
            name="Best Available Rate",
            base_rate=Decimal("150.00"),
            valid_from=date.today(),
            valid_to=date.today() + timedelta(days=365),
        )

        # Create calendar entry
        self.calendar_entry = Calendar.objects.create(
            room_type=self.room_type,
            rate_plan=self.rate_plan,
            date=date.today(),
            available_rooms=5,
            daily_rate=Decimal("150.00"),
            restrictions={
                "stopSell": False,
                "minimumStay": 2,
                "closeOnArrival": False,
                "closeOnDeparture": False,
            },
        )

        self.mapper = AioSellDataMapper(self.hotel)

    def test_mapper_initialization(self):
        """Test data mapper initialization."""
        self.assertEqual(self.mapper.hotel, self.hotel)
        self.assertIsInstance(self.mapper.config, dict)

    def test_room_code_mapping(self):
        """Test room code mapping."""
        # Test room type mapping
        room_code = self.mapper.map_room_to_code(room_type=self.room_type)
        self.assertEqual(room_code, "EXECUTIVE")

        # Test default mapping
        other_room_type = RoomType.objects.create(
            hotel=self.hotel, name="Standard Room"
        )
        room_code = self.mapper.map_room_to_code(room_type=other_room_type)
        self.assertEqual(room_code, "STANDARD_ROOM")

    def test_rate_plan_code_mapping(self):
        """Test rate plan code mapping."""
        # Test configured mapping
        rate_codes = self.mapper.map_rate_plan_to_codes("EXECUTIVE", self.rate_plan)
        self.assertEqual(rate_codes, ["EXECUTIVE-S-101", "EXECUTIVE-D-101"])

        # Test default mapping
        rate_codes = self.mapper.map_rate_plan_to_codes("STANDARD", self.rate_plan)
        self.assertIn("STANDARD-BEST_AVAILABLE_RATE", rate_codes[0])

    def test_calendar_to_rate_restrictions_mapping(self):
        """Test calendar entry to rate restrictions mapping."""
        payload = self.mapper.map_calendar_to_rate_restrictions(self.calendar_entry)

        # Verify structure
        self.assertIn("hotelCode", payload)
        self.assertIn("toChannels", payload)
        self.assertIn("updates", payload)

        self.assertEqual(len(payload["updates"]), 1)
        update = payload["updates"][0]

        self.assertEqual(update["startDate"], date.today().isoformat())
        self.assertEqual(update["endDate"], date.today().isoformat())
        self.assertEqual(len(update["rates"]), 2)  # Two rate plans configured

        rate = update["rates"][0]
        self.assertEqual(rate["roomCode"], "EXECUTIVE")
        self.assertIn("EXECUTIVE-", rate["rateplanCode"])

        restrictions = rate["restrictions"]
        self.assertFalse(restrictions["stopSell"])
        self.assertEqual(restrictions["minimumStay"], 2)
        self.assertFalse(restrictions["closeOnArrival"])

    def test_calendar_batch_mapping(self):
        """Test batch calendar mapping."""
        # Create additional calendar entries
        calendar_entries = [self.calendar_entry]

        for i in range(3):
            entry = Calendar.objects.create(
                room_type=self.room_type,
                rate_plan=self.rate_plan,
                date=date.today() + timedelta(days=i + 1),
                available_rooms=5,
                daily_rate=Decimal("150.00"),
                restrictions={
                    "stopSell": False,
                    "minimumStay": 1,
                    "closeOnArrival": False,
                },
            )
            calendar_entries.append(entry)

        payload, skipped = self.mapper.map_calendar_batch_to_rate_restrictions(
            calendar_entries
        )
        self.assertEqual(len(skipped), 0)

        # Verify batching
        self.assertEqual(
            len(payload["updates"]), 1
        )  # All entries for same room type/rate plan

        update = payload["updates"][0]
        self.assertEqual(update["startDate"], date.today().isoformat())
        self.assertEqual(
            update["endDate"], (date.today() + timedelta(days=3)).isoformat()
        )

    def test_room_block_to_inventory_restrictions_mapping(self):
        """Test room block to inventory restrictions mapping."""
        # Create test data
        room = Room.objects.create(property=self.hotel, room_no="101", max_guests=2)

        user = User.objects.create(username="testuser", email="<EMAIL>")

        reservation = Reservation.objects.create(
            user=user,
            property=self.hotel,
            check_in=timezone.now(),
            check_out=timezone.now() + timedelta(days=1),
            guests=2,
            total=150.0,
        )

        room_block = RoomBlock.objects.create(
            hotel=self.hotel,
            room=room,
            reservation=reservation,
            blocked_from=timezone.now(),
            blocked_until=timezone.now() + timedelta(days=1),
            is_active=True,
            reason="reservation",
        )

        payload = self.mapper.map_room_block_to_inventory_restrictions(
            room_block, "create"
        )

        # Verify structure
        self.assertIn("hotelCode", payload)
        self.assertIn("toChannels", payload)
        self.assertIn("updates", payload)

        self.assertEqual(len(payload["updates"]), 1)
        update = payload["updates"][0]

        self.assertEqual(len(update["rooms"]), 1)
        room_data = update["rooms"][0]

        self.assertIn("roomCode", room_data)
        self.assertTrue(room_data["restrictions"]["stopSell"])  # Should be blocked

    def test_payload_validation(self):
        """Test payload validation."""
        # Test valid rate restrictions payload
        valid_payload = self.mapper.get_sample_rate_restrictions_payload()
        is_valid, errors = self.mapper.validate_payload(valid_payload, "rate")
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

        # Test valid inventory restrictions payload
        valid_payload = self.mapper.get_sample_inventory_restrictions_payload()
        is_valid, errors = self.mapper.validate_payload(valid_payload, "inventory")
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

        # Test invalid payload
        invalid_payload = {
            "hotelCode": "TEST",
            # Missing toChannels and updates
        }
        is_valid, errors = self.mapper.validate_payload(invalid_payload, "rate")
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)

    def test_custom_restrictions_mapping(self):
        """Test custom restrictions mapping."""
        restrictions = {
            "stop_sell": True,
            "minimum_stay": 3,
            "close_on_arrival": True,
            "maximum_stay": 7,
        }

        # Test rate restrictions
        payload = self.mapper.map_custom_restrictions(
            room_type=self.room_type,
            rate_plan=self.rate_plan,
            start_date=date.today(),
            end_date=date.today() + timedelta(days=5),
            restrictions=restrictions,
            api_type="rate",
        )

        self.assertIn("rates", payload["updates"][0])
        rate_restrictions = payload["updates"][0]["rates"][0]["restrictions"]
        self.assertTrue(rate_restrictions["stopSell"])
        self.assertEqual(rate_restrictions["minimumStay"], 3)
        self.assertTrue(rate_restrictions["closeOnArrival"])
        self.assertEqual(rate_restrictions["maximumStay"], 7)

        # Test inventory restrictions
        payload = self.mapper.map_custom_restrictions(
            room_type=self.room_type,
            start_date=date.today(),
            end_date=date.today() + timedelta(days=5),
            restrictions=restrictions,
            api_type="inventory",
        )

        self.assertIn("rooms", payload["updates"][0])
        room_restrictions = payload["updates"][0]["rooms"][0]["restrictions"]
        self.assertTrue(room_restrictions["stopSell"])
        self.assertEqual(room_restrictions["minimumStay"], 3)

    def test_get_data_mapper_function(self):
        """Test get_data_mapper utility function."""
        mapper = get_data_mapper(self.hotel)
        self.assertIsInstance(mapper, AioSellDataMapper)
        self.assertEqual(mapper.hotel, self.hotel)


class AioSellSignalsTestCase(TestCase):
    """Test cases for AioSell Django signals."""

    def setUp(self):
        """Set up test data."""
        self.hotel = Property.objects.create(
            name="Test Hotel",
            rooms=10,
            channel_managers={
                "aiosell": {
                    "enabled": True,
                    "config": {
                        "sync_enabled": True,
                        "auto_sync_on_block": True,
                        "auto_sync_on_unblock": True,
                    },
                }
            },
        )

        # Create OTA platform and integration
        self.ota_platform = OTAPlatform.objects.create(name="aiosell", is_active=True)

        self.integration = HotelOTAIntegration.objects.create(
            hotel=self.hotel,
            ota_platform=self.ota_platform,
            external_hotel_id="TEST-HOTEL",
            credentials={"identifier": "test_token"},
            is_active=True,
        )

        self.room_type = RoomType.objects.create(
            hotel=self.hotel, name="Test Room", max_occupancy=2
        )

        self.rate_plan = RatePlan.objects.create(
            room_type=self.room_type,
            name="Test Rate",
            base_rate=Decimal("100.00"),
            valid_from=date.today(),
            valid_to=date.today() + timedelta(days=365),
        )

    @mock.patch("pms.signals.aiosell_signals.sync_calendar_to_aiosell.delay")
    def test_calendar_save_signal(self, mock_task):
        """Test calendar save signal triggers sync task."""
        # Create calendar entry
        calendar_entry = Calendar.objects.create(
            room_type=self.room_type,
            rate_plan=self.rate_plan,
            date=date.today(),
            available_rooms=5,
            daily_rate=Decimal("100.00"),
        )

        # Verify task was called
        mock_task.assert_called_once()
        args, kwargs = mock_task.call_args
        self.assertEqual(kwargs["calendar_id"], str(calendar_entry.id))
        self.assertEqual(kwargs["action"], "create")

    @mock.patch("pms.signals.aiosell_signals.sync_room_block_to_aiosell.delay")
    def test_room_block_save_signal(self, mock_task):
        """Test room block save signal triggers sync task."""
        room = Room.objects.create(property=self.hotel, room_no="101", max_guests=2)

        user = User.objects.create(username="testuser", email="<EMAIL>")

        reservation = Reservation.objects.create(
            user=user,
            property=self.hotel,
            check_in=timezone.now(),
            check_out=timezone.now() + timedelta(days=1),
            guests=2,
            total=100.0,
        )

        # Create room block
        room_block = RoomBlock.objects.create(
            hotel=self.hotel,
            room=room,
            reservation=reservation,
            blocked_from=timezone.now(),
            blocked_until=timezone.now() + timedelta(days=1),
            is_active=True,
        )

        # Verify task was called
        mock_task.assert_called_once()
        args, kwargs = mock_task.call_args
        self.assertEqual(kwargs["room_block_id"], str(room_block.id))
        self.assertEqual(kwargs["action"], "create")


if __name__ == "__main__":
    unittest.main()
