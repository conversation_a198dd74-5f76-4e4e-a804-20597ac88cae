# Generated by Django 4.2.7 on 2025-10-02 05:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('geo', '0001_initial'),
        ('stay', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('booking', '0001_initial'),
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='reservation',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reservations', to='stay.property'),
        ),
        migrations.AddField(
            model_name='reservation',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reservations', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='profileimage',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='booking.profile'),
        ),
        migrations.AddField(
            model_name='profile',
            name='location',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='geo.location'),
        ),
        migrations.AddField(
            model_name='profile',
            name='partner',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='core.partnerprofile'),
        ),
        migrations.AddField(
            model_name='profile',
            name='property',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to='stay.property'),
        ),
        migrations.AddField(
            model_name='precheckinguest',
            name='pre_checkin',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pre_checkin_guests', to='booking.precheckin'),
        ),
        migrations.AddField(
            model_name='precheckinguest',
            name='room',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='stay.room'),
        ),
        migrations.AddField(
            model_name='precheckinguest',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pre_checkin_guests', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='precheckin',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pre_checkins', to='stay.property'),
        ),
        migrations.AddField(
            model_name='precheckin',
            name='reservation',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='precheckins', to='booking.reservation'),
        ),
        migrations.AddField(
            model_name='payment',
            name='pre_checkin',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='booking.precheckin'),
        ),
        migrations.AddField(
            model_name='allotedroom',
            name='pre_checkin',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alloted_rooms', to='booking.precheckin'),
        ),
        migrations.AddField(
            model_name='allotedroom',
            name='room',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alloted_rooms', to='stay.room'),
        ),
    ]
