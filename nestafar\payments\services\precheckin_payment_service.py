"""
Precheckin Payment Service

Service for integrating payment processing into the precheckin flow.
Handles upfront payment collection for advance bookings.
"""

import logging
from decimal import Decimal
from typing import Dict
from django.db import transaction
from django.utils import timezone

from ..models import PaymentIntent
from ..constants import PaymentContext
from .razorpay_service import RazorpayService
from .payment_split_service import PaymentSplitService
from .payment_notification_service import PaymentNotificationService
from ..exceptions import PaymentException

logger = logging.getLogger(__name__)


class PrecheckinPaymentService:
    """
    Service for handling precheckin payment processing.
    
    Integrates with the existing precheckin flow to collect upfront
    payments that go entirely to the partner (no platform commission).
    """
    
    def __init__(self):
        self.razorpay_service = RazorpayService()
        self.split_service = PaymentSplitService()
        self.notification_service = PaymentNotificationService()
        self.logger = logger
    
    def _serialize_metadata(self, data: dict) -> dict:
        """Return a metadata dict safe for JSON (convert Decimals)."""
        safe = {}
        for k, v in (data or {}).items():
            if isinstance(v, Decimal):
                safe[k] = float(v)
            else:
                safe[k] = v
        return safe

    def create_precheckin_payment_link(self, precheckin, upfront_amount: Decimal,
                                     partner=None, description: str = None) -> Dict:
        """
        Create payment link for precheckin upfront payment.
        
        Args:
            precheckin: PreCheckin instance
            upfront_amount: Upfront payment amount
            partner: PartnerProfile instance (if not provided, will get from property)
            description: Payment description
            
        Returns:
            Dictionary with payment link details
        """
        try:
            with transaction.atomic():
                # Get partner from precheckin property or use provided partner
                if partner is None:
                    # If no partner provided, resolve from the precheckin's property
                    if not getattr(precheckin, 'property', None):
                        raise PaymentException("No property associated with this precheckin")

                    # Use the property's staffs relation queryset to find an actual PartnerProfile.
                    # Prefer staffs whose user is marked as partner (user__is_partner=True),
                    # otherwise fallback to the first staff entry if present.
                    staffs_qs = precheckin.property.staffs.all()
                    partner = staffs_qs.filter(user__is_partner=True).first()
                    if not partner:
                        partner = staffs_qs.first()

                    if not partner:
                        raise PaymentException("No partner found for this property")
                # Get primary guest for customer details
                primary_guest = precheckin.pre_checkin_guests.filter(is_primary=True).first()
                if not primary_guest:
                    raise PaymentException("No primary guest found for precheckin")
                
                # For precheckin, we need to handle the Guest field requirement
                # Check if there's an existing Guest object for this user, or create a temporary one
                from stay.models import Guest
                
                # First check if there's an existing Guest object for this user
                guest_obj = Guest.objects.filter(
                    user=primary_guest.user,
                    checked_out=False
                ).first()
                
                # If no existing guest, we need to create one for the payment intent
                # Use the first available room from the property as a placeholder
                if not guest_obj:
                    # Get a room from the property (for precheckin, we may not have allocated rooms yet)
                    property_room = precheckin.property.property_rooms.first()
                    if not property_room:
                        raise PaymentException("No rooms available in property for payment processing")
                    
                    # Create a temporary guest entry for the payment
                    guest_obj = Guest.objects.create(
                        user=primary_guest.user,
                        room=property_room,
                        checkin_key=f"precheckin_{precheckin.id}",
                        checked_in=False,
                        checked_out=False,
                        payment_completed=False
                    )
                
                # Create payment intent
                payment_intent = PaymentIntent.objects.create(
                    context=PaymentContext.PRECHECKIN,
                    partner=partner,
                    guest=guest_obj,  # Use the Guest object
                    precheckin=precheckin,
                    total_amount=upfront_amount,
                    expires_at=timezone.now() + timezone.timedelta(hours=72),  # Payment link expires in 72 hours
                    metadata=self._serialize_metadata({
                        'precheckin_id': str(precheckin.id),
                        'property_id': str(precheckin.property.id),
                        'expected_checkin': precheckin.expected_checkin.isoformat(),
                        'number_of_rooms': precheckin.number_of_rooms,
                        'stay_duration': precheckin.stay_duration,
                    })
                )
                
                
                # Calculate payment splits (for precheckin, all goes to partner)
                split_data = self.split_service.calculate_splits(payment_intent)
                payment_intent.split_details = split_data
                payment_intent.save()
                
                # Create split records
                splits = self.split_service.create_payment_splits(payment_intent, split_data)
                
                # Prepare customer data
                # Safely access user contact details
                phone_obj = getattr(primary_guest.user, 'phone', None)
                national_number = getattr(phone_obj, 'national_number', None) if phone_obj else None

                customer_data = {
                    'name': primary_guest.user.name,
                    'email': getattr(primary_guest.user, 'email', ''),
                    'contact': str(national_number) if national_number is not None else ''
                }
                
                # Create payment description
                if not description:
                    checkin_date = precheckin.expected_checkin.strftime('%d %b %Y')
                    description = f"Advance payment for {precheckin.property.name} - Check-in: {checkin_date}"
                
                # Create Razorpay payment link
                payment_link_data = self.razorpay_service.create_payment_link(
                    amount=upfront_amount,
                    description=description,
                    customer_details=customer_data,
                    expire_by=payment_intent.expires_at,
                    notes={
                        'payment_intent_id': str(payment_intent.id),
                        'context': PaymentContext.PRECHECKIN,
                        'precheckin_id': str(precheckin.id),
                        'property_name': precheckin.property.name,
                        'expected_checkin': precheckin.expected_checkin.isoformat(),
                    }
                )
                
                # Update payment intent with Razorpay details
                payment_intent.razorpay_payment_link_id = payment_link_data['id']
                payment_intent.save()
                
                # Send payment link notifications
                notifications_sent = self._send_precheckin_payment_notifications(
                    payment_intent, payment_link_data['short_url']
                )
                
                self.logger.info(f"Created precheckin payment link for precheckin {precheckin.id}")
                
                return {
                    'payment_intent_id': str(payment_intent.id),
                    'reference_number': payment_intent.reference_number,
                    'payment_link_id': payment_link_data['id'],
                    'payment_link_url': payment_link_data['short_url'],
                    'total_amount': float(upfront_amount),
                    'expires_at': payment_intent.expires_at.isoformat(),
                    'notifications_sent': notifications_sent,
                    'split_preview': {
                        'platform_commission': float(payment_intent.platform_commission or 0),
                        'partner_amount': float(payment_intent.partner_amount or 0),
                        'vendor_amount': float(payment_intent.vendor_amount or 0),
                    }
                }
                
        except Exception as e:
            self.logger.error(f"Failed to create precheckin payment link: {str(e)}")
            raise PaymentException(f"Failed to create precheckin payment link: {str(e)}")
    
    def process_precheckin_payment_completion(self, payment_intent):
        """
        Process precheckin payment completion.
        
        Args:
            payment_intent: PaymentIntent instance
        """
        try:
            with transaction.atomic():
                # Update precheckin payment status
                precheckin = payment_intent.precheckin
                if precheckin:
                    # Update payment amounts
                   
                    precheckin.amount_paid = Decimal(str(precheckin.amount_paid)) + payment_intent.total_amount
                    precheckin.pending_balance = max(Decimal('0'), Decimal(str(precheckin.total_amount)) - precheckin.amount_paid)
                    
                    # Update payment status
                    if precheckin.pending_balance <= 0:
                        precheckin.payment_status = 'completed'
                    elif precheckin.amount_paid > 0:
                        precheckin.payment_status = 'partial'
                    
                    # Update precheckin status if payment is complete
                    if precheckin.payment_status == 'completed' and precheckin.status == 'pending':
                        precheckin.status = 'confirmed'
                    
                    # Store payment reference
                    precheckin.payment_id = payment_intent.razorpay_payment_id
                    precheckin.save()
                
                # Send completion notifications
                self._send_precheckin_completion_notifications(payment_intent)
                
                # Trigger post-payment processes
                self._trigger_precheckin_post_payment_processes(payment_intent)
                
                self.logger.info(f"Processed precheckin payment completion for {payment_intent.id}")
                
        except Exception as e:
            # Log full traceback for debugging
            self.logger.exception("Failed to process precheckin completion")

            # Prepare contextual information for monitoring/alerts
            try:
                payment_intent_id = str(getattr(payment_intent, 'id', ''))
            except Exception:
                payment_intent_id = ''

            precheckin_obj = None
            try:
                precheckin_obj = getattr(payment_intent, 'precheckin', None)
            except Exception:
                precheckin_obj = None

            precheckin_id = str(getattr(precheckin_obj, 'id', '')) if precheckin_obj else ''

            # Try to obtain a user id from the payment intent guest or precheckin primary guest
            user_id = ''
            try:
                guest_obj = getattr(payment_intent, 'guest', None)
                if guest_obj and getattr(guest_obj, 'user', None):
                    user_id = str(getattr(guest_obj.user, 'id', ''))
                elif precheckin_obj:
                    primary_guest = None
                    try:
                        primary_guest = precheckin_obj.pre_checkin_guests.filter(is_primary=True).first()
                    except Exception:
                        primary_guest = None
                    if primary_guest and getattr(primary_guest, 'user', None):
                        user_id = str(getattr(primary_guest.user, 'id', ''))
            except Exception:
                user_id = ''

            alert_context = {
                'operation': 'process_precheckin_payment_completion',
                'payment_intent_id': payment_intent_id,
                'precheckin_id': precheckin_id,
                'user_id': user_id,
            }

            # Emit a metrics/alert log so external monitoring can pick this up
            try:
                metrics_logger = logging.getLogger('metrics')
                # Use error level for alerts; include context in message/extra
                metrics_logger.error('precheckin_payment_processing_failed', extra={'context': alert_context})
            except Exception:
                # If metrics emission fails, at least log that failure
                self.logger.exception('Failed to emit precheckin payment failure metric')

            # If the precheckin object exists and is marked completed, swallow the exception
            if precheckin_obj and getattr(precheckin_obj, 'payment_status', '') == 'completed':
                return

            # Otherwise re-raise so upstream can handle/retry as payment processing may be incomplete
            raise
    
    def calculate_upfront_amount(self, precheckin, percentage: Decimal = None) -> Decimal:
        """
        Calculate upfront payment amount for precheckin.
        
        Args:
            precheckin: PreCheckin instance
            percentage: Percentage of total amount (default from settings)
            
        Returns:
            Upfront payment amount
        """
        try:
            total_amount = Decimal(str(precheckin.total_amount))
            
            if percentage is None:
                # Default upfront percentage (can be configured)
                percentage = Decimal('20.00')  # 20% default
            
            upfront_amount = (total_amount * percentage) / Decimal('100')
            
            # Round to 2 decimal places
            upfront_amount = upfront_amount.quantize(Decimal('0.01'))
            
            # Ensure minimum amount (e.g., ₹100)
            min_amount = Decimal('100.00')
            upfront_amount = max(upfront_amount, min_amount)
            
            # Ensure not more than total amount
            upfront_amount = min(upfront_amount, total_amount)
            
            return upfront_amount
            
        except Exception as e:
            self.logger.error(f"Failed to calculate upfront amount: {str(e)}")
            raise PaymentException(f"Failed to calculate upfront amount: {str(e)}")
    
    def send_precheckin_payment_reminder(self, precheckin) -> Dict:
        """
        Send payment reminder for pending precheckin.
        
        Args:
            precheckin: PreCheckin instance
            
        Returns:
            Dictionary with reminder status
        """
        try:
            # Find pending payment intent for this precheckin
            payment_intent = PaymentIntent.objects.filter(
                precheckin=precheckin,
                context=PaymentContext.PRECHECKIN,
                status='pending'
            ).first()
            
            if not payment_intent:
                return {'sent': False, 'error': 'No pending payment found'}
            
            # Send reminder notification
            result = self.notification_service.send_payment_reminder_notification(payment_intent)
            
            self.logger.info(f"Sent precheckin payment reminder for {precheckin.id}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to send precheckin reminder: {str(e)}")
            return {'sent': False, 'error': str(e)}
    
    def _send_precheckin_payment_notifications(self, payment_intent, payment_url: str) -> Dict:
        """
        Send precheckin payment notifications.

        Args:
            payment_intent: PaymentIntent instance
            payment_url: Payment link URL

        Returns:
            Dictionary with notification results
        """
        try:
            # Send payment link notification using existing notification system
            result = self.notification_service.send_payment_link_notification(
                payment_intent, payment_url
            )

            return {
                'whatsapp': result
            }

        except Exception as e:
            self.logger.error(f"Failed to send precheckin notifications: {str(e)}")
            return {'error': str(e)}
    
    def _send_precheckin_completion_notifications(self, payment_intent):
        """
        Send precheckin completion notifications.
        
        Args:
            payment_intent: PaymentIntent instance
        """
        try:
            # Send success notification to guest
            self.notification_service.send_payment_success_notification(payment_intent)
            
            # Send confirmation to partner about advance payment received
            # This can be implemented based on business requirements
            
        except Exception as e:
            self.logger.error(f"Failed to send precheckin completion notifications: {str(e)}")
    
    def _trigger_precheckin_post_payment_processes(self, payment_intent):
        """
        Trigger post-payment processes for precheckin.
        
        Args:
            payment_intent: PaymentIntent instance
        """
        precheckin = payment_intent.precheckin
        
        # Send confirmation email/SMS with booking details
        try:
            self._send_booking_confirmation(precheckin)
        except Exception as e:
            self.logger.error(
                f"Error sending booking confirmation for precheckin {precheckin.id}: {str(e)}"
            )
        
        # Schedule reminder notifications for check-in
        try:
            self._schedule_checkin_reminders(precheckin)
        except Exception as e:
            self.logger.error(
                f"Error scheduling check-in reminders for precheckin {precheckin.id}: {str(e)}"
            )
        
        # Any other post-payment processes can be added here, each in its own try/except
    
    def _send_booking_confirmation(self, precheckin):
        """
        Send booking confirmation after payment.
        
        Args:
            precheckin: PreCheckin instance
        """
        try:
            # Get primary guest
            primary_guest = precheckin.pre_checkin_guests.filter(is_primary=True).first()
            if not primary_guest:
                return
            
            # Send booking confirmation notification
            from notification.tasks import send_notification
            
            confirmation_data = {
                'guest_name': primary_guest.user.name,
                'property_name': precheckin.property.name,
                'checkin_date': precheckin.expected_checkin.strftime('%d %b %Y'),
                'checkout_date': (precheckin.expected_checkin + timezone.timedelta(days=precheckin.stay_duration)).strftime('%d %b %Y'),
                'number_of_rooms': precheckin.number_of_rooms,
                'amount_paid': f"₹{precheckin.amount_paid:,.2f}",
                'pending_balance': f"₹{precheckin.pending_balance:,.2f}",
            }
            
            send_notification.delay(
                str(primary_guest.user.id),
                'BOOKING_CONFIRMED',
                confirmation_data
            )
            
        except Exception as e:
            self.logger.error(f"Failed to send booking confirmation: {str(e)}")
    
    def _schedule_checkin_reminders(self, precheckin):
        """
        Schedule check-in reminder notifications.
        
        Args:
            precheckin: PreCheckin instance
        """
        try:
            # Schedule reminders at different intervals before check-in
            # This can be implemented using Celery beat or similar scheduling
            
            # Example: Schedule reminder 24 hours before check-in
            from notification.tasks import send_notification
            
            reminder_time = precheckin.expected_checkin - timezone.timedelta(hours=24)
            
            if reminder_time > timezone.now():
                # Schedule the reminder (this is a simplified example)
                # In practice, you'd use Celery beat or similar scheduling system
                pass
            
        except Exception as e:
            self.logger.error(f"Failed to schedule checkin reminders: {str(e)}")
