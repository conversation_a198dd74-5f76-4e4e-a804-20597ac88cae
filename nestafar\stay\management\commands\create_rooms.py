import csv
import os
from django.core.management.base import BaseCommand, CommandError
from stay.models import Property, Room


class Command(BaseCommand):
    help = "Create rooms for a property from a CSV file"

    def add_arguments(self, parser):
        parser.add_argument(
            "property_id", type=str, help="The ID of the property to add rooms to"
        )
        parser.add_argument(
            "file_path", type=str, help="The file path of the CSV containing rooms data"
        )

    def handle(self, *args, **kwargs):
        property_id = kwargs["property_id"]
        file_path = kwargs["file_path"]

        try:
            property_instance = Property.objects.get(pk=property_id)
        except Property.DoesNotExist:
            raise CommandError(f"Property with ID {property_id} does not exist")

        if not os.path.exists(file_path):
            raise CommandError(f'File "{file_path}" does not exist')

        with open(file_path, "r") as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                room = Room(
                    property=property_instance,
                    room_no=row["room_no"],
                    type_of_room=row["room_type"],
                    description=row.get("description", None),
                    rate=row.get("rate", None),
                    bed=int(row["bed"]),
                    max_guests=int(row["max_guests"]),
                    amenities=row.get("amenities", None),
                )
                room.save()

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created rooms for property "{property_instance.name}"'
            )
        )
