# Generated by Django 4.2.7 on 2025-10-02 05:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('payments', '0001_initial'),
        ('stay', '0001_initial'),
        ('booking', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0002_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='paymentintent',
            name='guest',
            field=models.ForeignKey(help_text='Associated guest if applicable', on_delete=django.db.models.deletion.CASCADE, related_name='payment_intents', to='stay.guest'),
        ),
        migrations.AddField(
            model_name='paymentintent',
            name='partner',
            field=models.ForeignKey(help_text='Partner associated with this payment', on_delete=django.db.models.deletion.CASCADE, related_name='payment_intents', to='core.partnerprofile'),
        ),
        migrations.AddField(
            model_name='paymentintent',
            name='precheckin',
            field=models.ForeignKey(blank=True, help_text='Associated precheckin if applicable', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payment_intents', to='booking.precheckin'),
        ),
        migrations.AddField(
            model_name='partnerrazorpayaccount',
            name='partner',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='razorpay_account', to='core.partnerprofile'),
        ),
        migrations.AddField(
            model_name='partnerkycdocument',
            name='razorpay_account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='kyc_documents', to='payments.partnerrazorpayaccount'),
        ),
        migrations.AddField(
            model_name='partnerbankaccount',
            name='razorpay_account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bank_accounts', to='payments.partnerrazorpayaccount'),
        ),
        migrations.AddField(
            model_name='accountverificationlog',
            name='performed_by',
            field=models.ForeignKey(blank=True, help_text='User who performed the activity', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='accountverificationlog',
            name='razorpay_account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='verification_logs', to='payments.partnerrazorpayaccount'),
        ),
        migrations.AddField(
            model_name='accountverificationlog',
            name='related_bank_account',
            field=models.ForeignKey(blank=True, help_text='Related bank account if applicable', null=True, on_delete=django.db.models.deletion.SET_NULL, to='payments.partnerbankaccount'),
        ),
        migrations.AddField(
            model_name='accountverificationlog',
            name='related_document',
            field=models.ForeignKey(blank=True, help_text='Related KYC document if applicable', null=True, on_delete=django.db.models.deletion.SET_NULL, to='payments.partnerkycdocument'),
        ),
        migrations.AddIndex(
            model_name='paymentwebhookevent',
            index=models.Index(fields=['event_id'], name='payments_we_event_i_b93d23_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentwebhookevent',
            index=models.Index(fields=['event_type', 'processed'], name='payments_we_event_t_04d1bd_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentwebhookevent',
            index=models.Index(fields=['entity_type', 'entity_id'], name='payments_we_entity__c22e32_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentwebhookevent',
            index=models.Index(fields=['payment_intent', 'event_type'], name='payments_we_payment_542634_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentwebhookevent',
            index=models.Index(fields=['processed', 'received_at'], name='payments_we_process_1f0620_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentsplit',
            index=models.Index(fields=['payment_intent', 'recipient_type'], name='payments_pa_payment_5d7cfe_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentsplit',
            index=models.Index(fields=['razorpay_transfer_id'], name='payments_pa_razorpa_c9343c_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentsplit',
            index=models.Index(fields=['status', 'created_at'], name='payments_pa_status_b299d5_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentsplit',
            index=models.Index(fields=['recipient_type', 'status'], name='payments_pa_recipie_06a900_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentintent',
            index=models.Index(fields=['reference_number'], name='payments_pa_referen_853ea1_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentintent',
            index=models.Index(fields=['razorpay_payment_id'], name='payments_pa_razorpa_8e02fe_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentintent',
            index=models.Index(fields=['razorpay_order_id'], name='payments_pa_razorpa_33c79a_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentintent',
            index=models.Index(fields=['status', 'created_at'], name='payments_pa_status_0b0522_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentintent',
            index=models.Index(fields=['partner', 'status'], name='payments_pa_partner_9f8d7b_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentintent',
            index=models.Index(fields=['context', 'status'], name='payments_pa_context_19ad19_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='partnerkycdocument',
            unique_together={('razorpay_account', 'document_type')},
        ),
        migrations.AlterUniqueTogether(
            name='partnerbankaccount',
            unique_together={('razorpay_account', 'account_number')},
        ),
    ]
