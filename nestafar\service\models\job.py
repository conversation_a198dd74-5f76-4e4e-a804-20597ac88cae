import uuid
from django.db import models


class Job(models.Model):
    class JobStatus(models.TextChoices):
        PENDING = "pending", "Pending"
        IN_PROGRESS = "in_progress", "In Progress"
        COMPLETED = "completed", "Completed"
        CANCELLED = "cancelled", "Cancelled"

    class InitiatedBy(models.TextChoices):
        AUTO_CREATED = "auto_created", "Auto Created"
        GUEST_REQUEST = "guest_request", "Guest Request"
        OWNER_REQUEST = "owner_request", "Owner/Partner Request"

    class JobType(models.TextChoices):
        DELIVERY = "delivery", "Delivery"
        HOUSEKEEPING = "housekeeping", "Housekeeping"

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    staff = models.ForeignKey(
        "service.StaffProfile",
        on_delete=models.SET_NULL,
        related_name="jobs",
        null=True,
        blank=True,
    )
    room = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        help_text="Room number for housekeeping or in-property jobs",
    )
    status = models.CharField(
        max_length=20,
        choices=JobStatus.choices,
        default=JobStatus.PENDING,
        db_index=True,
    )
    initiated_by = models.CharField(
        max_length=20, choices=InitiatedBy.choices, default=InitiatedBy.AUTO_CREATED
    )
    type = models.CharField(max_length=20, choices=JobType.choices, db_index=True)
    property = models.ForeignKey(
        "stay.Property", on_delete=models.CASCADE, related_name="jobs"
    )

    # Delivery specific
    pickup_location = models.CharField(max_length=255, blank=True, null=True)
    dropoff_location = models.CharField(max_length=255, blank=True, null=True)
    earnings = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )

    # Details
    title = models.CharField(max_length=120, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    payload = models.JSONField(default=dict, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["property", "status"]),
            models.Index(fields=["property", "type"]),
            models.Index(fields=["property", "created_at"]),
        ]

    def __str__(self) -> str:
        return f"{self.get_type_display()} - {self.get_status_display()} ({self.id})"


class JobStatusLog(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    job = models.ForeignKey(Job, on_delete=models.CASCADE, related_name="status_logs")
    previous_status = models.CharField(
        max_length=20, choices=Job.JobStatus.choices, blank=True, null=True
    )
    new_status = models.CharField(max_length=20, choices=Job.JobStatus.choices)
    changed_by = models.ForeignKey(
        "core.User", on_delete=models.SET_NULL, null=True, blank=True
    )
    note = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=["job", "created_at"]),
        ]

    def __str__(self) -> str:
        return f"Job {self.job_id}: {self.previous_status} -> {self.new_status}"
