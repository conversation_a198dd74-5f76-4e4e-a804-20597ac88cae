# Generated by Django 4.2.7 on 2025-10-02 05:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('service', '0001_initial'),
        ('stay', '0001_initial'),
        ('laundry', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='laundryservice',
            name='partner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='service.servicepartner'),
        ),
        migrations.AddField(
            model_name='laundryorderitem',
            name='item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='laundry.laundryserviceitem'),
        ),
        migrations.AddField(
            model_name='laundryorderitem',
            name='order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='laundry.laundryorder'),
        ),
        migrations.AddField(
            model_name='laundryorder',
            name='cart',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='laundry.laundrycart'),
        ),
        migrations.AddField(
            model_name='laundryorder',
            name='guest',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.guest'),
        ),
        migrations.AddField(
            model_name='laundryorder',
            name='service',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='laundry.laundryservice'),
        ),
        migrations.AddField(
            model_name='laundryorder',
            name='service_partner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='service.servicepartner'),
        ),
        migrations.AddField(
            model_name='laundrycartitems',
            name='cart',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='laundry.laundrycart'),
        ),
        migrations.AddField(
            model_name='laundrycartitems',
            name='item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='laundry.laundryserviceitem'),
        ),
        migrations.AddField(
            model_name='laundrycart',
            name='guest',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.guest'),
        ),
        migrations.AddIndex(
            model_name='laundryorder',
            index=models.Index(fields=['guest', 'cart'], name='laundry_lau_guest_i_1a34fb_idx'),
        ),
    ]
