from django.db import models
from django.utils import timezone
from phonenumber_field.modelfields import PhoneNumberField
from django.core.exceptions import ValidationError
import uuid
from django.core.validators import MinValueValidator


class Profile(models.Model):
    """Represents a bookingprofile for a property to be listed on booking site"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    property = models.OneToOneField(
        "stay.Property", on_delete=models.CASCADE, related_name="profile"
    )
    partner = models.OneToOneField("core.PartnerProfile", on_delete=models.CASCADE)
    phone = PhoneNumberField(unique=True)
    email = models.EmailField()
    description = models.TextField()
    location = models.ForeignKey("geo.Location", on_delete=models.CASCADE)
    amenities = models.JSONField(null=True, blank=True)
    nearby = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Profile for {self.property}"


class ProfileImage(models.Model):
    """Represents a profile image for a property"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    profile = models.ForeignKey(
        Profile, on_delete=models.CASCADE, related_name="images"
    )
    image = models.ImageField(upload_to="booking_profiles/")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Image for Profile ID {self.profile.id}"


class PreCheckin(models.Model):
    """Represents a pre-checkin for a reservation in a property"""

    STATUS_CHOICES = [
        ("pending", "Pending"),
        ("confirmed", "Confirmed"),
        ("arrived", "Arrived"),
        ("partial", "Partial"),
        ("cancelled", "Cancelled"),
        ("checked_in", "Checked In"),
        ("checked_out", "Checked Out"),
    ]
    PAYMENT_STATUS_CHOICES = [
        ("completed", "Completed"),
        ("pending", "Pending"),
        ("partial", "Partial"),
        ("unpaid", "Unpaid"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    property = models.ForeignKey(
        "stay.Property", on_delete=models.CASCADE, related_name="pre_checkins"
    )
    reservation = models.ForeignKey(
        "booking.Reservation",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="precheckins",
    )
    number_of_rooms = models.PositiveIntegerField(default=1)
    stay_duration = models.PositiveIntegerField(default=1)
    welcome_message = models.TextField(blank=True, null=True)
    expected_checkin = models.DateTimeField()
    total_amount = models.FloatField(default=0)
    amount_paid = models.FloatField(default=0)
    pending_balance = models.FloatField(default=0)
    payment_status = models.CharField(
        max_length=20, choices=PAYMENT_STATUS_CHOICES, default="unpaid"
    )
    payment_id = models.CharField(max_length=50, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="pending")
    guest_address = models.CharField(max_length=255, blank=True, null=True)
    special_requests = models.TextField(blank=True, null=True)
    is_completed = models.BooleanField(default=False)

    def __str__(self):
        return f"PreCheckin for Guest {self.property.name}"

    def get_expected_checkout(self):
        """Calculate the expected checkout date based on checkin and stay duration"""
        return self.expected_checkin + timezone.timedelta(days=self.stay_duration)

    def clean(self):
        """Model-level validation to prevent cross-property reservation linking."""
        super().clean()
        if self.reservation_id and self.property_id:
            if self.reservation and self.reservation.property_id != self.property_id:
                raise ValidationError(
                    {
                        "reservation": "Reservation property does not match pre-checkin property."
                    }
                )

    def save(self, *args, **kwargs):  # noqa: D401
        """Ensure model validation runs on save."""
        self.full_clean()
        return super().save(*args, **kwargs)


class AllotedRoom(models.Model):
    """Represents a room that has been alloted to a precheckin"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    pre_checkin = models.ForeignKey(
        PreCheckin, on_delete=models.CASCADE, related_name="alloted_rooms"
    )
    room = models.ForeignKey(
        "stay.Room", on_delete=models.CASCADE, related_name="alloted_rooms"
    )
    created_at = models.DateTimeField(auto_now_add=True)


class PreCheckinGuest(models.Model):
    """Represents a  guest for a pre-checkin"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    pre_checkin = models.ForeignKey(
        PreCheckin, on_delete=models.CASCADE, related_name="pre_checkin_guests"
    )
    user = models.ForeignKey(
        "core.User", on_delete=models.CASCADE, related_name="pre_checkin_guests"
    )
    is_primary = models.BooleanField(default=False)
    room = models.ForeignKey(
        "stay.Room", on_delete=models.SET_NULL, null=True, blank=True
    )
    id_proof = models.ImageField(
        upload_to="precheckin/id_proofs/", blank=True, null=True
    )
    age = models.PositiveIntegerField(default=18)
    created_at = models.DateTimeField(auto_now_add=True)
    is_verified = models.BooleanField(default=False)

    def __str__(self):
        return f"PreCheckinGuest for {self.user.name}"


class Reservation(models.Model):
    """Represents a reservation for a property"""

    STATUS_CHOICES = [
        ("pending", "Pending"),
        ("confirmed", "Confirmed"),
        ("checked_in", "Checked In"),
        ("checked_out", "Checked Out"),
        ("cancelled", "Cancelled"),
    ]

    SEGMENT_CHOICES = [
        ("OTA", "Online Travel Agency"),
        ("DIRECT", "Direct Booking"),
        ("CORPORATE", "Corporate"),
        ("GROUPS", "Groups"),
        ("WALKIN", "Walk-in"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        "core.User", on_delete=models.CASCADE, related_name="reservations"
    )
    property = models.ForeignKey(
        "stay.Property", on_delete=models.CASCADE, related_name="reservations"
    )
    check_in = models.DateTimeField()
    check_out = models.DateTimeField()
    guests = models.IntegerField()
    total = models.FloatField()
    paid = models.FloatField(default=0)
    requests = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="pending")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    booking_details = models.JSONField(null=True, blank=True)

    # AIOSell specific fields
    external_booking_id = models.CharField(
        max_length=100, blank=True, null=True, help_text="External OTA booking ID"
    )
    cm_booking_id = models.CharField(
        max_length=100, blank=True, null=True, help_text="Channel Manager booking ID"
    )
    channel = models.CharField(
        max_length=100, blank=True, null=True, help_text="Booking channel name"
    )
    segment = models.CharField(max_length=20, choices=SEGMENT_CHOICES, default="DIRECT")
    pah = models.BooleanField(
        default=False, help_text="Payment At Hotel - True if payment collected at hotel"
    )
    booked_on = models.DateTimeField(
        blank=True, null=True, help_text="When the booking was originally made"
    )
    amount_before_tax = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(0)],
    )
    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(0)],
    )
    currency = models.CharField(max_length=3, default="INR")
    room_details = models.JSONField(
        blank=True, null=True, help_text="Room-wise pricing and occupancy details"
    )

    def __str__(self):
        return f"Reservation {self.id} by {self.user.name}"

    def is_upcoming(self):
        """Check if the reservation is in the future."""
        return self.check_in > timezone.now()

    def clean(self):
        if self.check_out <= self.check_in:
            raise ValidationError("Check-out must be after check-in")

    class Meta:
        ordering = ["-created_at"]


class Payment(models.Model):
    """Represents a payment for a pre-checkin"""

    PAYMENT_METHOD_CHOICES = [
        ("credit_card", "Credit Card"),
        ("debit_card", "Debit Card"),
        ("paypal", "Paypal"),
        ("cash", "Cash"),
        ("bank_transfer", "Bank Transfer"),
    ]
    STATUS_CHOICES = [
        ("completed", "Completed"),
        ("pending", "Pending"),
        ("failed", "Failed"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    pre_checkin = models.ForeignKey(
        PreCheckin, on_delete=models.CASCADE, related_name="payments"
    )
    amount = models.FloatField()
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES)
    transaction_id = models.CharField(max_length=50)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="pending")
    created_at = models.DateTimeField(auto_now_add=True)
    payment_details = models.JSONField(
        null=True, blank=True
    )  # Any additional payment details

    def __str__(self):
        return f"Payment {self.id} for PreCheckin {self.pre_checkin.id}"
