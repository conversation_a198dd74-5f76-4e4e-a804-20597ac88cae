"""
Razorpay Constants

Razorpay-specific constants and configurations.
"""


class RazorpayConstants:
    """Razorpay API constants"""

    # API URLs
    BASE_URL_TEST = "https://api.razorpay.com/v1"
    BASE_URL_LIVE = "https://api.razorpay.com/v1"

    # Currency
    DEFAULT_CURRENCY = "INR"

    # Amount multiplier (<PERSON><PERSON><PERSON><PERSON> uses paise)
    AMOUNT_MULTIPLIER = 100

    # API Timeouts
    DEFAULT_TIMEOUT = 30

    # Webhook signature header
    WEBHOOK_SIGNATURE_HEADER = "X-Razorpay-Signature"

    # Payment link settings
    PAYMENT_LINK_EXPIRE_BY_HOURS = 24

    # Transfer settings
    TRANSFER_CURRENCY = "INR"

    # Account types
    ACCOUNT_TYPE_ROUTE = "route"


class RazorpayWebhookEvents:
    """Razorpay webhook event names"""

    # Payment events
    PAYMENT_AUTHORIZED = "payment.authorized"
    PAYMENT_CAPTURED = "payment.captured"
    PAYMENT_FAILED = "payment.failed"

    # Transfer events
    TRANSFER_PROCESSED = "transfer.processed"
    TRANSFER_FAILED = "transfer.failed"
    TRANSFER_REVERSED = "transfer.reversed"

    # Payment link events
    PAYMENT_LINK_PAID = "payment_link.paid"
    PAYMENT_LINK_CANCELLED = "payment_link.cancelled"
    PAYMENT_LINK_EXPIRED = "payment_link.expired"

    # Order events
    ORDER_PAID = "order.paid"

    # All supported events
    SUPPORTED_EVENTS = [
        PAYMENT_AUTHORIZED,
        PAYMENT_CAPTURED,
        PAYMENT_FAILED,
        TRANSFER_PROCESSED,
        TRANSFER_FAILED,
        TRANSFER_REVERSED,
        PAYMENT_LINK_PAID,
        PAYMENT_LINK_CANCELLED,
        PAYMENT_LINK_EXPIRED,
        ORDER_PAID,
    ]


class RazorpayErrorCodes:
    """Common Razorpay error codes"""

    # Authentication errors
    BAD_REQUEST_ERROR = "BAD_REQUEST_ERROR"
    UNAUTHORIZED_ERROR = "UNAUTHORIZED_ERROR"

    # Payment errors
    PAYMENT_FAILED = "PAYMENT_FAILED"
    PAYMENT_CANCELLED = "PAYMENT_CANCELLED"

    # Transfer errors
    TRANSFER_FAILED = "TRANSFER_FAILED"
    INSUFFICIENT_BALANCE = "INSUFFICIENT_BALANCE"

    # Account errors
    INVALID_ACCOUNT = "INVALID_ACCOUNT"
    ACCOUNT_NOT_ACTIVATED = "ACCOUNT_NOT_ACTIVATED"

    # Rate limit errors
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"

    # Server errors
    SERVER_ERROR = "SERVER_ERROR"
    GATEWAY_ERROR = "GATEWAY_ERROR"
