from pytest_bdd import given, parsers, then, when
from django.urls import reverse
from service.subapps.food.models import FoodService, FoodServiceItem
from .service import subapp_url_mapping
import json
import logging
from stay.models import Guest
from service.subapps.food.models import (
    FoodCart,
    FoodCartItems,
    FoodOrder,
    FoodOrderItem,
)

logger = logging.getLogger(__name__)


@given(parsers.parse("Place FoodOrder\n{table}"))
def place_food_order(context, table):
    context.populate_table_from_str(table)
    auth_client = context.auth_client
    payloads = []
    for row in context.table:
        fsi = FoodServiceItem.objects.get(name=row["item"])
        payload = {
            "item": fsi.id,
            "quantity": row["quantity"],
            "add_ons": json.loads(row["add_ons"]),
        }
        payloads.append(payload)
    suburl = subapp_url_mapping["foodservice"][0]
    response = auth_client.post(
        reverse(f"service:foodservice:food-order"), data=payloads, format="json"
    )
    assert response.status_code == 201


@given(parsers.parse("Add Items to Food Cart\n{table}"))
def add_items_to_cart(context, table):
    context.populate_table_from_str(table)
    auth_client = context.auth_client
    payloads = []
    for row in context.table:
        fsi = FoodServiceItem.objects.get(name=row["item"])
        payload = {
            "item": fsi.id,
            "name": row["item"],
            "quantity": row["quantity"],
            "add_ons": json.loads(row["add_ons"]),
            "price": fsi.price,
        }
        payloads.append(payload)
    suburl = subapp_url_mapping["foodservice"][0]
    response = auth_client.post(
        reverse(f"service:foodservice:food-cart"), data=payloads, format="json"
    )
    assert response.status_code == 200


@given(parsers.parse("Remove Items from Food Cart\n{table}"))
def delete_items_from_cart(context, table):
    context.populate_table_from_str(table)
    auth_client = context.auth_client
    payloads = []
    for row in context.table:
        fsi = FoodServiceItem.objects.get(name=row["item"])
        payload = {
            "item": fsi.id,
            "quantity": row["quantity"],
            "add_ons": json.loads(row["add_ons"]),
        }
        payloads.append(payload)
    suburl = subapp_url_mapping["foodservice"][0]
    response = auth_client.delete(
        reverse(f"service:foodservice:food-cart"), data=payloads, format="json"
    )
    assert response.status_code == 200


@given(parsers.parse("Get Food Cart\n{table}"))
def get_cart(context, table):
    context.populate_table_from_str(table)
    auth_client = context.auth_client
    suburl = subapp_url_mapping["foodservice"][0]
    response = auth_client.get(reverse(f"service:foodservice:food-cart"))
    data = response.json()
    assert response.status_code == 200
    context.scratchpad["cart_id"] = data["data"]["id"]
    food_items = {d["name"]: d["quantity"] for d in data["data"]["food_cart_items"]}
    for row in context.table:
        assert row["item"] in food_items
        assert food_items[row["item"]] == int(row["quantity"])


@given(parsers.parse("Place FoodOrder from Cart"))
def place_food_order_from_cart(context):
    auth_client = context.auth_client
    cart_id = context.scratchpad["cart_id"]
    suburl = subapp_url_mapping["foodservice"][0]
    response = auth_client.post(
        reverse(f"service:foodservice:food-order"), data={"cart_id": cart_id}
    )
    assert response.status_code == 201


@then(parsers.parse("Validate FoodOrder\n{table}"))
def validate_food_order(context, table):
    guest = Guest.objects.get(user=context.auth_user, checked_in=True)
    context.populate_table_from_str(table)
    food_order = FoodOrder.objects.filter(guest=guest).first()
    assert food_order
    food_order_items = {
        d.item.name: d.quantity for d in food_order.food_order_items.all()
    }
    for row in context.table:
        assert row["item"] in food_order_items
        assert food_order_items[row["item"]] == int(row["quantity"])


@then(parsers.parse("Validate FoodCartItems"))
def validate_food_cart_items(context):
    guest = Guest.objects.get(user=context.auth_user, checked_in=True)
    food_cart = FoodCart.objects.filter(guest=guest).first()
    assert food_cart
    food_cart_items = FoodCartItems.objects.filter(cart=food_cart)
    assert food_cart_items.count() == 0


@then(parsers.parse("Get FoodOrder"))
def get_food_order(context):
    auth_client = context.auth_client
    response = auth_client.get(reverse(f"service:foodservice:food-order"))
    assert response.status_code == 200


@given(parsers.parse("Add Items to Laundry Cart\n{table}"))
def add_items_to_laundry_cart(context, table):
    context.populate_table_from_str(table)
    auth_client = context.auth_client
    payloads = []
    for row in context.table:
        payload = {"item": row["item"], "quantity": row["quantity"]}
        payloads.append(payload)
    response = auth_client.post(
        reverse(f"service:laundryservice:laundry-cart"), data=payloads, format="json"
    )
    assert response.status_code == 200


@given(parsers.parse("Get Laundry Cart\n{table}"))
def get_laundry_cart(context, table):
    context.populate_table_from_str(table)
    auth_client = context.auth_client
    response = auth_client.get(reverse(f"service:laundryservice:laundry-cart"))
    data = response.json()
    assert response.status_code == 200
    context.scratchpad["cart_id"] = data["data"]["id"]
    laundry_items = {
        d["item"]: d["quantity"] for d in data["data"]["laundry_cart_items"]
    }
    for row in context.table:
        assert int(row["item"]) in laundry_items
        assert laundry_items[int(row["item"])] == int(row["quantity"])


@given(parsers.parse("Place LaundryOrder from Cart"))
def place_laundry_order_from_cart(context):
    auth_client = context.auth_client
    cart_id = context.scratchpad["cart_id"]
    response = auth_client.post(
        reverse(f"service:laundryservice:laundry-order"), data={"cart_id": cart_id}
    )
    assert response.status_code == 201


@then(parsers.parse("Get FoodPartnerOrderView"))
def get_food_partner_order_view(context):
    auth_client = context.auth_client
    response = auth_client.get(reverse("service:foodservice:food-order-partner-list"))
    logger.info(f"Response: {response.json()}")
    data = [o["id"] for o in response.json()]
    context.scratchpad["order_ids"] = data
    assert response.status_code == 200


@then(parsers.parse("Accept FoodOrder"))
def accept_food_order(context):
    auth_client = context.auth_client
    response = auth_client.post(
        reverse("service:foodservice:food-order-partner-list"),
        data={"order_id": context.scratchpad["order_ids"][0], "action": "ACCEPTED"},
    )
    assert response.status_code == 200


@then(parsers.parse("Validate FoodOrder Status"))
def validate_food_order_status(context):
    auth_client = context.auth_client
    response = auth_client.get(reverse(f"service:foodservice:food-order-partner-list"))
    data = response.json()
    assert response.status_code == 200
    assert data[0]["status"] == "ACCEPTED"


@then(parsers.parse("Validate FoodCart Status"))
def validate_food_cart_status(context):
    from service.models import BaseCart

    auth_client = context.auth_client
    order = FoodOrder.objects.get(id=context.scratchpad["order_ids"][0])
    cart = order.cart
    assert cart.ordered_status == BaseCart.CartOrderedStatus.ACCEPTED.value
