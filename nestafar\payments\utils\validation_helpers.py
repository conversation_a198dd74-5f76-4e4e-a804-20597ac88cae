"""
Payment Validation Helpers

Validation functions for payment amounts, splits, and account details.
"""

import re
from decimal import Decimal, InvalidOperation
from typing import Dict, List, Optional, Tuple
from django.core.validators import validate_email
from django.core.exceptions import ValidationError


def validate_payment_amount(
    amount: Decimal,
    min_amount: Decimal = Decimal("1.00"),
    max_amount: Decimal = Decimal("100000.00"),
) -> Tuple[bool, Optional[str]]:
    """
    Validate payment amount against configured limits.

    Args:
        amount: Payment amount to validate
        min_amount: Minimum allowed amount
        max_amount: Maximum allowed amount

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not isinstance(amount, Decimal):
        try:
            amount = Decimal(str(amount))
        except (ValueError, TypeError):
            return False, "Invalid amount format"

    if amount <= 0:
        return False, "Amount must be greater than zero"

    if amount < min_amount:
        return False, f"Amount must be at least ₹{min_amount}"

    if amount > max_amount:
        return False, f"Amount cannot exceed ₹{max_amount}"

    # Check decimal places (max 2)
    if amount.as_tuple().exponent < -2:
        return False, "Amount cannot have more than 2 decimal places"

    return True, None


def validate_split_amounts(
    splits: List[Dict], total_amount: Decimal, tolerance: Decimal = Decimal("0.01")
) -> Tuple[bool, List[str]]:
    """
    Validate payment split amounts.

    Args:
        splits: List of split dictionaries
        total_amount: Expected total amount
        tolerance: Acceptable difference tolerance

    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []

    if not splits:
        errors.append("At least one split is required")
        return False, errors

    # Validate individual split amounts
    split_total = Decimal("0.00")
    for i, split in enumerate(splits):
        amount = split.get("amount")
        if not amount:
            errors.append(f"Split {i+1}: Amount is required")
            continue

        try:
            amount = Decimal(str(amount))
        except (ValueError, TypeError):
            errors.append(f"Split {i+1}: Invalid amount format")
            continue

        if amount <= 0:
            errors.append(f"Split {i+1}: Amount must be greater than zero")
            continue

        split_total += amount

    # Validate total
    if not errors:
        difference = abs(split_total - total_amount)
        if difference > tolerance:
            errors.append(
                f"Split total ₹{split_total} does not match payment amount ₹{total_amount} "
                f"(difference: ₹{difference})"
            )

    return len(errors) == 0, errors


def validate_account_details(account_data: Dict) -> Tuple[bool, List[str]]:
    """
    Validate Razorpay account details.

    Args:
        account_data: Account details dictionary

    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []

    # Validate account ID
    account_id = account_data.get("account_id", "").strip()
    if not account_id:
        errors.append("Account ID is required")
    elif not re.match(r"^acc_[A-Za-z0-9]+$", account_id):
        errors.append("Invalid Razorpay account ID format")

    # Validate account name
    account_name = account_data.get("name", "").strip()
    if not account_name:
        errors.append("Account name is required")
    elif len(account_name) > 200:
        errors.append("Account name cannot exceed 200 characters")

    return len(errors) == 0, errors


def validate_customer_details(customer_data: Dict) -> Tuple[bool, List[str]]:
    """
    Validate customer details for payment processing.

    Args:
        customer_data: Customer details dictionary

    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []

    # Validate name
    name = customer_data.get("name", "").strip()
    if not name:
        errors.append("Customer name is required")
    elif len(name) > 100:
        errors.append("Customer name cannot exceed 100 characters")

    # Validate email (optional but must be valid if provided)
    email = customer_data.get("email", "").strip()
    if email:
        try:
            validate_email(email)
        except ValidationError:
            errors.append("Invalid email format")

    # Validate phone number (optional but must be valid if provided)
    phone = customer_data.get("contact", "").strip()
    if phone:
        # Remove non-numeric characters for validation
        phone_digits = "".join(filter(str.isdigit, phone))
        if len(phone_digits) < 10:
            errors.append("Phone number must have at least 10 digits")
        elif len(phone_digits) > 15:
            errors.append("Phone number cannot exceed 15 digits")

    return len(errors) == 0, errors


def validate_commission_rate(
    rate: Decimal,
    min_rate: Decimal = Decimal("0.00"),
    max_rate: Decimal = Decimal("100.00"),
) -> Tuple[bool, Optional[str]]:
    """
    Validate commission rate.

    Args:
        rate: Commission rate to validate
        min_rate: Minimum allowed rate
        max_rate: Maximum allowed rate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not isinstance(rate, Decimal):
        try:
            rate = Decimal(str(rate))
        except (ValueError, TypeError):
            return False, "Invalid commission rate format"

    if rate < min_rate:
        return False, f"Commission rate cannot be less than {min_rate}%"

    if rate > max_rate:
        return False, f"Commission rate cannot exceed {max_rate}%"

    # Check decimal places (max 2)
    if rate.as_tuple().exponent < -2:
        return False, "Commission rate cannot have more than 2 decimal places"

    return True, None


def validate_payment_context(context: str) -> Tuple[bool, Optional[str]]:
    """
    Validate payment context.

    Args:
        context: Payment context to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    from ..constants import PaymentContext

    valid_contexts = [choice[0] for choice in PaymentContext.choices]

    if context not in valid_contexts:
        return (
            False,
            f"Invalid payment context. Must be one of: {', '.join(valid_contexts)}",
        )

    return True, None


def validate_transfer_data(transfer_data: Dict) -> Tuple[bool, List[str]]:
    """
    Validate transfer data for Razorpay Route.

    Args:
        transfer_data: Transfer data dictionary

    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []

    # Validate account ID
    account_id = transfer_data.get("account")
    if not account_id:
        errors.append("Transfer account ID is required")
    elif not isinstance(account_id, str) or not account_id.startswith("acc_"):
        errors.append("Invalid transfer account ID format")

    # Validate amount
    amount = transfer_data.get("amount")
    if not amount:
        errors.append("Transfer amount is required")
    else:
        try:
            amount = int(amount)
            if amount <= 0:
                errors.append("Transfer amount must be greater than zero")
        except (ValueError, TypeError):
            errors.append("Invalid transfer amount format")

    # Validate currency
    currency = transfer_data.get("currency", "INR")
    if currency != "INR":
        errors.append("Only INR currency is supported")

    return len(errors) == 0, errors


def validate_webhook_payload(payload: Dict) -> Tuple[bool, List[str]]:
    """
    Validate webhook payload structure.

    Args:
        payload: Webhook payload dictionary

    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []

    # Check required fields
    required_fields = ["event", "payload"]
    for field in required_fields:
        if field not in payload:
            errors.append(f"Missing required field: {field}")

    # Validate event type
    event_type = payload.get("event")
    if event_type:
        from ..utils.razorpay_helpers import is_webhook_event_supported

        if not is_webhook_event_supported(event_type):
            errors.append(f"Unsupported webhook event type: {event_type}")

    # Validate payload structure
    webhook_payload = payload.get("payload", {})
    if not isinstance(webhook_payload, dict):
        errors.append("Webhook payload must be a dictionary")

    return len(errors) == 0, errors


def validate_payment_link_data(link_data: Dict) -> Tuple[bool, List[str]]:
    """
    Validate payment link creation data.

    Args:
        link_data: Payment link data dictionary

    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []

    # Validate amount
    amount = link_data.get("amount")
    if not amount:
        errors.append("Payment amount is required")
    else:
        # Convert to Decimal and validate. Conversion may raise on bad input
        try:
            decimal_amount = Decimal(str(amount))
            is_valid, error_msg = validate_payment_amount(decimal_amount)
            if not is_valid:
                errors.append(f"Amount validation failed: {error_msg}")
        except (InvalidOperation, TypeError, ValueError) as ex:
            errors.append(f"Amount validation failed: {str(ex)}")

    # Validate description
    description = link_data.get("description", "").strip()
    if not description:
        errors.append("Payment description is required")
    elif len(description) > 255:
        errors.append("Description cannot exceed 255 characters")

    # Validate customer details
    customer = link_data.get("customer", {})
    if customer:
        is_valid, customer_errors = validate_customer_details(customer)
        if not is_valid:
            errors.extend([f"Customer {error}" for error in customer_errors])

    return len(errors) == 0, errors
