from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from io import BytesIO
import logging

logger = logging.getLogger(__name__)


def send_invoice_email(guest, room, pdf_buffer, property_name):
    """
    Send invoice PDF as email attachment to the guest.
    
    Args:
        guest: Guest object
        room: Room object
        pdf_buffer: BytesIO object containing PDF content
        property_name: Name of the property
        
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        # Check if guest has email
        if not guest.user.email:
            logger.warning(f"Guest {guest.user.name} does not have an email address")
            return False
        
        # Prepare email content
        subject = f"Invoice - {property_name} - Room {room.room_no}"
        
        # Create HTML email content
        html_content = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
                    Thank you for staying with us!
                </h2>
                
                <p>Dear {guest.user.name},</p>
                
                <p>We hope you had a wonderful stay at <strong>{property_name}</strong> in Room <strong>{room.room_no}</strong>.</p>
                
                <p>Please find attached your detailed invoice for all services used during your stay.</p>
                
                <div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #3498db; margin: 20px 0;">
                    <h3 style="margin-top: 0; color: #2c3e50;">Invoice Summary:</h3>
                    <ul style="margin: 0;">
                        <li><strong>Property:</strong> {property_name}</li>
                        <li><strong>Room:</strong> {room.room_no} - {room.type_of_room}</li>
                        <li><strong>Guest:</strong> {guest.user.name}</li>
                        <li><strong>Check-in:</strong> {guest.check_in_date.strftime('%B %d, %Y at %I:%M %p') if guest.check_in_date else 'N/A'}</li>
                        <li><strong>Check-out:</strong> {guest.check_out_date.strftime('%B %d, %Y at %I:%M %p') if guest.check_out_date else 'N/A'}</li>
                    </ul>
                </div>
                
                <p>The attached PDF contains a complete breakdown of:</p>
                <ul>
                    <li>Room stay charges</li>
                    <li>All service orders (food, laundry, transport, rental)</li>
                    <li>Taxes and additional charges</li>
                    <li>Total amount</li>
                </ul>
                
                <p>If you have any questions about your invoice, please don't hesitate to contact us.</p>
                
                <p>We look forward to welcoming you back soon!</p>
                
                <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                
                <p style="font-size: 12px; color: #666;">
                    This is an automated email. Please do not reply to this message.<br>
                    For any queries, please contact our customer service.
                </p>
            </div>
        </body>
        </html>
        """
        
        # Create plain text version
        text_content = f"""
        Dear {guest.user.name},
        
        Thank you for staying with us at {property_name} in Room {room.room_no}.
        
        Please find attached your detailed invoice for all services used during your stay.
        
        Invoice Summary:
        - Property: {property_name}
        - Room: {room.room_no} - {room.type_of_room}
        - Guest: {guest.user.name}
        - Check-in: {guest.check_in_date.strftime('%B %d, %Y at %I:%M %p') if guest.check_in_date else 'N/A'}
        - Check-out: {guest.check_out_date.strftime('%B %d, %Y at %I:%M %p') if guest.check_out_date else 'N/A'}
        
        The attached PDF contains a complete breakdown of room stay charges, all service orders, taxes, and total amount.
        
        If you have any questions about your invoice, please contact us.
        
        We look forward to welcoming you back soon!
        
        Best regards,
        {property_name} Team
        """
        
        # Create email message
        email = EmailMultiAlternatives(
            subject=subject,
            body=text_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[guest.user.email]
        )
        
        # Attach HTML content
        email.attach_alternative(html_content, "text/html")
        
        # Attach PDF
        pdf_buffer.seek(0)
        email.attach(
            filename=f"Invoice_{property_name}_{room.room_no}.pdf",
            content=pdf_buffer.read(),
            mimetype="application/pdf"
        )
        
        # Send email
        email.send()
        
        logger.info(f"Invoice email sent successfully to {guest.user.email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send invoice email to {guest.user.email}: {str(e)}")
        return False


def send_invoice_to_all_guests(guests, room, pdf_buffer, property_name):
    """
    Send invoice email to all guests who have email addresses.
    
    Args:
        guests: QuerySet or list of Guest objects
        room: Room object
        pdf_buffer: BytesIO object containing PDF content
        property_name: Name of the property
        
    Returns:
        dict: Summary of email sending results
    """
    results = {
        'sent': 0,
        'failed': 0,
        'no_email': 0,
        'details': []
    }
    
    for guest in guests:
        if guest.user.email:
            success = send_invoice_email(guest, room, pdf_buffer, property_name)
            if success:
                results['sent'] += 1
                results['details'].append({
                    'guest_name': guest.user.name,
                    'email': guest.user.email,
                    'status': 'sent'
                })
            else:
                results['failed'] += 1
                results['details'].append({
                    'guest_name': guest.user.name,
                    'email': guest.user.email,
                    'status': 'failed'
                })
        else:
            results['no_email'] += 1
            results['details'].append({
                'guest_name': guest.user.name,
                'email': None,
                'status': 'no_email'
            })
    
    return results


def send_invoice_to_partner(pre_checkin, pdf_buffer, property_name):
    """
    Send invoice PDF as email attachment to the partner/property owner.
    
    Args:
        pre_checkin: PreCheckin object
        pdf_buffer: BytesIO object containing PDF content
        property_name: Name of the property
        
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        # Get partner email from property
        partner_email = None
        if hasattr(pre_checkin.property, 'partner') and pre_checkin.property.partner:
            if hasattr(pre_checkin.property.partner, 'user') and pre_checkin.property.partner.user:
                partner_email = pre_checkin.property.partner.user.email
        
        if not partner_email:
            logger.warning(f"No partner email found for property {property_name}")
            return False
        
        # Prepare email content
        subject = f"Checkout Invoice - {property_name} - Reservation {str(pre_checkin.id)[:8]}..."
        
        # Create HTML email content
        html_content = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
                    Guest Checkout Completed
                </h2>
                
                <p>Dear Partner,</p>
                
                <p>A guest has completed their checkout at <strong>{property_name}</strong>.</p>
                
                <div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #3498db; margin: 20px 0;">
                    <h3 style="margin-top: 0; color: #2c3e50;">Reservation Summary:</h3>
                    <ul style="margin: 0;">
                        <li><strong>Property:</strong> {property_name}</li>
                        <li><strong>Reservation ID:</strong> {str(pre_checkin.id)[:8]}...</li>
                        <li><strong>Expected Check-in:</strong> {pre_checkin.expected_checkin.strftime('%B %d, %Y at %I:%M %p')}</li>
                        <li><strong>Expected Check-out:</strong> {pre_checkin.get_expected_checkout().strftime('%B %d, %Y at %I:%M %p')}</li>
                        <li><strong>Number of Rooms:</strong> {pre_checkin.number_of_rooms}</li>
                        <li><strong>Stay Duration:</strong> {pre_checkin.stay_duration} days</li>
                    </ul>
                </div>
                
                <p>The attached PDF contains a complete breakdown of:</p>
                <ul>
                    <li>Room-wise stay charges</li>
                    <li>All service orders by room and guest</li>
                    <li>Taxes and additional charges</li>
                    <li>Total amount for the reservation</li>
                </ul>
                
                <p>This invoice can be used for your records and accounting purposes.</p>
                
                <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                
                <p style="font-size: 12px; color: #666;">
                    This is an automated email. Please do not reply to this message.<br>
                    For any queries, please contact our support team.
                </p>
            </div>
        </body>
        </html>
        """
        
        # Create plain text version
        text_content = f"""
        Dear Partner,
        
        A guest has completed their checkout at {property_name}.
        
        Reservation Summary:
        - Property: {property_name}
        - Reservation ID: {str(pre_checkin.id)[:8]}...
        - Expected Check-in: {pre_checkin.expected_checkin.strftime('%B %d, %Y at %I:%M %p')}
        - Expected Check-out: {pre_checkin.get_expected_checkout().strftime('%B %d, %Y at %I:%M %p')}
        - Number of Rooms: {pre_checkin.number_of_rooms}
        - Stay Duration: {pre_checkin.stay_duration} days
        
        The attached PDF contains a complete breakdown of room-wise stay charges, all service orders, taxes, and total amount.
        
        This invoice can be used for your records and accounting purposes.
        
        Best regards,
        Nestafar Team
        """
        
        # Create email message
        email = EmailMultiAlternatives(
            subject=subject,
            body=text_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[partner_email]
        )
        
        # Attach HTML content
        email.attach_alternative(html_content, "text/html")
        
        # Attach PDF
        pdf_buffer.seek(0)
        email.attach(
            filename=f"Checkout_Invoice_{property_name}_{str(pre_checkin.id)[:8]}.pdf",
            content=pdf_buffer.read(),
            mimetype="application/pdf"
        )
        
        # Send email
        email.send()
        
        logger.info(f"Checkout invoice email sent successfully to partner {partner_email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send checkout invoice email to partner: {str(e)}")
        return False
