import enum
import uuid

from django.db import models
from django.core.exceptions import ValidationError
from phonenumber_field.modelfields import PhoneN<PERSON>berField

from core.models import User, UserProfile, PartnerProfile
from notification.templates.templates import *
from notification.templates.flow_templates import (
    onboarding_reminder_template,
    onboarding_completed_template,
    room_allotment_template,
    guest_arrived_welcome_template,
    new_service_available_template,
)


class NotificationChannel(enum.Enum):
    MESSAGE = "MESSAGE"
    EMAIL = "EMAIL"
    WHATSAPP = "WHATSAPP"
    PUSH = "PUSH"


NotificationChannelHandler = {
    NotificationChannel.PUSH.value: "notification.channel.firebase.FirebaseChannel",
    NotificationChannel.MESSAGE.value: "notification.channel.message.MessageChannel",
    NotificationChannel.WHATSAPP.value: "notification.channel.whatsapp.WhatsAppChannel",
    NotificationChannel.EMAIL.value: "notification.channel.email.EmailChannel",
}


class NotificationCategory(enum.Enum):
    USER_CHECKIN_INITIATED = "USER_CHECKIN_INITIATED"
    USER_CHECKIN = "USER_CHECKIN"
    USER_CHECKOUT = "USER_CHECKOUT"
    USER_ORDER_ACCEPTED = "USER_ORDER_ACCEPTED"
    USER_ORDER_CANCELLED = "USER_ORDER_CANCELLED"
    USER_ORDER_COMPLETED = "USER_ORDER_COMPLETED"
    USER_ORDER_PLACED = "USER_ORDER_PLACED"
    USER_ORDER_ONGOING = "USER_ORDER_ONGOING"
    USER_ORDER_REJECTED = "USER_ORDER_REJECTED"
    PARTNER_ORDER_PLACED = "PARTNER_ORDER_PLACED"
    PARTNER_ORDER_ACCEPTED = "PARTNER_ORDER_ACCEPTED"
    PARTNER_ORDER_CANCELLED = "PARTNER_ORDER_CANCELLED"
    PARTNER_ORDER_COMPLETED = "PARTNER_ORDER_COMPLETED"
    PARTNER_ORDER_ONGOING = "PARTNER_ORDER_ONGOING"
    PARTNER_ORDER_REJECTED = "PARTNER_ORDER_REJECTED"
    PRECHECKIN_CREATED = "PRECHECKIN_CREATED"
    PRECHECKIN_CONFIRMED = "PRECHECKIN_CONFIRMED"
    PRECHECKIN_STATUS_CHANGED = "PRECHECKIN_STATUS_CHANGED"
    DAILY_SUMMARY_GUEST = "DAILY_SUMMARY_GUEST"
    DAILY_SUMMARY_PARTNER = "DAILY_SUMMARY_PARTNER"

    # New WhatsApp notification categories for complete flow
    SIGNUP_SUCCESSFUL = "SIGNUP_SUCCESSFUL"
    ONBOARDING_REMINDER = "ONBOARDING_REMINDER"
    ONBOARDING_COMPLETED = "ONBOARDING_COMPLETED"
    PRECHECKIN_REMINDER = "PRECHECKIN_REMINDER"
    PRECHECKIN_CANCELLATION_WARNING = "PRECHECKIN_CANCELLATION_WARNING"
    ROOM_ALLOTMENT = "ROOM_ALLOTMENT"
    GUEST_ARRIVED_WELCOME = "GUEST_ARRIVED_WELCOME"
    CHECKIN_SUCCESSFUL = "CHECKIN_SUCCESSFUL"
    CHECKOUT_BILLS = "CHECKOUT_BILLS"
    REVIEW_REQUEST = "REVIEW_REQUEST"
    ORDER_CONFIRMED = "ORDER_CONFIRMED"
    ORDER_READY = "ORDER_READY"
    VENDOR_NEW_ORDER = "VENDOR_NEW_ORDER"
    VENDOR_ORDER_REMINDER = "VENDOR_ORDER_REMINDER"
    NEW_SERVICE_AVAILABLE = "NEW_SERVICE_AVAILABLE"
    DINNER_REMINDER = "DINNER_REMINDER"
    WEEKLY_REPORT = "WEEKLY_REPORT"
    SERVICE_HIDDEN_NOTIFICATION = "SERVICE_HIDDEN_NOTIFICATION"
    SERVICE_RESTORED_NOTIFICATION = "SERVICE_RESTORED_NOTIFICATION"

    # Additional categories for comprehensive testing
    WELCOME_MESSAGE = "WELCOME_MESSAGE"
    CHECKOUT_REMINDER = "CHECKOUT_REMINDER"
    CHECKOUT_SUCCESSFUL = "CHECKOUT_SUCCESSFUL"
    SERVICE_REQUEST_RECEIVED = "SERVICE_REQUEST_RECEIVED"
    SERVICE_IN_PROGRESS = "SERVICE_IN_PROGRESS"
    SERVICE_COMPLETED = "SERVICE_COMPLETED"
    FOOD_ORDER_PLACED = "FOOD_ORDER_PLACED"
    FOOD_ORDER_CONFIRMED = "FOOD_ORDER_CONFIRMED"
    FOOD_ORDER_READY = "FOOD_ORDER_READY"

    # Additional missing categories
    RATING_REQUEST = "RATING_REQUEST"
    DAILY_SUMMARY = "DAILY_SUMMARY"
    WEEKLY_SUMMARY = "WEEKLY_SUMMARY"
    MONTHLY_SUMMARY = "MONTHLY_SUMMARY"
    EXTERNAL_RESERVATION_ALERT = "EXTERNAL_RESERVATION_ALERT"

    # Payment system categories
    PAYMENT_LINK_CREATED = "PAYMENT_LINK_CREATED"
    PAYMENT_SUCCESS = "PAYMENT_SUCCESS"
    PAYMENT_FAILED = "PAYMENT_FAILED"  
    PAYMENT_REMINDER = "PAYMENT_REMINDER"
    TRANSFER_SUCCESS = "TRANSFER_SUCCESS"
    TRANSFER_FAILED = "TRANSFER_FAILED"

    @classmethod
    def from_name(cls, name):
        """Get NotificationCategory by its enum member name.

        Args:
            name: The enum member name (e.g., 'USER_CHECKIN_INITIATED')

        Returns:
            NotificationCategory enum member

        Raises:
            ValueError: If name is not found
        """
        try:
            return cls[name]
        except KeyError:
            raise ValueError(f"NotificationCategory with name '{name}' not found")


NotificationTemplates = {
    NotificationCategory.USER_CHECKIN_INITIATED.name: (
        checkin_initiated_template,
        {"username": None, "room_no": None, "property_name": None},
    ),
    NotificationCategory.USER_CHECKOUT.name: (
        checkout_template,
        {"username": None, "property_name": None},
    ),
    NotificationCategory.USER_ORDER_ACCEPTED.name: (
        order_accepted_template,
        {"username": None, "order_id": None},
    ),
    NotificationCategory.USER_ORDER_CANCELLED.name: (
        order_cancelled_template,
        {"username": None, "order_id": None},
    ),
    NotificationCategory.USER_ORDER_COMPLETED.name: (
        order_completed_template,
        {"username": None, "order_id": None},
    ),
    NotificationCategory.USER_ORDER_PLACED.name: (
        order_placed_template,
        {"username": None, "order_id": None},
    ),
    NotificationCategory.USER_ORDER_ONGOING.name: (
        order_ongoing_template,
        {"username": None, "order_id": None, "vendor_name": None, "estimated_time": None, "contact_number": None},
    ),
    NotificationCategory.USER_ORDER_REJECTED.name: (
        order_rejected_template,
        {"username": None, "order_id": None, "service_type": None, "rejection_reason": None, "refund_amount": None},
    ),
    NotificationCategory.PARTNER_ORDER_PLACED.name: (
        partner_order_placed_template,
        {"partner_name": None, "order_id": None, "room_no": None, "guest_name": None},
    ),
    NotificationCategory.PARTNER_ORDER_ACCEPTED.name: (
        partner_order_accepted_template,
        {"partner_name": None, "order_id": None, "room_no": None, "guest_name": None},
    ),
    NotificationCategory.PARTNER_ORDER_ONGOING.name: (
        partner_order_ongoing_template,
        {"partner_name": None, "order_id": None, "room_no": None, "guest_name": None, "estimated_completion": None},
    ),
    NotificationCategory.PARTNER_ORDER_COMPLETED.name: (
        partner_order_completed_template,
        {"partner_name": None, "order_id": None, "room_no": None, "guest_name": None},
    ),
    NotificationCategory.PARTNER_ORDER_CANCELLED.name: (
        partner_order_cancelled_template,
        {"partner_name": None, "order_id": None, "room_no": None, "guest_name": None},
    ),
    NotificationCategory.PARTNER_ORDER_REJECTED.name: (
        partner_order_rejected_template,
        {"partner_name": None, "order_id": None, "room_no": None, "guest_name": None, "rejection_reason": None, "refund_amount": None, "dashboard_link": None},
    ),
    NotificationCategory.PRECHECKIN_CREATED.name: (
        pre_checkin_created_template,
        {
            "guest_name": None,
            "property_owner_name": None,
            "expected_date": None,
            "room_number": None,
            "precheckin_link": None,
        },
    ),
    NotificationCategory.PRECHECKIN_CONFIRMED.name: (
        pre_checkin_confirmed_template,
        {
            "guest_name": None,
            "property_owner_name": None,
            "expected_date": None,
            "room_number": None,
        },
    ),
    NotificationCategory.PRECHECKIN_STATUS_CHANGED.name: (
        precheckin_status_changed_template,
        {
            "guest_name": None,
            "property_owner_name": None,
            "status": None,
            "room_number": None,
        },
    ),
    NotificationCategory.DAILY_SUMMARY_GUEST.name: (
        daily_summary_guest_template,
        {
            "username": None,
            "total_orders": None,
            "total_spent": None,
            "property_name": None,
        },
    ),
    NotificationCategory.DAILY_SUMMARY_PARTNER.name: (
        daily_summary_partner_template,
        {
            "partner_name": None,
            "total_orders": None,
            "total_revenue": None,
            "property_name": None,
        },
    ),
    # New WhatsApp notification templates
    NotificationCategory.SIGNUP_SUCCESSFUL.name: (
        signup_successful_template,
        {"partner_name": None, "property_name": None},
    ),
    NotificationCategory.ONBOARDING_REMINDER.name: (
        onboarding_reminder_template,
        {
            "partner_name": None,
            "property_name": None,
            "completion_percentage": None,
            "missing_steps": None,
        },
    ),
    NotificationCategory.ONBOARDING_COMPLETED.name: (
        onboarding_completed_template,
        {"partner_name": None, "property_name": None},
    ),
    NotificationCategory.PRECHECKIN_REMINDER.name: (
        precheckin_reminder_template,
        {
            "guest_name": None,
            "property_name": None,
            "checkin_date": None,
            "hours_remaining": None,
        },
    ),
    NotificationCategory.PRECHECKIN_CANCELLATION_WARNING.name: (
        precheckin_cancellation_warning_template,
        {
            "guest_name": None,
            "property_name": None,
            "checkin_date": None,
            "hours_remaining": None,
        },
    ),
    NotificationCategory.ROOM_ALLOTMENT.name: (
        room_allotment_template,
        {
            "guest_name": None,
            "property_name": None,
            "room_number": None,
            "checkin_date": None,
        },
    ),
    NotificationCategory.GUEST_ARRIVED_WELCOME.name: (
        guest_arrived_welcome_template,
        {"guest_name": None, "property_name": None, "room_number": None},
    ),
    NotificationCategory.CHECKIN_SUCCESSFUL.name: (
        checkin_successful_template,
        {
            "guest_name": None,
            "property_name": None,
            "room_details": None,
            "checkout_date": None,
        },
    ),
    NotificationCategory.CHECKOUT_BILLS.name: (
        checkout_bills_template,
        {
            "guest_name": None,
            "property_name": None,
            "checkout_date": None,
            "total_amount": None,
        },
    ),
    NotificationCategory.REVIEW_REQUEST.name: (
        review_request_template,
        {"guest_name": None, "property_name": None},
    ),
    NotificationCategory.ORDER_CONFIRMED.name: (
        order_confirmed_template,
        {
            "guest_name": None,
            "service_type": None,
            "order_id": None,
            "order_items": None,
            "total_amount": None,
            "delivery_time": None,
        },
    ),
    NotificationCategory.ORDER_READY.name: (
        order_ready_template,
        {
            "guest_name": None,
            "service_type": None,
            "order_id": None,
            "status": None,
            "total_amount": None,
            "instructions": None,
        },
    ),
    NotificationCategory.VENDOR_NEW_ORDER.name: (
        vendor_new_order_template,
        {
            "vendor_name": None,
            "order_id": None,
            "guest_name": None,
            "property_name": None,
            "order_items": None,
            "total_amount": None,
        },
    ),
    NotificationCategory.VENDOR_ORDER_REMINDER.name: (
        vendor_order_reminder_template,
        {
            "vendor_name": None,
            "minutes_pending": None,
            "order_id": None,
            "total_amount": None,
        },
    ),
    NotificationCategory.DINNER_REMINDER.name: (
        dinner_reminder_template,
        {"guest_name": None, "property_name": None},
    ),
    NotificationCategory.NEW_SERVICE_AVAILABLE.name: (
        new_service_available_template,
        {
            "guest_name": None,
            "service_name": None,
            "service_type": None,
            "property_name": None,
        },
    ),
    NotificationCategory.WEEKLY_REPORT.name: (
        weekly_report_template,
        {
            "partner_name": None,
            "property_name": None,
            "week_start": None,
            "week_end": None,
            "reservations": None,
            "occupancy_rate": None,
            "avg_orders": None,
            "gmv": None,
            "commission": None,
            "recommendations": None,
        },
    ),
    NotificationCategory.SERVICE_HIDDEN_NOTIFICATION.name: (
        service_hidden_notification_template,
        {"guest_name": None, "service_name": None, "property_name": None},
    ),
    NotificationCategory.SERVICE_RESTORED_NOTIFICATION.name: (
        service_restored_notification_template,
        {"guest_name": None, "service_name": None, "property_name": None},
    ),
    # Additional templates for comprehensive testing
    NotificationCategory.WELCOME_MESSAGE.name: (
        welcome_message_template,
        {
            "guest_name": None,
            "property_name": None,
            "room_number": None,
            "wifi_password": None,
        },
    ),
    NotificationCategory.CHECKOUT_REMINDER.name: (
        checkout_reminder_template,
        {
            "guest_name": None,
            "property_name": None,
            "checkout_time": None,
            "extension_available": None,
        },
    ),
    NotificationCategory.CHECKOUT_SUCCESSFUL.name: (
        checkout_successful_template,
        {
            "guest_name": None,
            "property_name": None,
            "total_amount": None,
            "payment_method": None,
        },
    ),
    NotificationCategory.SERVICE_REQUEST_RECEIVED.name: (
        service_request_received_template,
        {
            "guest_name": None,
            "service_name": None,
            "estimated_time": None,
            "property_name": None,
        },
    ),
    NotificationCategory.SERVICE_IN_PROGRESS.name: (
        service_in_progress_template,
        {"guest_name": None, "service_name": None, "eta": None, "property_name": None},
    ),
    NotificationCategory.SERVICE_COMPLETED.name: (
        service_completed_template,
        {
            "guest_name": None,
            "service_name": None,
            "completion_time": None,
            "property_name": None,
        },
    ),
    NotificationCategory.FOOD_ORDER_PLACED.name: (
        food_order_placed_template,
        {
            "guest_name": None,
            "property_name": None,
            "order_items": None,
            "total_amount": None,
        },
    ),
    NotificationCategory.FOOD_ORDER_CONFIRMED.name: (
        food_order_confirmed_template,
        {
            "guest_name": None,
            "property_name": None,
            "order_items": None,
            "delivery_time": None,
        },
    ),
    NotificationCategory.FOOD_ORDER_READY.name: (
        food_order_ready_template,
        {
            "guest_name": None,
            "property_name": None,
            "order_items": None,
            "total_amount": None,
        },
    ),
    # Additional missing templates
    NotificationCategory.RATING_REQUEST.name: (
        rating_request_template,
        {"guest_name": None, "property_name": None},
    ),
    NotificationCategory.DAILY_SUMMARY.name: (
        daily_summary_template,
        {
            "property_name": None,
            "total_checkins": None,
            "total_checkouts": None,
            "occupancy_rate": None,
            "total_revenue": None,
        },
    ),
    NotificationCategory.WEEKLY_SUMMARY.name: (
        weekly_summary_template,
        {
            "property_name": None,
            "week_start": None,
            "total_guests": None,
            "average_occupancy": None,
            "total_revenue": None,
        },
    ),
    NotificationCategory.MONTHLY_SUMMARY.name: (
        monthly_summary_template,
        {
            "property_name": None,
            "month": None,
            "total_bookings": None,
            "total_revenue": None,
            "top_service": None,
        },
    ),
    NotificationCategory.EXTERNAL_RESERVATION_ALERT.name: (
        external_reservation_alert_template,
        {
            "guest_name": None,
            "property_name": None,
            "checkin_date": None,
            "checkout_date": None,
            "guest_count": None,
            "room_type": None,
            "booking_source": None,
            "total_amount": None,
            "external_booking_id": None,
        },
    ),
    # Payment system templates
    NotificationCategory.PAYMENT_LINK_CREATED.name: (
        payment_link_template,
        {
            "customer_name": None,
            "context_type": None,
            "amount": None,
            "property_name": None,
            "reference_number": None,
            "payment_url": None,
        },
    ),
    NotificationCategory.PAYMENT_SUCCESS.name: (
        payment_success_template,
        {
            "customer_name": None,
            "amount": None,
            "property_name": None,
            "payment_method": None,
            "reference_number": None,
            "payment_id": None,
        },
    ),
    NotificationCategory.PAYMENT_FAILED.name: (
        payment_failure_template,
        {
            "customer_name": None,
            "amount": None,
            "property_name": None,
            "reference_number": None,
            "error_reason": None,
            "retry_url": None,
        },
    ),
    NotificationCategory.PAYMENT_REMINDER.name: (
        payment_reminder_template,
        {
            "customer_name": None,
            "amount": None,
            "property_name": None,
            "reference_number": None,
            "hours_remaining": None,
            "payment_url": None,
        },
    ),
    NotificationCategory.TRANSFER_SUCCESS.name: (
        transfer_success_template,
        {
            "recipient_name": None,
            "amount": None,
            "payment_reference": None,
            "transfer_id": None,
            "settlement_date": None,
            "recipient_type": None,
        },
    ),
    NotificationCategory.TRANSFER_FAILED.name: (
        transfer_failure_template,
        {
            "recipient_name": None,
            "amount": None,
            "payment_reference": None,
            "recipient_type": None,
            "error_message": None,
        },
    ),
}


class UserNotificationProfile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(
        UserProfile, on_delete=models.CASCADE, related_name="notification_profile"
    )

    def clean(self):
        if self.user and self.user.user.is_partner:
            raise ValidationError(
                "User is a partner, create a partner notification profile instead"
            )

    def __str__(self) -> str:
        return (
            self.user.user.name
            if self.user and self.user.user
            else f"UserNotificationProfile({self.id})"
        )


class PartnerNotificationProfile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    partner = models.OneToOneField(
        PartnerProfile, on_delete=models.CASCADE, related_name="notification_profile"
    )

    def clean(self):
        if self.partner and not self.partner.user.is_partner:
            raise ValidationError(
                "User is not a partner, create a user notification profile instead"
            )

    def __str__(self) -> str:
        return (
            self.partner.user.name
            if self.partner and self.partner.user
            else f"PartnerNotificationProfile({self.id})"
        )


class Notification(models.Model):
    class UserTypeOptions(models.IntegerChoices):
        USER = 1
        PARTNER = 2

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    category = models.CharField(
        max_length=50,
        choices=[(tag.value, tag.value) for tag in NotificationCategory],
    )
    channel = models.CharField(
        max_length=50,
        choices=[(tag.value, tag.value) for tag in NotificationChannel],
    )
    description = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    user_type = models.PositiveSmallIntegerField(choices=UserTypeOptions.choices)

    def __str__(self):
        return self.category + " - " + self.channel


class NotificationSubscription(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        UserProfile, on_delete=models.CASCADE, blank=True, null=True
    )
    partner = models.ForeignKey(
        PartnerProfile, on_delete=models.CASCADE, blank=True, null=True
    )
    notification = models.ForeignKey(
        Notification, on_delete=models.CASCADE, related_name="subscriptions"
    )
    is_active = models.BooleanField(default=True)

    def clean(self):
        if not self.user and not self.partner:
            raise ValidationError("Either user or partner must be set")
        if self.user and self.partner:
            raise ValidationError("Cannot set both user and partner")

    def __str__(self):
        if self.user:
            name = self.user.user.name if self.user.user else "Unknown User"
        elif self.partner:
            name = self.partner.user.name if self.partner.user else "Unknown Partner"
        else:
            name = "Unknown"
        return f"{name} - {self.notification.category} - {self.notification.channel}"


class NotificationLog(models.Model):
    """Model to track notification delivery and status"""

    # Message status choices for WhatsApp
    MESSAGE_STATUS_CHOICES = [
        ("sent", "Sent"),
        ("delivered", "Delivered"),
        ("read", "Read"),
        ("failed", "Failed"),
        ("pending", "Pending"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user_notification_profile = models.ForeignKey(
        UserNotificationProfile, on_delete=models.CASCADE, blank=True, null=True
    )
    partner_notification_profile = models.ForeignKey(
        PartnerNotificationProfile, on_delete=models.CASCADE, blank=True, null=True
    )
    service_partner = models.ForeignKey(
        "service.ServicePartner",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        help_text="For notifications sent to service partners",
    )
    notification = models.ForeignKey(Notification, on_delete=models.CASCADE)
    is_sent = models.BooleanField(default=False)
    is_read = models.BooleanField(default=False)
    error_msg = models.TextField(blank=True, null=True)

    # New fields for message tracking
    message_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="External message ID from service provider",
    )
    message_status = models.CharField(
        max_length=20, choices=MESSAGE_STATUS_CHOICES, default="pending"
    )
    delivery_timestamp = models.DateTimeField(blank=True, null=True)
    read_timestamp = models.DateTimeField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def clean(self):
        if not any(
            [
                self.user_notification_profile,
                self.partner_notification_profile,
                self.service_partner,
            ]
        ):
            raise ValidationError("At least one recipient must be specified")

    def __str__(self):
        return f"{self.notification.category} - {self.notification.channel} - {self.message_status}"

    class Meta:
        indexes = [
            models.Index(fields=["message_id"]),
            models.Index(fields=["message_status"]),
            models.Index(fields=["created_at"]),
        ]


class FirebaseDeviceToken(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="firebase_device_tokens"
    )
    token = models.CharField(max_length=255, unique=True)
    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.user.name + " - " + self.token


class WhatsAppContact(models.Model):
    """Model to store WhatsApp contact information for users"""

    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="whatsapp_contacts"
    )
    phone_number = PhoneNumberField(help_text="WhatsApp phone number")
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ["user", "phone_number"]

    def __str__(self):
        return f"{self.user.name} - {self.phone_number}"
