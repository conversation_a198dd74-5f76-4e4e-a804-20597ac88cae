from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from django.core.exceptions import PermissionDenied
from .models import ItinerarySpot
from .serializers import ItinerarySerializer
from nestafar.responses import (
    SuccessResponse,
    CreateResponse,
    NotFoundResponse,
    BadRequestResponse,
    NoContentResponse,
    InternalServerErrorResponse,
)


class ItinerarySpotViewSet(viewsets.ModelViewSet):
    queryset = ItinerarySpot.objects.all()
    serializer_class = ItinerarySerializer
    permission_classes = [IsAuthenticated]

    def list(self, request, *args, **kwargs):
        try:
            queryset = self.filter_queryset(self.get_queryset())
            serializer = self.get_serializer(queryset, many=True)
            return SuccessResponse(
                data=serializer.data, message="Itinerary spots retrieved successfully"
            )
        except Exception as e:
            return InternalServerErrorResponse(
                message=f"Error retrieving itinerary spots: {str(e)}"
            )

    def create(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                self.perform_create(serializer)
                return CreateResponse(
                    data=serializer.data, message="Itinerary spot created successfully"
                )
            return BadRequestResponse(
                data=serializer.errors, message="Invalid data provided"
            )
        except Exception as e:
            return InternalServerErrorResponse(
                message=f"Error creating itinerary spot: {str(e)}"
            )

    def retrieve(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return SuccessResponse(
                data=serializer.data, message="Itinerary spot retrieved successfully"
            )
        except ItinerarySpot.DoesNotExist:
            return NotFoundResponse(message="Itinerary spot not found")
        except Exception as e:
            return InternalServerErrorResponse(
                message=f"Error retrieving itinerary spot: {str(e)}"
            )

    def update(self, request, *args, **kwargs):
        try:
            try:
                instance = self.get_object()
            except ItinerarySpot.DoesNotExist:
                return NotFoundResponse(message="Itinerary spot not found")
            serializer = self.get_serializer(
                instance, data=request.data, partial=kwargs.get("partial", True)
            )
            if serializer.is_valid():
                self.perform_update(serializer)
                return SuccessResponse(
                    data=serializer.data, message="Itinerary spot updated successfully"
                )
            return BadRequestResponse(
                data=serializer.errors, message="Invalid data provided"
            )
        except Exception as e:
            return InternalServerErrorResponse(
                message=f"Error updating itinerary spot: {str(e)}"
            )

    def destroy(self, request, *args, **kwargs):
        try:
            try:
                instance = self.get_object()
            except ItinerarySpot.DoesNotExist:
                return NotFoundResponse(message="Itinerary spot not found")
            self.perform_destroy(instance)
            return NoContentResponse(message="Itinerary spot deleted successfully")
        except Exception as e:
            return InternalServerErrorResponse(
                message=f"Error deleting itinerary spot: {str(e)}"
            )
