from django.contrib import admin
from django.contrib import messages
from booking.models import (
    PreCheckinGuest,
    Profile,
    ProfileImage,
    PreCheckin,
    Reservation,
    Payment,
    AllotedRoom,
)


class ProfileImageInline(admin.TabularInline):
    model = ProfileImage
    extra = 1
    fields = ("image", "created_at")
    readonly_fields = ("created_at",)


class PaymentInline(admin.TabularInline):
    model = Payment
    extra = 1
    fields = ("amount", "payment_method", "status", "transaction_id", "created_at")
    readonly_fields = ("created_at",)


class AllotedRoomInline(admin.TabularInline):
    model = AllotedRoom
    extra = 1
    fields = ("room", "created_at")
    readonly_fields = ("created_at",)


class PreCheckinGuestInline(admin.TabularInline):
    model = PreCheckinGuest
    extra = 1
    fields = (
        "user",
        "is_primary",
        "room",
        "age",
        "id_proof",
        "is_verified",
        "created_at",
    )
    readonly_fields = ("created_at",)


@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    list_display = ("property", "partner", "phone", "email", "location")
    list_filter = ("location", "created_at")
    search_fields = ("property__name", "partner__name", "phone", "email")
    readonly_fields = ("created_at", "updated_at")
    inlines = [ProfileImageInline]
    date_hierarchy = "created_at"


@admin.register(AllotedRoom)
class AllotedRoomAdmin(admin.ModelAdmin):
    list_display = ("pre_checkin", "room", "created_at")
    list_filter = ("created_at",)
    search_fields = ("pre_checkin__property__name", "room__room_no")
    readonly_fields = ("created_at",)
    date_hierarchy = "created_at"


@admin.register(PreCheckin)
class PreCheckinAdmin(admin.ModelAdmin):
    list_display = (
        "property",
        "expected_checkin",
        "number_of_rooms",
        "total_amount",
        "payment_status",
        "status",
    )
    list_filter = ("payment_status", "status", "created_at")
    search_fields = ("property__name", "payment_id")
    readonly_fields = ("created_at",)
    inlines = [AllotedRoomInline, PreCheckinGuestInline, PaymentInline]
    date_hierarchy = "expected_checkin"
    fieldsets = (
        (
            "Basic Information",
            {"fields": ("property", "number_of_rooms", "stay_duration")},
        ),
        (
            "Check-in Details",
            {
                "fields": (
                    "expected_checkin",
                    "welcome_message",
                    "guest_address",
                    "special_requests",
                )
            },
        ),
        (
            "Payment Information",
            {
                "fields": (
                    "total_amount",
                    "amount_paid",
                    "pending_balance",
                    "payment_status",
                    "payment_id",
                )
            },
        ),
        ("Status", {"fields": ("status", "created_at")}),
    )


@admin.register(PreCheckinGuest)
class PreCheckinGuestAdmin(admin.ModelAdmin):
    list_display = (
        "pre_checkin",
        "user",
        "is_primary",
        "room",
        "is_verified",
        "created_at",
    )
    list_filter = ("is_verified", "is_primary", "created_at")
    search_fields = ("user__name", "room__room_no", "pre_checkin__property__name")
    readonly_fields = ("created_at",)
    date_hierarchy = "created_at"
    fieldsets = (
        (
            "Guest Information",
            {"fields": ("pre_checkin", "user", "room", "is_primary", "age")},
        ),
        ("ID Details", {"fields": ("id_proof", "is_verified")}),
        ("Additional Information", {"fields": ("created_at",)}),
    )


@admin.register(Reservation)
class ReservationAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "property",
        "check_in",
        "check_out",
        "status",
        "total",
        "paid",
    )
    list_filter = ("status", "created_at")
    search_fields = ("user__name", "property__name")
    readonly_fields = ("created_at", "updated_at", "total", "paid")
    date_hierarchy = "check_in"
    list_per_page = 25
    actions = ['force_delete_reservations']
    fieldsets = (
        (
            "Reservation Details",
            {"fields": ("user", "property", "check_in", "check_out", "guests")},
        ),
        ("Additional Information", {"fields": ("requests", "booking_details")}),
        ("Payment Details", {"fields": ("total", "paid")}),
        ("Status Information", {"fields": ("status", "created_at", "updated_at")}),
    )

    def delete_model(self, request, obj):
        """Override delete to handle cascade relationships properly."""
        # Get related sync logs before deletion for logging
        from pms.models import RoomBlockSyncLog
        related_logs = RoomBlockSyncLog.objects.filter(
            room_block__reservation=obj
        ).count()
        
        if related_logs > 0:
            self.message_user(
                request, 
                f"Deleting reservation {obj.id} and {related_logs} related sync log(s).",
                level=messages.WARNING
            )
        
        # This will handle the cascade deletion properly
        obj.delete()

    def delete_queryset(self, request, queryset):
        """Override bulk delete to handle cascade relationships properly."""
        for obj in queryset:
            self.delete_model(request, obj)

    def force_delete_reservations(self, request, queryset):
        """Custom admin action to force delete reservations with related objects."""
        from pms.models import RoomBlockSyncLog
        
        deleted_count = 0
        error_count = 0
        
        for reservation in queryset:
            try:
                # Count related objects for logging
                related_logs = RoomBlockSyncLog.objects.filter(
                    room_block__reservation=reservation
                ).count()
                
                # Delete the reservation (cascade will handle related objects)
                reservation.delete()
                deleted_count += 1
                
                if related_logs > 0:
                    self.message_user(
                        request,
                        f"Deleted reservation {reservation.id} with {related_logs} sync log(s)",
                        level=messages.SUCCESS
                    )
                    
            except Exception as e:
                error_count += 1
                self.message_user(
                    request,
                    f"Failed to delete reservation {reservation.id}: {str(e)}",
                    level=messages.ERROR
                )
        
        if deleted_count > 0:
            self.message_user(
                request,
                f"Successfully deleted {deleted_count} reservation(s)",
                level=messages.SUCCESS
            )
        
        if error_count > 0:
            self.message_user(
                request,
                f"Failed to delete {error_count} reservation(s)",
                level=messages.ERROR
            )

    force_delete_reservations.short_description = "Force delete selected reservations"


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ("pre_checkin", "amount", "payment_method", "status", "created_at")
    list_filter = ("payment_method", "status", "created_at")
    search_fields = ("transaction_id", "pre_checkin__property__name")
    readonly_fields = ("created_at",)
    date_hierarchy = "created_at"
    fieldsets = (
        (
            "Payment Details",
            {"fields": ("pre_checkin", "amount", "payment_method", "transaction_id")},
        ),
        ("Status Information", {"fields": ("status", "payment_details", "created_at")}),
    )
