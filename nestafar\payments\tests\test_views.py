"""
Payment Views Tests

Tests for payment system API views including checkout integration,
precheckin integration, and webhook handling.
"""

import json
from decimal import Decimal
from unittest.mock import patch, Mock
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from ..models import PaymentIntent, PaymentWebhookEvent
from ..constants import PaymentContext, PaymentStatus
from .test_base import PaymentAPITestBase


class CheckoutIntegrationViewsTest(PaymentAPITestBase):
    """Tests for checkout integration API views"""
    
    def test_create_checkout_payment_authenticated_partner(self):
        """Test creating checkout payment as authenticated partner"""
        self.authenticate_as_partner()
        
        url = reverse('create-checkout-payment', kwargs={'guest_id': self.guest.id})
        data = {
            'custom_amount': 1500.00,
            'description': 'Test checkout payment'
        }
        
        with patch('payments.services.checkout_payment_service.CheckoutPaymentService') as mock_service:
            mock_service.return_value.create_checkout_payment_link.return_value = {
                'payment_intent_id': 'test-payment-id',
                'payment_link_url': 'https://rzp.io/l/test',
                'total_amount': Decimal('1500.00'),
                'expires_at': '2024-01-01T12:00:00Z',
                'notifications_sent': {'sms': {'sent': True}, 'whatsapp': {'sent': True}},
                'split_preview': {
                    'platform_commission': Decimal('75.00'),
                    'partner_amount': Decimal('1425.00'),
                    'vendor_amount': Decimal('0.00')
                }
            }
            
            response = self.client.post(url, data, format='json')
            
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            self.assertIn('payment_link', response.data)
            self.assertIn('guest_info', response.data)
    
    def test_create_checkout_payment_unauthorized(self):
        """Test creating checkout payment without authentication"""
        url = reverse('create-checkout-payment', kwargs={'guest_id': self.guest.id})
        data = {'custom_amount': 1500.00}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_create_checkout_payment_wrong_partner(self):
        """Test creating checkout payment for another partner's guest"""
        # Create another partner
        from django.contrib.auth import get_user_model
        from core.models import PartnerProfile
        
        User = get_user_model()
        other_partner_user = User.objects.create_user(
            username='otherpartner',
            email='<EMAIL>',
            name='Other Partner'
        )
        other_partner_profile = PartnerProfile.objects.create(
            user=other_partner_user,
            platform_commission_rate=Decimal('5.00')
        )
        
        # Authenticate as other partner
        from rest_framework_simplejwt.tokens import RefreshToken
        other_token = RefreshToken.for_user(other_partner_user).access_token
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {other_token}')
        
        url = reverse('create-checkout-payment', kwargs={'guest_id': self.guest.id})
        data = {'custom_amount': 1500.00}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_get_guest_bill(self):
        """Test getting guest bill breakdown"""
        self.authenticate_as_partner()
        
        url = reverse('guest-bill', kwargs={'guest_id': self.guest.id})
        
        with patch('payments.services.checkout_payment_service.CheckoutPaymentService') as mock_service:
            mock_service.return_value.calculate_guest_bill.return_value = {
                'room_charges': Decimal('1000.00'),
                'service_orders': [],
                'total_service_amount': Decimal('500.00'),
                'taxes': Decimal('0.00'),
                'total_amount': Decimal('1500.00')
            }
            
            response = self.client.get(url)
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn('guest_info', response.data)
            self.assertIn('bill_breakdown', response.data)
            self.assertEqual(response.data['bill_breakdown']['total_amount'], Decimal('1500.00'))
    
    def test_checkout_payment_status(self):
        """Test getting checkout payment status"""
        self.authenticate_as_partner()
        
        # Create a payment intent for the guest
        payment_intent = self.create_payment_intent(
            context=PaymentContext.CHECKOUT,
            amount=Decimal('1000.00')
        )
        
        url = reverse('checkout-payment-status', kwargs={'guest_id': self.guest.id})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('guest_id', response.data)
        self.assertIn('payments', response.data)
        self.assertEqual(len(response.data['payments']), 1)
    
    def test_process_checkout_completion(self):
        """Test processing checkout completion"""
        self.authenticate_as_partner()
        
        # Create and mark payment as completed
        payment_intent = self.create_payment_intent(
            context=PaymentContext.CHECKOUT,
            amount=Decimal('1000.00')
        )
        payment_intent.mark_as_paid()
        
        url = reverse('process-checkout-completion', kwargs={'guest_id': self.guest.id})
        data = {'payment_intent_id': str(payment_intent.id)}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
        
        # Verify guest was checked out
        self.guest.refresh_from_db()
        self.assertTrue(self.guest.checked_out)


class PrecheckinIntegrationViewsTest(PaymentAPITestBase):
    """Tests for precheckin integration API views"""
    
    def test_create_precheckin_payment(self):
        """Test creating precheckin payment link"""
        self.authenticate_as_partner()
        
        url = reverse('create-precheckin-payment', kwargs={'precheckin_id': self.precheckin.id})
        data = {
            'upfront_amount': 400.00,
            'description': 'Test precheckin payment'
        }
        
        with patch('payments.services.precheckin_payment_service.PrecheckinPaymentService') as mock_service:
            mock_service.return_value.create_precheckin_payment_link.return_value = {
                'payment_intent_id': 'test-precheckin-payment-id',
                'payment_link_url': 'https://rzp.io/l/precheckin_test',
                'total_amount': Decimal('400.00'),
                'expires_at': '2024-01-01T12:00:00Z',
                'notifications_sent': {'sms': {'sent': True}, 'whatsapp': {'sent': True}},
                'split_preview': {
                    'platform_commission': Decimal('0.00'),
                    'partner_amount': Decimal('400.00'),
                    'vendor_amount': Decimal('0.00')
                }
            }
            
            response = self.client.post(url, data, format='json')
            
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            self.assertIn('payment_link', response.data)
            self.assertIn('precheckin_info', response.data)
            self.assertIn('upfront_details', response.data)
    
    def test_create_precheckin_payment_existing_payment(self):
        """Test creating precheckin payment when one already exists"""
        self.authenticate_as_partner()
        
        # Create existing payment intent
        existing_payment = self.create_payment_intent(
            context=PaymentContext.PRECHECKIN,
            amount=Decimal('400.00'),
            guest=None
        )
        
        url = reverse('create-precheckin-payment', kwargs={'precheckin_id': self.precheckin.id})
        data = {'upfront_amount': 400.00}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Payment already exists', response.data['error'])
    
    def test_precheckin_payment_status(self):
        """Test getting precheckin payment status"""
        self.authenticate_as_partner()
        
        # Create payment intent
        payment_intent = self.create_payment_intent(
            context=PaymentContext.PRECHECKIN,
            amount=Decimal('400.00'),
            guest=None
        )
        
        url = reverse('precheckin-payment-status', kwargs={'precheckin_id': self.precheckin.id})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('precheckin_id', response.data)
        self.assertIn('payment_status', response.data)
        self.assertIn('payments', response.data)
    
    def test_send_precheckin_payment_reminder(self):
        """Test sending precheckin payment reminder"""
        self.authenticate_as_partner()
        
        url = reverse('send-precheckin-reminder', kwargs={'precheckin_id': self.precheckin.id})
        
        with patch('payments.services.precheckin_payment_service.PrecheckinPaymentService') as mock_service:
            mock_service.return_value.send_precheckin_payment_reminder.return_value = {
                'sent': True,
                'message_id': 'test_reminder_123'
            }
            
            response = self.client.post(url)
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn('message', response.data)
    
    def test_calculate_upfront_amount(self):
        """Test calculating upfront amount"""
        self.authenticate_as_partner()
        
        url = reverse('calculate-upfront-amount', kwargs={'precheckin_id': self.precheckin.id})
        data = {'upfront_percentage': 25.0}
        
        with patch('payments.services.precheckin_payment_service.PrecheckinPaymentService') as mock_service:
            mock_service.return_value.calculate_upfront_amount.return_value = Decimal('500.00')
            
            response = self.client.post(url, data, format='json')
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn('upfront_amount', response.data)
            self.assertEqual(response.data['upfront_amount'], Decimal('500.00'))
    
    def test_precheckin_payment_history(self):
        """Test getting precheckin payment history"""
        self.authenticate_as_partner()
        
        # Create multiple payment intents
        payment1 = self.create_payment_intent(
            context=PaymentContext.PRECHECKIN,
            amount=Decimal('400.00'),
            guest=None
        )
        payment2 = self.create_payment_intent(
            context=PaymentContext.PRECHECKIN,
            amount=Decimal('200.00'),
            guest=None
        )
        payment2.mark_as_paid()
        
        url = reverse('precheckin-payment-history', kwargs={'precheckin_id': self.precheckin.id})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('payment_history', response.data)
        self.assertIn('summary', response.data)
        self.assertEqual(len(response.data['payment_history']), 2)
        self.assertEqual(response.data['summary']['total_payments'], 2)
        self.assertEqual(response.data['summary']['completed_payments'], 1)


class WebhookViewsTest(PaymentAPITestBase):
    """Tests for webhook handling views"""
    
    def test_razorpay_webhook_payment_captured(self):
        """Test processing payment.captured webhook"""
        # Create payment intent that webhook will reference
        payment_intent = self.create_payment_intent()
        
        webhook_payload = {
            'event': 'payment.captured',
            'payload': {
                'payment': {
                    'entity': {
                        'id': 'pay_webhook_test',
                        'status': 'captured',
                        'amount': 100000,  # 1000.00 in paise
                        'notes': {
                            'payment_intent_id': str(payment_intent.id)
                        }
                    }
                }
            }
        }
        
        url = reverse('razorpay-webhook')
        
        with patch('payments.webhooks.razorpay_webhook_handler.RazorpayService') as mock_razorpay:
            mock_razorpay.return_value.verify_webhook_signature.return_value = True
            
            response = self.client.post(
                url,
                data=json.dumps(webhook_payload),
                content_type='application/json',
                HTTP_X_RAZORPAY_SIGNATURE='test_signature'
            )
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            
            # Verify webhook event was created
            webhook_event = PaymentWebhookEvent.objects.filter(
                entity_id='pay_webhook_test'
            ).first()
            self.assertIsNotNone(webhook_event)
    
    def test_razorpay_webhook_invalid_signature(self):
        """Test webhook with invalid signature"""
        webhook_payload = {
            'event': 'payment.captured',
            'payload': {
                'payment': {
                    'entity': {
                        'id': 'pay_invalid_test',
                        'status': 'captured'
                    }
                }
            }
        }
        
        url = reverse('razorpay-webhook')
        
        with patch('payments.webhooks.razorpay_webhook_handler.RazorpayService') as mock_razorpay:
            mock_razorpay.return_value.verify_webhook_signature.return_value = False
            
            response = self.client.post(
                url,
                data=json.dumps(webhook_payload),
                content_type='application/json',
                HTTP_X_RAZORPAY_SIGNATURE='invalid_signature'
            )
            
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_webhook_status_view(self):
        """Test webhook status endpoint"""
        # Create some webhook events
        webhook1 = self.create_webhook_event(event_type='payment.captured')
        webhook2 = self.create_webhook_event(event_type='transfer.processed')
        webhook2.mark_as_processed()
        
        url = reverse('webhook-status')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_webhooks', response.data)
        self.assertIn('processed_webhooks', response.data)
        self.assertIn('failed_webhooks', response.data)
    
    def test_webhook_retry_view(self):
        """Test webhook retry endpoint"""
        # Create failed webhook event
        webhook_event = self.create_webhook_event()
        webhook_event.mark_processing_error('Test error')
        
        url = reverse('webhook-retry')
        data = {'webhook_event_id': str(webhook_event.id)}
        
        with patch('payments.tasks.process_webhook_event.delay') as mock_task:
            response = self.client.post(url, data, format='json')
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            mock_task.assert_called_once_with(str(webhook_event.id))


class PaymentLinkViewsTest(PaymentAPITestBase):
    """Tests for payment link API views"""
    
    def test_create_payment_link(self):
        """Test creating payment link via API"""
        self.authenticate_as_partner()
        
        url = reverse('paymentlink-list')
        data = {
            'context': 'checkout',
            'guest_id': str(self.guest.id),
            'total_amount': 1000.00,
            'description': 'Test payment link',
            'customer_name': 'Test Customer',
            'customer_phone': '+919876543210',
            'expire_hours': 24
        }
        
        with patch('payments.services.razorpay_service.RazorpayService') as mock_razorpay:
            mock_razorpay.return_value.create_payment_link.return_value = {
                'id': 'plink_api_test',
                'short_url': 'https://rzp.io/l/plink_api_test'
            }
            
            response = self.client.post(url, data, format='json')
            
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            self.assertIn('payment_intent', response.data)
            self.assertIn('payment_link', response.data)
    
    def test_get_payment_link_status(self):
        """Test getting payment link status"""
        self.authenticate_as_partner()
        
        # Create payment intent
        payment_intent = self.create_payment_intent()
        payment_intent.razorpay_payment_link_id = 'plink_status_test'
        payment_intent.save()
        
        url = reverse('paymentlink-detail', kwargs={'pk': payment_intent.id})
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('payment_intent', response.data)
        self.assertEqual(response.data['payment_intent']['id'], str(payment_intent.id))


class PaymentStatusViewsTest(PaymentAPITestBase):
    """Tests for payment status API views"""
    
    def test_payment_status_single(self):
        """Test getting single payment status"""
        self.authenticate_as_partner()
        
        payment_intent = self.create_payment_intent()
        
        url = reverse('payment-status')
        params = {'payment_intent_id': str(payment_intent.id)}
        
        response = self.client.get(url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('payment_intent_id', response.data)
        self.assertEqual(response.data['status'], PaymentStatus.PENDING)
    
    def test_payment_status_bulk(self):
        """Test getting bulk payment status"""
        self.authenticate_as_partner()
        
        payment1 = self.create_payment_intent()
        payment2 = self.create_payment_intent(amount=Decimal('2000.00'))
        
        url = reverse('payment-status-bulk')
        data = {
            'payment_intent_ids': [str(payment1.id), str(payment2.id)]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('payments', response.data)
        self.assertEqual(len(response.data['payments']), 2)
