import logging

from django.dispatch import receiver
from django.db.models.signals import post_save, pre_save, m2m_changed

from stay.models import Property, Room, Guest
from notification.models import NotificationCategory
from notification.tasks.flow_tasks import send_notification

logger = logging.getLogger(__name__)


@receiver(m2m_changed, sender=Property.staffs.through)
def property_staff_added_handler(sender, instance, action, pk_set, **kwargs):
    """Handle when staff is added to a property - start onboarding process"""
    if action == "post_add" and pk_set:
        from notification.models.onboarding import OnboardingStatus
        from notification.tasks.flow_tasks import send_signup_welcome_message
        
        # Get the property owner (first staff member)
        property_owner = instance.staffs.first()
        if property_owner:
            # Create onboarding status if it doesn't exist
            onboarding_status, created = OnboardingStatus.objects.get_or_create(
                partner=property_owner, property=instance
            )

            # Only send welcome message if this is a new onboarding status
            if created:
                # Send signup welcome message
                send_signup_welcome_message.delay(str(property_owner.id), str(instance.id))

                logger.info(
                    f"Created onboarding status for partner {property_owner.user.name} and property {instance.name}"
                )


@receiver(post_save, sender=Property)
def property_created_handler(sender, instance, created, **kwargs):
    """Handle property creation - start onboarding process (fallback for edge cases)"""
    if created:
        from notification.models.onboarding import OnboardingStatus
        from notification.tasks.flow_tasks import send_signup_welcome_message

        # Get the property owner (first staff member)
        property_owner = instance.staffs.first()
        if property_owner:
            # Create onboarding status
            onboarding_status, created = OnboardingStatus.objects.get_or_create(
                partner=property_owner, property=instance
            )

            # Only send welcome message if this is a new onboarding status
            if created:
                # Send signup welcome message
                send_signup_welcome_message.delay(str(property_owner.id), str(instance.id))

                logger.info(
                    f"Created onboarding status for partner {property_owner.user.name} and property {instance.name}"
                )


@receiver(post_save, sender=Room)
def room_created_handler(sender, instance, created, **kwargs):
    """Handle room creation - update onboarding status"""
    if created:
        from notification.models.onboarding import OnboardingStatus
        from notification.tasks.flow_tasks import check_and_update_onboarding_status

        # Get the property owner
        property_owner = instance.property.staffs.first()
        if property_owner:
            # Update onboarding status
            try:
                onboarding = OnboardingStatus.objects.get(
                    partner=property_owner, property=instance.property
                )
                onboarding.rooms_added = True
                onboarding.update_status()
                onboarding.save()

                # Check if onboarding is complete
                check_and_update_onboarding_status.delay(
                    str(property_owner.id), str(instance.property.id)
                )

                logger.info(
                    f"Updated onboarding status for room creation: {instance.property.name}"
                )
            except OnboardingStatus.DoesNotExist:
                logger.warning(
                    f"No onboarding status found for property {instance.property.name}"
                )


@receiver(pre_save, sender=Guest)
def capture_guest_original_state(sender, instance, **kwargs):
    """Capture the original checked_in and checked_out state before save to detect changes"""
    if instance.pk:  # Only for existing instances (updates)
        try:
            original_instance = sender.objects.get(pk=instance.pk)
            instance._original_checked_in = original_instance.checked_in
            instance._original_checked_out = original_instance.checked_out
        except sender.DoesNotExist:
            instance._original_checked_in = None
            instance._original_checked_out = None
    else:
        instance._original_checked_in = None
        instance._original_checked_out = None


@receiver(post_save, sender=Guest)
def guest_notification_handler(sender, instance, created, **kwargs):
    """Handle notifications for guest check-in and checkout events"""

    # Skip processing for new guest creation
    if created:
        return

    # Handle check-in notification
    if (
        hasattr(instance, "_original_checked_in")
        and instance._original_checked_in is not None
        and not instance._original_checked_in
        and instance.checked_in
    ):

        # Guest just checked in - send notification
        checkin_data = {
            "username": instance.user.name,
            "property_name": (
                instance.room.property.name
                if instance.room and instance.room.property
                else "Property"
            ),
            "room_no": str(instance.room.room_no) if instance.room else "N/A",
        }

        send_notification.delay(
            str(instance.user.id),
            NotificationCategory.USER_CHECKIN_INITIATED.name,
            checkin_data,
        )

    # Handle checkout notification
    if (
        hasattr(instance, "_original_checked_out")
        and instance._original_checked_out is not None
        and not instance._original_checked_out
        and instance.checked_out
    ):

        # Guest just checked out - process complete checkout flow
        from notification.tasks.flow_tasks import process_guest_checkout_flow

        process_guest_checkout_flow.delay(str(instance.id))


@receiver(post_save, sender=Guest)
def guest_checkin_welcome_handler(sender, instance, **kwargs):
    """Send welcome message when guest actually arrives"""
    # Check if guest just arrived (checked_in status changed to True)
    if (
        hasattr(instance, "_original_checked_in")
        and instance._original_checked_in is not None
        and not instance._original_checked_in
        and instance.checked_in
    ):

        # Send check-in successful notification
        checkin_data = {
            "guest_name": instance.user.name,
            "property_name": (
                instance.room.property.name
                if instance.room and instance.room.property
                else "Property"
            ),
            "room_details": (
                f"Room {instance.room.room_no} - {instance.room.type_of_room}"
                if instance.room
                else "Room"
            ),
            "checkout_date": (
                instance.check_out_date.strftime("%Y-%m-%d")
                if instance.check_out_date
                else "TBD"
            ),
        }

        send_notification.delay(
            str(instance.user.id), "CHECKIN_SUCCESSFUL", checkin_data
        )


@receiver(post_save, sender=Property)
def property_updated_handler(sender, instance, created, **kwargs):
    """Update onboarding status when property is updated"""
    if not created:  # Only for updates
        try:
            # Get partner profile
            partner = instance.owner if hasattr(instance, "owner") else None
            if partner:
                from notification.tasks.flow_tasks import (
                    check_and_update_onboarding_status,
                )

                check_and_update_onboarding_status.delay(
                    str(partner.id), str(instance.id)
                )
        except Exception as e:
            logger.error(f"Error triggering onboarding status check: {str(e)}")


@receiver(post_save, sender=Room)
def room_updated_handler(sender, instance, created, **kwargs):
    """Update onboarding status when rooms are added/updated"""
    try:
        # Get partner profile through property
        property_obj = instance.property
        partner = property_obj.owner if hasattr(property_obj, "owner") else None
        if partner:
            from notification.tasks.flow_tasks import check_and_update_onboarding_status

            check_and_update_onboarding_status.delay(
                str(partner.id), str(property_obj.id)
            )
    except Exception as e:
        logger.error(f"Error triggering onboarding status check: {str(e)}")

