# WHATSAPP INTEGRATION QUICK REFERENCE

## 🚀 Quick Start Commands

### Test Complete Integration
```bash
# Test all flows with your phone number
python manage.py test_complete_whatsapp_integration --phone +2347088212727

# Test specific flow
python manage.py test_complete_whatsapp_integration --phone +2347088212727 --flow onboarding

# Dry run (no actual messages sent)
python manage.py test_complete_whatsapp_integration --phone +2347088212727 --dry-run
```

### Test Individual Templates
```bash
# Test active templates
python manage.py test_whatsapp_integration --phone +2347088212727 --template checkin_initiated

# See what templates need approval
python manage.py test_missing_templates --phone +2347088212727
```

### Setup Commands
```bash
# Database migration
python manage.py makemigrations notification
python manage.py migrate

# Setup notification configs
python manage.py setup_notification_configs
```

## 📱 WhatsApp Template Quick Reference

### ✅ ACTIVE (Ready to Use)
- `checkin_initiated` - Guest check-in
- `order_completed` - Order completion
- `order_placed` - Order placement
- `precheckin_created` - Pre-checkin creation

### 🟡 NEED APPROVAL (Implemented, Pending WhatsApp)
- `signup_successful` - Partner welcome
- `onboarding_reminder` - Daily onboarding reminders
- `onboarding_completed` - Onboarding success
- `precheckin_reminder` - Pre-checkin reminders
- `room_allotment` - Room assignment
- `guest_arrived_welcome` - Guest welcome
- `dinner_reminder` - Daily 8 PM dinner reminder
- `checkout_bills` - Post-checkout bill
- `review_request` - Review collection
- `weekly_report` - Business performance
- And 16+ more order/service templates...

## 🔄 Automated Flows

### Daily (Scheduled)
- **09:00** - Pre-checkin reminders
- **10:00** - Onboarding reminders  
- **20:00** - Dinner reminders
- **23:30** - Daily summaries

### Frequent
- **Every 15 min** - Vendor order reminders

### Weekly
- **Monday 10:00** - Business reports

### Real-time (Event-driven)
- Partner signup → Welcome
- Room allotment → Guest notification
- Guest arrival → Welcome + QR codes
- Order status → Multi-party updates
- Service changes → Guest notifications
- Checkout → Bill + review request

## 🧪 Testing Scenarios

### Onboarding Flow Test
```python
# Triggers: signup_successful, onboarding_reminder, onboarding_completed
python manage.py test_complete_whatsapp_integration --phone +XX --flow onboarding
```

### Check-in Flow Test  
```python
# Triggers: precheckin_reminder, room_allotment, guest_arrived_welcome
python manage.py test_complete_whatsapp_integration --phone +XX --flow checkin
```

### Service Flow Test
```python
# Triggers: dinner_reminder, new_service_available, service_hidden_notification
python manage.py test_complete_whatsapp_integration --phone +XX --flow service
```

### Order Flow Test
```python
# Triggers: order_placed, order_accepted, order_completed
python manage.py test_complete_whatsapp_integration --phone +XX --flow orders
```

## 📊 Monitoring & Debugging

### Check Celery Tasks
```bash
# View active tasks
celery -A nestafar inspect active

# View scheduled tasks  
celery -A nestafar inspect scheduled

# Check task history
celery -A nestafar events
```

### Check Logs
```bash
# WhatsApp API logs
tail -f logs/whatsapp.log

# Celery task logs
tail -f logs/celery.log

# Django application logs
tail -f logs/django.log
```

### Common Issues & Solutions

**Problem**: Templates not sending
- **Solution**: Check if template is approved in WhatsApp Business Manager

**Problem**: Celery tasks not running
- **Solution**: Ensure Redis is running and celery beat is started

**Problem**: Phone number format errors
- **Solution**: Use international format with country code (+XX format)

**Problem**: Rate limiting
- **Solution**: Check WhatsApp API rate limits and implement backoff

## 🛠 Development Workflow

### Adding New Template
1. Add template function to `notification/templates/flow_templates.py`
2. Add mapping to `NotificationTemplates` in `notification/models.py`  
3. Add category to `NotificationCategory` enum
4. Document in `WHATSAPP_TEMPLATES.md`
5. Test with management command
6. Submit to WhatsApp Business Manager

### Adding New Flow
1. Create task in `notification/tasks/flow_tasks.py`
2. Add signal handler in `notification/signals.py`
3. Schedule if needed in `nestafar/celery.py`
4. Add test cases in `notification/tests/test_integration.py`
5. Test with management command

### Debugging Template Issues
1. Check template exists in `NotificationTemplates`
2. Verify WhatsApp Business Manager approval
3. Test with `test_missing_templates` command
4. Check logs for API response errors
5. Validate phone number format

## 📞 Template Submission Checklist

For each template in WhatsApp Business Manager:

- [ ] **Category**: UTILITY (for transactional) or MARKETING (for promotional)
- [ ] **Language**: English (US)
- [ ] **Variables**: Exact count and order as documented
- [ ] **Text**: Copy exact suggested template from `WHATSAPP_TEMPLATES.md`
- [ ] **Submission**: Submit for Facebook review
- [ ] **Testing**: Test with management command once approved

## 🎯 Performance Optimization

### Database Queries
- Use `select_related()` for foreign keys
- Use `prefetch_related()` for many-to-many
- Bulk operations where possible
- Optimize with database indexes

### WhatsApp API
- Respect rate limits (80 messages/second)
- Use template messages only
- Handle errors gracefully
- Implement retry logic with exponential backoff

### Celery Tasks
- Keep tasks idempotent
- Use database transactions
- Handle exceptions properly
- Monitor task execution times

---

**Quick Help**: For any issues, check the logs first, then the WhatsApp Business Manager template status, then the Celery task status.
