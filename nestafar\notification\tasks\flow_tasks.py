from celery import shared_task
from django.utils import timezone
from datetime import timedelta, datetime, date
from django.db.models import Sum, F
from .send_tasks import send_notification
from stay.models import Guest, Property, Room
from booking.models import PreCheckin
from core.models import PartnerProfile
from service.models.service import ServicePartner
from notification.models.onboarding import OnboardingStatus, WeeklyReport
import logging

logger = logging.getLogger(__name__)


@shared_task
def check_onboarding_reminders():
    """
    Daily task to check incomplete onboardings and send reminders
    """
    try:
        logger.info("Starting onboarding reminders check")

        now = timezone.now()

        # Get all incomplete onboardings with related data to avoid N+1 queries
        incomplete_onboardings = OnboardingStatus.objects.filter(
            status__in=[
                OnboardingStatus.StatusChoices.PENDING,
                OnboardingStatus.StatusChoices.IN_PROGRESS,
            ]
        ).select_related("partner__user", "property")

        # Collect onboardings that need reminders for bulk operations
        onboardings_to_update = []

        for onboarding in incomplete_onboardings:
            # Check if 24 hours have passed since last reminder
            if (
                onboarding.last_reminder_sent is None
                or now - onboarding.last_reminder_sent >= timedelta(hours=24)
            ):

                missing_steps = onboarding.get_missing_steps()
                completion_percentage = int(
                    onboarding.calculate_completion_percentage()
                )

                # Send reminder notification
                notification_data = {
                    "partner_name": (
                        getattr(onboarding.partner.user, "name", "Partner")
                        if onboarding.partner.user
                        else "Partner"
                    ),
                    "property_name": onboarding.property.name,
                    "missing_steps": missing_steps,
                    "completion_percentage": completion_percentage,
                }

                send_notification.delay(
                    str(onboarding.partner.user.id),
                    "ONBOARDING_REMINDER",
                    notification_data,
                )

                # Add to bulk update list
                onboardings_to_update.append(onboarding.id)

                logger.info(
                    f"Sent onboarding reminder to {getattr(onboarding.partner.user, 'name', 'Partner')}"
                )

        # Bulk update reminder tracking for all onboardings that received reminders
        if onboardings_to_update:
            OnboardingStatus.objects.filter(id__in=onboardings_to_update).update(
                last_reminder_sent=now, reminder_count=F("reminder_count") + 1
            )

        logger.info(
            f"Completed onboarding reminders check. Processed {len(onboardings_to_update)} reminders"
        )

    except Exception as e:
        logger.error(f"Error in onboarding reminders task: {str(e)}")
        raise


@shared_task
def check_precheckin_reminders():
    """
    Daily task to check incomplete pre-checkins and send reminders
    """
    try:
        logger.info("Starting pre-checkin reminders check")

        now = timezone.now()

        # Get pre-checkins that are pending and check-in is within next 72 hours
        # Use select_related and prefetch_related to optimize queries
        pending_precheckins = (
            PreCheckin.objects.filter(
                status="pending",
                expected_checkin__gte=now,
                expected_checkin__lte=now + timedelta(hours=72),
            )
            .select_related("property")
            .prefetch_related("pre_checkin_guests__user")
        )

        reminder_count = 0

        for precheckin in pending_precheckins:
            # Calculate hours remaining until check-in
            time_diff = precheckin.expected_checkin - now
            hours_remaining = int(time_diff.total_seconds() / 3600)

            # Get primary guest from prefetched data
            primary_guest = None
            try:
                primary_guest = precheckin.pre_checkin_guests.filter(
                    is_primary=True
                ).first()
                if not primary_guest:
                    continue
            except Exception as e:
                logger.warning(
                    f"Error getting primary guest for pre-checkin {precheckin.id}: {str(e)}"
                )
                continue
            if hours_remaining <= 24:
                # Urgent reminder
                notification_data = {
                    "guest_name": primary_guest.user.name,
                    "property_name": precheckin.property.name,
                    "checkin_date": precheckin.expected_checkin.strftime(
                        "%Y-%m-%d %H:%M"
                    ),
                    "hours_remaining": hours_remaining,
                }

                if hours_remaining <= 6:
                    # Warning before auto-cancellation
                    send_notification.delay(
                        str(primary_guest.user.id),
                        "PRECHECKIN_CANCELLATION_WARNING",
                        notification_data,
                    )
                else:
                    # Regular reminder
                    send_notification.delay(
                        str(primary_guest.user.id),
                        "PRECHECKIN_REMINDER",
                        notification_data,
                    )

                reminder_count += 1
                logger.info(f"Sent pre-checkin reminder to {primary_guest.user.name}")

        # Auto-cancel pre-checkins that are overdue using bulk update
        overdue_precheckins = PreCheckin.objects.filter(
            status="pending", expected_checkin__lt=now - timedelta(hours=2)
        )

        overdue_count = overdue_precheckins.update(status="cancelled")
        if overdue_count > 0:
            logger.info(f"Auto-cancelled {overdue_count} overdue pre-checkins")

        logger.info(
            f"Completed pre-checkin reminders check. Sent {reminder_count} reminders"
        )

    except Exception as e:
        logger.error(f"Error in pre-checkin reminders task: {str(e)}")
        raise


@shared_task
def send_dinner_reminders():
    """
    Daily task at 8 PM to send dinner reminders to all checked-in guests
    """
    try:
        logger.info("Starting dinner reminders")

        # Get all currently checked-in guests with related data to avoid N+1 queries
        checked_in_guests = Guest.objects.filter(
            checked_in=True, checked_out=False
        ).select_related("user", "room__property")

        for guest in checked_in_guests:
            notification_data = {
                "guest_name": guest.user.name,
                "property_name": guest.room.property.name,
            }

            send_notification.delay(
                str(guest.user.id), "DINNER_REMINDER", notification_data
            )

        logger.info(f"Sent dinner reminders to {checked_in_guests.count()} guests")

    except Exception as e:
        logger.error(f"Error in dinner reminders task: {str(e)}")
        raise


@shared_task
def check_vendor_order_reminders():
    """
    Task to remind vendors every 15 minutes about pending orders
    """
    from service.subapps.food.models import FoodOrder
    from service.subapps.laundry.models import LaundryOrder
    from service.subapps.rental.models import RentalOrder
    from service.subapps.transport.models import TransportOrder
    from service.subapps.shop.models import ShopOrder
    from service.subapps.tourism.models import TourismOrder
    from notification.tasks import send_service_partner_notification

    try:
        logger.info("Checking vendor order reminders")

        order_models = [
            FoodOrder,
            LaundryOrder,
            RentalOrder,
            TransportOrder,
            ShopOrder,
            TourismOrder,
        ]
        now = timezone.now()

        for order_model in order_models:
            # Get orders that are pending for more than 15 minutes
            pending_orders = order_model.objects.filter(
                status=order_model.OrderStatus.PENDING,
                created_at__lte=now - timedelta(minutes=15),
            )

            for order in pending_orders:
                if order.service_partner:
                    # Calculate minutes pending
                    time_diff = now - order.created_at
                    minutes_pending = int(time_diff.total_seconds() / 60)

                    # Check if order has been pending for at least 15 minutes
                    if minutes_pending >= 15:
                        # Check if no reminder has been sent, or if 15+ minutes have passed since last reminder
                        should_send_reminder = (
                            order.last_reminder_sent is None
                            or now - order.last_reminder_sent >= timedelta(minutes=15)
                        )

                        if should_send_reminder:
                            notification_data = {
                                "vendor_name": order.service_partner.name,
                                "order_id": str(order.id),
                                "minutes_pending": minutes_pending,
                                "total_amount": (
                                    f"{order.total:,.2f}"
                                    if hasattr(order, "total")
                                    else "0.00"
                                ),
                            }

                            # Send notification to service partner's phone number
                            from notification.tasks import (
                                send_service_partner_notification,
                            )

                            send_service_partner_notification.delay(
                                str(order.service_partner.id),
                                "VENDOR_ORDER_REMINDER",
                                notification_data,
                            )

                            # Update reminder tracking
                            order.last_reminder_sent = now
                            order.reminder_count += 1
                            order.save(
                                update_fields=["last_reminder_sent", "reminder_count"]
                            )

        logger.info("Completed vendor order reminders check")

    except Exception as e:
        logger.error(f"Error in vendor order reminders task: {str(e)}")
        raise


@shared_task
def generate_weekly_reports():
    """
    Weekly task to generate business reports for all partners
    """
    try:
        logger.info("Starting weekly report generation")

        # Calculate last week's date range
        today = date.today()
        week_start = today - timedelta(days=today.weekday() + 7)  # Last Monday
        week_end = week_start + timedelta(days=6)  # Last Sunday

        # Get all active partners with properties
        partners = (
            PartnerProfile.objects.filter(
                user__is_active=True, properties__isnull=False
            )
            .distinct()
            .select_related("user")
            .prefetch_related("properties")
        )

        for partner in partners:
            for property_obj in partner.properties.all():
                try:
                    # Calculate metrics
                    report_data = calculate_weekly_metrics(
                        partner, property_obj, week_start, week_end
                    )

                    # Generate recommendations
                    recommendations = generate_recommendations(report_data)

                    # Create or update weekly report
                    weekly_report, created = WeeklyReport.objects.update_or_create(
                        partner=partner,
                        property=property_obj,
                        week_start=week_start,
                        defaults={
                            "week_end": week_end,
                            "total_reservations": report_data["reservations"],
                            "occupancy_rate": report_data["occupancy_rate"],
                            "average_orders_per_guest": report_data["avg_orders"],
                            "total_gmv": report_data["gmv"],
                            "total_commission": report_data["commission"],
                            "recommendations": recommendations,
                        },
                    )

                    # Send notification
                    notification_data = {
                        "partner_name": partner.user.name,
                        "property_name": property_obj.name,
                        "week_start": week_start.strftime("%Y-%m-%d"),
                        "week_end": week_end.strftime("%Y-%m-%d"),
                        "reservations": report_data["reservations"],
                        "occupancy_rate": f"{report_data['occupancy_rate']:.1f}",
                        "avg_orders": f"{report_data['avg_orders']:.1f}",
                        "gmv": report_data["gmv"],
                        "commission": report_data["commission"],
                        "recommendations": recommendations,
                    }

                    send_notification.delay(
                        str(partner.user.id), "WEEKLY_REPORT", notification_data
                    )

                    weekly_report.sent_at = timezone.now()
                    weekly_report.save()

                    logger.info(
                        f"Generated weekly report for {partner.user.name} - {property_obj.name}"
                    )

                except Exception as e:
                    logger.error(
                        f"Error generating report for {partner.user.name}: {str(e)}"
                    )
                    continue

        logger.info("Completed weekly report generation")

    except Exception as e:
        logger.error(f"Error in weekly report generation: {str(e)}")
        raise


def calculate_weekly_metrics(partner, property_obj, week_start, week_end):
    """Calculate weekly business metrics"""
    try:
        # Import order models
        from service.subapps.food.models import FoodOrder
        from service.subapps.laundry.models import LaundryOrder
        from service.subapps.rental.models import RentalOrder
        from service.subapps.transport.models import TransportOrder
        from service.subapps.shop.models import ShopOrder
        from service.subapps.tourism.models import TourismOrder
        from booking.models import Reservation

        week_start_dt = datetime.combine(week_start, datetime.min.time())
        week_end_dt = datetime.combine(week_end, datetime.max.time())

        # Reservations
        reservations = Reservation.objects.filter(
            property=property_obj,
            created_at__gte=week_start_dt,
            created_at__lte=week_end_dt,
        ).count()

        # Occupancy rate - calculate actual room nights occupied during the week
        total_rooms = Room.objects.filter(property=property_obj).count()

        # Get all guests whose stay overlaps with the week period
        guests_in_period = Guest.objects.filter(
            room__property=property_obj,
            check_in_date__lte=week_end_dt,  # Check-in before or during week end
            check_out_date__gte=week_start_dt,  # Check-out after or during week start
            checked_in=True,
        ).values("check_in_date", "check_out_date")

        total_room_nights = 0
        week_start_date = week_start_dt.date()
        week_end_date = week_end_dt.date()

        for guest in guests_in_period:
            # Calculate overlap between guest stay and week period
            stay_start = max(
                (
                    guest["check_in_date"].date()
                    if guest["check_in_date"]
                    else week_start_date
                ),
                week_start_date,
            )
            stay_end = min(
                (
                    guest["check_out_date"].date()
                    if guest["check_out_date"]
                    else week_end_date
                ),
                week_end_date,
            )

            # Calculate nights occupied (only if there's actual overlap)
            if stay_start <= stay_end:
                nights_in_week = (stay_end - stay_start).days
                if nights_in_week > 0:
                    total_room_nights += nights_in_week

        # Calculate occupancy rate as percentage
        total_possible_room_nights = total_rooms * 7
        occupancy_rate = (
            (total_room_nights / total_possible_room_nights * 100)
            if total_possible_room_nights > 0
            else 0
        )

        # Order metrics
        order_models = [
            FoodOrder,
            LaundryOrder,
            RentalOrder,
            TransportOrder,
            ShopOrder,
            TourismOrder,
        ]
        total_orders = 0
        total_gmv = 0
        total_commission = 0

        for order_model in order_models:
            orders = order_model.objects.filter(
                guest__room__property=property_obj,
                created_at__gte=week_start_dt,
                created_at__lte=week_end_dt,
                status__in=[
                    order_model.OrderStatus.COMPLETED,
                    order_model.OrderStatus.ACCEPTED,
                ],
            )

            total_orders += orders.count()
            total_gmv += orders.aggregate(total=Sum("total"))["total"] or 0
            total_commission += orders.aggregate(total=Sum("commissions"))["total"] or 0

        # Average orders per guest
        total_guests = Guest.objects.filter(
            room__property=property_obj,
            check_in_date__gte=week_start_dt,
            check_in_date__lte=week_end_dt,
            checked_in=True,
        ).count()

        avg_orders = (total_orders / total_guests) if total_guests > 0 else 0

        return {
            "reservations": reservations,
            "occupancy_rate": occupancy_rate,
            "avg_orders": avg_orders,
            "gmv": total_gmv,
            "commission": total_commission,
            "total_guests": total_guests,
            "total_rooms": total_rooms,
        }

    except Exception as e:
        logger.error(f"Error calculating metrics: {str(e)}")
        return {
            "reservations": 0,
            "occupancy_rate": 0,
            "avg_orders": 0,
            "gmv": 0,
            "commission": 0,
            "total_guests": 0,
            "total_rooms": 0,
        }


def generate_recommendations(report_data):
    """Generate business recommendations based on metrics"""
    recommendations = []

    # Occupancy rate recommendations
    if report_data["occupancy_rate"] < 50:
        recommendations.append(
            "Low occupancy! Consider listing on more OTAs using Nestafar's channel manager"
        )
        recommendations.append(
            "Improve property photos and descriptions to attract more bookings"
        )

    # Customer usage recommendations
    if report_data["avg_orders"] < 1.0:
        recommendations.append(
            "Low service usage. Add more QR codes around the property"
        )
        recommendations.append("Train staff to promote Nestafar services to guests")

    # GMV recommendations
    gmv_per_room = (
        report_data["gmv"] / report_data["total_rooms"]
        if report_data["total_rooms"] > 0
        else 0
    )
    if gmv_per_room < 10000:
        recommendations.append(
            "Low revenue per room. Consider adding more vendors and services"
        )
        recommendations.append(
            "Promote premium services like spa, room service, and tours"
        )

    # Commission recommendations
    commission_per_room = (
        report_data["commission"] / report_data["total_rooms"]
        if report_data["total_rooms"] > 0
        else 0
    )
    if commission_per_room < 1000:
        recommendations.append("Consider increasing commission rates to boost revenue")
        recommendations.append(
            "Focus on high-margin services like tours and experiences"
        )

    # General positive reinforcement
    if not recommendations:
        recommendations.append("Excellent performance! Keep up the great work")
        recommendations.append("Consider expanding to more properties with Nestafar")

    return recommendations


@shared_task
def send_signup_welcome_message(partner_id, property_id):
    """
    Send welcome message after successful signup
    """
    try:
        partner = PartnerProfile.objects.get(id=partner_id)
        property_obj = Property.objects.get(id=property_id)

        # Create onboarding status
        onboarding_status, created = OnboardingStatus.objects.get_or_create(
            partner=partner,
            property=property_obj,
            defaults={"status": OnboardingStatus.StatusChoices.PENDING},
        )

        # Send welcome message
        notification_data = {
            "partner_name": partner.user.name,
            "property_name": property_obj.name,
        }

        send_notification.delay(
            str(partner.user.id), "SIGNUP_SUCCESSFUL", notification_data
        )

        logger.info(f"Sent signup welcome message to {partner.user.name}")

    except Exception as e:
        logger.error(f"Error sending signup welcome message: {str(e)}")
        raise


@shared_task
def check_and_update_onboarding_status(partner_id, property_id):
    """
    Check onboarding completion and send notifications if complete
    """
    try:

        onboarding_status = OnboardingStatus.objects.get(
            partner_id=partner_id, property_id=property_id
        )

        # Check property details
        property_obj = Property.objects.select_related('location').get(id=property_id)
        onboarding_status.property_details_added = bool(
            property_obj.name and 
            property_obj.location and 
            property_obj.location.address and 
            property_obj.description
        )

        # Check property photos
        onboarding_status.property_photos_uploaded = bool(
            hasattr(property_obj, "images") and property_obj.images.exists()
        )

        # Check rooms added
        rooms = Room.objects.filter(property=property_obj)
        onboarding_status.rooms_added = rooms.exists()

        # Check room photos
        room_photos_exist = False
        for room in rooms:
            if hasattr(room, "images") and room.images.exists():
                room_photos_exist = True
                break
        onboarding_status.room_photos_uploaded = room_photos_exist

        # Check services added
        from stay.models import PropertyPartner

        onboarding_status.services_added = PropertyPartner.objects.filter(
            property=property_obj
        ).exists()

        # Update status and save to trigger signals
        old_status = onboarding_status.status
        onboarding_status.update_status()
        onboarding_status.save()  # This will trigger the signal if completion detected

        logger.info(f"Updated onboarding status for {property_obj.name}")

    except Exception as e:
        logger.error(f"Error checking onboarding status: {str(e)}")
        raise


@shared_task
def process_guest_checkout_flow(guest_id):
    """
    Process complete checkout flow including bill and review request
    """
    try:
        # Get guest with related data to avoid additional queries
        guest = Guest.objects.select_related("user", "room__property").get(id=guest_id)

        # Calculate total bill amount (this is a simplified calculation)
        from service.subapps.food.models import FoodOrder
        from service.subapps.laundry.models import LaundryOrder
        from service.subapps.rental.models import RentalOrder
        from service.subapps.transport.models import TransportOrder
        from service.subapps.shop.models import ShopOrder
        from service.subapps.tourism.models import TourismOrder

        order_models = [
            FoodOrder,
            LaundryOrder,
            RentalOrder,
            TransportOrder,
            ShopOrder,
            TourismOrder,
        ]
        total_amount = 0

        for order_model in order_models:
            orders = order_model.objects.filter(
                guest=guest, status=order_model.OrderStatus.COMPLETED
            )
            total_amount += orders.aggregate(total=Sum("total"))["total"] or 0

        # Add room charges (simplified)
        room_rate = guest.room.rate or 0
        days_stayed = (guest.check_out_date - guest.check_in_date).days
        room_charges = room_rate * days_stayed
        total_amount += room_charges

        # Send bill notification
        checkout_data = {
            "guest_name": guest.user.name,
            "property_name": guest.room.property.name,
            "checkout_date": guest.check_out_date.strftime("%Y-%m-%d"),
            "total_amount": f"{total_amount:,.2f}",
        }

        send_notification.delay(str(guest.user.id), "CHECKOUT_BILLS", checkout_data)

        # Send review request (after 30 minutes)
        send_review_request.apply_async(args=[guest_id], countdown=1800)  # 30 minutes

        logger.info(f"Processed checkout flow for guest {guest.user.name}")

    except Exception as e:
        logger.error(f"Error processing checkout flow: {str(e)}")
        raise


@shared_task
def send_review_request(guest_id):
    """
    Send review request to guest after checkout
    """
    try:
        # Get guest with related data to avoid additional queries
        guest = Guest.objects.select_related("user", "room__property").get(id=guest_id)

        review_data = {
            "guest_name": guest.user.name,
            "property_name": guest.room.property.name,
        }

        send_notification.delay(str(guest.user.id), "REVIEW_REQUEST", review_data)

        logger.info(f"Sent review request to {guest.user.name}")

    except Exception as e:
        logger.error(f"Error sending review request: {str(e)}")
        raise


@shared_task
def send_room_allotment_notification(guest_id, room_id):
    """
    Send room allotment notification to guest
    """
    try:
        guest = Guest.objects.select_related("user").get(id=guest_id)
        room = Room.objects.select_related("property").get(id=room_id)

        notification_data = {
            "guest_name": guest.user.name,
            "property_name": room.property.name,
            "room_number": room.room_no,
            "checkin_date": (
                guest.check_in_date.strftime("%Y-%m-%d %H:%M")
                if guest.check_in_date
                else "TBD"
            ),
        }

        send_notification.delay(str(guest.user.id), "ROOM_ALLOTMENT", notification_data)

        logger.info(
            f"Sent room allotment notification to {guest.user.name} for room {room.room_no}"
        )

    except Exception as e:
        logger.error(f"Error sending room allotment notification: {str(e)}")
        raise


@shared_task
def send_guest_arrived_welcome(guest_id):
    """
    Send welcome message when guest arrives and checks in
    """
    try:
        guest = Guest.objects.select_related("user", "room__property").get(id=guest_id)

        notification_data = {
            "guest_name": guest.user.name,
            "property_name": guest.room.property.name,
            "room_number": guest.room.room_no,
        }

        send_notification.delay(
            str(guest.user.id), "GUEST_ARRIVED_WELCOME", notification_data
        )

        logger.info(f"Sent welcome message to {guest.user.name}")

    except Exception as e:
        logger.error(f"Error sending guest welcome message: {str(e)}")
        raise


@shared_task
def send_onboarding_completed_notification(partner_id, property_id):
    """
    Send congratulatory message when onboarding is completed
    """
    try:
        partner = PartnerProfile.objects.get(id=partner_id)
        property_obj = Property.objects.get(id=property_id)

        notification_data = {
            "partner_name": partner.user.name,
            "property_name": property_obj.name,
        }

        send_notification.delay(
            str(partner.user.id), "ONBOARDING_COMPLETED", notification_data
        )

        logger.info(f"Sent onboarding completion notification to {partner.user.name}")

    except Exception as e:
        logger.error(f"Error sending onboarding completion notification: {str(e)}")
        raise


@shared_task
def notify_service_change(service_partner_id, action, property_id):
    """
    Notify all guests when a service is hidden or restored
    """
    try:
        service_partner = ServicePartner.objects.get(id=service_partner_id)
        property_obj = Property.objects.get(id=property_id)

        # Get all checked-in guests at the property with related user data
        guests = Guest.objects.filter(
            room__property=property_obj, checked_in=True, checked_out=False
        ).select_related("user")

        for guest in guests:
            if action == "hidden":
                notification_data = {
                    "guest_name": guest.user.name,
                    "service_name": service_partner.name,
                    "property_name": property_obj.name,
                }
                event = "SERVICE_HIDDEN_NOTIFICATION"
            elif action == "restored":
                notification_data = {
                    "guest_name": guest.user.name,
                    "service_name": service_partner.name,
                    "property_name": property_obj.name,
                }
                event = "SERVICE_RESTORED_NOTIFICATION"
            else:
                continue

            send_notification.delay(str(guest.user.id), event, notification_data)

        logger.info(
            f"Notified {guests.count()} guests about service {action}: {service_partner.name}"
        )

    except Exception as e:
        logger.error(f"Error notifying service change: {str(e)}")
        raise
