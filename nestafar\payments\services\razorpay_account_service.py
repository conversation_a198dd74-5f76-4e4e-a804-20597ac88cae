"""
Razorpay Account Service

Service for managing Razorpay Route accounts including account creation,
bank account management, and KYC document handling.
"""

import logging
import mimetypes
import os
import razorpay
import requests
from typing import Dict, Any, Optional, List
from django.conf import settings
from django.core.files.uploadedfile import UploadedFile

from ..models import (
    PartnerRazorpayAccount,
    PartnerBankAccount,
    PartnerKYCDocument,
    AccountVerificationLog
)
from ..constants import (
    AccountStatus,
    KYCStatus,
    VerificationStatus,
    DocumentType
)
from ..exceptions import RazorpayException, AccountCreationException

logger = logging.getLogger(__name__)


class RazorpayAccountService:
    """Service for Razorpay Account API operations"""

    def __init__(self):
        razorpay_settings = getattr(settings, 'RAZORPAY_SETTINGS', {})
        key_id = razorpay_settings.get('KEY_ID')
        key_secret = razorpay_settings.get('KEY_SECRET')

        if not key_id or not key_secret:
            raise ValueError("Razorpay API credentials not configured in RAZORPAY_SETTINGS")

        self._auth = (key_id, key_secret)

        self.client = razorpay.Client(auth=(key_id, key_secret))

    def _validate_document_file(self, document_file: UploadedFile) -> None:
        """
        Validate document file type and size according to Razorpay requirements
        
        Args:
            document_file: Uploaded file to validate
            
        Raises:
            RazorpayException: If file validation fails
        """
        # Razorpay allowed file types
        ALLOWED_EXTENSIONS = {'.pdf', '.jpeg', '.jpg', '.png', '.jfif', '.heic', '.heif'}
        ALLOWED_MIME_TYPES = {
            'application/pdf',
            'image/jpeg', 
            'image/jpg',
            'image/png',
            'image/jfif',
            'image/heic',
            'image/heif'
        }
        
        # Maximum file size (10MB as per Razorpay limits)
        MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB in bytes
        
        # Check file size
        if document_file.size > MAX_FILE_SIZE:
            raise RazorpayException(f"File size ({document_file.size / (1024*1024):.1f}MB) exceeds maximum allowed size (10MB)")
        
        # Get file extension
        file_extension = os.path.splitext(document_file.name)[1].lower()
        
        # Check file extension
        if file_extension not in ALLOWED_EXTENSIONS:
            allowed_list = ', '.join(sorted(ALLOWED_EXTENSIONS))
            raise RazorpayException(f"File type '{file_extension}' not allowed. Supported formats: {allowed_list}")
        
        # Check MIME type if available
        if hasattr(document_file, 'content_type') and document_file.content_type:
            if document_file.content_type not in ALLOWED_MIME_TYPES:
                # Try to detect MIME type from file name as fallback
                detected_type, _ = mimetypes.guess_type(document_file.name)
                if detected_type and detected_type not in ALLOWED_MIME_TYPES:
                    allowed_mimes = ', '.join(sorted(ALLOWED_MIME_TYPES))
                    raise RazorpayException(f"File MIME type '{document_file.content_type}' not allowed. Supported types: {allowed_mimes}")

    def create_partner_account(self, partner_account: PartnerRazorpayAccount, category: Optional[str]=None, subcategory: Optional[str]=None) -> Dict[str, Any]:
        """
        Create a new Razorpay linked account for partner

        Args:
            partner_account: PartnerRazorpayAccount instance

        Returns:
            Dict containing account creation response

        Raises:
            AccountCreationException: If account creation fails
        """
        try:
            # Prepare account data
            # Build legal_info conditionally to avoid sending empty values to Razorpay
            legal_info = {}
            if getattr(partner_account, 'pan_number', None):
                legal_info['pan'] = partner_account.pan_number
            if getattr(partner_account, 'gst_number', None):
                legal_info['gst'] = partner_account.gst_number

            # Determine category and subcategory: prefer explicit args, then partner profile fields, then sensible defaults
            ALLOWED_CATEGORIES = ('healthcare', 'retail', 'services', 'education', 'hospitality')
            ALLOWED_SUBCATEGORIES = ('clinic', 'pharmacy', 'store', 'consulting', 'hotel')

            resolved_category = (category or getattr(partner_account, 'category', None) or 'healthcare').lower()
            resolved_subcategory = (subcategory or getattr(partner_account, 'subcategory', None) or 'clinic').lower()

            # Validate
            if resolved_category not in ALLOWED_CATEGORIES:
                logger.warning(f"Unknown category '{resolved_category}' provided; defaulting to 'healthcare'")
                resolved_category = 'healthcare'
            if resolved_subcategory not in ALLOWED_SUBCATEGORIES:
                logger.warning(f"Unknown subcategory '{resolved_subcategory}' provided; defaulting to 'clinic'")
                resolved_subcategory = 'clinic'

            # Prepare a Razorpay-safe reference_id (max 20 chars). Razorpay
            # enforces a 20 character limit; prefer UUID hex (no dashes) and
            # truncate if necessary.
            raw_ref = None
            try:
                raw_ref = getattr(partner_account.id, 'hex', None) or str(partner_account.id).replace('-', '')
            except Exception:
                raw_ref = str(partner_account.id)

            reference_id = raw_ref[:20]
            if len(raw_ref) > 20:
                logger.debug(f"Truncated reference_id for partner_account {partner_account.id} to '{reference_id}' to meet Razorpay 20-char limit")

            account_data = {
                'email': partner_account.contact_email,
                'phone': partner_account.contact_phone,
                'type': 'route',
                'reference_id': reference_id,
                'legal_business_name': partner_account.business_name,
                'business_type': partner_account.business_type,
                'contact_name': partner_account.contact_name,
                'profile': {
                    'category': resolved_category,
                    'subcategory': resolved_subcategory,
                    'addresses': {
                        'registered': {
                            'street1': partner_account.address_line1,
                            'street2': partner_account.address_line2 or 'N/A',
                            'city': partner_account.city,
                            'state': partner_account.state,
                            'postal_code': partner_account.postal_code,
                            'country': partner_account.country
                        }
                    }
                },
                'legal_info': legal_info
            }

            # Create account via Razorpay API
            logger.info(f"Creating Razorpay account for partner {partner_account.partner.user.id}")
            response = self.client.account.create(account_data)

            # Update partner account with Razorpay response
            partner_account.razorpay_account_id = response['id']
            partner_account.account_status = AccountStatus.PENDING
            partner_account.razorpay_response = response
            partner_account.save()

            # Log the activity
            self._log_activity(
                partner_account,
                'account_created',
                f"Razorpay account created with ID: {response['id']}"
            )

            logger.info(f"Successfully created Razorpay account {response['id']} for partner {partner_account.partner.user.id}")
            return response

        except (razorpay.errors.BadRequestError, razorpay.errors.GatewayError, razorpay.errors.ServerError) as e:
            error_msg = f"Razorpay account creation failed: {str(e)}"
            logger.error(error_msg)
            partner_account.record_error(error_msg)
            raise RazorpayException(error_msg) from e
        except Exception as e:
            error_msg = f"Unexpected error during account creation: {str(e)}"
            logger.error(error_msg)
    def _v2_request(self, method: str, path: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Minimal HTTP client for Razorpay v2-only endpoints (Accounts, Stakeholders, Product Config).
        Uses basic auth from settings. Raises RazorpayException on HTTP errors.
        """
        base_url_v2 = "https://api.razorpay.com/v2"
        url = f"{base_url_v2}/{path.lstrip('/')}"
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Nestafar-Platform/1.0",
        }
        try:
            resp = requests.request(
                method=method,
                url=url,
                auth=self._auth,
                headers=headers,
                json=data,
                timeout=30,
            )
            try:
                body = resp.json()
            except Exception:
                body = {"raw_response": resp.text}

            if resp.status_code >= 400:
                msg = body.get("error", {}).get("description") or body.get("message") or f"HTTP {resp.status_code}"
                logger.error({
                    'event': 'razorpay_v2_error',
                    'method': method,
                    'url': url,
                    'status_code': resp.status_code,
                    'error': body,
                })
                raise RazorpayException(f"Razorpay v2 API error: {msg}")
            return body
        except RazorpayException:
            raise
        except Exception as e:
            logger.exception(f"Razorpay v2 request failed: {e}")
            raise RazorpayException(f"Razorpay v2 request failed: {e}")

    def create_stakeholder(self, partner_account: PartnerRazorpayAccount, stakeholder_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Step 2: Create a Stakeholder for the Linked Account (Route allows only one).
        Stores stakeholder_id inside partner_account.razorpay_response for tracking.
        """
        if not partner_account.razorpay_account_id:
            raise RazorpayException("Linked Account ID is missing. Create the account first.")

        # Build minimal compliant payload from partner profile if not provided
        payload = stakeholder_data or {}
        if not stakeholder_data:
            payload = {
                'name': partner_account.contact_name,
                'email': partner_account.contact_email,
                'addresses': {
                    'residential': {
                        'street': partner_account.address_line1,
                        'city': partner_account.city,
                        'state': partner_account.state,
                        'postal_code': partner_account.postal_code,
                        'country': partner_account.country,
                    }
                }
            }
            # Optional phone structure if available
            phone = (partner_account.contact_phone or '').strip()
            if phone:
                # Strip non-digits for API numeric field compatibility where required
                digits = ''.join(ch for ch in phone if ch.isdigit())
                if len(digits) >= 8:
                    payload['phone'] = {'primary': int(digits[-10:])}
            # Optional KYC PAN for stakeholder if present on model
            pan = getattr(partner_account, 'pan_number', None)
            if pan:
                payload['kyc'] = {'pan': pan}

        logger.info(f"Creating stakeholder for linked account {partner_account.razorpay_account_id}")
        resp = self._v2_request(
            'POST', f"/accounts/{partner_account.razorpay_account_id}/stakeholders", payload
        )

        # Persist stakeholder details inside JSON response blob to avoid schema change
        rp = dict(partner_account.razorpay_response or {})
        rp['stakeholder'] = resp
        rp['stakeholder_id'] = resp.get('id')
        partner_account.razorpay_response = rp
        partner_account.stakeholder_id = resp.get('id')
        partner_account.save(update_fields=['razorpay_response', 'stakeholder_id'])

        logger.info(f"Stakeholder created: {resp.get('id')} for account {partner_account.razorpay_account_id}")
        return resp

    def request_product_configuration(self, partner_account: PartnerRazorpayAccount) -> Dict[str, Any]:
        """Step 3: Request Route product configuration for the linked account.
        Stores product configuration id, activation_status and requirements.
        """
        if not partner_account.razorpay_account_id:
            raise RazorpayException("Linked Account ID is missing. Create the account first.")

        payload = {'product_name': 'route', 'tnc_accepted': True}
        logger.info(f"Requesting product configuration for account {partner_account.razorpay_account_id}")
        resp = self._v2_request('POST', f"/accounts/{partner_account.razorpay_account_id}/products", payload)

        rp = dict(partner_account.razorpay_response or {})
        rp['product_config'] = {
            'id': resp.get('id'),
            'activation_status': resp.get('activation_status'),
            'requirements': resp.get('requirements', []),
            'active_configuration': resp.get('active_configuration', {}),
        }
        partner_account.razorpay_response = rp
        partner_account.product_config_id = resp.get('id')
        partner_account.product_activation_status = resp.get('activation_status')
        partner_account.product_requirements = resp.get('requirements', [])
        partner_account.save(update_fields=['razorpay_response', 'product_config_id', 'product_activation_status', 'product_requirements'])

        logger.info(
            f"Product config requested: id={resp.get('id')} status={resp.get('activation_status')}"
        )
        return resp

    def update_product_configuration(self, partner_account: PartnerRazorpayAccount, bank_data: Dict[str, Any]) -> Dict[str, Any]:
        """Step 4: Update product configuration by submitting bank (settlement) details.
        Handles activation_status including needs_clarification by storing requirements.
        """
        if not partner_account.razorpay_account_id:
            raise RazorpayException("Linked Account ID is missing. Create the account first.")

        rp = dict(partner_account.razorpay_response or {})
        prd = (rp.get('product_config') or {})
        product_id = prd.get('id')
        if not product_id:
            pr = self.request_product_configuration(partner_account)
            product_id = pr.get('id')

        settlements = {
            'account_number': bank_data['account_number'],
            'ifsc_code': bank_data['ifsc_code'],
            'beneficiary_name': bank_data['account_holder_name'],
        }
        payload = {
            'settlements': settlements,
            'tnc_accepted': True,
        }

        logger.info(
            f"Updating product configuration {product_id} for account {partner_account.razorpay_account_id}"
        )
        resp = self._v2_request(
            'PATCH', f"/accounts/{partner_account.razorpay_account_id}/products/{product_id}", payload
        )

        # Update stored product config state
        rp['product_config'] = {
            'id': resp.get('id'),
            'activation_status': resp.get('activation_status'),
            'requirements': resp.get('requirements', []),
            'active_configuration': resp.get('active_configuration', {}),
        }
        partner_account.razorpay_response = rp
        partner_account.product_config_id = resp.get('id')
        partner_account.product_activation_status = resp.get('activation_status')
        partner_account.product_requirements = resp.get('requirements', [])
        partner_account.save(update_fields=['razorpay_response', 'product_config_id', 'product_activation_status', 'product_requirements'])

        activation_status = resp.get('activation_status')
        requirements = resp.get('requirements') or []
        if activation_status == 'activated':
            # Mark bank verification as verified when product is activated
            try:
                partner_account.bank_verification_status = VerificationStatus.VERIFIED
                partner_account.save(update_fields=['bank_verification_status'])
            except Exception:
                logger.warning("Could not update bank_verification_status post activation")
            logger.info(f"Product configuration activated for account {partner_account.razorpay_account_id}")
        elif activation_status == 'needs_clarification':
            # Keep verification in progress; surface requirements
            try:
                if partner_account.bank_verification_status != VerificationStatus.IN_PROGRESS:
                    partner_account.bank_verification_status = VerificationStatus.IN_PROGRESS
                    partner_account.save(update_fields=['bank_verification_status'])
            except Exception:
                logger.warning("Could not set bank_verification_status to IN_PROGRESS")
            # Log each requirement for debugging/ops follow up
            for req in requirements:
                logger.warning({
                    'event': 'razorpay_product_requirement',
                    'account_id': partner_account.razorpay_account_id,
                    'product_id': resp.get('id'),
                    'field_reference': req.get('field_reference'),
                    'resolution_url': req.get('resolution_url'),
                    'reason_code': req.get('reason_code'),
                    'status': req.get('status'),
                })

        return resp


    def add_bank_account(self, partner_account: PartnerRazorpayAccount, bank_data: Dict[str, Any]) -> PartnerBankAccount:
        """
        Add bank account and complete Razorpay Route onboarding steps (stakeholder, product config request, product config update).

        Args:
            partner_account: PartnerRazorpayAccount instance
            bank_data: Dictionary containing bank account details

        Returns:
            PartnerBankAccount instance

        Raises:
            RazorpayException: If any onboarding step fails
        """
        bank_account = None

        try:
            # Create local bank account record first
            bank_account = PartnerBankAccount.objects.create(
                razorpay_account=partner_account,
                account_holder_name=bank_data['account_holder_name'],
                account_number=bank_data['account_number'],
                ifsc_code=bank_data['ifsc_code'],
                bank_name=bank_data['bank_name'],
                branch_name=bank_data['branch_name'],
                account_type=bank_data.get('account_type', 'savings'),
                is_primary=bank_data.get('is_primary', False)
            )

            if not partner_account.razorpay_account_id:
                raise RazorpayException("Linked Account ID is missing. Create the account first.")

            # Ensure stakeholder exists (Route allows only one)
            rp = partner_account.razorpay_response or {}
            if not rp.get('stakeholder_id'):
                try:
                    self.create_stakeholder(partner_account)
                except RazorpayException as e:
                    # Not fatal for local storage; but required for product config in next steps
                    logger.warning(f"Stakeholder creation failed: {e}")
                    raise

            # Request product configuration if not already requested
            rp = partner_account.razorpay_response or {}
            product_config = rp.get('product_config') or {}
            if not product_config.get('id'):
                self.request_product_configuration(partner_account)

            # Update product configuration with settlement (bank) details
            self.update_product_configuration(partner_account, bank_data)

            # Track in bank account for reference (store product_config id)
            try:
                bank_account.razorpay_bank_account_id = (partner_account.razorpay_response.get('product_config') or {}).get('id')
                bank_account.save(update_fields=['razorpay_bank_account_id'])
            except Exception:
                pass

            activation_status = (partner_account.razorpay_response.get('product_config') or {}).get('activation_status')
            requirements = (partner_account.razorpay_response.get('product_config') or {}).get('requirements') or []

            if activation_status == 'activated':
                # Mark verified
                try:
                    bank_account.mark_as_verified()
                except Exception:
                    bank_account.verification_status = VerificationStatus.VERIFIED
                    bank_account.save(update_fields=['verification_status'])
                logger.info(f"Bank account verified via product activation for account {partner_account.razorpay_account_id}")
            elif activation_status == 'needs_clarification':
                # Keep in progress and capture requirement summary
                issues = ", ".join([str(r.get('field_reference')) for r in requirements if r.get('field_reference')])
                bank_account.verification_status = VerificationStatus.IN_PROGRESS
                bank_account.verification_error = f"Razorpay needs clarification for: {issues}" if issues else "Razorpay needs clarification"
                bank_account.save(update_fields=['verification_status', 'verification_error'])
                logger.warning({
                    'event': 'razorpay_needs_clarification',
                    'account_id': partner_account.razorpay_account_id,
                    'product_id': (partner_account.razorpay_response.get('product_config') or {}).get('id'),
                    'requirements': requirements,
                })

            logger.info(f"Added bank account {bank_account.account_number[-4:]} and updated product config for {partner_account.razorpay_account_id}")

            # Log the activity (re-using existing activity types)
            self._log_activity(
                partner_account,
                'bank_added',
                f"Bank account added: {bank_account.bank_name} - {bank_account.account_number[-4:]}",
                related_bank_account=bank_account
            )

            return bank_account

        except (razorpay.errors.BadRequestError, razorpay.errors.GatewayError, razorpay.errors.ServerError) as e:
            error_msg = f"Failed to add bank account to Razorpay: {str(e)}"
            logger.error(error_msg)
            if bank_account is not None:
                try:
                    bank_account.record_verification_attempt(error_msg)
                except Exception:
                    logger.exception("Failed to record verification attempt on bank_account")
            raise RazorpayException(error_msg) from e
        except RazorpayException as e:
            # Already logged above
            if bank_account is not None:
                try:
                    bank_account.record_verification_attempt(str(e))
                except Exception:
                    pass
            raise
        except Exception as e:
            error_msg = f"Unexpected error adding bank account: {str(e)}"
            logger.error(error_msg)
            raise RazorpayException(error_msg) from e

    def upload_kyc_document(self, partner_account: PartnerRazorpayAccount,
                           document_type: str, document_file: UploadedFile,
                           document_number: str) -> PartnerKYCDocument:
        """
        Upload KYC document for partner account

        Args:
            partner_account: PartnerRazorpayAccount instance
            document_type: Type of document (PAN, Aadhaar, etc.)
            document_file: Uploaded file
            document_number: Document number

        Returns:
            PartnerKYCDocument instance

        Raises:
            RazorpayException: If document upload fails
        """
        try:
            # Validate file before processing
            self._validate_document_file(document_file)
            
            # Check if account exists and get current status
            if not partner_account.razorpay_account_id:
                raise RazorpayException("Razorpay account must be created before uploading documents")
            
            # Check account status to determine if documents can be uploaded
            account_status = self.get_account_status(partner_account)
            if not self._can_upload_documents(partner_account):
                raise RazorpayException(
                    f"Documents cannot be uploaded for account in current status: {partner_account.account_status}. "
                    "Documents can only be uploaded when account needs clarification or is pending review."
                )
            
            # Upload to Razorpay using direct API call
            razorpay_document_id = None
            
            # Map document type to Razorpay format
            razorpay_doc_type = self._map_document_type_to_razorpay(document_type)
            
            # Prepare file data for Razorpay API
            # Reset file pointer to beginning
            document_file.seek(0)
            
            # Get or detect the content type
            content_type = getattr(document_file, 'content_type', None)
            if not content_type:
                content_type, _ = mimetypes.guess_type(document_file.name)
                if not content_type:
                    # Default content type based on file extension
                    file_extension = os.path.splitext(document_file.name)[1].lower()
                    if file_extension == '.pdf':
                        content_type = 'application/pdf'
                    elif file_extension in ['.jpg', '.jpeg']:
                        content_type = 'image/jpeg'
                    elif file_extension == '.png':
                        content_type = 'image/png'
                    elif file_extension == '.jfif':
                        content_type = 'image/jfif'
                    elif file_extension == '.heic':
                        content_type = 'image/heic'
                    elif file_extension == '.heif':
                        content_type = 'image/heif'
                    else:
                        content_type = 'application/octet-stream'
            
            # Read file content and reset pointer
            file_content = document_file.read()
            document_file.seek(0)  # Reset for Django model save
            
            # Validate file content is not empty
            if not file_content:
                raise RazorpayException("Document file is empty or corrupted")
            
            # Use direct HTTP request to upload document via v2 API
            url = f"https://api.razorpay.com/v2/accounts/{partner_account.razorpay_account_id}/documents"
            
            # Prepare files data for multipart form upload
            files = {
                'file': (document_file.name, file_content, content_type)
            }
            
            data = {
                'document_type': razorpay_doc_type
            }
            
            # Make the API request
            response = requests.post(
                url,
                auth=self._auth,
                files=files,
                data=data,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                # Extract document ID from response
                if 'business_proof_of_identification' in response_data:
                    docs = response_data['business_proof_of_identification']
                    if docs and len(docs) > 0:
                        razorpay_document_id = docs[-1].get('url', '')  # Use URL as ID
                elif 'additional_documents' in response_data:
                    docs = response_data['additional_documents']
                    if docs and len(docs) > 0:
                        razorpay_document_id = docs[-1].get('url', '')  # Use URL as ID
                
                logger.info(f"Uploaded {document_type} document to Razorpay account {partner_account.razorpay_account_id}")
            else:
                error_details = response.text
                try:
                    error_json = response.json()
                    error_details = error_json.get('error', {}).get('description', error_details)
                except:
                    pass
                raise RazorpayException(f"Document upload failed: {error_details}")
            
            # Create local document record after successful Razorpay upload
            kyc_document = PartnerKYCDocument.objects.create(
                razorpay_account=partner_account,
                document_type=document_type,
                document_number=document_number,
                document_file=document_file,
                razorpay_document_id=razorpay_document_id,
                verification_status=VerificationStatus.IN_PROGRESS if razorpay_document_id else VerificationStatus.PENDING
            )

            # Update account KYC status
            self._update_kyc_status(partner_account)

            # Log the activity
            self._log_activity(
                partner_account,
                'kyc_submitted',
                f"KYC document uploaded: {document_type}",
                related_document=kyc_document
            )

            return kyc_document

        except requests.exceptions.RequestException as e:
            error_msg = f"Network error uploading document to Razorpay: {str(e)}"
            logger.error(error_msg)
            raise RazorpayException(error_msg) from e
        except (razorpay.errors.BadRequestError, razorpay.errors.GatewayError, razorpay.errors.ServerError) as e:
            error_msg = f"Failed to upload document to Razorpay: {str(e)}"
            logger.error(error_msg)
            raise RazorpayException(error_msg) from e
        except Exception as e:
            error_msg = f"Unexpected error uploading document: {str(e)}"
            logger.error(error_msg)
            raise RazorpayException(error_msg) from e

    def get_account_status(self, partner_account: PartnerRazorpayAccount) -> Dict[str, Any]:
        """
        Get current account status from Razorpay

        Args:
            partner_account: PartnerRazorpayAccount instance

        Returns:
            Dict containing account status information
        """
        try:
            if not partner_account.razorpay_account_id:
                return {
                    'account_status': partner_account.account_status,
                    'kyc_status': partner_account.kyc_status,
                    'bank_verification_status': partner_account.bank_verification_status,
                    'can_receive_transfers': False,
                    'razorpay_data': None
                }

            # Fetch account details from Razorpay
            response = self.client.account.fetch(partner_account.razorpay_account_id)

            # Update local status based on Razorpay response
            self._sync_account_status(partner_account, response)

            return {
                'account_status': partner_account.account_status,
                'kyc_status': partner_account.kyc_status,
                'bank_verification_status': partner_account.bank_verification_status,
                'can_receive_transfers': partner_account.can_receive_transfers(),
                'razorpay_data': response,
                'activation_form_milestone': partner_account.activation_form_milestone
            }

        except (razorpay.errors.BadRequestError, razorpay.errors.GatewayError, razorpay.errors.ServerError) as e:
            logger.error(f"Failed to fetch account status from Razorpay: {str(e)}")
            return {
                'account_status': partner_account.account_status,
                'kyc_status': partner_account.kyc_status,
                'bank_verification_status': partner_account.bank_verification_status,
                'can_receive_transfers': False,
                'error': str(e)
            }

    def verify_bank_account(self, bank_account: PartnerBankAccount) -> Dict[str, Any]:
        """
        Initiate bank account verification by requesting product configuration (Step 3).
        Use add_bank_account to also submit settlements (Step 4).

        Args:
            bank_account: PartnerBankAccount instance

        Returns:
            Dict containing product configuration response
        """
        try:
            account = bank_account.razorpay_account
            if not getattr(account, 'razorpay_account_id', None):
                raise RazorpayException("Linked Account ID is missing. Create the account first.")

            # Ensure stakeholder exists before requesting product configuration
            rp = account.razorpay_response or {}
            if not rp.get('stakeholder_id'):
                self.create_stakeholder(account)

            # Request product configuration
            response = self.request_product_configuration(account)

            bank_account.verification_status = VerificationStatus.IN_PROGRESS
            bank_account.record_verification_attempt()

            logger.info(f"Initiated bank account verification for {bank_account.account_number[-4:]} (product config requested)")
            return response
        except (razorpay.errors.BadRequestError, razorpay.errors.GatewayError, razorpay.errors.ServerError) as e:
            error_msg = f"Bank account verification failed: {str(e)}"
            logger.error(error_msg)
            bank_account.record_verification_attempt(error_msg)
            raise RazorpayException(error_msg) from e
        except RazorpayException:
            raise
        except Exception as e:
            error_msg = f"Unexpected error initiating verification: {str(e)}"
            logger.error(error_msg)
            bank_account.record_verification_attempt(error_msg)
            raise RazorpayException(error_msg) from e

    def _map_document_type_to_razorpay(self, document_type: str) -> str:
        """
        Map internal document type to Razorpay v2 API document_type values.
        Returns the correct document_type value for Razorpay upload API based on documentation.
        
        Available document types from Razorpay documentation:
        - business_proof_of_identification:
            - shop_establishment_certificate
            - gst_certificate  
            - msme_certificate
            - business_proof_url (for HUF deed, Certificate of Incorporation, Partnership Deed, NGO Certificate, Trust Certificate and Society Certificate)
            - business_pan_url
        - additional_documents:
            - form_12_a_url
            - form_80g_url
            - cancelled_cheque
        """
        mapping = {
            DocumentType.PAN_CARD: 'business_pan_url',
            DocumentType.AADHAAR_CARD: 'business_proof_url',  # Individual proof but can be used as business proof
            DocumentType.BANK_STATEMENT: 'business_proof_url',
            DocumentType.CANCELLED_CHEQUE: 'cancelled_cheque',
            DocumentType.BUSINESS_REGISTRATION: 'business_proof_url',
            DocumentType.GST_CERTIFICATE: 'gst_certificate',
            # Additional common document types
            'shop_establishment_certificate': 'shop_establishment_certificate',
            'msme_certificate': 'msme_certificate',
            'form_12_a': 'form_12_a_url',
            'form_80g': 'form_80g_url',
        }
        
        # Return mapped type or default to business_proof_url for most business documents
        return mapping.get(document_type, 'business_proof_url')

    def _can_upload_documents(self, partner_account: PartnerRazorpayAccount) -> bool:
        """
        Check if documents can be uploaded for the current account status.
        Documents can only be uploaded when Razorpay requests clarification or when account is under review.
        
        Args:
            partner_account: PartnerRazorpayAccount instance
            
        Returns:
            bool: True if documents can be uploaded, False otherwise
        """
        # Get fresh account status from Razorpay
        try:
            if not partner_account.razorpay_account_id:
                return False
                
            response = self.client.account.fetch(partner_account.razorpay_account_id)
            
            # Check if account needs clarification
            activation_form_milestone = response.get('activation_form_milestone')
            
            # Documents can be uploaded in these scenarios:
            # 1. Account is under review (submitted status)
            # 2. Account needs clarification (needs_clarification status)  
            # 3. Account has fields that need update (under_review with specific milestones)
            account_status = response.get('status', '').lower()
            
            # These are the statuses where document upload is typically allowed
            uploadable_statuses = [
                'under_review',
                'needs_clarification', 
                'submitted',
                'activated'  # Sometimes documents can be uploaded even when activated
            ]
            
            if account_status in uploadable_statuses:
                return True
                
            # Check specific milestones that might require document uploads
            if activation_form_milestone:
                clarification_milestones = [
                    'needs_clarification',
                    'bank_details_verification_pending',
                    'kyc_verification_pending'
                ]
                if any(milestone in str(activation_form_milestone).lower() for milestone in clarification_milestones):
                    return True
            
            # If account is newly created, allow document upload
            if account_status in ['created', 'activated'] and not partner_account.kyc_documents.exists():
                return True
                
            logger.info(f"Document upload not allowed for account {partner_account.razorpay_account_id} with status: {account_status}, milestone: {activation_form_milestone}")
            return False
            
        except Exception as e:
            logger.warning(f"Error checking if documents can be uploaded: {str(e)}")
            # If we can't determine status, allow upload (fail open)
            return True

    def upload_stakeholder_document(self, partner_account: PartnerRazorpayAccount,
                                   stakeholder_id: str, document_type: str, 
                                   document_file: UploadedFile) -> Dict[str, Any]:
        """
        Upload document for a specific stakeholder
        
        Args:
            partner_account: PartnerRazorpayAccount instance
            stakeholder_id: Razorpay stakeholder ID
            document_type: Type of document to upload
            document_file: Uploaded file
            
        Returns:
            Dict containing upload response
            
        Raises:
            RazorpayException: If document upload fails
        """
        try:
            # Validate file before processing
            self._validate_document_file(document_file)
            
            if not partner_account.razorpay_account_id:
                raise RazorpayException("Razorpay account must be created before uploading stakeholder documents")
            
            # Check if documents can be uploaded
            if not self._can_upload_documents(partner_account):
                raise RazorpayException(
                    f"Stakeholder documents cannot be uploaded for account in current status: {partner_account.account_status}"
                )
            
            # Prepare file data
            document_file.seek(0)
            content_type = getattr(document_file, 'content_type', None) or 'application/octet-stream'
            file_content = document_file.read()
            document_file.seek(0)
            
            if not file_content:
                raise RazorpayException("Document file is empty or corrupted")
            
            # Use direct HTTP request for stakeholder document upload
            url = f"https://api.razorpay.com/v2/accounts/{partner_account.razorpay_account_id}/stakeholders/{stakeholder_id}/documents"
            
            files = {
                'file': (document_file.name, file_content, content_type)
            }
            
            data = {
                'document_type': document_type
            }
            
            response = requests.post(
                url,
                auth=self._auth,
                files=files,
                data=data,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                logger.info(f"Uploaded stakeholder document to account {partner_account.razorpay_account_id}, stakeholder {stakeholder_id}")
                return response_data
            else:
                error_details = response.text
                try:
                    error_json = response.json()
                    error_details = error_json.get('error', {}).get('description', error_details)
                except:
                    pass
                raise RazorpayException(f"Stakeholder document upload failed: {error_details}")
                
        except requests.exceptions.RequestException as e:
            error_msg = f"Network error uploading stakeholder document: {str(e)}"
            logger.error(error_msg)
            raise RazorpayException(error_msg) from e
        except Exception as e:
            error_msg = f"Unexpected error uploading stakeholder document: {str(e)}"
            logger.error(error_msg)
            raise RazorpayException(error_msg) from e

    def get_upload_requirements(self, partner_account: PartnerRazorpayAccount) -> Dict[str, Any]:
        """
        Get the current upload requirements for an account.
        This helps determine what documents are needed for clarification.
        
        Args:
            partner_account: PartnerRazorpayAccount instance
            
        Returns:
            Dict containing upload requirements and account status
        """
        try:
            if not partner_account.razorpay_account_id:
                return {
                    'can_upload': False,
                    'reason': 'Razorpay account not created',
                    'required_documents': []
                }
            
            # Fetch account details
            response = self.client.account.fetch(partner_account.razorpay_account_id)
            
            account_status = response.get('status', '')
            activation_form_milestone = response.get('activation_form_milestone')
            can_upload = self._can_upload_documents(partner_account)
            
            # Determine required documents based on status
            required_documents = []
            if can_upload:
                # Basic required documents
                required_documents = [
                    {
                        'type': 'business_pan_url',
                        'description': 'Business PAN Card'
                    },
                    {
                        'type': 'business_proof_url', 
                        'description': 'Business Registration Certificate'
                    }
                ]
                
                # Add conditional documents based on business type
                if hasattr(partner_account, 'business_type'):
                    if partner_account.business_type in ['partnership', 'private_limited', 'public_limited']:
                        required_documents.append({
                            'type': 'gst_certificate',
                            'description': 'GST Certificate'
                        })
            
            return {
                'can_upload': can_upload,
                'account_status': account_status,
                'activation_form_milestone': activation_form_milestone,
                'required_documents': required_documents,
                'reason': f"Account status: {account_status}" if not can_upload else "Upload allowed"
            }
            
        except Exception as e:
            logger.error(f"Error getting upload requirements: {str(e)}")
            return {
                'can_upload': False,
                'reason': f"Error checking requirements: {str(e)}",
                'required_documents': []
            }

    def debug_upload_status(self, partner_account: PartnerRazorpayAccount) -> Dict[str, Any]:
        """
        Debug method to get detailed information about why document upload might be failing.
        Useful for troubleshooting upload issues.
        
        Args:
            partner_account: PartnerRazorpayAccount instance
            
        Returns:
            Dict containing detailed debug information
        """
        debug_info = {
            'partner_account_id': partner_account.id,
            'razorpay_account_id': partner_account.razorpay_account_id,
            'local_account_status': partner_account.account_status,
            'local_kyc_status': partner_account.kyc_status,
            'can_upload_documents': False,
            'razorpay_account_details': {},
            'existing_documents': [],
            'recommendations': []
        }
        
        try:
            if not partner_account.razorpay_account_id:
                debug_info['recommendations'].append("Create Razorpay account first before uploading documents")
                return debug_info
            
            # Get account details from Razorpay
            try:
                account_details = self.client.account.fetch(partner_account.razorpay_account_id)
                debug_info['razorpay_account_details'] = {
                    'status': account_details.get('status'),
                    'activation_form_milestone': account_details.get('activation_form_milestone'),
                    'created_at': account_details.get('created_at'),
                    'activated_at': account_details.get('activated_at')
                }
            except Exception as e:
                debug_info['razorpay_fetch_error'] = str(e)
                debug_info['recommendations'].append("Check if Razorpay account ID is valid")
            
            # Check upload capability
            debug_info['can_upload_documents'] = self._can_upload_documents(partner_account)
            
            # Get existing documents
            debug_info['existing_documents'] = list(
                partner_account.kyc_documents.values_list('document_type', 'verification_status')
            )
            
            # Add recommendations based on status
            account_status = debug_info['razorpay_account_details'].get('status', '').lower()
            if account_status == 'activated':
                debug_info['recommendations'].append(
                    "Account is activated. Documents may only be uploadable if Razorpay requests clarification."
                )
            elif account_status == 'under_review':
                debug_info['recommendations'].append(
                    "Account is under review. Documents should be uploadable."
                )
            elif account_status == 'needs_clarification':
                debug_info['recommendations'].append(
                    "Account needs clarification. Documents should be uploadable."
                )
            elif not account_status:
                debug_info['recommendations'].append(
                    "Could not determine account status. Check Razorpay connection."
                )
            
            if not debug_info['can_upload_documents']:
                debug_info['recommendations'].append(
                    "Try checking account status directly in Razorpay dashboard to see if clarification is needed."
                )
            
        except Exception as e:
            debug_info['debug_error'] = str(e)
            debug_info['recommendations'].append(f"Debug failed: {str(e)}")
        
        return debug_info

    def _update_kyc_status(self, partner_account: PartnerRazorpayAccount):
        """Update KYC status based on uploaded documents"""
        required_docs = [DocumentType.PAN_CARD]
        uploaded_docs = partner_account.kyc_documents.values_list('document_type', flat=True)

        if all(doc in uploaded_docs for doc in required_docs):
            partner_account.kyc_status = KYCStatus.SUBMITTED
            partner_account.save(update_fields=['kyc_status'])

    def _sync_account_status(self, partner_account: PartnerRazorpayAccount, razorpay_data: Dict[str, Any]):
        """Sync local account status with Razorpay data"""
        status_mapping = {
            'created': AccountStatus.PENDING,
            'activated': AccountStatus.ACTIVE,
            'suspended': AccountStatus.SUSPENDED,
            'rejected': AccountStatus.REJECTED
        }

        razorpay_status = razorpay_data.get('status', 'created')
        new_status = status_mapping.get(razorpay_status, AccountStatus.PENDING)

        if partner_account.account_status != new_status:
            old_status = partner_account.account_status
            partner_account.account_status = new_status
            partner_account.activation_form_milestone = razorpay_data.get('activation_form_milestone')
            partner_account.save(update_fields=['account_status', 'activation_form_milestone'])

            # Log status change
            self._log_activity(
                partner_account,
                'account_status_updated',
                f"Account status changed from {old_status} to {new_status}"
            )

    def _log_activity(self, partner_account: PartnerRazorpayAccount, activity_type: str,
                     description: str, related_document: Optional[PartnerKYCDocument] = None,
                     related_bank_account: Optional[PartnerBankAccount] = None,
                     performed_by: Optional[Any] = None):
        """Log account verification activity"""
        AccountVerificationLog.objects.create(
            razorpay_account=partner_account,
            activity_type=activity_type,
            description=description,
            related_document=related_document,
            related_bank_account=related_bank_account,
            performed_by=performed_by
        )
