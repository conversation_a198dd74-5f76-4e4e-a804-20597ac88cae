"""
Account Security Module

Provides security features for partner account management including:
- Rate limiting for account operations
- File upload security for KYC documents
- Audit logging for sensitive operations
- Data encryption for sensitive fields
"""

import hashlib
import base64
import os
from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken
from cryptography.hazmat.primitives.kdf.pbkdf2 import PB<PERSON><PERSON>2HMAC
from cryptography.hazmat.primitives import hashes
import logging
from typing import Dict, Any, Optional
from django.core.cache import cache
from django.core.files.uploadedfile import UploadedFile
from django.conf import settings
from django.utils import timezone
try:
    from zoneinfo import ZoneInfo
except Exception:
    # Fallback to pytz if zoneinfo not available
    try:
        from pytz import timezone as ZoneInfo
    except Exception:
        ZoneInfo = None

from ..models import AccountVerificationLog, PartnerRazorpayAccount
from ..exceptions import AccountCreationException, KYCException

logger = logging.getLogger(__name__)


class AccountSecurityManager:
    """Manages security features for partner accounts"""
    
    # Rate limiting settings
    ACCOUNT_CREATION_LIMIT = 3  # Max 3 account creation attempts per day
    DOCUMENT_UPLOAD_LIMIT = 10  # Max 10 document uploads per hour
    BANK_ACCOUNT_LIMIT = 5  # Max 5 bank account additions per day
    
    # File upload security settings
    ALLOWED_DOCUMENT_TYPES = {
        'image/jpeg', 'image/jpg', 'image/png', 'application/pdf'
    }
    MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB
    
    def __init__(self):
        self.cache_timeout = 3600  # 1 hour
    
    def check_account_creation_rate_limit(self, partner_id: str) -> bool:
        """
        Check if partner can create account (rate limiting)
        
        Args:
            partner_id: Partner profile ID
            
        Returns:
            bool: True if allowed, False if rate limited
        """
        cache_key = f"account_creation_limit:{partner_id}"

        # Use atomic cache operations to avoid TOCTOU race conditions.
        # Strategy:
        # 1. Attempt cache.incr(cache_key). If key missing (ValueError), attempt cache.add(key, 1, 24h).
        # 2. If add succeeds, attempts = 1. If add fails (race), retry incr.
        # 3. If backend lacks incr/add, fall back to a best-effort read-modify-write (not fully atomic).
        try:
            attempts = cache.incr(cache_key)
        except ValueError:
            # Key does not exist yet; try to create it atomically with initial value 1
            added = cache.add(cache_key, 1, timeout=86400)
            if added:
                attempts = 1
            else:
                # Race: key created by another process; retry incr once
                try:
                    attempts = cache.incr(cache_key)
                except Exception:
                    attempts = cache.get(cache_key, 0) or 0
        except (AttributeError, NotImplementedError):
            # Backend doesn't support incr/add semantics; fallback (not perfectly atomic)
            current = cache.get(cache_key, 0) or 0
            attempts = int(current) + 1
            cache.set(cache_key, attempts, timeout=86400)

        if attempts > self.ACCOUNT_CREATION_LIMIT:
            logger.warning(f"Account creation rate limit exceeded for partner {partner_id}")
            return False

        return True
    
    def check_document_upload_rate_limit(self, partner_id: str) -> bool:
        """
        Check if partner can upload documents (rate limiting)
        
        Args:
            partner_id: Partner profile ID
            
        Returns:
            bool: True if allowed, False if rate limited
        """
        cache_key = f"document_upload_limit:{partner_id}"

        # Try atomic increment first
        try:
            attempts = cache.incr(cache_key)
        except ValueError:
            # Key missing - try to atomically add it with initial value 1 and timeout
            added = cache.add(cache_key, 1, timeout=self.cache_timeout)
            if added:
                attempts = 1
            else:
                # Race: key was created between incr and add; try incr again
                try:
                    attempts = cache.incr(cache_key)
                except Exception:
                    attempts = cache.get(cache_key, 0)
        except (AttributeError, NotImplementedError):
            # Backend doesn't support incr; fall back to add+get/set
            added = cache.add(cache_key, 1, timeout=self.cache_timeout)
            if added:
                attempts = 1
            else:
                current = cache.get(cache_key, 0) or 0
                attempts = int(current) + 1
                cache.set(cache_key, attempts, timeout=self.cache_timeout)

        if attempts > self.DOCUMENT_UPLOAD_LIMIT:
            logger.warning(f"Document upload rate limit exceeded for partner {partner_id}")
            return False

        return True
    
    def check_bank_account_rate_limit(self, partner_id: str) -> bool:
        """
        Check if partner can add bank accounts (rate limiting)
        
        Args:
            partner_id: Partner profile ID
            
        Returns:
            bool: True if allowed, False if rate limited
        """
        cache_key = f"bank_account_limit:{partner_id}"

        # Try an atomic increment first. Django cache.backends typically
        # support `incr`, which raises ValueError if the key does not exist.
        try:
            attempts = cache.incr(cache_key)
        except ValueError:
            # Key missing - try to atomically add it with initial value 1 and 24h TTL
            added = cache.add(cache_key, 1, timeout=86400)
            if added:
                attempts = 1
            else:
                # Race: key was created between incr and add; try incr again
                try:
                    attempts = cache.incr(cache_key)
                except Exception:
                    # Last-resort: read current value
                    attempts = cache.get(cache_key, 0)
        except (AttributeError, NotImplementedError):
            # Backend doesn't implement incr. Fall back to add+get/set with best-effort atomicity.
            added = cache.add(cache_key, 1, timeout=86400)
            if added:
                attempts = 1
            else:
                # Read-modify-write fallback; not perfectly atomic but a reasonable fallback
                current = cache.get(cache_key, 0) or 0
                attempts = int(current) + 1
                cache.set(cache_key, attempts, timeout=86400)

        # If the incremented attempts exceed the allowed limit, rate limit the partner
        if attempts > self.BANK_ACCOUNT_LIMIT:
            logger.warning(f"Bank account addition rate limit exceeded for partner {partner_id}")
            return False

        return True
    
    def validate_document_upload(self, uploaded_file: UploadedFile) -> Dict[str, Any]:
        """
        Validate uploaded KYC document for security
        
        Args:
            uploaded_file: Django UploadedFile instance
            
        Returns:
            dict: Validation result with status and details
            
        Raises:
            KYCException: If validation fails
        """
        validation_result = {
            'is_valid': True,
            'file_hash': None,
            'file_size': uploaded_file.size,
            'content_type': uploaded_file.content_type,
            'warnings': []
        }
        
        # Check file size
        if uploaded_file.size > self.MAX_FILE_SIZE:
            raise KYCException(f"File size {uploaded_file.size} exceeds maximum allowed size {self.MAX_FILE_SIZE}")
        
        # Check content type
        if uploaded_file.content_type not in self.ALLOWED_DOCUMENT_TYPES:
            raise KYCException(f"File type {uploaded_file.content_type} not allowed")
        
        # Generate file hash for duplicate detection by streaming in fixed-size chunks
        # to avoid loading the entire file into memory when handling large or
        # concurrent uploads. Also perform a streaming check for suspicious
        # patterns and capture the initial header bytes for magic-bytes validation.
        hasher = hashlib.sha256()
        chunk_size = 64 * 1024  # 64KB

        # Patterns to scan for (lowercase for case-insensitive search).
        suspicious_patterns = [p.lower() for p in [
            b'<script',
            b'javascript:',
            b'<?php',
            b'<%',
            b'eval(',
        ]]
        max_pattern_len = max(len(p) for p in suspicious_patterns)

        suspicious_found = False
        header_bytes = b''
        prev_tail = b''

        # Read file sequentially in chunks
        while True:
            chunk = uploaded_file.read(chunk_size)
            if not chunk:
                break

            # Capture header bytes from the first chunk for magic-bytes validation
            if not header_bytes:
                header_bytes = chunk[:max(16, max_pattern_len)]

            hasher.update(chunk)

            # Lowercase chunk for pattern searching
            lower_chunk = chunk.lower()

            # Combine with previous tail to detect patterns that straddle chunks
            combined = prev_tail + lower_chunk
            for pattern in suspicious_patterns:
                if pattern in combined:
                    suspicious_found = True
                    break
            if suspicious_found:
                # We can stop pattern scanning early but must continue hashing until EOF
                # so we don't change file pointer state expected by callers.
                pass

            # Keep only the last (max_pattern_len - 1) bytes for next iteration
            if len(lower_chunk) >= (max_pattern_len - 1):
                prev_tail = lower_chunk[-(max_pattern_len - 1):]
            else:
                prev_tail = (prev_tail + lower_chunk)[- (max_pattern_len - 1):]

        # Reset file pointer for downstream code that expects to read the file
        try:
            uploaded_file.seek(0)
        except Exception:
            # Some uploaded file wrappers may not support seek; proceed defensively
            logger.debug("UploadedFile does not support seek(); downstream reads may fail")

        file_hash = hasher.hexdigest()
        validation_result['file_hash'] = file_hash

        # Check for suspicious file patterns discovered during streaming scan
        if suspicious_found:
            validation_result['warnings'].append('File contains suspicious patterns')

        # Validate file header (magic bytes) — use the captured header bytes
        if not self._validate_file_header(header_bytes, uploaded_file.content_type):
            raise KYCException("File header does not match declared content type")
        
        logger.info(f"Document validation successful: {uploaded_file.name}, hash: {file_hash[:16]}...")
        return validation_result
    
    def _check_suspicious_patterns(self, file_content: bytes) -> bool:
        """Check for suspicious patterns in file content"""
        suspicious_patterns = [
            b'<script',
            b'javascript:',
            b'<?php',
            b'<%',
            b'eval(',
        ]
        
        content_lower = file_content.lower()
        for pattern in suspicious_patterns:
            if pattern in content_lower:
                return True
        return False
    
    def _validate_file_header(self, file_content: bytes, content_type: str) -> bool:
        """Validate file header matches content type"""
        if not file_content:
            return False
        
        # Magic bytes for different file types
        magic_bytes = {
            'image/jpeg': [b'\xff\xd8\xff'],
            'image/jpg': [b'\xff\xd8\xff'],
            'image/png': [b'\x89PNG\r\n\x1a\n'],
            'application/pdf': [b'%PDF-']
        }
        
        expected_headers = magic_bytes.get(content_type, [])
        if not expected_headers:
            return True  # Unknown type, allow
        
        for header in expected_headers:
            if file_content.startswith(header):
                return True
        
        return False
    
    def log_sensitive_operation(self, 
                              partner_account: PartnerRazorpayAccount,
                              operation_type: str,
                              description: str,
                              user=None,
                              metadata: Optional[Dict[str, Any]] = None):
        """
        Log sensitive account operations for audit trail
        
        Args:
            partner_account: PartnerRazorpayAccount instance
            operation_type: Type of operation (e.g., 'account_created', 'bank_added')
            description: Human-readable description
            user: User who performed the operation
            metadata: Additional metadata
        """
        try:
            AccountVerificationLog.objects.create(
                razorpay_account=partner_account,
                activity_type=operation_type,
                description=description,
                performed_by=user,
                metadata=metadata or {}
            )
            
            logger.info(f"Logged sensitive operation: {operation_type} for partner {partner_account.partner.id}")
            
        except Exception as e:
            logger.error(f"Failed to log sensitive operation: {str(e)}")
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """
        Encrypt sensitive data using Fernet symmetric encryption.

        Args:
            data: Data to encrypt

        Returns:
            str: URL-safe base64 encoded encrypted token

        Raises:
            RuntimeError: If ENCRYPTION_KEY is not set in settings
        """
        f = self._get_fernet()
        token = f.encrypt(data.encode())
        # return as str
        return token.decode()

    def decrypt_sensitive_data(self, token: str) -> str:
        """
        Decrypt data previously encrypted with `encrypt_sensitive_data`.

        Args:
            token: Encrypted token (string)

        Returns:
            str: Decrypted original data

        Raises:
            RuntimeError: If ENCRYPTION_KEY is not set in settings
            ValueError: If token is invalid or decryption fails
        """
        f = self._get_fernet()
        try:
            data = f.decrypt(token.encode())
            return data.decode()
        except InvalidToken:
            raise ValueError("Invalid encrypted token or wrong encryption key")

    def _get_fernet(self) -> Fernet:
        """Return a cached Fernet instance initialized from settings.ENCRYPTION_KEY.

        Expects `settings.ENCRYPTION_KEY` to be a URL-safe base64-encoded 32-byte key.
        """
        key = getattr(settings, 'ENCRYPTION_KEY', None)
        if not key:
            raise RuntimeError("ENCRYPTION_KEY is not configured in Django settings")
        # Accept either bytes or str
        if isinstance(key, str):
            key_bytes = key.encode()
        else:
            key_bytes = key

        # First, try to interpret the provided key as a URL-safe base64-encoded key
        # and ensure the decoded result is exactly 32 bytes as required by Fernet.
        try:
            decoded = base64.urlsafe_b64decode(key_bytes)
            if len(decoded) == 32:
                # Re-encode to ensure proper padding and return Fernet
                fernet_key = base64.urlsafe_b64encode(decoded)
                return Fernet(fernet_key)
            # If decoded length is not 32, fall through to KDF derivation attempt
        except Exception:
            # Decoding failed; treat as possible passphrase/raw bytes to be derived
            decoded = None

        # If the key is not a valid urlsafe base64 32-byte key, attempt to derive
        # a key using PBKDF2HMAC from a passphrase. This requires a salt to be
        # configured via settings.ENCRYPTION_KEY_SALT or environment variable
        # ENCRYPTION_KEY_SALT. As a last resort we may use settings.SECRET_KEY
        # as the salt, but it's strongly recommended to provide a dedicated salt.
        salt = getattr(settings, 'ENCRYPTION_KEY_SALT', None) or os.environ.get('ENCRYPTION_KEY_SALT')
        if salt is None and hasattr(settings, 'SECRET_KEY'):
            salt = settings.SECRET_KEY

        if salt is None:
            raise RuntimeError(
                "Invalid ENCRYPTION_KEY: not a 32-byte urlsafe base64 key and no salt available to derive one. "
                "Provide a valid URL-safe base64 32-byte key or set ENCRYPTION_KEY_SALT in settings/env to derive a key from a passphrase."
            )

        if isinstance(salt, str):
            salt_bytes = salt.encode()
        else:
            salt_bytes = salt

        # Iterations may be tuned via settings; default to >=100_000 for security
        iterations = int(getattr(settings, 'ENCRYPTION_KEY_KDF_ITERATIONS', 100_000))

        try:
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt_bytes,
                iterations=iterations,
            )

            # Use the original key bytes (passphrase) as input to KDF
            derived_key = kdf.derive(key_bytes)
            fernet_key = base64.urlsafe_b64encode(derived_key)
            return Fernet(fernet_key)
        except Exception as exc:
            logger.exception("Failed to derive Fernet key from ENCRYPTION_KEY")
            raise RuntimeError(
                "Invalid ENCRYPTION_KEY; must be a 32 url-safe base64-encoded key or a passphrase with ENCRYPTION_KEY_SALT configured"
            ) from exc
    
    def mask_sensitive_data(self, data: str, mask_char: str = '*', visible_chars: int = 4) -> str:
        """
        Mask sensitive data for logging/display
        
        Args:
            data: Data to mask
            mask_char: Character to use for masking
            visible_chars: Number of characters to keep visible
            
        Returns:
            str: Masked data
        """
        if not data or len(data) <= visible_chars:
            return data if data else ''
        
        return data[:visible_chars] + mask_char * (len(data) - visible_chars)
    
    def check_duplicate_document(self, file_hash: str, partner_id: str) -> bool:
        """
        Check if document with same hash already exists for partner
        
        Args:
            file_hash: SHA256 hash of file content
            partner_id: Partner profile ID
            
        Returns:
            bool: True if duplicate found, False otherwise
        """
        from ..models import PartnerKYCDocument
        
        return PartnerKYCDocument.objects.filter(
            razorpay_account__partner_id=partner_id,
            file_hash=file_hash
        ).exists()
    
    def generate_secure_reference(self, prefix: str = 'REF') -> str:
        """
        Generate secure reference number
        
        Args:
            prefix: Prefix for reference number
            
        Returns:
            str: Secure reference number
        """
        timestamp = int(timezone.now().timestamp())
        random_part = hashlib.sha256(f"{timestamp}{settings.SECRET_KEY}".encode()).hexdigest()[:8]
        return f"{prefix}_{timestamp}_{random_part}".upper()
    
    def validate_business_hours(self) -> bool:
        """
        Check if current time is within business hours for sensitive operations
        
        Returns:
            bool: True if within business hours, False otherwise
        """
        now = timezone.now()
        # Convert to Asia/Kolkata (IST) to evaluate business hours correctly
        try:
            if ZoneInfo is not None:
                ist_now = timezone.localtime(now, ZoneInfo("Asia/Kolkata"))
            else:
                # As a last resort, use server local time
                ist_now = now
        except Exception:
            # If timezone conversion fails, fallback to server time
            ist_now = now

        current_hour = ist_now.hour
        
        # Business hours: 9 AM to 6 PM IST
        business_start = 9
        business_end = 18
        
        return business_start <= current_hour < business_end
    
    def get_security_headers(self) -> Dict[str, str]:
        """
        Get security headers for API responses
        
        Returns:
            dict: Security headers
        """
        return {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=********; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'",
            'Referrer-Policy': 'strict-origin-when-cross-origin'
        }


# Global instance
account_security = AccountSecurityManager()
