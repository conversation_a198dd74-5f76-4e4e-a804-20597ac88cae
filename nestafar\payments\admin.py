from django.contrib import admin

from .models import (
	PaymentIntent,
	PaymentSplit,
	PaymentWebhookEvent,
	PartnerRazorpayAccount,
	PartnerBankAccount,
	PartnerKYCDocument,
	AccountVerificationLog,
)


class PaymentSplitInline(admin.TabularInline):
	model = PaymentSplit
	extra = 0
	fields = (
		"recipient_type",
		"recipient_name",
		"amount",
		"percentage",
		"status",
		"razorpay_account_id",
		"razorpay_transfer_id",
		"transfer_initiated_at",
		"transfer_completed_at",
	)
	readonly_fields = (
		"razorpay_transfer_id",
		"transfer_initiated_at",
		"transfer_completed_at",
	)
	show_change_link = True


class PaymentWebhookEventInline(admin.TabularInline):
	model = PaymentWebhookEvent
	extra = 0
	fields = (
		"event_id",
		"event_type",
		"entity_type",
		"entity_id",
		"processed",
		"signature_verified",
		"received_at",
	)
	readonly_fields = fields
	can_delete = False
	show_change_link = True


@admin.register(PaymentIntent)
class PaymentIntentAdmin(admin.ModelAdmin):
	list_display = (
		"reference_number",
		"context",
		"partner_name",
		"guest_name",
		"total_amount",
		"platform_commission",
		"partner_amount",
		"vendor_amount",
		"status",
		"payment_method",
		"paid_at",
		"created_at",
	)
	list_filter = (
		"context",
		"status",
		"payment_method",
		"created_at",
		"paid_at",
	)
	search_fields = (
		"reference_number",
		"razorpay_order_id",
		"razorpay_payment_id",
		"idempotency_key",
		"partner__user__name",
	)
	ordering = ("-created_at",)
	date_hierarchy = "created_at"
	inlines = [PaymentSplitInline, PaymentWebhookEventInline]
	readonly_fields = (
		"id",
		"reference_number",
		"idempotency_key",
		"razorpay_order_id",
		"razorpay_payment_id",
		"razorpay_payment_link_id",
		"split_details",
		"webhook_processed_at",
		"created_at",
		"updated_at",
		"paid_at",
		"expires_at",
		"metadata",
	)
	raw_id_fields = ("partner", "guest", "precheckin", "created_by")
	list_select_related = ("partner", "partner__user",)

	fieldsets = (
		(
			"Identification",
			{
				"fields": (
					"id",
					"reference_number",
					"idempotency_key",
					"context",
					"status",
					"payment_method",
				)
			},
		),
		(
			"Relationships",
			{"fields": ("partner", "guest", "precheckin", "created_by")},
		),
		(
			"Amounts",
			{
				"fields": (
					"total_amount",
					"platform_commission",
					"partner_amount",
					"vendor_amount",
				)
			},
		),
		(
			"Razorpay",
			{
				"fields": (
					"razorpay_order_id",
					"razorpay_payment_id",
					"razorpay_payment_link_id",
				)
			},
		),
		(
			"Timing",
			{"fields": ("paid_at", "expires_at", "webhook_processed_at")},
		),
		(
			"Details",
			{"fields": ("split_details", "metadata")},
		),
		(
			"Audit",
			{"fields": ("created_at", "updated_at")},
		),
	)

	def partner_name(self, obj):  # pragma: no cover - admin display helper
		if not obj.partner:
			return "-"
		# PartnerProfile.__str__ returns user.name
		return str(obj.partner)
	partner_name.short_description = "Partner"

	def guest_name(self, obj):  # pragma: no cover - admin display helper
		guest = getattr(obj, "guest", None)
		if not guest:
			return "-"
		return getattr(guest, "name", str(guest))
	guest_name.short_description = "Guest"

	def get_queryset(self, request):  # optimize
		qs = super().get_queryset(request)
		return qs.select_related("partner", "partner__user").prefetch_related("splits")


@admin.register(PaymentSplit)
class PaymentSplitAdmin(admin.ModelAdmin):
	list_display = (
		"payment_intent",
		"recipient_type",
		"recipient_name",
		"amount",
		"percentage",
		"status",
		"razorpay_transfer_id",
		"transfer_initiated_at",
		"transfer_completed_at",
		"retry_count",
		"created_at",
	)
	list_filter = ("recipient_type", "status", "created_at")
	search_fields = (
		"payment_intent__reference_number",
		"recipient_name",
		"razorpay_transfer_id",
		"razorpay_account_id",
	)
	ordering = ("-created_at",)
	readonly_fields = (
		"id",
		"transfer_initiated_at",
		"transfer_completed_at",
		"settled_at",
		"created_at",
		"updated_at",
		"retry_count",
		"metadata",
	)
	raw_id_fields = ("payment_intent",)
	date_hierarchy = "created_at"

	fieldsets = (
		("Association", {"fields": ("payment_intent",)}),
		(
			"Recipient",
			{"fields": ("recipient_type", "recipient_id", "recipient_name")},
		),
		(
			"Amounts",
			{"fields": ("amount", "percentage")},
		),
		(
			"Transfer",
			{
				"fields": (
					"razorpay_account_id",
					"razorpay_transfer_id",
					"status",
					"transfer_initiated_at",
					"transfer_completed_at",
					"settlement_id",
					"settled_at",
				)
			},
		),
		(
			"Errors",
			{"fields": ("error_code", "error_message", "retry_count")},
		),
		("Meta", {"fields": ("metadata",)}),
		("Audit", {"fields": ("created_at", "updated_at")}),
	)


@admin.register(PaymentWebhookEvent)
class PaymentWebhookEventAdmin(admin.ModelAdmin):
	list_display = (
		"event_id",
		"event_type",
		"entity_type",
		"entity_id",
		"payment_intent",
		"processed",
		"signature_verified",
		"received_at",
	)
	list_filter = (
		"event_type",
		"processed",
		"signature_verified",
		"received_at",
	)
	search_fields = (
		"event_id",
		"entity_id",
		"payment_intent__reference_number",
	)
	date_hierarchy = "received_at"
	ordering = ("-received_at",)
	readonly_fields = (
		"id",
		"event_id",
		"event_type",
		"entity_type",
		"entity_id",
		"payment_intent",
		"raw_payload",
		"headers",
		"processed",
		"processed_at",
		"processing_attempts",
		"processing_error",
		"last_error_at",
		"signature_verified",
		"signature",
		"received_at",
		"created_at",
		"updated_at",
		"source_ip",
	)
	fieldsets = (
		("Identification", {"fields": ("event_id", "event_type")}),
		("Entity", {"fields": ("entity_type", "entity_id", "payment_intent")}),
		("Payload", {"fields": ("raw_payload", "headers")}),
		(
			"Processing",
			{
				"fields": (
					"processed",
					"processed_at",
					"processing_attempts",
					"processing_error",
					"last_error_at",
				)
			},
		),
		(
			"Security",
			{"fields": ("signature_verified", "signature", "source_ip")},
		),
		("Audit", {"fields": ("received_at", "created_at", "updated_at")}),
	)
	def has_add_permission(self, request):  # log entries only
		return False

	def has_change_permission(self, request, obj=None):  # read-only
		return False


# Inline classes for partner account models
class PartnerBankAccountInline(admin.TabularInline):
	model = PartnerBankAccount
	extra = 0
	fields = (
		"account_holder_name",
		"account_number",
		"ifsc_code",
		"bank_name",
		"account_type",
		"verification_status",
		"is_primary",
		"verification_attempts",
		"verified_at",
	)
	readonly_fields = (
		"razorpay_bank_account_id",
		"verification_attempts",
		"last_verification_attempt",
		"verification_error",
		"verified_at",
		"created_at",
		"updated_at",
	)
	show_change_link = True


class PartnerKYCDocumentInline(admin.TabularInline):
	model = PartnerKYCDocument
	extra = 0
	fields = (
		"document_type",
		"document_number",
		"verification_status",
		"file_size",
		"file_type",
		"verified_at",
	)
	readonly_fields = (
		"razorpay_document_id",
		"file_size",
		"file_type",
		"verification_notes",
		"verified_by",
		"verified_at",
		"created_at",
		"updated_at",
	)
	show_change_link = True


class AccountVerificationLogInline(admin.TabularInline):
	model = AccountVerificationLog
	extra = 0
	fields = (
		"activity_type",
		"description",
		"performed_by",
		"created_at",
	)
	readonly_fields = fields
	can_delete = False
	show_change_link = True


@admin.register(PartnerRazorpayAccount)
class PartnerRazorpayAccountAdmin(admin.ModelAdmin):
	list_display = (
		"partner_name",
		"business_name",
		"business_type",
		"account_status",
		"kyc_status",
		"bank_verification_status",
		"contact_email",
		"contact_phone",
		"created_at",
		"activated_at",
	)
	list_filter = (
		"account_status",
		"kyc_status",
		"bank_verification_status",
		"business_type",
		"country",
		"created_at",
		"activated_at",
	)
	search_fields = (
		"partner__user__name",
		"partner__user__email",
		"business_name",
		"contact_name",
		"contact_email",
		"razorpay_account_id",
	)
	ordering = ("-created_at",)
	date_hierarchy = "created_at"
	inlines = [PartnerBankAccountInline, PartnerKYCDocumentInline, AccountVerificationLogInline]
	readonly_fields = (
		"id",
		"razorpay_account_id",
		"razorpay_response",
		"activation_form_milestone",
		"created_at",
		"updated_at",
		"activated_at",
		"last_error",
		"error_count",
	)
	raw_id_fields = ("partner",)
	list_select_related = ("partner", "partner__user")

	fieldsets = (
		(
			"Partner Information",
			{
				"fields": (
					"id",
					"partner",
					"razorpay_account_id",
				)
			},
		),
		(
			"Business Details",
			{
				"fields": (
					"business_name",
					"business_type",
				)
			},
		),
		(
			"Contact Information",
			{
				"fields": (
					"contact_name",
					"contact_email",
					"contact_phone",
				)
			},
		),
		(
			"Address",
			{
				"fields": (
					"address_line1",
					"address_line2",
					"city",
					"state",
					"postal_code",
					"country",
				)
			},
		),
		(
			"Status",
			{
				"fields": (
					"account_status",
					"kyc_status",
					"bank_verification_status",
					"activation_form_milestone",
				)
			},
		),
		(
			"Timestamps",
			{
				"fields": (
					"created_at",
					"updated_at",
					"activated_at",
				)
			},
		),
		(
			"Error Tracking",
			{
				"fields": (
					"last_error",
					"error_count",
				)
			},
		),
		(
			"Razorpay Data",
			{
				"fields": ("razorpay_response",),
				"classes": ("collapse",),
			},
		),
	)

	def partner_name(self, obj):
		if not obj.partner or not obj.partner.user:
			return "-"
		return obj.partner.user.name
	partner_name.short_description = "Partner Name"
	partner_name.admin_order_field = "partner__user__name"

	def get_queryset(self, request):
		qs = super().get_queryset(request)
		return qs.select_related("partner", "partner__user").prefetch_related(
			"bank_accounts", "kyc_documents", "verification_logs"
		)


@admin.register(PartnerBankAccount)
class PartnerBankAccountAdmin(admin.ModelAdmin):
	list_display = (
		"razorpay_account_partner",
		"account_holder_name",
		"masked_account_number",
		"ifsc_code",
		"bank_name",
		"account_type",
		"verification_status",
		"is_primary",
		"verification_attempts",
		"verified_at",
		"created_at",
	)
	list_filter = (
		"account_type",
		"verification_status",
		"is_primary",
		"created_at",
		"verified_at",
	)
	search_fields = (
		"razorpay_account__partner__user__name",
		"account_holder_name",
		"account_number",
		"ifsc_code",
		"bank_name",
		"razorpay_bank_account_id",
	)
	ordering = ("-created_at",)
	date_hierarchy = "created_at"
	readonly_fields = (
		"id",
		"razorpay_bank_account_id",
		"verification_attempts",
		"last_verification_attempt",
		"verification_error",
		"verified_at",
		"created_at",
		"updated_at",
	)
	raw_id_fields = ("razorpay_account",)
	list_select_related = ("razorpay_account", "razorpay_account__partner", "razorpay_account__partner__user")

	fieldsets = (
		(
			"Account Association",
			{"fields": ("razorpay_account",)}
		),
		(
			"Bank Details",
			{
				"fields": (
					"account_holder_name",
					"account_number",
					"ifsc_code",
					"bank_name",
					"branch_name",
					"account_type",
				)
			},
		),
		(
			"Verification",
			{
				"fields": (
					"verification_status",
					"is_primary",
					"razorpay_bank_account_id",
					"verification_attempts",
					"last_verification_attempt",
					"verification_error",
					"verified_at",
				)
			},
		),
		(
			"Timestamps",
			{"fields": ("created_at", "updated_at")}
		),
	)

	def razorpay_account_partner(self, obj):
		if not obj.razorpay_account or not obj.razorpay_account.partner or not obj.razorpay_account.partner.user:
			return "-"
		return obj.razorpay_account.partner.user.name
	razorpay_account_partner.short_description = "Partner"
	razorpay_account_partner.admin_order_field = "razorpay_account__partner__user__name"

	def masked_account_number(self, obj):
		if obj.account_number:
			return f"****{obj.account_number[-4:]}"
		return "-"
	masked_account_number.short_description = "Account Number"


@admin.register(PartnerKYCDocument)
class PartnerKYCDocumentAdmin(admin.ModelAdmin):
	list_display = (
		"razorpay_account_partner",
		"document_type",
		"document_number",
		"verification_status",
		"file_type",
		"file_size_mb",
		"verified_by",
		"verified_at",
		"created_at",
	)
	list_filter = (
		"document_type",
		"verification_status",
		"file_type",
		"created_at",
		"verified_at",
	)
	search_fields = (
		"razorpay_account__partner__user__name",
		"document_number",
		"razorpay_document_id",
		"verified_by",
	)
	ordering = ("-created_at",)
	date_hierarchy = "created_at"
	readonly_fields = (
		"id",
		"razorpay_document_id",
		"file_size",
		"file_type",
		"verification_notes",
		"verified_by",
		"verified_at",
		"created_at",
		"updated_at",
	)
	raw_id_fields = ("razorpay_account",)
	list_select_related = ("razorpay_account", "razorpay_account__partner", "razorpay_account__partner__user")

	fieldsets = (
		(
			"Account Association",
			{"fields": ("razorpay_account",)}
		),
		(
			"Document Details",
			{
				"fields": (
					"document_type",
					"document_number",
					"document_file",
				)
			},
		),
		(
			"File Information",
			{
				"fields": (
					"file_size",
					"file_type",
					"razorpay_document_id",
				)
			},
		),
		(
			"Verification",
			{
				"fields": (
					"verification_status",
					"verification_notes",
					"verified_by",
					"verified_at",
				)
			},
		),
		(
			"Timestamps",
			{"fields": ("created_at", "updated_at")}
		),
	)

	def razorpay_account_partner(self, obj):
		if not obj.razorpay_account or not obj.razorpay_account.partner or not obj.razorpay_account.partner.user:
			return "-"
		return obj.razorpay_account.partner.user.name
	razorpay_account_partner.short_description = "Partner"
	razorpay_account_partner.admin_order_field = "razorpay_account__partner__user__name"

	def file_size_mb(self, obj):
		if obj.file_size:
			return f"{obj.file_size / (1024 * 1024):.2f} MB"
		return "-"
	file_size_mb.short_description = "File Size"


@admin.register(AccountVerificationLog)
class AccountVerificationLogAdmin(admin.ModelAdmin):
	list_display = (
		"razorpay_account_partner",
		"activity_type",
		"description_short",
		"performed_by",
		"related_document_type",
		"related_bank_account_info",
		"created_at",
	)
	list_filter = (
		"activity_type",
		"created_at",
	)
	search_fields = (
		"razorpay_account__partner__user__name",
		"description",
		"performed_by__name",
	)
	ordering = ("-created_at",)
	date_hierarchy = "created_at"
	readonly_fields = (
		"id",
		"razorpay_account",
		"activity_type",
		"description",
		"related_document",
		"related_bank_account",
		"metadata",
		"performed_by",
		"created_at",
	)
	raw_id_fields = ("razorpay_account", "related_document", "related_bank_account", "performed_by")
	list_select_related = (
		"razorpay_account",
		"razorpay_account__partner",
		"razorpay_account__partner__user",
		"performed_by",
		"related_document",
		"related_bank_account",
	)

	fieldsets = (
		(
			"Account Association",
			{"fields": ("razorpay_account",)}
		),
		(
			"Activity Details",
			{
				"fields": (
					"activity_type",
					"description",
					"performed_by",
				)
			},
		),
		(
			"Related Objects",
			{
				"fields": (
					"related_document",
					"related_bank_account",
				)
			},
		),
		(
			"Metadata",
			{
				"fields": ("metadata",),
				"classes": ("collapse",),
			},
		),
		(
			"Timestamp",
			{"fields": ("created_at",)}
		),
	)

	def razorpay_account_partner(self, obj):
		if not obj.razorpay_account or not obj.razorpay_account.partner or not obj.razorpay_account.partner.user:
			return "-"
		return obj.razorpay_account.partner.user.name
	razorpay_account_partner.short_description = "Partner"
	razorpay_account_partner.admin_order_field = "razorpay_account__partner__user__name"

	def description_short(self, obj):
		if len(obj.description) > 50:
			return f"{obj.description[:50]}..."
		return obj.description
	description_short.short_description = "Description"

	def related_document_type(self, obj):
		if obj.related_document:
			return obj.related_document.document_type
		return "-"
	related_document_type.short_description = "Document Type"

	def related_bank_account_info(self, obj):
		if obj.related_bank_account:
			return f"{obj.related_bank_account.bank_name} - ****{obj.related_bank_account.account_number[-4:]}"
		return "-"
	related_bank_account_info.short_description = "Bank Account"

	def has_add_permission(self, request):
		return False  # Logs are created programmatically

	def has_change_permission(self, request, obj=None):
		return False  # Read-only logs
