"""Clean AioSell management command.

Actions:
    test-connection | setup-integration | quick-setup | configure |
    sync-calendar | sync-room-blocks | sync-all | bulk-calendar-sync |
    retry-failed | show-config | update-config | validate-mapping

Notes:
    * Preferred credential flag is --api-token. Legacy internal key 'identifier' still accepted.
    * Use --async-mode to enqueue Celery tasks; omit for direct synchronous execution.
"""

from __future__ import annotations

import json
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone

from pms.models import (
    Calendar,
    RoomBlock,
    RoomBlockSyncLog,
    OTAPlatform,
    HotelOTAIntegration,
    RoomType,
    RatePlan,
)
from stay.models import Property
from pms.services import get_aiosell_service
from pms.tasks.aiosell_tasks import (
    sync_calendar_to_aiosell,
    sync_room_block_to_aiosell,
    sync_hotel_initial_data_to_aiosell,
    bulk_sync_calendar_entries_to_aiosell,
)


class Command(BaseCommand):
    help = "Manage AioSell integration operations"

    def add_arguments(self, parser):  # noqa: D401
        parser.add_argument("--hotel-id", type=str, help="Hotel ID")
        parser.add_argument(
            "--action",
            required=True,
            choices=[
                "test-connection",
                "setup-integration",
                "quick-setup",
                "configure",
                "sync-calendar",
                "sync-room-blocks",
                "sync-all",
                "bulk-calendar-sync",
                "retry-failed",
                "show-config",
                "update-config",
                "validate-mapping",
            ],
        )
        parser.add_argument(
            "--configure",
            action="store_true",
            help="Alias for configure action (deprecated)",
        )
        # credentials
        parser.add_argument("--external-hotel-id", type=str)
        parser.add_argument("--api-token", type=str)
        parser.add_argument("--api-key", type=str)
        # date / file
        parser.add_argument("--date-from", type=str)
        parser.add_argument("--date-to", type=str)
        parser.add_argument("--config-file", type=str)
        # behaviour
        parser.add_argument("--dry-run", action="store_true")
        parser.add_argument("--async-mode", dest="async_mode", action="store_true")
        parser.add_argument("--initial-sync", action="store_true")
        parser.add_argument("--no-test", action="store_true")
        parser.add_argument("--generate-only", action="store_true")
        parser.add_argument("--summary", action="store_true")
        # config helpers
        parser.add_argument("--enable-sync", action="store_true")
        parser.add_argument("--disable-sync", action="store_true")
        parser.add_argument("--target-channels", type=str)
        parser.add_argument("--room-mapping-json", type=str)
        parser.add_argument("--rate-plans-json", type=str)
        parser.add_argument("--auto-map", action="store_true")
        parser.add_argument("--primary", action="store_true")
        # deprecated alias
        parser.add_argument(
            "--async", dest="async_mode", action="store_true", help="Deprecated alias"
        )

    # ---------------- dispatch ----------------
    def handle(self, *args, **opts):  # noqa: D401
        self.verbosity = int(opts.get("verbosity", 1))
        action = opts.get("action")
        if action in ("configure", "quick-setup") or opts.get("configure"):
            self.handle_configure(opts, quick=(action == "quick-setup"))
            return
        mapping = {
            "test-connection": self.handle_test_connection,
            "setup-integration": self.handle_setup_integration,
            "sync-calendar": self.handle_sync_calendar,
            "sync-room-blocks": self.handle_sync_room_blocks,
            "sync-all": self.handle_sync_all,
            "bulk-calendar-sync": self.handle_bulk_calendar_sync,
            "retry-failed": self.handle_retry_failed,
            "show-config": self.handle_show_config,
            "update-config": self.handle_update_config,
            "validate-mapping": self.handle_validate_mapping,
        }
        handler = mapping.get(action)
        if not handler:
            raise CommandError(f"Unknown action: {action}")
        handler(opts)

    # ---------------- helpers ----------------
    def get_hotel(self, hotel_id: str) -> Property:
        try:
            return Property.objects.get(id=hotel_id)
        except Property.DoesNotExist:  # pragma: no cover
            raise CommandError(f"Hotel with ID {hotel_id} not found")

    # ---------------- actions ----------------
    def handle_test_connection(self, opts):
        hotel_id = opts.get("hotel_id")
        if not hotel_id:
            raise CommandError("--hotel-id required")
        hotel = self.get_hotel(hotel_id)
        service = get_aiosell_service(hotel)
        if not service:
            self.stdout.write(self.style.WARNING("No AioSell integration found"))
            return
        try:
            result = service.test_connection()
            if result.get("success"):
                self.stdout.write(self.style.SUCCESS("✓ Connection test successful"))
            else:
                self.stdout.write(self.style.ERROR(f"✗ Failed: {result.get('error')}"))
        except Exception as e:  # pragma: no cover
            self.stdout.write(self.style.ERROR(f"✗ Error: {e}"))

    def handle_setup_integration(self, opts):
        hotel_id = opts.get("hotel_id")
        external = opts.get("external_hotel_id")
        # Accept --api-token (preferred) or legacy 'identifier'.
        token = opts.get("api_token") or opts.get("identifier")
        api_key = opts.get("api_key")
        if not all([hotel_id, external, token]):
            raise CommandError("--hotel-id, --external-hotel-id, --api-token required")
        hotel = self.get_hotel(hotel_id)
        if opts.get("dry_run"):
            self.stdout.write(self.style.WARNING("DRY RUN: Would set up integration"))
            return
        platform, _ = OTAPlatform.objects.get_or_create(
            name="aiosell",
            defaults={
                "api_endpoint": "https://live.aiosell.com/api/v2/cm",
                "configuration": {},
                "is_active": True,
            },
        )
        creds = {"identifier": token}
        if api_key:
            creds["api_key"] = api_key
        integration, created = HotelOTAIntegration.objects.get_or_create(
            hotel=hotel,
            ota_platform=platform,
            defaults={
                "external_hotel_id": external,
                "credentials": creds,
                "is_active": True,
            },
        )
        if not created:
            integration.external_hotel_id = external
            integration.credentials = creds
            integration.is_active = True
            integration.save()
        # Minimal initial channel manager config
        hotel.enable_channel_manager(
            "aiosell",
            {
                "sync_enabled": True,
                "auto_sync_on_block": True,
                "auto_sync_on_unblock": True,
                "target_channels": ["agoda", "booking.com"],
                "room_mapping": {},
                "rate_plans": {},
            },
        )
        hotel.save()
        self.stdout.write(self.style.SUCCESS("✓ Integration upserted"))
        service = get_aiosell_service(hotel)
        if service and (opts.get("async_mode") or opts.get("initial_sync")):
            sync_hotel_initial_data_to_aiosell.delay(
                hotel_id=str(hotel.id), integration_id=str(integration.id)
            )
            self.stdout.write("✓ Queued initial data sync")

    def handle_configure(self, opts, quick: bool = False):  # noqa: C901
        hotel_id = opts.get("hotel_id")
        if not hotel_id:
            raise CommandError("--hotel-id required")
        hotel = self.get_hotel(hotel_id)
        # Accept proper --api-token plus legacy 'identifier'
        token = opts.get("api_token") or opts.get("identifier")
        api_key = opts.get("api_key")
        external = opts.get("external_hotel_id")
        enable = bool(opts.get("enable_sync"))
        disable = bool(opts.get("disable_sync"))
        if enable and disable:
            raise CommandError("Use only one of --enable-sync / --disable-sync")
        target_channels = opts.get("target_channels")
        if target_channels:
            target_channels = [
                c.strip() for c in target_channels.split(",") if c.strip()
            ]
        platform, _ = OTAPlatform.objects.get_or_create(
            name="aiosell", defaults={"is_active": True}
        )
        if token or external:
            creds = {"identifier": token} if token else {}
            if api_key:
                creds["api_key"] = api_key
            integ, created = HotelOTAIntegration.objects.get_or_create(
                hotel=hotel,
                ota_platform=platform,
                defaults={
                    "external_hotel_id": external,
                    "credentials": creds,
                    "is_active": True,
                },
            )
            if not created:
                if external:
                    integ.external_hotel_id = external
                if creds:
                    integ.credentials = creds
                integ.is_active = True
                integ.save()
        cfg = hotel.get_channel_manager_config("aiosell") or {}
        if "enabled" in cfg and "config" in cfg:  # unwrap legacy nested structure
            cfg = cfg.get("config") or {}
        if enable:
            cfg["sync_enabled"] = True
        if disable:
            cfg["sync_enabled"] = False
        if target_channels is not None:
            cfg["target_channels"] = target_channels
        # JSON overrides
        for key, opt_name in [
            ("room_mapping", "room_mapping_json"),
            ("rate_plans", "rate_plans_json"),
        ]:
            raw = opts.get(opt_name)
            if raw:
                try:
                    cfg[key] = json.loads(raw)
                except json.JSONDecodeError as e:
                    raise CommandError(
                        f'Invalid JSON for --{opt_name.replace("_","-")}: {e}'
                    )
        # Auto map (idempotent if existing keys present)
        if opts.get("auto_map"):

            def _san(v: str) -> str:
                return "".join(
                    c for c in v.upper().replace(" ", "_") if c.isalnum() or c == "_"
                )[:12]

            rtypes = RoomType.objects.filter(hotel=hotel).order_by("name")
            rplans = RatePlan.objects.filter(room_type__hotel=hotel).select_related(
                "room_type"
            )
            cfg.setdefault("room_mapping", {rt.name: _san(rt.name) for rt in rtypes})
            gen_plans: dict[str, list[str]] = {}
            for rp in rplans:
                room_code = cfg["room_mapping"].get(rp.room_type.name) or _san(
                    rp.room_type.name
                )
                code = _san(f"{room_code}_{rp.name}")
                gen_plans.setdefault(room_code, []).append(code)
            cfg.setdefault("rate_plans", gen_plans)
        if opts.get("generate_only") or opts.get("dry_run"):
            self.stdout.write(
                self.style.WARNING("DRY RUN: Generated configuration (not saved)")
            )
            self.stdout.write(json.dumps(cfg, indent=2))
            return
        hotel.enable_channel_manager("aiosell", cfg)
        if opts.get("primary"):
            hotel.primary_channel_manager = "aiosell"
        hotel.save()
        self.stdout.write(self.style.SUCCESS("✓ Configuration saved"))
        service = get_aiosell_service(hotel)
        if service and not opts.get("no_test"):
            try:
                test = service.test_connection()
                if test.get("success"):
                    self.stdout.write(
                        self.style.SUCCESS("✓ Connection test successful")
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(
                            f"⚠ Connection test failed: {test.get('error')}"
                        )
                    )
            except Exception as e:  # pragma: no cover
                self.stdout.write(self.style.ERROR(f"✗ Test error: {e}"))
        if (opts.get("async_mode") or opts.get("initial_sync")) and service:
            sync_hotel_initial_data_to_aiosell.delay(
                hotel_id=str(hotel.id), integration_id=str(service.integration.id)
            )
            self.stdout.write("✓ Queued initial data sync")
        if opts.get("summary"):
            eff = hotel.get_channel_manager_config("aiosell") or {}
            self.stdout.write("--- SUMMARY ---")
            for line in [
                f"Hotel: {hotel.name} ({hotel.id})",
                f'Sync Enabled: {eff.get("sync_enabled", True)}',
                f'Target Channels: {eff.get("target_channels")}',
                f'Rooms Mapped: {len(eff.get("room_mapping", {}))}',
                f'Rate Plan Groups: {len(eff.get("rate_plans", {}))}',
            ]:
                self.stdout.write("  " + line)

    def handle_sync_calendar(self, opts):  # noqa: C901
        hotel_id = opts.get("hotel_id")
        if not hotel_id:
            raise CommandError("--hotel-id required")
        hotel = self.get_hotel(hotel_id)
        start = (
            datetime.strptime(opts["date_from"], "%Y-%m-%d").date()
            if opts.get("date_from")
            else timezone.now().date()
        )
        end = (
            datetime.strptime(opts["date_to"], "%Y-%m-%d").date()
            if opts.get("date_to")
            else start + timedelta(days=7)
        )
        if start > end:
            raise CommandError("Start date after end date")
        entries = Calendar.objects.filter(
            room_type__hotel=hotel, date__range=(start, end)
        ).select_related("room_type", "rate_plan")
        if not entries.exists():
            self.stdout.write(self.style.WARNING("No calendar entries"))
            return
        if opts["dry_run"]:
            self.stdout.write(
                self.style.WARNING(f"DRY RUN: Would sync {entries.count()} entries")
            )
            return
        if opts.get("async_mode"):
            for e in entries:
                sync_calendar_to_aiosell.delay(calendar_id=str(e.id), action="update")
            self.stdout.write(
                self.style.SUCCESS(f"✓ Queued {entries.count()} calendar tasks")
            )
        else:
            service = get_aiosell_service(hotel)
            if not service:
                raise CommandError("No service available")
            results = service.bulk_sync_calendar_entries(list(entries))
            ok = sum(1 for r in results if "error" not in r)
            err = len(results) - ok
            self.stdout.write(
                self.style.SUCCESS(
                    f"✓ Calendar sync done. Success: {ok}, Errors: {err}"
                )
            )

    def handle_sync_room_blocks(self, opts):  # noqa: C901
        hotel_id = opts.get("hotel_id")
        if not hotel_id:
            raise CommandError("--hotel-id required")
        hotel = self.get_hotel(hotel_id)
        blocks = RoomBlock.objects.filter(hotel=hotel, is_active=True).select_related(
            "room"
        )
        if not blocks.exists():
            self.stdout.write(self.style.WARNING("No active room blocks"))
            return
        if opts["dry_run"]:
            self.stdout.write(
                self.style.WARNING(f"DRY RUN: Would sync {blocks.count()} blocks")
            )
            return
        if opts.get("async_mode"):
            q = 0
            for b in blocks:
                try:
                    sync_log = RoomBlockSyncLog.objects.create(
                        room_block=b,
                        hotel=hotel,
                        ota_platform=HotelOTAIntegration.objects.get(
                            hotel=hotel, ota_platform__name="aiosell", is_active=True
                        ).ota_platform,
                        action="update",
                        sync_status="pending",
                    )
                    sync_room_block_to_aiosell.delay(
                        room_block_id=str(b.id),
                        sync_log_id=str(sync_log.id),
                        action="update",
                    )
                    q += 1
                except Exception as e:  # pragma: no cover
                    self.stdout.write(
                        self.style.WARNING(f"Block {b.id} queue error: {e}")
                    )
            self.stdout.write(self.style.SUCCESS(f"✓ Queued {q} room block tasks"))
        else:
            service = get_aiosell_service(hotel)
            if not service:
                raise CommandError("No service available")
            ok = err = 0
            for b in blocks:
                try:
                    service.sync_room_block(b, "update")
                    ok += 1
                except Exception as e:  # pragma: no cover
                    err += 1
                    if self.verbosity >= 1:
                        self.stdout.write(f"  ✗ Block {b.id} error: {e}")
            self.stdout.write(
                self.style.SUCCESS(
                    f"✓ Room block sync done. Success: {ok}, Errors: {err}"
                )
            )

    def handle_sync_all(self, opts):  # noqa: C901
        hotel_id = opts.get("hotel_id")
        if not hotel_id:
            raise CommandError("--hotel-id required")
        hotel = self.get_hotel(hotel_id)
        if opts["dry_run"]:
            self.stdout.write(self.style.WARNING("DRY RUN: Would full-sync"))
            return
        try:
            integration = HotelOTAIntegration.objects.get(
                hotel=hotel, ota_platform__name="aiosell", is_active=True
            )
        except HotelOTAIntegration.DoesNotExist:
            raise CommandError("No active integration")
        if opts.get("async_mode"):
            sync_hotel_initial_data_to_aiosell.delay(
                hotel_id=str(hotel.id), integration_id=str(integration.id)
            )
            self.stdout.write(self.style.SUCCESS("✓ Queued full sync task"))
            return
        service = get_aiosell_service(hotel)
        if not service:
            raise CommandError("No service available")
        test = service.test_connection()
        if not test.get("success"):
            raise CommandError(f"Connection failed: {test.get('error')}")
        start = timezone.now().date()
        end = start + timedelta(days=30)
        entries = Calendar.objects.filter(
            room_type__hotel=hotel, date__range=(start, end)
        )
        cal_ok = cal_err = 0
        if entries.exists():
            try:
                res = service.bulk_sync_calendar_entries(list(entries))
                cal_ok = sum(1 for r in res if "error" not in r)
                cal_err = len(res) - cal_ok
            except Exception as e:  # pragma: no cover
                self.stdout.write(f"Calendar sync error: {e}")
                cal_err = entries.count()
        blocks = RoomBlock.objects.filter(hotel=hotel, is_active=True)
        blk_ok = blk_err = 0
        for b in blocks:
            try:
                service.sync_room_block(b, "update")
                blk_ok += 1
            except Exception as e:  # pragma: no cover
                blk_err += 1
        self.stdout.write(
            self.style.SUCCESS(
                f"✓ Complete sync finished. Calendar: {cal_ok} success, {cal_err} errors. Blocks: {blk_ok} success, {blk_err} errors."
            )
        )

    def handle_bulk_calendar_sync(self, opts):
        hotel_id = opts.get("hotel_id")
        if not hotel_id:
            raise CommandError("--hotel-id required")
        hotel = self.get_hotel(hotel_id)
        if opts.get("date_from") and opts.get("date_to"):
            start = datetime.strptime(opts["date_from"], "%Y-%m-%d").date()
            end = datetime.strptime(opts["date_to"], "%Y-%m-%d").date()
            days = (end - start).days + 1
        else:
            days = 30
        if opts["dry_run"]:
            self.stdout.write(
                self.style.WARNING(f"DRY RUN: Would bulk sync {days} days")
            )
            return
        bulk_sync_calendar_entries_to_aiosell.delay(
            hotel_id=str(hotel.id), date_range_days=days
        )
        self.stdout.write(
            self.style.SUCCESS(f"✓ Queued bulk calendar sync ({days} days)")
        )

    def handle_retry_failed(self, opts):
        if opts["dry_run"]:
            count = RoomBlockSyncLog.objects.filter(
                sync_status="failed",
                retry_count__lt=3,
                created_at__gte=timezone.now() - timedelta(hours=24),
                ota_platform__name="aiosell",
            ).count()
            self.stdout.write(
                self.style.WARNING(f"DRY RUN: Would retry {count} failed syncs")
            )
            return
        from pms.tasks.aiosell_tasks import retry_failed_aiosell_syncs

        retry_failed_aiosell_syncs.delay()
        self.stdout.write(self.style.SUCCESS("✓ Queued retry task"))

    def handle_show_config(self, opts):
        hotel_id = opts.get("hotel_id")
        if not hotel_id:
            raise CommandError("--hotel-id required")
        hotel = self.get_hotel(hotel_id)
        self.stdout.write(f"AioSell configuration for hotel: {hotel.name}")
        self.stdout.write("=" * 50)
        try:
            integration = HotelOTAIntegration.objects.get(
                hotel=hotel, ota_platform__name="aiosell"
            )
            self.stdout.write(
                f'Integration Status: {"Active" if integration.is_active else "Inactive"}'
            )
            self.stdout.write(f"External Hotel ID: {integration.external_hotel_id}")
            creds = integration.credentials or {}
            self.stdout.write(
                f'Has API Token: {"Yes" if creds.get("identifier") else "No"}'
            )
            self.stdout.write(f'Has API Key: {"Yes" if creds.get("api_key") else "No"}')
        except HotelOTAIntegration.DoesNotExist:
            self.stdout.write(self.style.WARNING("No AioSell integration found"))
            return
        cfg = hotel.get_channel_manager_config("aiosell") or {}
        self.stdout.write("\nChannel Manager Configuration:")
        self.stdout.write(f"  Sync Enabled: {cfg.get('sync_enabled')}")
        self.stdout.write(f"  Target Channels: {cfg.get('target_channels')}")
        rm = cfg.get("room_mapping", {})
        if rm:
            self.stdout.write(f"\nRoom Mapping ({len(rm)}):")
            for k, v in rm.items():
                self.stdout.write(f"    {k} -> {v}")
        else:
            self.stdout.write("\nRoom Mapping: None")
        rp = cfg.get("rate_plans", {})
        if rp:
            self.stdout.write(f"\nRate Plans ({len(rp)}):")
            for k, v in rp.items():
                self.stdout.write(f"    {k}: {v}")
        else:
            self.stdout.write("\nRate Plans: None")

    def handle_update_config(self, opts):
        hotel_id = opts.get("hotel_id")
        cfg_file = opts.get("config_file")
        if not hotel_id:
            raise CommandError("--hotel-id required")
        if not cfg_file:
            raise CommandError("--config-file required")
        hotel = self.get_hotel(hotel_id)
        import os

        try:
            path = os.path.abspath(cfg_file)
            if not os.path.isfile(path):
                raise CommandError("Config file missing")
            if not path.endswith(".json"):
                raise CommandError("Config file must be JSON")
            with open(path, "r", encoding="utf-8") as f:
                new_cfg = json.load(f)
        except Exception as e:  # pragma: no cover
            raise CommandError(f"Failed reading config file: {e}") from e
        if self.verbosity >= 1:
            self.stdout.write(self.style.WARNING("DRY RUN: Would update configuration"))
            self.stdout.write(json.dumps(new_cfg, indent=2))
            return
        current = hotel.get_channel_manager_config("aiosell") or {}
        current.update(new_cfg)
        hotel.enable_channel_manager("aiosell", current)
        hotel.save()
        self.stdout.write(self.style.SUCCESS("✓ Configuration updated"))

    def handle_validate_mapping(self, opts):  # noqa: C901
        hotel_id = opts.get("hotel_id")
        if not hotel_id:
            raise CommandError("--hotel-id required")
        hotel = self.get_hotel(hotel_id)
        self.stdout.write(f"Validating mappings for hotel: {hotel.name}")
        from pms.utils.aiosell_mapper import get_data_mapper

        mapper = get_data_mapper(hotel)
        rtypes = RoomType.objects.filter(hotel=hotel)
        self.stdout.write(f"\nRoom Type Mappings ({rtypes.count()}):")
        for rt in rtypes:
            code = mapper.map_room_to_code(room_type=rt)
            self.stdout.write(f"  {rt.name} -> {code}")
        rplans = RatePlan.objects.filter(room_type__hotel=hotel).select_related(
            "room_type"
        )
        self.stdout.write(f"\nRate Plan Mappings ({rplans.count()}):")
        for rp in rplans:
            rcode = mapper.map_room_to_code(room_type=rp.room_type)
            codes = mapper.map_rate_plan_to_codes(rcode, rp)
            self.stdout.write(f"  {rp.room_type.name}/{rp.name} -> {codes}")
        if rtypes.exists() and rplans.exists():
            self.stdout.write("\nTesting payload generation...")
            try:
                rate_payload = mapper.get_sample_rate_restrictions_payload()
                ok, errs = mapper.validate_payload(rate_payload, "rate")
                if ok:
                    self.stdout.write(
                        self.style.SUCCESS("✓ Rate restrictions payload valid")
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR("✗ Rate restrictions payload errors:")
                    )
                    for e in errs:
                        self.stdout.write("    " + e)
                inv_payload = mapper.get_sample_inventory_restrictions_payload()
                ok, errs = mapper.validate_payload(inv_payload, "inventory")
                if ok:
                    self.stdout.write(
                        self.style.SUCCESS("✓ Inventory restrictions payload valid")
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR("✗ Inventory restrictions payload errors:")
                    )
                    for e in errs:
                        self.stdout.write("    " + e)
            except Exception as e:  # pragma: no cover
                self.stdout.write(self.style.ERROR(f"✗ Payload generation failed: {e}"))
        self.stdout.write(self.style.SUCCESS("\n✓ Mapping validation completed"))
