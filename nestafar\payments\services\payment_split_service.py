"""
Payment Split Calculation Service

Business logic for fund distribution with context-aware splitting rules,
commission calculations, and validation.
"""

import logging
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List
from django.db import transaction

from payments.constants import PaymentContext
from payments.exceptions import PaymentSplitException
from payments.utils.payment_helpers import (
    calculate_commission,
)
from payments.utils.validation_helpers import validate_split_amounts

logger = logging.getLogger(__name__)


class PaymentSplitService:
    """
    Service for calculating payment splits based on context and business rules.

    This service handles the complex logic of splitting payments between:
    - Platform commission
    - Partner amounts
    - Vendor payouts

    Split calculations vary based on payment context (checkout vs precheckin).
    """

    def __init__(self):
        self.logger = logger

    def calculate_splits(self, payment_intent) -> Dict:
        """
        Calculate payment splits for a payment intent.

        Args:
            payment_intent: PaymentIntent instance

        Returns:
            Dictionary containing split calculations and transfer details

        Raises:
            PaymentSplitException: If split calculation fails
        """
        try:
            context = payment_intent.context
            total_amount = payment_intent.total_amount

            self.logger.info(
                f"Calculating splits for payment {payment_intent.id}, "
                f"context: {context}, amount: ₹{total_amount}"
            )

            if context == PaymentContext.CHECKOUT:
                return self._calculate_checkout_splits(payment_intent)
            elif context == PaymentContext.PRECHECKIN:
                return self._calculate_precheckin_splits(payment_intent)
            else:
                raise PaymentSplitException(f"Unsupported payment context: {context}")

        except Exception as e:
            self.logger.error(
                f"Split calculation failed for payment {payment_intent.id}: {str(e)}"
            )
            raise PaymentSplitException(f"Split calculation failed: {str(e)}")

    def _calculate_checkout_splits(self, payment_intent) -> Dict:
        """
        Calculate splits for guest checkout payments.

        For checkout payments:
        - Platform gets commission (default 5%)
        - Partner gets remaining amount after commission and vendor payouts
        - Vendors get their service amounts

        Args:
            payment_intent: PaymentIntent instance

        Returns:
            Split calculation results
        """
        total_amount = payment_intent.total_amount
        partner = payment_intent.partner

        # Get and normalize platform commission rate (fallback to 5%, handle percentages and clamp 0-1)
        raw_rate = partner.platform_commission_rate
        commission_rate = Decimal(str(raw_rate)) if raw_rate is not None else Decimal('0.05')
        # Convert whole-number percentages (e.g., 5) to fraction
        if commission_rate > 1:
            commission_rate = commission_rate / Decimal('100')
        # Clamp between 0 and 1
        commission_rate = max(Decimal('0.0'), min(commission_rate, Decimal('1.0')))

        platform_commission = calculate_commission(total_amount, commission_rate)

        # Calculate vendor amounts from service orders
        vendor_splits = self._calculate_vendor_splits(payment_intent)
        total_vendor_amount = sum(
            Decimal(str(split["amount"])) for split in vendor_splits
        )

        # Partner gets remaining amount
        partner_amount = total_amount - platform_commission - total_vendor_amount

        # Ensure partner amount is not negative
        if partner_amount < 0:
            raise PaymentSplitException(
                f"Partner amount cannot be negative. "
                f"Total: ₹{total_amount}, Commission: ₹{platform_commission}, "
                f"Vendor: ₹{total_vendor_amount}"
            )

        # Build splits list
        splits = []

        # Platform commission split
        if platform_commission > 0:
            splits.append(
                {
                    "recipient_type": "platform",
                    "recipient_id": "platform",
                    "recipient_name": "Nestafar Platform",
                    "amount": float(platform_commission),
                    "percentage": float(commission_rate),
                    "razorpay_account_id": None,  # Platform doesn't need transfer
                }
            )

        # Partner split
        if partner_amount > 0:
            splits.append(
                {
                    "recipient_type": "partner",
                    "recipient_id": str(partner.id),
                    "recipient_name": partner.user.name,
                    "amount": float(partner_amount),
                    "percentage": None,
                    "razorpay_account_id": partner.razorpay_linked_account_id,
                }
            )

        # Vendor splits - convert amounts to float
        for split in vendor_splits:
            if 'amount' in split:
                split['amount'] = float(split['amount'])
        splits.extend(vendor_splits)

        # Validate splits
        self._validate_splits(splits, total_amount)

        # Update payment intent amounts
        payment_intent.platform_commission = platform_commission
        payment_intent.partner_amount = partner_amount
        payment_intent.vendor_amount = total_vendor_amount

        return {
            "splits": splits,
            "summary": {
                "total_amount": float(total_amount),
                "platform_commission": float(platform_commission),
                "partner_amount": float(partner_amount),
                "vendor_amount": float(total_vendor_amount),
                "commission_rate": float(commission_rate),
            },
            "transfers": self._build_transfer_list(splits),
        }

    def _calculate_precheckin_splits(self, payment_intent) -> Dict:
        """
        Calculate splits for precheckin upfront payments.

        For precheckin payments:
        - All amount goes to partner (no platform commission)
        - No vendor splits (services paid separately)

        Args:
            payment_intent: PaymentIntent instance

        Returns:
            Split calculation results
        """
        total_amount = payment_intent.total_amount
        partner = payment_intent.partner

        # For precheckin, entire amount goes to partner
        splits = [
            {
                "recipient_type": "partner",
                "recipient_id": str(partner.id),
                "recipient_name": partner.user.name,
                "amount": float(total_amount),
                "percentage": float(Decimal("100.00")),
                "razorpay_account_id": partner.razorpay_linked_account_id,
            }
        ]

        # Update payment intent amounts
        payment_intent.platform_commission = Decimal("0.00")
        payment_intent.partner_amount = total_amount
        payment_intent.vendor_amount = Decimal("0.00")

        return {
            "splits": splits,
            "summary": {
                "total_amount": float(total_amount),
                "platform_commission": float(Decimal("0.00")),
                "partner_amount": float(total_amount),
                "vendor_amount": float(Decimal("0.00")),
                "commission_rate": float(Decimal("0.00")),
            },
            "transfers": self._build_transfer_list(splits),
        }

    def _calculate_vendor_splits(self, payment_intent) -> List[Dict]:
        """
        Calculate vendor splits based on service orders.

        Args:
            payment_intent: PaymentIntent instance

        Returns:
            List of vendor split dictionaries
        """
        vendor_splits = []

        # Get service orders from guest checkout
        if payment_intent.guest:
            vendor_splits = self._get_service_order_splits(payment_intent.guest)

        return vendor_splits

    def _get_service_order_splits(self, guest) -> List[Dict]:
        """
        Get vendor splits from guest's service orders.

        Args:
            guest: Guest instance

        Returns:
            List of vendor split dictionaries
        """
        from service.subapps.food.models import FoodOrder
        from service.subapps.laundry.models import LaundryOrder
        from service.subapps.rental.models import RentalOrder
        from service.subapps.transport.models import TransportOrder
        from service.subapps.shop.models import ShopOrder
        from service.subapps.tourism.models import TourismOrder

        vendor_splits = []
        order_models = [
            FoodOrder,
            LaundryOrder,
            RentalOrder,
            TransportOrder,
            ShopOrder,
            TourismOrder,
        ]

        for order_model in order_models:
            orders = order_model.objects.filter(
                guest=guest, status="completed"  # Only completed orders
            ).select_related("service__partner")

            for order in orders:
                service_partner = order.service.partner

                # Only include vendors with valid Razorpay accounts
                if service_partner.can_receive_payouts():
                    # Normalize order total_amount to two decimal places
                    raw_amount = Decimal(str(order.total_amount))
                    normalized_amount = raw_amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                    vendor_splits.append(
                        {
                            "recipient_type": "vendor",
                            "recipient_id": str(service_partner.id),
                            "recipient_name": service_partner.name,
                            "amount": float(normalized_amount),
                            "percentage": None,
                            "razorpay_account_id": service_partner.razorpay_linked_account_id,
                            "service_type": service_partner.get_type_of_service_display(),
                            "order_id": str(order.id),
                        }
                    )

        return vendor_splits

    def _build_transfer_list(self, splits: List[Dict]) -> List[Dict]:
        """
        Build transfer list for Razorpay Route API.

        Args:
            splits: List of split dictionaries

        Returns:
            List of transfer dictionaries for Razorpay API
        """
        transfers = []

        for split in splits:
            # Skip platform commission (no transfer needed)
            if split["recipient_type"] == "platform":
                continue

            # Skip splits without Razorpay account
            if not split.get("razorpay_account_id"):
                self.logger.warning(
                    f"Skipping transfer for {split['recipient_name']} - "
                    f"no Razorpay account configured"
                )
                continue

            # Use Decimal arithmetic and round half-up to avoid underpaying by 1 paise
            try:
                amt_decimal = (
                    Decimal(str(split["amount"]))
                    .quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
                )
            except Exception:
                # If conversion fails, skip this transfer and log
                self.logger.warning(
                    f"Skipping transfer for {split.get('recipient_name')} due to invalid amount: {split.get('amount')}"
                )
                continue

            # Convert to paise and round half-up to nearest integer paise
            paise_decimal = (amt_decimal * Decimal("100")).quantize(
                Decimal("1"), rounding=ROUND_HALF_UP
            )
            paise_amount = int(paise_decimal)

            # Skip transfers that would be zero or negative in paise
            if paise_amount <= 0:
                self.logger.info(
                    f"Skipping transfer for {split.get('recipient_name')} - paise amount <= 0 ({paise_amount})"
                )
                continue

            transfers.append(
                {
                    "account": split["razorpay_account_id"],
                    "amount": paise_amount,
                    "currency": "INR",
                    "notes": {
                        "recipient_type": split["recipient_type"],
                        "recipient_id": split["recipient_id"],
                        "recipient_name": split["recipient_name"],
                    },
                }
            )

        return transfers

    def _validate_splits(self, splits: List[Dict], total_amount: Decimal):
        """
        Validate calculated splits.

        Args:
            splits: List of split dictionaries
            total_amount: Expected total amount

        Raises:
            PaymentSplitException: If validation fails
        """
        # Validate split amounts sum to total
        is_valid, errors = validate_split_amounts(splits, total_amount)
        if not is_valid:
            raise PaymentSplitException(f"Split validation failed: {'; '.join(errors)}")

        # Validate individual splits
        for split in splits:
            amount = split.get("amount", 0)
            if amount < 0:
                raise PaymentSplitException(
                    f"Negative split amount for {split.get('recipient_name', 'Unknown')}: ₹{amount}"
                )

    @transaction.atomic
    def create_payment_splits(self, payment_intent, split_data: Dict) -> List:
        """
        Create PaymentSplit records from split calculation results.

        Args:
            payment_intent: PaymentIntent instance
            split_data: Split calculation results

        Returns:
            List of created PaymentSplit instances
        """
        from ..models import PaymentSplit

        created_count = 0
        resulting_splits = []

        # Build a list of existing splits for quick lookup (optional)
        existing_qs = PaymentSplit.objects.filter(payment_intent=payment_intent)

        for split_info in split_data["splits"]:
            # Normalize amount to Decimal with two decimals to use as part of uniqueness key
            try:
                # Try safe coercion in order of likely types
                raw_val = split_info.get("amount")
                if isinstance(raw_val, Decimal):
                    amt_decimal = raw_val
                else:
                    # For float/int/str/etc, convert via str() to avoid float binary issues
                    amt_decimal = Decimal(str(raw_val))

                # Quantize to two decimal places (paise precision)
                amt_decimal = amt_decimal.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
            except Exception as exc:
                # Deterministic fallback to 0.00 (quantized) to ensure lookup keys remain Decimal
                self.logger.warning(
                    "Failed to normalize split amount to Decimal for payment %s. "
                    "Offending value: %r, split_info: %r, error: %s. "
                    "Falling back to Decimal('0.00') for lookup key.",
                    getattr(payment_intent, 'id', 'unknown'),
                    split_info.get('amount'),
                    {
                        'recipient_type': split_info.get('recipient_type'),
                        'recipient_id': split_info.get('recipient_id'),
                        'recipient_name': split_info.get('recipient_name'),
                    },
                    str(exc),
                )
                amt_decimal = Decimal("0.00").quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

            lookup = {
                "payment_intent": payment_intent,
                "recipient_type": split_info["recipient_type"],
                "recipient_id": split_info["recipient_id"],
                "amount": amt_decimal,
            }

            defaults = {
                "recipient_name": split_info["recipient_name"],
                "percentage": split_info.get("percentage"),
                "razorpay_account_id": split_info.get("razorpay_account_id"),
                "metadata": {
                    "service_type": split_info.get("service_type"),
                    "order_id": split_info.get("order_id"),
                },
            }

            split_obj, created = PaymentSplit.objects.get_or_create(defaults=defaults, **lookup)
            if created:
                created_count += 1
            resulting_splits.append(split_obj)

        total = len(resulting_splits)
        if created_count:
            self.logger.info(
                f"Created {created_count} new payment splits (total {total}) for payment {payment_intent.id}"
            )
        else:
            self.logger.info(
                f"Returned {total} existing payment splits for payment {payment_intent.id}"
            )

        return resulting_splits
