from django.contrib.auth import get_user_model

# from django.test import Client
from rest_framework.test import APIClient
from django.urls import reverse
from core.models import User
import json

# Create a reusable client object


def create_user(context, username, password):
    client = APIClient()
    user = User.objects.get(username=username)
    login_response = client.post(
        reverse("login"), {"username": username, "password": password}
    )
    token = json.loads(login_response.content)["access_token"]
    client.credentials(HTTP_AUTHORIZATION="Bearer " + token)
    context.auth_client = client
    context.auth_user = user


def create_staff_user(username="staff_user", password="secret"):
    """
    Creates a test user object with staff privileges.
    """
    user = create_user(username, password)
    user.is_staff = True
    user.save()
    return user


def authenticate_user(client, user):
    """
    Authenticates a user with the test client.
    """
    client.force_authenticate(user=user)


# Table data parsing (helper function)
def parse_table_data(table):
    rows = []
    headers = [cell.value for cell in table.header.cells]
    for row in table.body:
        row_data = [cell.value for cell in row.cells]
        rows.append(dict(zip(headers, row_data)))
    return rows


# pytest hook for feature file discovery
def pytest_bdd_features_from_project(config):
    # Replace with your feature file directory path
    feature_paths = ["tests/features"]
    return feature_paths


# pytest hook for scenario outlines
def pytest_bdd_step_table_parser(table, scenario_outline):
    data = parse_table_data(table)
    return data
