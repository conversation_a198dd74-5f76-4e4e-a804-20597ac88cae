"""
Account Webhook Processor

Processes Razorpay webhooks related to account verification,
KYC status updates, and bank account verification.
"""

import logging
from typing import Dict, Any, Union, Optional
from django.utils import timezone
from django.conf import settings
from django.db import transaction

from ..models import (
    PartnerRazorpayAccount,
    PartnerKYCDocument,
    AccountVerificationLog,
    PaymentWebhookEvent
)
from ..constants.payment_constants import (
    AccountStatus,
    KYCStatus,
    VerificationStatus,
)
# Import the TextChoices-based RazorpayWebhookEvents which includes account events
from ..constants.payment_constants import RazorpayWebhookEvents
from ..exceptions import WebhookException

logger = logging.getLogger(__name__)

# Optional phonenumbers import for robust validation/formatting
try:
    import phonenumbers
except Exception:
    phonenumbers = None


def _validate_and_format_phone(phone: str, partner_id) -> Optional[str]:
    """Validate and return E.164 formatted phone number, or None if invalid.

    Uses the phonenumbers library when available, falling back to a simple
    digit-length check when not.
    Logs a warning including the partner id on failure.
    """
    if not phone or not str(phone).strip():
        logger.warning(f"Invalid/empty contact phone for partner {partner_id}")
        return None

    # Use configured default region if available (phonenumber_field uses this setting)
    default_region = getattr(settings, 'PHONENUMBER_DEFAULT_REGION', 'IN')

    if phonenumbers is None:
        # Best-effort fallback: ensure 10-15 digits
        digits = ''.join(filter(str.isdigit, str(phone)))
        if len(digits) < 10 or len(digits) > 15:
            logger.warning(f"Invalid contact phone for partner {partner_id}: {phone}")
            return None
        # Return a naive E.164-like string (prefix with + if original input started with '+')
        return f"+{digits}" if str(phone).strip().startswith('+') else digits

    try:
        parsed = phonenumbers.parse(str(phone), default_region)
        if not phonenumbers.is_valid_number(parsed):
            logger.warning(f"Invalid contact phone for partner {partner_id}: {phone}")
            return None
        return phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
    except Exception as e:
        logger.warning(f"Failed to parse contact phone for partner {partner_id}: {phone} - {e}")
        return None


class AccountWebhookProcessor:
    """Processes account-related webhook events from Razorpay"""
    
    def _resolve_partner_display(self, partner_account: PartnerRazorpayAccount) -> str:
        """Safely resolve a display string for the partner associated with a PartnerRazorpayAccount.

        Falls back to sensible identifiers if partner/user/name are missing to avoid
        raising AttributeError in logging calls.
        """
        try:
            partner = getattr(partner_account, 'partner', None)
            if not partner:
                return f"partner_account:{getattr(partner_account, 'id', 'unknown')}"

            user = getattr(partner, 'user', None)
            if user:
                name = getattr(user, 'name', None) or getattr(user, 'username', None) or getattr(user, 'email', None)
                if name:
                    return str(name)

            # Fallback to partner id if user details are not available
            return f"partner:{getattr(partner, 'id', getattr(partner_account, 'id', 'unknown'))}"
        except Exception:
            return 'unknown partner'
    
    def process_event(self, webhook_event: PaymentWebhookEvent):
        """
        Process account webhook event
        
        Args:
            webhook_event: PaymentWebhookEvent instance
            
        Raises:
            WebhookException: If processing fails
        """
        try:
            event_type = webhook_event.event_type
            payload = webhook_event.raw_payload
            
            logger.info(f"Processing account webhook event: {event_type}")
            
            # Route to appropriate handler
            if event_type == RazorpayWebhookEvents.ACCOUNT_ACTIVATED:
                self._handle_account_activated(webhook_event, payload)
            elif event_type == RazorpayWebhookEvents.ACCOUNT_SUSPENDED:
                self._handle_account_suspended(webhook_event, payload)
            elif event_type == RazorpayWebhookEvents.ACCOUNT_UNDER_REVIEW:
                self._handle_account_under_review(webhook_event, payload)
            else:
                logger.warning(f"Unhandled account webhook event type: {event_type}")
            
            # Mark webhook as processed
            webhook_event.mark_as_processed()
            
        except Exception as e:
            error_msg = f"Failed to process account webhook event {webhook_event.id}: {str(e)}"
            logger.error(error_msg)
            webhook_event.mark_processing_error(error_msg)
            raise WebhookException(error_msg) from e
    
    def _handle_account_activated(self, webhook_event: PaymentWebhookEvent, payload: Dict[str, Any]):
        """Handle account.activated webhook"""
        try:
            # Defensive validation of the webhook payload structure to avoid
            # AttributeError/TypeError on malformed payloads. Expecting a dict
            # with nested keys: 'payload' -> 'account' -> 'entity' -> 'id'.
            if not isinstance(payload, dict):
                logger.warning("Unexpected webhook payload type: %s", type(payload))
                return

            outer = payload.get('payload')
            if not isinstance(outer, dict):
                logger.warning("Unexpected webhook payload structure: 'payload' missing or not a dict: %s", payload)
                return

            account = outer.get('account')
            if not isinstance(account, dict):
                logger.warning("Unexpected webhook payload structure: 'account' missing or not a dict: %s", payload)
                return

            account_data = account.get('entity')
            if not isinstance(account_data, dict):
                logger.warning("Unexpected webhook payload structure: 'entity' missing or not a dict: %s", payload)
                return

            account_id = account_data.get('id')
            if account_id is None or not isinstance(account_id, (str, int)):
                logger.warning("Account ID missing or invalid in webhook payload: %s", payload)
                return
            
            # Find partner account
            partner_account = PartnerRazorpayAccount.objects.filter(
                razorpay_account_id=account_id
            ).first()
            
            if not partner_account:
                logger.warning(f"Partner account not found for Razorpay account ID: {account_id}")
                return
            
            # Update account status and related models inside a transaction.
            # Lock the partner account row to prevent concurrent modifications.
            old_status = partner_account.account_status
            with transaction.atomic():
                locked_account = PartnerRazorpayAccount.objects.select_for_update().get(
                    pk=partner_account.pk
                )

                # Apply all field changes first, then save once.
                locked_account.account_status = AccountStatus.ACTIVE
                locked_account.activated_at = timezone.now()
                locked_account.activation_form_milestone = account_data.get('activation_form_milestone')
                locked_account.clear_errors()
                locked_account.save(update_fields=['account_status', 'activated_at', 'activation_form_milestone'])

                # Update partner profile within same transaction to keep DB consistent
                partner_profile = locked_account.partner
                partner_profile.razorpay_account_verified = True
                partner_profile.save(update_fields=['razorpay_account_verified'])

                # Use the locked_account reference going forward
                partner_account = locked_account
            
            # Log activity
            self._log_activity(
                partner_account,
                'account_activated',
                f"Account activated via webhook. Status changed from {old_status} to {AccountStatus.ACTIVE}",
                metadata={
                    'webhook_event_id': str(webhook_event.id),
                    'activation_form_milestone': account_data.get('activation_form_milestone'),
                    'razorpay_data': account_data
                }
            )
            
            # Send notification to partner
            self._send_account_activated_notification(partner_account)
            
            partner_display = self._resolve_partner_display(partner_account)
            logger.info(f"Account activated for partner {partner_display}")
            
        except Exception as e:
            logger.error(f"Error handling account.activated webhook: {str(e)}")
            raise
    
    def _handle_account_suspended(self, webhook_event: PaymentWebhookEvent, payload: Dict[str, Any]):
        """Handle account.suspended webhook"""
        try:
            account_data = payload.get('payload', {}).get('account', {}).get('entity', {})
            account_id = account_data.get('id')
            
            if not account_id:
                logger.warning("Account ID not found in webhook payload")
                return
            
            # Find partner account
            partner_account = PartnerRazorpayAccount.objects.filter(
                razorpay_account_id=account_id
            ).first()
            
            if not partner_account:
                logger.warning(f"Partner account not found for Razorpay account ID: {account_id}")
                return
            
            # Update account status
            old_status = partner_account.account_status
            partner_account.account_status = AccountStatus.SUSPENDED
            partner_account.save()
            
            # Update partner profile
            partner_profile = partner_account.partner
            partner_profile.razorpay_account_verified = False
            partner_profile.save(update_fields=['razorpay_account_verified'])
            
            # Log activity
            suspension_reason = account_data.get('suspension_reason', 'Not specified')
            self._log_activity(
                partner_account,
                'account_suspended',
                f"Account suspended via webhook. Status changed from {old_status} to {AccountStatus.SUSPENDED}. Reason: {suspension_reason}",
                metadata={
                    'webhook_event_id': str(webhook_event.id),
                    'suspension_reason': suspension_reason,
                    'razorpay_data': account_data
                }
            )
            
            # Send notification to partner
            self._send_account_suspended_notification(partner_account, suspension_reason)
            
            partner_display = self._resolve_partner_display(partner_account)
            logger.info(f"Account suspended for partner {partner_display}")
            
        except Exception as e:
            logger.error(f"Error handling account.suspended webhook: {str(e)}")
            raise
    
    def _handle_account_under_review(self, webhook_event: PaymentWebhookEvent, payload: Dict[str, Any]):
        """Handle account.under_review webhook"""
        try:
            account_data = payload.get('payload', {}).get('account', {}).get('entity', {})
            account_id = account_data.get('id')
            
            if not account_id:
                logger.warning("Account ID not found in webhook payload")
                return
            
            # Find partner account
            partner_account = PartnerRazorpayAccount.objects.filter(
                razorpay_account_id=account_id
            ).first()
            
            if not partner_account:
                logger.warning(f"Partner account not found for Razorpay account ID: {account_id}")
                return
            
            # Update account status
            old_status = partner_account.account_status
            partner_account.account_status = AccountStatus.UNDER_REVIEW
            partner_account.activation_form_milestone = account_data.get('activation_form_milestone')
            partner_account.save()
            
            # Log activity
            self._log_activity(
                partner_account,
                'account_under_review',
                f"Account under review via webhook. Status changed from {old_status} to {AccountStatus.UNDER_REVIEW}",
                metadata={
                    'webhook_event_id': str(webhook_event.id),
                    'activation_form_milestone': account_data.get('activation_form_milestone'),
                    'razorpay_data': account_data
                }
            )
            
            # Send notification to partner
            self._send_account_under_review_notification(partner_account)
            
            partner_display = self._resolve_partner_display(partner_account)
            logger.info(f"Account under review for partner {partner_display}")
            
        except Exception as e:
            logger.error(f"Error handling account.under_review webhook: {str(e)}")
            raise
    
    def _send_account_activated_notification(self, partner_account: PartnerRazorpayAccount):
        """Send account activation notification to partner via WhatsApp"""
        try:
            # Get partner user for notification
            partner = partner_account.partner
            if not partner or not partner.user:
                logger.warning(f"No user found for partner account {partner_account.id}")
                return

            # Prepare notification data
            notification_data = {
                'partner_name': partner_account.contact_name or partner.user.get_full_name() or 'Partner',
                'account_id': partner_account.razorpay_account_id,
                'activation_date': partner_account.activated_at.strftime('%B %d, %Y') if partner_account.activated_at else 'today',
                'bank_account_details': 'your registered account',
                'dashboard_url': 'https://partner.nestafar.com',
                'support_contact': '+91-**********',
            }
            
            # Send via unified notification system
            from notification.tasks.send_tasks import send_notification
            
            result = send_notification(
                user_id=str(partner.user.id),
                event='ACCOUNT_ACTIVATED',
                data=notification_data
            )
            
            logger.info(f"Account activation notification sent to partner {partner.id}: {result}")
            
        except Exception as e:
            logger.error(f"Failed to send account activation notification: {str(e)}")
    
    def _send_account_suspended_notification(self, partner_account: PartnerRazorpayAccount, reason: str):
        """Send account suspension notification to partner via WhatsApp"""
        try:
            # Get partner user for notification
            partner = partner_account.partner
            if not partner or not partner.user:
                logger.warning(f"No user found for partner account {partner_account.id}")
                return

            # Prepare notification data
            notification_data = {
                'partner_name': partner_account.contact_name or partner.user.get_full_name() or 'Partner',
                'account_id': partner_account.razorpay_account_id,
                'suspension_reason': reason,
                'suspension_date': 'today',
                'appeal_process': 'contact our support team',
                'support_contact': '+91-**********',
            }
            
            # Send via unified notification system
            from notification.tasks.send_tasks import send_notification
            
            result = send_notification(
                user_id=str(partner.user.id),
                event='ACCOUNT_SUSPENDED',
                data=notification_data
            )
            
            logger.info(f"Account suspension notification sent to partner {partner.id}: {result}")
            
        except Exception as e:
            logger.error(f"Failed to send account suspension notification: {str(e)}")
    
    def _send_account_under_review_notification(self, partner_account: PartnerRazorpayAccount):
        """Send account under review notification to partner via WhatsApp"""
        try:
            # Get partner user for notification
            partner = partner_account.partner
            if not partner or not partner.user:
                logger.warning(f"No user found for partner account {partner_account.id}")
                return

            # Prepare notification data
            notification_data = {
                'partner_name': partner_account.contact_name or partner.user.get_full_name() or 'Partner',
                'account_id': partner_account.razorpay_account_id,
                'review_reason': 'routine compliance check',
                'estimated_duration': '1-2 business days',
                'review_date': 'today',
                'support_contact': '+91-**********',
            }
            
            # Send via unified notification system
            from notification.tasks.send_tasks import send_notification
            
            result = send_notification(
                user_id=str(partner.user.id),
                event='ACCOUNT_UNDER_REVIEW',
                data=notification_data
            )
            
            logger.info(f"Account under review notification sent to partner {partner.id}: {result}")
            
        except Exception as e:
            logger.error(f"Failed to send account under review notification: {str(e)}")
    
    def _log_activity(self, partner_account: PartnerRazorpayAccount, activity_type: str,
                     description: str, metadata: Dict[str, Any] = None):
        """Log account verification activity"""
        try:
            AccountVerificationLog.objects.create(
                razorpay_account=partner_account,
                activity_type=activity_type,
                description=description,
                metadata=metadata or {}
            )
        except Exception as e:
            logger.error(f"Failed to log activity: {str(e)}")


class KYCWebhookProcessor:
    """Processes KYC-related webhook events"""
    
    def process_kyc_verification_event(self, webhook_event: PaymentWebhookEvent):
        """Process KYC verification webhook events"""
        try:
            payload = webhook_event.raw_payload

            # Defensive validation similar to account webhook handling
            if not isinstance(payload, dict):
                logger.warning("Unexpected KYC webhook payload type: %s", type(payload))
                return

            outer = payload.get('payload')
            if not isinstance(outer, dict):
                logger.warning("Unexpected KYC webhook payload structure: 'payload' missing or not a dict: %s", payload)
                return

            document = outer.get('document')
            if not isinstance(document, dict):
                logger.warning("Unexpected KYC webhook payload structure: 'document' missing or not a dict: %s", payload)
                return

            document_data = document.get('entity')
            if not isinstance(document_data, dict):
                logger.warning("Unexpected KYC webhook payload structure: 'entity' missing or not a dict: %s", payload)
                return

            document_id = document_data.get('id')
            if document_id is None or not isinstance(document_id, (str, int)):
                logger.warning("Document ID missing or invalid in KYC webhook payload: %s", payload)
                return

            # Find KYC document
            kyc_document = PartnerKYCDocument.objects.filter(
                razorpay_document_id=document_id
            ).first()

            if not kyc_document:
                logger.warning(f"KYC document not found for Razorpay document ID: {document_id}")
                return

            # Perform processing inside try so we can mark webhook_event appropriately
            try:
                # Update verification status
                verification_status = document_data.get('verification_status', 'pending')
                status_mapping = {
                    'verified': VerificationStatus.VERIFIED,
                    'rejected': VerificationStatus.REJECTED,
                    'pending': VerificationStatus.PENDING,
                    'under_review': VerificationStatus.IN_PROGRESS
                }

                new_status = status_mapping.get(verification_status, VerificationStatus.PENDING)
                old_status = kyc_document.verification_status

                kyc_document.verification_status = new_status
                kyc_document.verification_notes = document_data.get('verification_notes', '')

                if new_status == VerificationStatus.VERIFIED:
                    kyc_document.verified_at = timezone.now()

                kyc_document.save()

                # Update overall KYC status
                self._update_overall_kyc_status(kyc_document.razorpay_account)

                # Send notifications based on status
                if new_status == VerificationStatus.VERIFIED:
                    self._send_kyc_verified_notification(kyc_document)
                elif new_status == VerificationStatus.REJECTED:
                    self._send_kyc_rejected_notification(kyc_document, document_data.get('verification_notes', ''))
                elif new_status == VerificationStatus.IN_PROGRESS:
                    self._send_kyc_under_review_notification(kyc_document)

                # Log activity
                activity_map = {
                    VerificationStatus.VERIFIED: 'kyc_verified',
                    VerificationStatus.REJECTED: 'kyc_rejected',
                    VerificationStatus.PENDING: 'kyc_pending',
                    VerificationStatus.IN_PROGRESS: 'kyc_under_review',
                    VerificationStatus.FAILED: 'kyc_failed',
                }

                activity_type = activity_map.get(new_status, 'kyc_status_changed')

                AccountVerificationLog.objects.create(
                    razorpay_account=kyc_document.razorpay_account,
                    activity_type=activity_type,
                    description=f"KYC document {kyc_document.document_type} status changed from {old_status} to {new_status}",
                    related_document=kyc_document,
                    metadata={
                        'webhook_event_id': str(webhook_event.id),
                        'verification_notes': document_data.get('verification_notes', ''),
                        'razorpay_data': document_data
                    }
                )

                logger.info(f"KYC document {kyc_document.document_type} status updated to {new_status}")

                # Mark webhook processed on success
                webhook_event.processed = True
                webhook_event.save(update_fields=['processed'])

            except Exception as e:
                # Log and mark webhook as errored so it can be retried or inspected
                logger.error(f"Error processing KYC verification webhook: {str(e)}")
                webhook_event.processed = False
                webhook_event.error = str(e) if hasattr(webhook_event, 'error') else ''
                webhook_event.save()
                raise

        except Exception:
            # Re-raise to let callers handle/log as needed (mirrors AccountWebhookProcessor)
            raise
    
    def _update_overall_kyc_status(self, partner_account: PartnerRazorpayAccount):
        """Update overall KYC status based on individual document statuses"""
        try:
            documents = partner_account.kyc_documents.all()
            
            if not documents.exists():
                partner_account.kyc_status = KYCStatus.PENDING
            elif documents.filter(verification_status=VerificationStatus.REJECTED).exists():
                partner_account.kyc_status = KYCStatus.REJECTED
            elif documents.filter(verification_status=VerificationStatus.VERIFIED).count() == documents.count():
                partner_account.kyc_status = KYCStatus.VERIFIED
            else:
                partner_account.kyc_status = KYCStatus.UNDER_REVIEW
            
            partner_account.save(update_fields=['kyc_status'])
            
        except Exception as e:
            logger.error(f"Error updating overall KYC status: {str(e)}")

    def _send_kyc_verified_notification(self, kyc_document: PartnerKYCDocument):
        """Send KYC verification success notification via WhatsApp"""
        try:
            partner_account = kyc_document.razorpay_account
            partner = partner_account.partner
            if not partner or not partner.user:
                logger.warning(f"No user found for KYC document {kyc_document.id}")
                return

            # Prepare notification data
            notification_data = {
                'partner_name': partner_account.contact_name or partner.user.get_full_name() or 'Partner',
                'document_type': kyc_document.get_document_type_display(),
                'verification_date': kyc_document.verified_at.strftime('%B %d, %Y') if kyc_document.verified_at else 'today',
                'account_status': partner_account.get_account_status_display(),
                'dashboard_url': 'https://partner.nestafar.com',
                'next_steps': 'Start accepting payments',
            }
            
            # Send via unified notification system
            from notification.tasks.send_tasks import send_notification
            
            result = send_notification(
                user_id=str(partner.user.id),
                event='KYC_VERIFIED',
                data=notification_data
            )
            
            logger.info(f"KYC verified notification sent to partner {partner.id}: {result}")
            
        except Exception as e:
            logger.error(f"Failed to send KYC verified notification: {str(e)}")

    def _send_kyc_rejected_notification(self, kyc_document: PartnerKYCDocument, rejection_reason: str):
        """Send KYC rejection notification via WhatsApp"""
        try:
            partner_account = kyc_document.razorpay_account
            partner = partner_account.partner
            if not partner or not partner.user:
                logger.warning(f"No user found for KYC document {kyc_document.id}")
                return

            # Prepare notification data
            notification_data = {
                'partner_name': partner_account.contact_name or partner.user.get_full_name() or 'Partner',
                'document_type': kyc_document.get_document_type_display(),
                'rejection_reason': rejection_reason or 'Document unclear or invalid',
                'resubmission_url': 'https://partner.nestafar.com/kyc',
                'support_contact': '+91-**********',
                'deadline': '7 days',
            }
            
            # Send via unified notification system
            from notification.tasks.send_tasks import send_notification
            
            result = send_notification(
                user_id=str(partner.user.id),
                event='KYC_REJECTED',
                data=notification_data
            )
            
            logger.info(f"KYC rejected notification sent to partner {partner.id}: {result}")
            
        except Exception as e:
            logger.error(f"Failed to send KYC rejected notification: {str(e)}")

    def _send_kyc_under_review_notification(self, kyc_document: PartnerKYCDocument):
        """Send KYC under review notification via WhatsApp"""
        try:
            partner_account = kyc_document.razorpay_account
            partner = partner_account.partner
            if not partner or not partner.user:
                logger.warning(f"No user found for KYC document {kyc_document.id}")
                return

            # Prepare notification data
            notification_data = {
                'partner_name': partner_account.contact_name or partner.user.get_full_name() or 'Partner',
                'document_type': kyc_document.get_document_type_display(),
                'submission_date': kyc_document.created_at.strftime('%B %d, %Y') if kyc_document.created_at else 'today',
                'estimated_duration': '24-48 hours',
                'reference_id': str(kyc_document.id),
                'support_contact': '+91-**********',
            }
            
            # Send via unified notification system
            from notification.tasks.send_tasks import send_notification
            
            result = send_notification(
                user_id=str(partner.user.id),
                event='KYC_UNDER_REVIEW',
                data=notification_data
            )
            
            logger.info(f"KYC under review notification sent to partner {partner.id}: {result}")
            
        except Exception as e:
            logger.error(f"Failed to send KYC under review notification: {str(e)}")
