from rest_framework.response import Response
from rest_framework import status


class RestResponse(Response):
    def __init__(
        self,
        data=None,
        message=None,
        status_code=None,
        success=True,
        template_name=None,
        headers=None,
        exception=False,
        content_type=None,
    ):
        if message:
            self.message = message

        data_content = {
            "success": success,
            "message": self.message,
            "data": data,
        }

        if status_code:
            self.status_code = status_code

        super(RestResponse, self).__init__(
            data=data_content,
            status=self.status_code,
            headers=headers,
            content_type=content_type,
        )


class BaseResponse(RestResponse):
    status_code = None
    message = None
    success = True

    def __init__(self, data=None, message=None, success=None, **kwargs):
        if message is None:
            message = self.message
        if success is None:
            success = self.success
        super().__init__(
            data=data,
            message=message,
            status_code=self.status_code,
            success=success,
            **kwargs
        )


class SuccessResponse(BaseResponse):
    status_code = status.HTTP_200_OK
    message = "Success"


class ConflictResponse(BaseResponse):
    status_code = status.HTTP_409_CONFLICT
    message = "Conflict"


class CreateResponse(BaseResponse):
    status_code = status.HTTP_201_CREATED
    message = "Created"


class NoContentResponse(BaseResponse):
    status_code = status.HTTP_204_NO_CONTENT
    message = "No Content"


class BadRequestResponse(BaseResponse):
    status_code = status.HTTP_400_BAD_REQUEST
    message = "Bad Request"
    success = False


class NotFoundResponse(BaseResponse):
    status_code = status.HTTP_404_NOT_FOUND
    message = "Not Found"


class PermissionDeniedResponse(BaseResponse):
    status_code = status.HTTP_403_FORBIDDEN
    message = "Permission Denied"


class UnAuthorizedResponse(BaseResponse):
    status_code = status.HTTP_401_UNAUTHORIZED
    message = "Unauthorized"


class InternalServerErrorResponse(BaseResponse):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    message = "Internal Server Error"
    success = False
