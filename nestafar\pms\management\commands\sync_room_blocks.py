"""
Management command to manually sync room blocks to OTA providers.
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from datetime import datetime, timedelta
from pms.models import Room<PERSON>lock, RoomBlockSyncLog, HotelOTAIntegration
from django.db import transaction
from pms.tasks import sync_room_block_to_ota, retry_failed_room_block_syncs
from stay.models import Property


class Command(BaseCommand):
    help = "Sync room blocks to OTA providers"

    def add_arguments(self, parser):
        parser.add_argument(
            "--hotel-id",
            type=str,
            help="Sync room blocks for specific hotel ID",
        )
        parser.add_argument(
            "--room-block-id",
            type=str,
            help="Sync specific room block ID",
        )
        parser.add_argument(
            "--ota-platform",
            type=str,
            help="Sync to specific OTA platform (e.g., aiosell)",
        )
        parser.add_argument(
            "--retry-failed",
            action="store_true",
            help="Retry failed syncs from the last 24 hours",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be synced without actually syncing",
        )
        parser.add_argument(
            "--days-back",
            type=int,
            default=1,
            help="Number of days back to look for room blocks (default: 1)",
        )

    def handle(self, *args, **options):
        if options["retry_failed"]:
            self.handle_retry_failed()
            return

        hotel_id = options.get("hotel_id")
        room_block_id = options.get("room_block_id")
        ota_platform = options.get("ota_platform")
        dry_run = options.get("dry_run", False)
        days_back = options.get("days_back", 1)
        if days_back < 0:
            raise CommandError("--days-back must be >= 0")

        # Build query filter
        query_filter = {}

        if hotel_id:
            try:
                hotel = Property.objects.get(id=hotel_id)
                query_filter["hotel"] = hotel
                self.stdout.write(f"Filtering by hotel: {hotel.name}")
            except Property.DoesNotExist:
                raise CommandError(f"Hotel with ID {hotel_id} not found")

        if room_block_id:
            query_filter["id"] = room_block_id
        else:
            # Get room blocks from the last N days (unless a specific room block is targeted)
            since_date = timezone.now() - timedelta(days=days_back)
            query_filter["created_at__gte"] = since_date

        # Execute the query once with all filters applied
        room_blocks = RoomBlock.objects.filter(**query_filter).select_related(
            "hotel", "room"
        )
        if not room_blocks.exists():
            self.stdout.write(
                self.style.WARNING("No room blocks found matching criteria")
            )
            return
        self.stdout.write(f"Found {room_blocks.count()} room blocks to sync")

        synced_count = 0
        skipped_count = 0

        for room_block in room_blocks:
            # Get active OTA integrations for this hotel
            integrations = HotelOTAIntegration.objects.filter(
                hotel=room_block.hotel, is_active=True, ota_platform__is_active=True
            ).select_related("ota_platform")

            if ota_platform:
                integrations = integrations.filter(
                    ota_platform__name__iexact=ota_platform
                )

            if not integrations.exists():
                self.stdout.write(
                    self.style.WARNING(
                        f"No active OTA integrations found for hotel {room_block.hotel.name}"
                    )
                )
                skipped_count += 1
                continue

            for integration in integrations:
                if dry_run:
                    self.stdout.write(
                        f"Would sync room block {room_block.id} "
                        f"(Room: {room_block.room.room_no if room_block.room else 'N/A'}) "
                        f"to {integration.ota_platform.name}"
                    )
                else:
                    sync_log = None  # For exception handling visibility
                    try:
                        with transaction.atomic():
                            sync_log = RoomBlockSyncLog.objects.create(
                                room_block=room_block,
                                hotel=room_block.hotel,
                                ota_platform=integration.ota_platform,
                                action="update",  # Manual sync treated as update
                                sync_status="pending",
                            )

                            # Defer task queuing until after successful commit
                            transaction.on_commit(
                                lambda rb_id=str(room_block.id), h_id=str(
                                    room_block.hotel.id
                                ), ota_id=str(integration.ota_platform.id), sl_id=str(
                                    sync_log.id
                                ): sync_room_block_to_ota.delay(
                                    room_block_id=rb_id,
                                    hotel_id=h_id,
                                    ota_platform_id=ota_id,
                                    sync_log_id=sl_id,
                                    action="update",
                                )
                            )
                        # If we reach here, transaction committed and task enqueued
                        self.stdout.write(
                            self.style.SUCCESS(
                                f"Queued sync for room block {room_block.id} to {integration.ota_platform.name}"
                            )
                        )
                        synced_count += 1
                    except Exception as exc:
                        # Attempt to mark or create a failed log (outside failed atomic block)
                        try:
                            if sync_log and getattr(sync_log, "id", None):
                                # Sync log object may not be committed if exception occurred inside atomic before commit
                                # Create a new failed log to represent the failure if original wasn't persisted
                                RoomBlockSyncLog.objects.create(
                                    room_block=room_block,
                                    hotel=room_block.hotel,
                                    ota_platform=integration.ota_platform,
                                    action="update",
                                    sync_status="failed",
                                    error_message=(
                                        str(exc)
                                        if hasattr(sync_log, "error_message")
                                        else None
                                    ),
                                )
                            else:
                                RoomBlockSyncLog.objects.create(
                                    room_block=room_block,
                                    hotel=room_block.hotel,
                                    ota_platform=integration.ota_platform,
                                    action="update",
                                    sync_status="failed",
                                    error_message=(
                                        str(exc)
                                        if hasattr(RoomBlockSyncLog, "error_message")
                                        else None
                                    ),
                                )
                        except Exception:
                            # Swallow any secondary errors while recording failure state
                            pass

                        self.stderr.write(
                            self.style.ERROR(
                                f"Failed to queue sync for room block {room_block.id} to {integration.ota_platform.name}: {exc}"
                            )
                        )
                        skipped_count += 1
                        continue
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Dry run complete. Would have synced {synced_count} room blocks"
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Queued {synced_count} room block syncs, skipped {skipped_count}"
                )
            )

    def handle_retry_failed(self):
        """Handle retry of failed syncs."""
        self.stdout.write("Retrying failed room block syncs...")

        try:
            retry_failed_room_block_syncs.delay()
            self.stdout.write(
                self.style.SUCCESS("Queued retry task for failed room block syncs")
            )
        except Exception as exc:
            self.stderr.write(self.style.ERROR(f"Failed to queue retry task: {exc}"))
            raise CommandError(f"Failed to queue retry task: {exc}")

        # Also show current failed syncs count
        failed_count = RoomBlockSyncLog.objects.filter(
            sync_status="failed", created_at__gte=timezone.now() - timedelta(days=1)
        ).count()

        self.stdout.write(f"Found {failed_count} failed syncs from the last 24 hours")
