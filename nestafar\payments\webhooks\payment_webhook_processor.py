"""
Payment Webhook Processor

Processes payment-related webhook events from Razorpay including
payment authorization, capture, failure, and completion events.
"""

import logging
from decimal import Decimal
from django.db import transaction
from django.utils import timezone

from ..models import PaymentIntent
from ..services import RazorpayService, PaymentSplitService
from ..constants import PaymentStatus, RazorpayWebhookEvents
from ..exceptions import WebhookException, PaymentProcessingException

logger = logging.getLogger(__name__)


class PaymentWebhookProcessor:
    """
    Processor for payment-related webhook events.
    
    Handles payment lifecycle events and triggers appropriate
    actions like split creation, transfer initiation, and
    status updates.
    """
    
    def __init__(self):
        self.razorpay_service = RazorpayService()
        self.split_service = PaymentSplitService()
    
    def process_event(self, webhook_event):
        """
        Process a payment webhook event.
        
        Args:
            webhook_event: PaymentWebhookEvent instance
        """
        event_type = webhook_event.event_type

        logger.info(
            f"Processing payment webhook event: {event_type} for entity {webhook_event.entity_id}"
        )

        # If there's no handler for this event, persist the processing error and return
        if event_type not in (
            RazorpayWebhookEvents.PAYMENT_AUTHORIZED,
            RazorpayWebhookEvents.PAYMENT_CAPTURED,
            RazorpayWebhookEvents.PAYMENT_FAILED,
            RazorpayWebhookEvents.PAYMENT_LINK_PAID,
            RazorpayWebhookEvents.PAYMENT_LINK_CANCELLED,
            RazorpayWebhookEvents.PAYMENT_LINK_EXPIRED,
        ):
            logger.warning(f"No handler for payment event type: {event_type}")
            try:
                # Persist the processing error immediately
                with transaction.atomic():
                    webhook_event.mark_processing_error(
                        f"No handler for event type: {event_type}"
                    )
            except Exception:
                logger.exception(
                    f"Failed to persist processing error for webhook {getattr(webhook_event, 'event_id', '<unknown>')}"
                )
            return

        # Route to the appropriate handler inside a DB transaction so handler work and
        # marking the webhook as processed are atomic.
        try:
            with transaction.atomic():
                if event_type == RazorpayWebhookEvents.PAYMENT_AUTHORIZED:
                    self._handle_payment_authorized(webhook_event)
                elif event_type == RazorpayWebhookEvents.PAYMENT_CAPTURED:
                    self._handle_payment_captured(webhook_event)
                elif event_type == RazorpayWebhookEvents.PAYMENT_FAILED:
                    self._handle_payment_failed(webhook_event)
                elif event_type == RazorpayWebhookEvents.PAYMENT_LINK_PAID:
                    self._handle_payment_link_paid(webhook_event)
                elif event_type == RazorpayWebhookEvents.PAYMENT_LINK_CANCELLED:
                    self._handle_payment_link_cancelled(webhook_event)
                elif event_type == RazorpayWebhookEvents.PAYMENT_LINK_EXPIRED:
                    self._handle_payment_link_expired(webhook_event)

                # Mark as processed within the same transaction
                webhook_event.mark_as_processed()
                logger.info(f"Successfully processed webhook event {webhook_event.event_id}")

        except Exception as e:
            # Log and persist the processing error in a separate transaction so the
            # rollback of the handler work does not prevent saving the error state.
            logger.error(
                f"Error processing payment webhook {getattr(webhook_event, 'event_id', '<unknown>')}: {str(e)}"
            )
            try:
                with transaction.atomic():
                    webhook_event.mark_processing_error(str(e))
            except Exception:
                logger.exception(
                    f"Failed to persist processing error for webhook {getattr(webhook_event, 'event_id', '<unknown>')} after rollback"
                )

            # Re-raise as a WebhookException so callers/monitoring can act on it
            raise WebhookException(f"Payment webhook processing failed: {str(e)}")
    
    def _handle_payment_authorized(self, webhook_event):
        """
        Handle payment.authorized event.
        
        This event is triggered when a payment is authorized
        but not yet captured (for manual capture flows).
        """
        payment_data = webhook_event.get_payment_data()
        payment_id = payment_data.get('id')
        
        if not payment_id:
            raise WebhookException("No payment ID in webhook data")
        
        # Find payment intent
        payment_intent = self._find_payment_intent_by_order(payment_data)
        if not payment_intent:
            logger.warning(f"No payment intent found for payment {payment_id}")
            return
        
        # Check for idempotency - skip if already has a payment ID
        if payment_intent.razorpay_payment_id == payment_id:
            logger.info(f"Payment {payment_id} already processed for intent {payment_intent.id}")
            return
        
        # Update payment intent with Razorpay payment ID
        if not payment_intent.razorpay_payment_id:
            payment_intent.razorpay_payment_id = payment_id
            payment_intent.status = PaymentStatus.PROCESSING
            payment_intent.save()
            
            logger.info(f"Updated payment intent {payment_intent.id} with payment ID {payment_id}")
    
    def _handle_payment_captured(self, webhook_event):
        """
        Handle payment.captured event.
        
        This is the main event that indicates successful payment.
        Triggers split creation and transfer initiation.
        """
        payment_data = webhook_event.get_payment_data()
        payment_id = payment_data.get('id')
        
        if not payment_id:
            raise WebhookException("No payment ID in webhook data")
        
        try:
            with transaction.atomic():
                # Find payment intent
                payment_intent = self._find_payment_intent_by_order(payment_data)
                if not payment_intent:
                    logger.warning(f"No payment intent found for payment {payment_id}")
                    return
                
                # Skip if already processed
                if payment_intent.is_paid():
                    logger.info(f"Payment {payment_id} already processed")
                    return
                
                # Update payment intent
                payment_intent.razorpay_payment_id = payment_id
                payment_intent.payment_method = payment_data.get('method', '')
                payment_intent.mark_as_paid()
                
                # Calculate and create splits if not already done
                if not payment_intent.splits.exists():
                    split_data = self.split_service.calculate_splits(payment_intent)
                    payment_intent.split_details = split_data
                    payment_intent.save()
                    
                    # Create split records
                    splits = self.split_service.create_payment_splits(payment_intent, split_data)
                    logger.info(f"Created {len(splits)} payment splits for payment {payment_intent.id}")
                
                # Initiate transfers
                self._initiate_transfers(payment_intent)
                
                # Send notifications
                self._send_payment_success_notifications(payment_intent)
                
                logger.info(f"Successfully processed payment capture for {payment_intent.id}")
                
        except Exception as e:
            logger.error(f"Error handling payment captured: {str(e)}")
            raise PaymentProcessingException(f"Failed to process payment capture: {str(e)}")
    
    def _handle_payment_failed(self, webhook_event):
        """
        Handle payment.failed event.
        
        Updates payment status and sends failure notifications.
        """
        payment_data = webhook_event.get_payment_data()
        payment_id = payment_data.get('id')
        
        if not payment_id:
            raise WebhookException("No payment ID in webhook data")
        
        # Find payment intent
        payment_intent = self._find_payment_intent_by_order(payment_data)
        if not payment_intent:
            logger.warning(f"No payment intent found for failed payment {payment_id}")
            return
        
        # Update payment status
        payment_intent.razorpay_payment_id = payment_id
        payment_intent.status = PaymentStatus.FAILED
        payment_intent.save()
        
        # Send failure notifications
        self._send_payment_failure_notifications(payment_intent, payment_data)
        
        logger.info(f"Marked payment {payment_intent.id} as failed")
    
    def _handle_payment_link_paid(self, webhook_event):
        """
        Handle payment_link.paid event.
        
        Similar to payment.captured but for payment links.
        """
        entity_data = webhook_event.get_entity_data()
        payment_link_id = entity_data.get('id')
        try:
            payment_intent = PaymentIntent.objects.select_for_update().get(
                razorpay_payment_link_id=payment_link_id
            )
        except PaymentIntent.DoesNotExist:
            logger.warning(f"No payment intent found for payment link {payment_link_id}")
            return
        
        # Get payment details from the link
        payments = entity_data.get('payments', [])
        if payments:
            payment_data = payments[0]  # Get first payment
            self._process_payment_link_payment(payment_intent, payment_data)
    
    def _handle_payment_link_cancelled(self, webhook_event):
        """
        Handle payment_link.cancelled event.
        """
        entity_data = webhook_event.get_entity_data()
        payment_link_id = entity_data.get('id')
        
        try:
            payment_intent = PaymentIntent.objects.get(
                razorpay_payment_link_id=payment_link_id
            )
            payment_intent.status = PaymentStatus.CANCELLED
            payment_intent.save()
            
            logger.info(f"Marked payment link {payment_link_id} as cancelled")
            
        except PaymentIntent.DoesNotExist:
            logger.warning(f"No payment intent found for cancelled payment link {payment_link_id}")
    
    def _handle_payment_link_expired(self, webhook_event):
        """
        Handle payment_link.expired event.
        """
        entity_data = webhook_event.get_entity_data()
        payment_link_id = entity_data.get('id')
        
        try:
            payment_intent = PaymentIntent.objects.get(
                razorpay_payment_link_id=payment_link_id
            )
            payment_intent.status = PaymentStatus.CANCELLED  # Treat expired as cancelled
            payment_intent.save()
            
            logger.info(f"Marked expired payment link {payment_link_id} as cancelled")
            
        except PaymentIntent.DoesNotExist:
            logger.warning(f"No payment intent found for expired payment link {payment_link_id}")
    
    def _find_payment_intent_by_order(self, payment_data):
        """
        Find payment intent by Razorpay order ID.
        
        Args:
            payment_data: Payment data from webhook
            
        Returns:
            PaymentIntent instance or None
        """
        order_id = payment_data.get('order_id')
        if not order_id:
            return None
        
        try:
            return PaymentIntent.objects.select_for_update().get(razorpay_order_id=order_id)
        except PaymentIntent.DoesNotExist:
            return None
    
    def _process_payment_link_payment(self, payment_intent, payment_data):
        """
        Process payment from payment link.
        
        Args:
            payment_intent: PaymentIntent instance
            payment_data: Payment data from webhook
        """
        try:
            with transaction.atomic():
                # Skip if already processed
                if payment_intent.is_paid():
                    return
                
                # Update payment intent
                payment_intent.razorpay_payment_id = payment_data.get('id')
                payment_intent.payment_method = payment_data.get('method', '')
                payment_intent.mark_as_paid()
                
                # Process splits and transfers
                if not payment_intent.splits.exists():
                    split_data = self.split_service.calculate_splits(payment_intent)
                    payment_intent.split_details = split_data
                    payment_intent.save()
                    
                    splits = self.split_service.create_payment_splits(payment_intent, split_data)
                    logger.info(f"Created {len(splits)} splits for payment link payment")
                
                # Initiate transfers
                self._initiate_transfers(payment_intent)
                
                # Send notifications
                self._send_payment_success_notifications(payment_intent)
                
        except Exception as e:
            logger.error(f"Error processing payment link payment: {str(e)}")
            raise
    
    def _initiate_transfers(self, payment_intent):
        """
        Initiate Razorpay Route transfers for payment splits.
        
        Args:
            payment_intent: PaymentIntent instance
        """
        try:
            # Get splits that need transfers (exclude platform commission)
            splits_for_transfer = payment_intent.splits.filter(
                recipient_type__in=['partner', 'vendor'],
                razorpay_account_id__isnull=False,
                status='pending'
            )
            
            if not splits_for_transfer.exists():
                logger.info(f"No transfers needed for payment {payment_intent.id}")
                return
            
            # Build transfer list
            transfers = []
            split_mapping = {}  # Map transfer index to split
            for split in splits_for_transfer:
                transfer_payload = split.get_transfer_payload()
                if transfer_payload:
                    # Add a reference to track which split this transfer belongs to
                    transfer_payload['notes'] = transfer_payload.get('notes', {})
                    transfer_payload['notes']['split_id'] = str(split.id)
                    transfers.append(transfer_payload)
                    split_mapping[len(transfers) - 1] = split
            
            if not transfers:
                logger.warning(f"No valid transfers for payment {payment_intent.id}")
                return
            
            # Create transfers via Razorpay and handle partial failures
            try:
                transfer_results = self.razorpay_service.create_transfer(
                    payment_intent.razorpay_payment_id,
                    transfers,
                )
            except Exception as e:
                # Unexpected exception while calling Razorpay - mark splits for retry
                logger.exception(f"Exception while creating transfers for payment {payment_intent.id}: {str(e)}")

                # Mark all pending splits as failed (will increment retry_count) and enqueue retries where possible
                for split in splits_for_transfer:
                    try:
                        split.mark_as_failed(error_message=str(e))
                        # Enqueue retry task for transient issues
                        try:
                            from ..tasks import retry_failed_transfer

                            if split.can_retry():
                                retry_failed_transfer.delay(split.id)
                        except Exception:
                            logger.exception(f"Failed to schedule retry for split {split.id}")
                    except Exception:
                        logger.exception(f"Failed to mark split {split.id} as failed after transfer exception")

                # Raise a processing exception so outer handlers can mark webhook as partially failed
                raise PaymentProcessingException(
                    f"Failed to create transfers for payment {payment_intent.id}: {str(e)}"
                )

            # Inspect per-transfer results for success/failure
            failed_transfers = []
            successful_count = 0
            for i, transfer_result in enumerate(transfer_results):
                if i not in split_mapping:
                    # Unknown mapping - skip but log
                    logger.warning(f"Received transfer result for unknown index {i}: {transfer_result}")
                    continue

                split = split_mapping[i]

                # Extract commonly used fields from Razorpay response
                transfer_id = transfer_result.get("id")
                status = transfer_result.get("status")
                # Razorpay may include error details in different keys
                error_obj = transfer_result.get("error") or {}
                error_code = transfer_result.get("error_code") or error_obj.get("code")
                error_message = (
                    transfer_result.get("failure_reason")
                    or transfer_result.get("error_message")
                    or error_obj.get("description")
                    or transfer_result.get("message")
                )

                # Treat as failure if explicit error present or status indicates failure
                is_failed = bool(error_obj) or (status and str(status).lower() in ["failed", "rejected", "errored"])

                if is_failed:
                    try:
                        split.mark_as_failed(error_code=error_code, error_message=error_message)
                        failed_transfers.append({"split_id": str(split.id), "error_code": error_code, "error_message": error_message})

                        # Schedule retries for transient failures when allowed
                        try:
                            from ..tasks import retry_failed_transfer

                            if split.can_retry():
                                retry_failed_transfer.delay(split.id)
                        except Exception:
                            logger.exception(f"Failed to schedule retry for failed split {split.id}")

                        logger.error(
                            f"Transfer failed for split {split.id}: code={error_code} message={error_message}"
                        )
                    except Exception:
                        logger.exception(f"Error updating split {split.id} after failed transfer result")

                else:
                    # Success path: update split with transfer id and mark as initiated
                    if transfer_id:
                        try:
                            split.mark_as_initiated(transfer_id)
                            successful_count += 1
                        except Exception:
                            logger.exception(f"Failed to mark split {split.id} as initiated with transfer {transfer_id}")
                    else:
                        # No transfer id but not flagged as error - treat as partial failure
                        try:
                            split.mark_as_failed(error_message=f"No transfer id returned: {transfer_result}")
                            failed_transfers.append({"split_id": str(split.id), "error_message": "No transfer id returned"})
                        except Exception:
                            logger.exception(f"Failed to mark split {split.id} as failed when transfer id missing")

            logger.info(f"Initiated {successful_count} transfers for payment {payment_intent.id}")

            # If there were any failures, raise an exception summarizing them so callers/monitoring know it's partial
            if failed_transfers:
                summary = {
                    "payment_intent": str(payment_intent.id),
                    "successful": successful_count,
                    "failed_count": len(failed_transfers),
                    "failures": failed_transfers,
                }
                logger.error(f"Partial transfer failure for payment {payment_intent.id}: {summary}")
                # Raise so outer transaction can decide whether to rollback or mark webhook as errored
                raise PaymentProcessingException(f"Partial transfer failure: {summary}")
            
        except Exception as e:
            logger.error(f"Error initiating transfers: {str(e)}")
            # Re-raise to ensure webhook processing marks this as failed
            raise    
    def _send_payment_success_notifications(self, payment_intent):
        """
        Send payment success notifications.
        
        Args:
            payment_intent: PaymentIntent instance
        """
        try:
            # Import here to avoid circular imports
            from ..services import PaymentNotificationService
            
            notification_service = PaymentNotificationService()
            notification_service.send_payment_success_notification(payment_intent)
            
        except Exception as e:
            logger.error(f"Error sending success notifications: {str(e)}")
            # Don't raise exception - notifications are not critical
    
    def _send_payment_failure_notifications(self, payment_intent, payment_data):
        """
        Send payment failure notifications.
        
        Args:
            payment_intent: PaymentIntent instance
            payment_data: Payment data from webhook
        """
        try:
            # Import here to avoid circular imports
            from ..services import PaymentNotificationService
            
            notification_service = PaymentNotificationService()
            error_reason = payment_data.get('error_description', 'Payment failed')
            notification_service.send_payment_failure_notification(
                payment_intent, 
                error_reason
            )
            
        except Exception as e:
            logger.error(f"Error sending failure notifications: {str(e)}")
            # Don't raise exception - notifications are not critical
