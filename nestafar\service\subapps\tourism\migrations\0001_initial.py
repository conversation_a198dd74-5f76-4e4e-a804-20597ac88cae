# Generated by Django 4.2.7 on 2025-10-02 05:11

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('service', '0001_initial'),
        ('geo', '0001_initial'),
        ('stay', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TourismCart',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.PositiveSmallIntegerField(choices=[(0, 'Pending'), (1, 'Ordered'), (2, 'Partially Accepted'), (3, 'Accepted'), (4, 'Ongoing'), (5, 'Partially Rejected'), (6, 'Rejected'), (7, 'Cancelled'), (8, 'Completed'), (9, 'Incomplete')], default=0)),
                ('subtotal', models.FloatField(default=0)),
                ('taxes', models.FloatField(default=0)),
                ('charges', models.FloatField(default=0)),
                ('total', models.FloatField(default=0)),
                ('order_created_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pickup_time', models.DateTimeField(blank=True, null=True)),
                ('special_request', models.TextField(blank=True, null=True)),
                ('guest', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.guest')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TourismOrder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.PositiveSmallIntegerField(choices=[(0, 'Pending'), (1, 'Accepted'), (2, 'Ongoing'), (3, 'Rejected'), (4, 'Cancelled'), (5, 'Completed')], default=0)),
                ('subtotal', models.FloatField(default=0)),
                ('commissions', models.FloatField(default=0)),
                ('taxes', models.FloatField(default=0)),
                ('charges', models.FloatField(default=0)),
                ('total', models.FloatField(default=0)),
                ('rating', models.FloatField(default=0)),
                ('review', models.TextField(default='')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_reminder_sent', models.DateTimeField(blank=True, null=True)),
                ('reminder_count', models.PositiveIntegerField(default=0)),
                ('pickup_time', models.DateTimeField(blank=True, null=True)),
                ('special_request', models.TextField(blank=True, null=True)),
                ('cart', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='tourism.tourismcart')),
                ('guest', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.guest')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TourismService',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('charges', models.FloatField(blank=True, null=True)),
                ('tax_rate', models.FloatField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('local', models.BooleanField(default=False)),
                ('per_person', models.BooleanField(default=False)),
                ('vehicle_type', models.CharField(choices=[('Auto', 'Auto'), ('Hatchback', 'Hatchback'), ('Sedan', 'Sedan'), ('SUV', 'Suv'), ('Van', 'Van'), ('Bus', 'Bus')], default='Sedan', max_length=100)),
                ('disclaimer', models.CharField(blank=True, max_length=500, null=True)),
                ('partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='service.servicepartner')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TourismServiceItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('image', models.ImageField(blank=True, null=True, upload_to='service_item_images')),
                ('addon', models.JSONField(blank=True, null=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('price', models.FloatField()),
                ('is_active', models.BooleanField(default=True)),
                ('rating', models.FloatField(blank=True, default=0, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('start_time', models.TimeField(blank=True, default=None, null=True)),
                ('duration', models.FloatField(default=0)),
                ('duration_type', models.CharField(choices=[('Hour', 'Hour'), ('Day', 'Day')], default='Hour', max_length=100)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_items', to='tourism.tourismservice')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TourismOrderItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantity', models.IntegerField(default=1)),
                ('add_ons', models.JSONField(blank=True, default=dict, null=True)),
                ('price', models.FloatField(default=0)),
                ('rating', models.FloatField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='tourism.tourismserviceitem')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='tourism.tourismorder')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='tourismorder',
            name='service',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='tourism.tourismservice'),
        ),
        migrations.AddField(
            model_name='tourismorder',
            name='service_partner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='service.servicepartner'),
        ),
        migrations.CreateModel(
            name='TourismCartItems',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('quantity', models.IntegerField(default=1)),
                ('price', models.FloatField(default=0)),
                ('ordered', models.BooleanField(default=False)),
                ('add_ons', models.JSONField(blank=True, default=list, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cart', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='tourism.tourismcart')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='tourism.tourismserviceitem')),
            ],
            options={
                'verbose_name_plural': 'Tourism cart items',
            },
        ),
        migrations.CreateModel(
            name='ItinerarySpot',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('image', models.ImageField(blank=True, null=True, upload_to='itinerary_spot_images')),
                ('description', models.TextField(blank=True, null=True)),
                ('stop_no', models.PositiveIntegerField(default=0)),
                ('wait_time', models.FloatField(default=0)),
                ('duration_type', models.CharField(choices=[('Hour', 'Hour'), ('Day', 'Day')], default='Hour', max_length=100)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='itinerary_spots', to='geo.location')),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='itinerary_spots', to='tourism.tourismserviceitem')),
            ],
        ),
        migrations.AddIndex(
            model_name='tourismorder',
            index=models.Index(fields=['guest', 'cart'], name='tourism_tou_guest_i_5450e8_idx'),
        ),
    ]
