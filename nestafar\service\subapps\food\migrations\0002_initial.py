# Generated by Django 4.2.7 on 2025-10-02 05:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('service', '0001_initial'),
        ('stay', '0001_initial'),
        ('food', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='foodservice',
            name='partner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='service.servicepartner'),
        ),
        migrations.AddField(
            model_name='foodorderitem',
            name='item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='food.foodserviceitem'),
        ),
        migrations.AddField(
            model_name='foodorderitem',
            name='order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='food.foodorder'),
        ),
        migrations.AddField(
            model_name='foodorder',
            name='cart',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='food.foodcart'),
        ),
        migrations.AddField(
            model_name='foodorder',
            name='guest',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.guest'),
        ),
        migrations.AddField(
            model_name='foodorder',
            name='service',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='food.foodservice'),
        ),
        migrations.AddField(
            model_name='foodorder',
            name='service_partner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='service.servicepartner'),
        ),
        migrations.AddField(
            model_name='foodcartitems',
            name='cart',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='food.foodcart'),
        ),
        migrations.AddField(
            model_name='foodcartitems',
            name='item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='food.foodserviceitem'),
        ),
        migrations.AddField(
            model_name='foodcart',
            name='guest',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.guest'),
        ),
        migrations.AddIndex(
            model_name='foodorder',
            index=models.Index(fields=['guest', 'cart'], name='food_foodor_guest_i_a62068_idx'),
        ),
    ]
