"""
Razorpay Webhook Handler

Main webhook handler for processing Razorpay webhook events with
signature verification, event processing, and error handling.
"""

import json
import logging
import ipaddress
from django.http import HttpResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.conf import settings
from django.db import transaction
from django_ratelimit.decorators import ratelimit
from ..models import PaymentWebhookEvent
from ..services import RazorpayService
from ..exceptions import SignatureVerificationException, WebhookException
from ..constants import RazorpayWebhookEvents
from .payment_webhook_processor import PaymentWebhookProcessor
from .transfer_webhook_processor import TransferWebhookProcessor
from .account_webhook_processor import AccountWebhookProcessor, KYCWebhookProcessor

logger = logging.getLogger(__name__)


@method_decorator(ratelimit(key='ip', rate=getattr(settings, 'RAZORPAY_WEBHOOK_RATE', '100/m'), block=False), name='post')
@method_decorator(csrf_exempt, name='dispatch')
class RazorpayWebhookHandler(View):
    """
    Main webhook handler for Razorpay events.
    
    Handles incoming webhooks with signature verification,
    event logging, and processing delegation.
    """
    
    def __init__(self):
        super().__init__()
        self._razorpay_service = None
        self._payment_processor = None
        self._transfer_processor = None
    
    @property
    def razorpay_service(self):
        """Lazy-loaded RazorpayService instance"""
        if self._razorpay_service is None:
            self._razorpay_service = RazorpayService()
        return self._razorpay_service
    
    @property
    def payment_processor(self):
        """Lazy-loaded PaymentWebhookProcessor instance"""
        if self._payment_processor is None:
            self._payment_processor = PaymentWebhookProcessor()
        return self._payment_processor
    
    @property
    def transfer_processor(self):
        """Lazy-loaded TransferWebhookProcessor instance"""
        if self._transfer_processor is None:
            self._transfer_processor = TransferWebhookProcessor()
        return self._transfer_processor
    
    def post(self, request):
        """
        Handle incoming webhook POST requests.
        
        Process includes:
        1. Signature verification
        2. Event logging
        3. Event processing
        4. Response handling
        """
        # If django-ratelimit flagged this request as limited, return 429
        if getattr(request, 'limited', False):
            return HttpResponse(
                json.dumps({'error': 'Too many requests'}),
                content_type='application/json',
                status=429
            )

        try:
            # Get request data
            payload = request.body.decode('utf-8')
            signature = request.headers.get('X-Razorpay-Signature', '')
            source_ip = self._get_client_ip(request)
            
            # Verify webhook signature
            if not self._verify_signature(payload, signature):
                logger.warning(f"Invalid webhook signature from IP {source_ip}")
                return HttpResponse('Invalid signature', status=400)
            
            # Parse webhook data
            try:
                webhook_data = json.loads(payload)
            except json.JSONDecodeError:
                logger.error("Invalid JSON in webhook payload")
                return HttpResponse('Invalid JSON', status=400)
            
            # Validate webhook structure
            if not self._validate_webhook_structure(webhook_data):
                logger.error("Invalid webhook structure")
                return HttpResponse('Invalid webhook structure', status=400)
            
            # Materialize headers into a plain dict of strings to ensure serializability
            try:
                headers_dict = {k: str(v) for k, v in request.headers.items()}
            except Exception:
                # Fallback: empty dict if headers cannot be iterated
                headers_dict = {}

            # Create webhook event record
            webhook_event = self._create_webhook_event(
                webhook_data, headers_dict, source_ip, signature
            )
            
            # Process webhook asynchronously
            self._process_webhook_async(webhook_event)
            
            # Return success response
            return HttpResponse('OK', status=200)
            
        except Exception as e:
            # Log full stacktrace for internal debugging but do not leak internal details to clients
            logger.exception(f"Webhook processing error while handling incoming request: {str(e)}")
            return HttpResponse("Internal server error", status=500)
    
    def _verify_signature(self, payload: str, signature: str) -> bool:
        """
        Verify webhook signature.
        
        Args:
            payload: Raw webhook payload
            signature: Signature from headers
            
        Returns:
            True if signature is valid
        """
        try:
            return self.razorpay_service.verify_webhook_signature(payload, signature)
        except SignatureVerificationException as e:
            logger.error(f"Signature verification failed: {str(e)}")
            return False
    
    def _validate_webhook_structure(self, webhook_data: dict) -> bool:
        """
        Validate webhook data structure.
        
        Args:
            webhook_data: Parsed webhook data
            
        Returns:
            True if structure is valid
        """
        required_fields = ['event', 'payload']
        
        for field in required_fields:
            if field not in webhook_data:
                return False
        
        # Check if event type is supported
        event_type = webhook_data.get('event', '')
        if event_type not in RazorpayWebhookEvents.SUPPORTED_EVENTS:
            logger.warning(f"Unsupported webhook event: {event_type}")
            return False
        
        return True
    
    def _create_webhook_event(self, webhook_data: dict, headers: dict, 
                            source_ip: str, signature: str) -> PaymentWebhookEvent:
        """
        Create webhook event record.
        
        Args:
            webhook_data: Parsed webhook data
            headers: Request headers
            source_ip: Source IP address
            signature: Webhook signature
            
        Returns:
            Created PaymentWebhookEvent instance
        """
        try:
            with transaction.atomic():
                webhook_event = PaymentWebhookEvent.create_from_webhook(
                    webhook_data, 
                    headers=dict(headers),
                    source_ip=source_ip
                )
                webhook_event.signature = signature
                webhook_event.signature_verified = True
                webhook_event.save()
                
                logger.info(f"Created webhook event {webhook_event.event_id} "
                          f"for event type {webhook_event.event_type}")
                
                return webhook_event
                
        except Exception as e:
            logger.error(f"Failed to create webhook event: {str(e)}")
            raise WebhookException(f"Failed to create webhook event: {str(e)}")
    
    def _process_webhook_async(self, webhook_event: PaymentWebhookEvent):
        """
        Process webhook event asynchronously.
        
        Args:
            webhook_event: PaymentWebhookEvent instance
        """
        try:
            # Import here to avoid circular imports
            from ..tasks import process_webhook_event
            
            # Queue webhook processing task
            process_webhook_event.delay(webhook_event.id)
            
            logger.info(f"Queued webhook processing for event {webhook_event.event_id}")
            
        except Exception as e:
            logger.error(f"Failed to queue webhook processing: {str(e)}")
            # Fall back to synchronous processing
            self._process_webhook_sync(webhook_event)
    
    def _process_webhook_sync(self, webhook_event: PaymentWebhookEvent):
        """
        Process webhook event synchronously.
        
        Args:
            webhook_event: PaymentWebhookEvent instance
        """
        try:
            if webhook_event.is_payment_event():
                self.payment_processor.process_event(webhook_event)
            elif webhook_event.is_transfer_event():
                self.transfer_processor.process_event(webhook_event)
            elif webhook_event.is_account_event():
                account_processor = AccountWebhookProcessor()
                account_processor.process_event(webhook_event)
            elif webhook_event.is_kyc_event():
                kyc_processor = KYCWebhookProcessor()
                kyc_processor.process_kyc_verification_event(webhook_event)
            else:
                logger.warning(f"No processor for event type: {webhook_event.event_type}")
                webhook_event.mark_processing_error("No processor available")
                
        except Exception as e:
            logger.error(f"Webhook processing failed: {str(e)}")
            webhook_event.mark_processing_error(str(e))
    
    def _get_client_ip(self, request) -> str:
        """
        Get client IP address from request.
        
        Args:
            request: Django request object
            
        Returns:
            Client IP address
        """
        # Prefer X-Forwarded-For if present (may contain multiple IPs)
        candidates = []
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            # Split and strip potential multiple proxies
            candidates.extend([c.strip() for c in x_forwarded_for.split(',') if c.strip()])

        # Add standard remote addresses as fallbacks
        remote_addr = request.META.get('REMOTE_ADDR')
        if remote_addr:
            candidates.append(remote_addr)

        # Validate candidates using ipaddress; return first valid IPv4/IPv6
        for candidate in candidates:
            try:
                ipaddress.ip_address(candidate)
                return candidate
            except Exception:
                continue

        # No valid IP found; log a warning and return empty string instead of 'unknown'
        logger.warning('No valid client IP found in request headers or META')
        return ''
    
    def get(self, request):
        """
        Handle GET requests (for webhook URL verification).
        
        Some webhook providers send GET requests to verify URLs.
        """
        return HttpResponse('Webhook endpoint active', status=200)


class WebhookStatusView(View):
    """
    View for checking webhook processing status and statistics.
    """
    
    def get(self, request):
        """
        Get webhook processing statistics.
        
        Returns JSON with webhook processing stats.
        """
        try:
            # Calculate statistics
            total_events = PaymentWebhookEvent.objects.count()
            processed_events = PaymentWebhookEvent.objects.filter(processed=True).count()
            failed_events = PaymentWebhookEvent.objects.filter(
                processed=False, 
                processing_error__isnull=False
            ).count()
            pending_events = PaymentWebhookEvent.objects.filter(
                processed=False,
                processing_error__isnull=True
            ).count()
            
            # Calculate success rate
            success_rate = (processed_events / total_events * 100) if total_events > 0 else 0
            
            # Get recent events
            from django.utils import timezone
            from datetime import timedelta
            
            last_24h = timezone.now() - timedelta(hours=24)
            events_last_24h = PaymentWebhookEvent.objects.filter(
                received_at__gte=last_24h
            ).count()
            
            stats = {
                'total_events': total_events,
                'processed_events': processed_events,
                'failed_events': failed_events,
                'pending_events': pending_events,
                'success_rate': round(success_rate, 2),
                'events_last_24h': events_last_24h,
                'status': 'healthy' if success_rate > 95 else 'degraded' if success_rate > 80 else 'unhealthy'
            }
            
            return HttpResponse(
                json.dumps(stats),
                content_type='application/json',
                status=200
            )
            
        except Exception as e:
            logger.error(f"Error getting webhook stats: {str(e)}")
            return HttpResponse(
                json.dumps({'error': 'Failed to get statistics'}),
                content_type='application/json',
                status=500
            )


class WebhookRetryView(View):
    """
    View for manually retrying failed webhook events.
    """
    def post(self, request):
        """Accept a JSON body with `event_ids` (list) and optional `force_retry`.

        This view performs a single bulk DB lookup to verify which event_ids
        exist, reports any missing IDs as a validation error, and enqueues
        retry tasks for the existing events.
        """
        from django.http import JsonResponse
        from rest_framework import status
        from rest_framework.exceptions import ValidationError
        from ..serializers.webhook_event_serializer import WebhookRetrySerializer
        from ..models import PaymentWebhookEvent

        try:
            data = json.loads(request.body.decode('utf-8') or '{}')
        except Exception:
            return JsonResponse({'error': 'Invalid JSON'}, status=400)

        serializer = WebhookRetrySerializer(data=data)
        if not serializer.is_valid():
            return JsonResponse(serializer.errors, status=400)

        validated = serializer.validated_data
        event_ids = validated.get('event_ids', [])
        force_retry = validated.get('force_retry', False)

        # Bulk fetch existing event_ids
        existing = set(
            PaymentWebhookEvent.objects.filter(
                event_id__in=event_ids
            ).values_list('event_id', flat=True)
        )

        missing = set(event_ids) - existing
        if missing:
            # Return a validation error listing missing IDs
            raise ValidationError({'event_ids': f"Event IDs not found: {', '.join(sorted(missing))}"})

        try:
            # Return a validation error listing missing IDs
            return JsonResponse(
                {'event_ids': f"Event IDs not found: {', '.join(sorted(missing))}"}, 
                status=400
            )  
          
            from ..tasks import process_webhook_event

            # Bulk fetch the DB records for the requested external event_ids and
            # map external id -> PK so we queue the task with the DB primary key
            events_qs = PaymentWebhookEvent.objects.filter(event_id__in=event_ids).only('id', 'event_id')
            events_map = {e.event_id: e.id for e in events_qs}

            queued = 0
            for external_id in event_ids:
                pk = events_map.get(external_id)
                if pk is None:
                    # Shouldn't happen because we validated existence earlier,
                    # but handle defensively in case of race conditions.
                    logger.warning(f"WebhookRetryView: event_id not found when scheduling retry: {external_id}")
                    continue

                try:
                    # Pass the DB PK (int) to the task as other callers do
                    process_webhook_event.delay(pk)
                    queued += 1
                except Exception as e:
                    logger.error(f"Failed to enqueue retry for event {external_id} (pk={pk}): {str(e)}")

            return JsonResponse({'message': 'Retry scheduled', 'count': queued}, status=200)

        except Exception as e:
            logger.error(f"Failed to schedule retry tasks: {str(e)}")
            return JsonResponse({'error': 'Failed to schedule retries'}, status=500)

