import uuid
from django.db import models
from django.utils import timezone
from core.models import PartnerProfile
from stay.models import Property


class OnboardingStatus(models.Model):
    """Track onboarding progress for property partners"""

    class StatusChoices(models.TextChoices):
        PENDING = "pending", "Pending"
        IN_PROGRESS = "in_progress", "In Progress"
        COMPLETED = "completed", "Completed"
        INCOMPLETE = "incomplete", "Incomplete"

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    partner = models.ForeignKey(
        PartnerProfile, on_delete=models.CASCADE, related_name="onboarding_statuses"
    )
    property = models.ForeignKey(
        Property, on_delete=models.CASCADE, related_name="onboarding_statuses"
    )

    # Onboarding steps
    property_details_added = models.BooleanField(default=False)
    property_photos_uploaded = models.BooleanField(default=False)
    rooms_added = models.BooleanField(default=False)
    room_photos_uploaded = models.BooleanField(default=False)
    services_added = models.BooleanField(default=False)
    status = models.CharField(
        max_length=20, choices=StatusChoices.choices, default=StatusChoices.PENDING
    )

    total_gmv = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )  # Gross Merchandise Value
    total_commission = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Reminder tracking
    last_reminder_sent = models.DateTimeField(null=True, blank=True)
    reminder_count = models.IntegerField(default=0)

    class Meta:
        unique_together = ["partner", "property"]
        ordering = ["-updated_at"]
        verbose_name_plural = "Onboarding Statuses"

    def __str__(self):
        partner_name = (
            getattr(self.partner.user, "name", "Unknown")
            if self.partner.user
            else "Unknown"
        )
        return f"Onboarding for {partner_name} - {self.status}"

    def calculate_completion_percentage(self):
        """Calculate completion percentage"""
        onboarding_fields = [
            "property_details_added",
            "property_photos_uploaded",
            "rooms_added",
            "room_photos_uploaded",
            "services_added",
        ]
        total_steps = len(onboarding_fields)
        completed_steps = sum([getattr(self, field) for field in onboarding_fields])
        return (completed_steps / total_steps) * 100

    def is_complete(self):
        """Check if onboarding is complete"""
        return all(
            [
                self.property_details_added,
                self.property_photos_uploaded,
                self.rooms_added,
                self.room_photos_uploaded,
                self.services_added,
            ]
        )

    def get_missing_steps(self):
        """Get list of missing onboarding steps"""
        missing = []
        if not self.property_details_added:
            missing.append("Property details")
        if not self.property_photos_uploaded:
            missing.append("Property photos")
        if not self.rooms_added:
            missing.append("Room details")
        if not self.room_photos_uploaded:
            missing.append("Room photos")
        if not self.services_added:
            missing.append("Services setup")
        return missing

    def update_status(self):
        """Update status based on completion (caller should save)"""
        if self.is_complete():
            self.status = self.StatusChoices.COMPLETED
            self.completed_at = timezone.now()
        elif self.calculate_completion_percentage() > 0:
            self.status = self.StatusChoices.IN_PROGRESS
        else:
            self.status = self.StatusChoices.PENDING


class WeeklyReport(models.Model):
    """Weekly business report for partners"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    partner = models.ForeignKey(
        PartnerProfile, on_delete=models.CASCADE, related_name="weekly_reports"
    )
    property = models.ForeignKey(
        Property, on_delete=models.CASCADE, related_name="weekly_reports"
    )

    # Report period
    week_start = models.DateField()
    week_end = models.DateField()

    # Metrics
    total_reservations = models.IntegerField(default=0)
    occupancy_rate = models.FloatField(default=0.0)  # Percentage
    average_orders_per_guest = models.FloatField(default=0.0)
    total_gmv = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )  # Gross Merchandise Value
    total_commission = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )
    # Recommendations
    recommendations = models.JSONField(default=list)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    sent_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = ["partner", "property", "week_start"]

    def __str__(self):
        partner_name = (
            getattr(self.partner.user, "name", "Unknown")
            if self.partner.user
            else "Unknown"
        )
        return f"Weekly Report - {partner_name} ({self.week_start} to {self.week_end})"

