from django.contrib.auth.forms import ReadOnlyPassword<PERSON>ashField
from django.core.exceptions import ValidationError
from phonenumber_field.formfields import PhoneNumber<PERSON>ield
from django import forms
from ..models import User, UserProfile, PartnerProfile


class UserCreationForm(forms.ModelForm):
    """A form for creating new users. Includes all the required fields, plus a repeated password."""

    phone = PhoneNumberField()
    is_partner = forms.BooleanField(
        label="Partner", widget=forms.CheckboxInput, initial=True, required=False
    )
    password1 = forms.CharField(label="Password", widget=forms.PasswordInput)
    password2 = forms.CharField(
        label="Password confirmation", widget=forms.PasswordInput
    )

    class Meta:
        model = User
        fields = ["name", "phone", "email", "is_partner"]

    def clean_password2(self):
        # Check that the two password entries match
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")
        if password1 and password2 and password1 != password2:
            raise ValidationError("Passwords don't match")
        return password2

    def save(self, commit=True):
        user = super().save(commit=False)
        user.set_password(self.cleaned_data["password1"])
        if commit:
            user.save()
            UserProfile.objects.create(user=user)
            if self.cleaned_data.get("is_partner"):
                PartnerProfile.objects.create(user=user)
        return user


class UserChangeForm(forms.ModelForm):
    """A form for updating users. Includes all the fields on
    the user, but replaces the password field with admin's
    disabled password hash display field.
    """

    phone = PhoneNumberField()
    password = ReadOnlyPasswordHashField()

    class Meta:
        model = User
        fields = ["name", "phone", "email", "is_partner", "is_active", "is_admin"]
