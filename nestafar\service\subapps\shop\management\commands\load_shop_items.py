import pandas as pd
import re
import traceback
import urllib
import os
import requests
from decimal import Decimal, InvalidOperation
from django.core.management.base import BaseCommand, CommandError
from django.core.files.base import ContentFile
from django.db import models, transaction
from service.subapps.shop.models import ShopServiceItem, ShopService


class Command(BaseCommand):
    help = "Add shop items from a CSV file and generate missing images and descriptions using OpenAI"

    # OpenAI compatibility state
    _openai_client = None
    _openai_mode = None  # 'client' (new SDK), 'module' (legacy), 'http' (fallback)
    _openai_initialized = False

    def add_arguments(self, parser):
        parser.add_argument(
            "service_id", type=str, help="ID of the service to which items belong"
        )
        parser.add_argument(
            "file_path",
            type=str,
            nargs="?",
            help="Optional Path to the CSV file with shop items",
        )
        parser.add_argument(
            "--generate-descriptions",
            action="store_true",
            help="Generate descriptions for items using OpenAI",
        )
        parser.add_argument(
            "--generate-images",
            action="store_true", 
            help="Generate images for items using OpenAI",
        )
        parser.add_argument(
            "--update-existing",
            action="store_true",
            help="Update existing items if they already exist",
        )

    def handle(self, *args, **options):
        service_id = options["service_id"]
        file_path = options.get("file_path", None)
        generate_descriptions = options.get("generate_descriptions", False)
        generate_images = options.get("generate_images", False)
        update_existing = options.get("update_existing", False)
        self.verbosity = options.get("verbosity", 1)

        try:
            service = ShopService.objects.get(id=service_id)
        except ShopService.DoesNotExist:
            raise CommandError(f"Service with ID {service_id} does not exist.")

        if file_path:
            self.stdout.write(f"Loading items from {file_path}...")
            self.add_items_from_csv(service, file_path, update_existing)
        
        if generate_descriptions:
            self.stdout.write(f"Generating descriptions for items in service {service.name}...")
            self.generate_descriptions_for_missing_items(service)
            
        if generate_images:
            self.stdout.write(f"Generating images for items in service {service.name}...")
            self.generate_images_for_missing_items(service)

    def add_items_from_csv(self, service, file_path, update_existing=False):
        """Load shop items from CSV file with improved error handling and data parsing."""
        try:
            df = pd.read_csv(file_path)
        except FileNotFoundError:
            raise CommandError(f"File {file_path} not found.")
        except pd.errors.EmptyDataError:
            raise CommandError(f"File {file_path} is empty.")
        except Exception as e:
            raise CommandError(f"Error reading CSV file: {e}")

        # Clean column names (strip whitespace and standardize)
        df.columns = df.columns.str.strip()
        
        # Check for required columns with flexible naming
        column_mapping = self.get_column_mapping(df.columns)
        if not column_mapping:
            available_cols = ", ".join(df.columns.tolist())
            raise CommandError(
                f"CSV file must contain recognizable product information columns. "
                f"Available columns: {available_cols}. "
                f"Expected columns like: Name/Product, Price/MRP, Category, Pack Size, Brand, etc."
            )

        # Filter out empty rows
        df = df.dropna(how='all')
        
        items_created = 0
        items_updated = 0
        items_failed = 0

        with transaction.atomic():
            for index, row in df.iterrows():
                try:
                    if self.is_empty_row(row):
                        continue
                        
                    # Extract and clean data from row
                    item_data = self.extract_item_data(row, column_mapping)
                    
                    if not item_data:
                        self.stdout.write(
                            self.style.WARNING(f"Skipping row {index + 1}: insufficient data")
                        )
                        continue

                    # Check if item exists
                    existing_item = self.find_existing_item(service, item_data)
                    
                    if existing_item:
                        if update_existing:
                            self.update_item(existing_item, item_data)
                            items_updated += 1
                            self.stdout.write(
                                self.style.SUCCESS(f"Updated: {existing_item.name}")
                            )
                        else:
                            self.stdout.write(
                                self.style.WARNING(
                                    f"Item '{item_data['name']}' already exists. Use --update-existing to update."
                                )
                            )
                    else:
                        # Create new item
                        new_item = self.create_item(service, item_data)
                        items_created += 1
                        self.stdout.write(
                            self.style.SUCCESS(f"Created: {new_item.name}")
                        )

                except Exception as e:
                    items_failed += 1
                    self.stdout.write(
                        self.style.ERROR(f"Error processing row {index + 1}: {e}")
                    )
                    if self.verbosity >= 2:
                        self.stdout.write(self.style.ERROR(traceback.format_exc()))

        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f"\nSummary for service '{service.name}':\n"
                f"  - Items created: {items_created}\n"
                f"  - Items updated: {items_updated}\n"
                f"  - Items failed: {items_failed}\n"
                f"  - Total processed: {items_created + items_updated + items_failed}"
            )
        )

    def get_column_mapping(self, columns):
        """Map CSV columns to expected fields with flexible naming."""
        column_mapping = {}
        
        # Possible column names for each field (case-insensitive)
        field_patterns = {
            'name': ['name', 'product', 'product_name', 'item_name'],
            'price': ['price', 'mrp', 'cost', 'amount', 'mrp/price'],
            'category': ['category', 'type', 'product_type'],
            'pack_size': ['pack_size', 'size', 'pack', 'package_size'],
            'brand': ['brand', 'manufacturer', 'company'],
            'description': ['description', 'desc', 'details'],
            'source': ['source', 'supplier', 'vendor'],
            'source_url': ['source_url', 'url', 'link']
        }
        
        columns_lower = [col.lower() for col in columns]
        
        for field, patterns in field_patterns.items():
            for pattern in patterns:
                pattern_lower = pattern.lower()
                # Exact match first
                if pattern_lower in columns_lower:
                    column_mapping[field] = columns[columns_lower.index(pattern_lower)]
                    break
                # Partial match
                else:
                    for col in columns:
                        if pattern_lower in col.lower():
                            column_mapping[field] = col
                            break
                    if field in column_mapping:
                        break
        
        # Ensure we have at least name and price
        if 'name' not in column_mapping or 'price' not in column_mapping:
            return None
            
        return column_mapping

    def is_empty_row(self, row):
        """Check if a row is essentially empty."""
        return row.isna().all() or (row.astype(str).str.strip() == '').all()

    def extract_item_data(self, row, column_mapping):
        """Extract and clean item data from CSV row."""
        try:
            # Get name - combine brand and product if available
            name = self.get_clean_value(row, column_mapping.get('name', ''))
            brand = self.get_clean_value(row, column_mapping.get('brand', ''))
            
            if not name:
                return None
                
            # Create full name with brand if available
            if brand and brand.lower() not in name.lower():
                full_name = f"{brand} {name}"
            else:
                full_name = name
            
            # Get price
            price_raw = self.get_clean_value(row, column_mapping.get('price', ''))
            price = self.parse_price(price_raw)
            
            if price is None:
                self.stdout.write(
                    self.style.WARNING(f"Could not parse price for item: {full_name}")
                )
                return None
            
            # Get other fields
            category = self.get_clean_value(row, column_mapping.get('category', ''), default='General')
            pack_size = self.get_clean_value(row, column_mapping.get('pack_size', ''))
            description = self.get_clean_value(row, column_mapping.get('description', ''))
            source = self.get_clean_value(row, column_mapping.get('source', ''))
            source_url = self.get_clean_value(row, column_mapping.get('source_url', ''))
            
            # Generate description if not provided
            if not description:
                description = self.generate_basic_description(full_name, pack_size, category)
            
            return {
                'name': full_name[:100],  # Truncate to model limit
                'price': float(price),
                'description': description,
                'category': category[:100] if category else 'General',
                'pack_size': pack_size,
                'brand': brand,
                'source': source,
                'source_url': source_url,
            }
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error extracting data from row: {e}")
            )
            return None

    def get_clean_value(self, row, column_name, default=''):
        """Safely get and clean a value from a row."""
        if not column_name or column_name not in row:
            return default
            
        value = row[column_name]
        if pd.isna(value):
            return default
            
        # Clean string value
        value = str(value).strip()
        return value if value else default

    def parse_price(self, price_str):
        """Parse price from various formats."""
        if not price_str:
            return None
            
        try:
            # Remove currency symbols and clean
            price_str = str(price_str).strip()
            
            # Remove common currency symbols and text
            price_clean = re.sub(r'[₹$€£¥,\s]', '', price_str)
            
            # Handle ranges (take the first value)
            if '–' in price_clean or '-' in price_clean:
                price_clean = re.split(r'[–-]', price_clean)[0]
            
            # Handle slashes (take the first value)
            if '/' in price_clean:
                price_clean = price_clean.split('/')[0]
            
            # Extract number
            price_match = re.search(r'[\d.]+', price_clean)
            if price_match:
                return Decimal(price_match.group())
                
        except (ValueError, InvalidOperation):
            pass
            
        return None

    def generate_basic_description(self, name, pack_size, category):
        """Generate a basic description from available data."""
        description_parts = []
        
        if category and category.lower() != 'general':
            description_parts.append(f"{category} product")
        
        if pack_size:
            description_parts.append(f"Available in {pack_size} pack")
        
        if description_parts:
            return f"{name} - " + ", ".join(description_parts) + "."
        else:
            return f"High-quality {name} for your convenience."

    def find_existing_item(self, service, item_data):
        """Find existing item by name or similar criteria."""
        try:
            return ShopServiceItem.objects.get(
                service=service,
                name__iexact=item_data['name']
            )
        except ShopServiceItem.DoesNotExist:
            return None
        except ShopServiceItem.MultipleObjectsReturned:
            # Return the first one if multiple exist
            return ShopServiceItem.objects.filter(
                service=service,
                name__iexact=item_data['name']
            ).first()

    def create_item(self, service, item_data):
        """Create a new shop service item."""
        return ShopServiceItem.objects.create(
            service=service,
            name=item_data['name'],
            price=item_data['price'],
            description=item_data['description'],
            category=item_data['category'],
            # Add any additional fields as needed
        )

    def update_item(self, item, item_data):
        """Update an existing shop service item."""
        item.name = item_data['name']
        item.price = item_data['price']
        item.description = item_data['description']
        item.category = item_data['category']
        item.save()
        return item

    def generate_descriptions_for_missing_items(self, service):
        """Generate descriptions for items that don't have them using OpenAI."""
        items_without_descriptions = ShopServiceItem.objects.filter(
            service=service
        ).filter(
            models.Q(description__isnull=True) | 
            models.Q(description="") |
            models.Q(description__icontains="High-quality")  # Replace basic descriptions
        )

        if not items_without_descriptions.exists():
            self.stdout.write(
                self.style.SUCCESS("No items with missing descriptions found.")
            )
            return

        for item in items_without_descriptions:
            try:
                description = self.generate_description_with_openai(item)
                if description:
                    item.description = description
                    item.save()
                    self.stdout.write(
                        self.style.SUCCESS(f"Generated description for: {item.name}")
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error generating description for {item.name}: {e}")
                )

        self.stdout.write(
            self.style.SUCCESS(
                "Description generation completed for all items with missing descriptions."
            )
        )

    def generate_description_with_openai(self, item):
        """Generate a product description using OpenAI with SDK or HTTP fallback."""
        self.init_openai()
        prompt = f"""
            Create a compelling and informative product description for: {item.name}
            Category: {item.category}
            The description should be:
            - 1-2 sentences long
            - Highlight key benefits or features
            - Be suitable for an e-commerce platform
            - Professional and engaging
            - Focus on what makes this product valuable to customers
            Do not include pricing information.
            """
        if not self._openai_client:
            return self._http_generate_description(prompt, item)
        try:
            if self._openai_mode == "client":
                response = self._openai_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are a professional copywriter specializing in product descriptions."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=150,
                    temperature=0.7,
                )
                try:
                    description = response.choices[0].message.content.strip()
                except AttributeError:
                    description = response["choices"][0]["message"]["content"].strip()
            else:  # legacy module
                response = self._openai_client.ChatCompletion.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are a professional copywriter specializing in product descriptions."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=150,
                    temperature=0.7,
                )
                description = response["choices"][0]["message"]["content"].strip()
            description = description.replace('"', '').replace("'", "'")
            return description[:500]
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"SDK chat failed for {item.name}: {e}; trying HTTP fallback."))
            return self._http_generate_description(prompt, item)

    def init_openai(self):
        """Initialize OpenAI once with backward compatibility."""
        if self._openai_initialized:
            return
        self._openai_initialized = True
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            self.stdout.write(self.style.ERROR("OPENAI_API_KEY not set; skipping OpenAI operations."))
            return
        
        # Check if we have a compatible new SDK client
        try:
            from openai import OpenAI
            import openai as openai_module
            
            # Check version to determine compatibility
            sdk_version = getattr(openai_module, "__version__", "0.0.0")
            major_version = int(sdk_version.split('.')[0])
            
            if major_version >= 1:
                # Try new SDK approach with api_key from env only
                try:
                    self._openai_client = OpenAI(api_key=api_key)
                    self._openai_mode = "client"
                    return
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f"OpenAI v1+ client failed ({e}). Using HTTP fallback."))
            else:
                # Legacy SDK (< 1.0)
                try:
                    openai_module.api_key = api_key
                    if hasattr(openai_module, "ChatCompletion") and hasattr(openai_module, "Image"):
                        self._openai_client = openai_module
                        self._openai_mode = "module"
                        return
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f"Legacy OpenAI setup failed ({e}). Using HTTP fallback."))
        except ImportError:
            self.stdout.write(self.style.WARNING("OpenAI module not found. Using HTTP fallback."))
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"OpenAI initialization failed ({e}). Using HTTP fallback."))
        
        # Fallback to HTTP
        self._openai_client = None
        self._openai_mode = "http"
    def generate_images_for_missing_items(self, service):
        """Generate images for items that don't have them using OpenAI DALL-E."""
        items_without_images = ShopServiceItem.objects.filter(service=service).filter(
            models.Q(image__isnull=True) | models.Q(image="")
        )

        if not items_without_images.exists():
            self.stdout.write(
                self.style.SUCCESS("No items with missing images found.")
            )
            return

        for item in items_without_images:
            try:
                self.generate_image_with_openai(item)
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error generating image for {item.name}: {e}")
                )

        self.stdout.write(
            self.style.SUCCESS(
                "Image generation completed for all items with missing images."
            )
        )

    def generate_image_with_openai(self, item):
        """Generate a product image using OpenAI DALL-E."""
        # Create a detailed prompt for better image generation
        prompt = self.create_image_prompt(item)
        
        self.stdout.write(
            f"Generating image for '{item.name}' with prompt: {prompt[:100]}..."
        )

        try:
            self.init_openai()
            image_url = None
            
            # Try SDK only if we have a proper client (not legacy v1+ incompatible)
            if self._openai_client and self._openai_mode == "client":
                try:
                    response = self._openai_client.images.generate(
                        model="dall-e-2",
                        prompt=prompt,
                        size="512x512",
                        quality="standard",
                        n=1,
                    )
                    try:
                        image_url = response.data[0].url
                    except AttributeError:
                        image_url = response["data"][0]["url"]
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f"SDK image generation failed ({e}); using HTTP fallback."))
            elif self._openai_client and self._openai_mode == "module":
                # Only try legacy if we confirmed it has the right attributes and version < 1.0
                try:
                    response = self._openai_client.Image.create(
                        prompt=prompt,
                        size="512x512",
                        n=1,
                    )
                    image_url = response["data"][0]["url"]
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f"Legacy Image.create failed ({e}); using HTTP fallback."))
            
            # Use HTTP fallback if SDK failed or not available
            if not image_url:
                image_url = self._http_generate_image(prompt)
            
            if not image_url:
                raise ValueError("Image URL could not be obtained from any method")

            result = urllib.request.urlretrieve(image_url)
            with open(result[0], "rb") as img_file:
                image_content = ContentFile(img_file.read())
                item.image.save(f"{item.name}_generated.png", image_content)
                item.save()
            self.stdout.write(self.style.SUCCESS(f"Image successfully generated and saved for {item.name}"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error generating image for {item.name}: {e}"))
            # Continue with other items

    def create_image_prompt(self, item):
        """Create a detailed prompt for image generation."""
        base_prompt = f"Professional product photography of {item.name}"
        
        # Add category-specific details
        category_prompts = {
            'food': 'on a clean white background, well-lit, appetizing presentation',
            'beverage': 'on a clean white background, professional lighting, refreshing look',
            'personal care': 'on a clean white background, professional product shot',
            'household': 'on a clean white background, clean professional presentation',
            'snacks': 'on a clean white background, appetizing and colorful presentation',
            'dairy': 'on a clean white background, fresh and clean presentation',
            'confectionery': 'on a clean white background, appealing and colorful',
            'chocolate': 'on a clean white background, rich and appealing presentation',
        }
        
        category_lower = item.category.lower()
        category_addition = None
        
        for key, addition in category_prompts.items():
            if key in category_lower:
                category_addition = addition
                break
        
        if not category_addition:
            category_addition = 'on a clean white background, professional product photography'
        
        # Create the full prompt
        full_prompt = f"{base_prompt}, {category_addition}, high quality, commercial photography style"
        
        # Limit prompt length
        return full_prompt[:400]
    def _http_generate_image(self, prompt: str):
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            return None
        url = os.getenv("OPENAI_IMAGES_ENDPOINT", "https://api.openai.com/v1/images/generations")
        payload = {"model": "dall-e-2", "prompt": prompt, "size": "512x512", "n": 1}
        try:
            r = requests.post(
                url,
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json",
                },
                json=payload,
                timeout=60,
            )
            if r.status_code != 200:
                self.stdout.write(self.style.ERROR(f"HTTP image gen failed {r.status_code}: {r.text[:200]}"))
                return None
            data = r.json()
            return data.get("data", [{}])[0].get("url")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"HTTP image generation exception: {e}"))
            return None
    def _http_generate_description(self, prompt: str, item):
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            return None
        url = os.getenv("OPENAI_CHAT_ENDPOINT", "https://api.openai.com/v1/chat/completions")
        payload = {
            "model": "gpt-3.5-turbo",
            "messages": [
                {"role": "system", "content": "You are a professional copywriter specializing in product descriptions."},
                {"role": "user", "content": prompt},
            ],
            "max_tokens": 150,
            "temperature": 0.7,
        }
        try:
            r = requests.post(
                url,
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json",
                },
                json=payload,
                timeout=60,
            )
            if r.status_code != 200:
                self.stdout.write(self.style.ERROR(f"HTTP description gen failed {r.status_code}: {r.text[:160]}"))
                return None
            data = r.json()
            content = data.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
            if not content:
                return None
            return content.replace('"', '').replace("'", "'")[:500]
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"HTTP description generation exception for {item.name}: {e}"))
            return None
