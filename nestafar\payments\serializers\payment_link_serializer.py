"""
Payment Link Serializers

Serializers for payment link creation and management operations.
"""

from rest_framework import serializers
from decimal import Decimal
from core.models import PartnerProfile
from ..constants import PaymentContext, PaymentStatus
from ..models import PaymentIntent
from booking.models import PreCheckin
from stay.models import Guest


class PaymentLinkSerializer(serializers.ModelSerializer):
    """Main serializer for PaymentIntent model used in payment links"""

    class Meta:
        model = PaymentIntent
        fields = [
            'id', 'reference_number', 'context', 'total_amount',
            'description', 'status', 'created_at', 'expires_at',
            'razorpay_payment_link_id', 'razorpay_payment_id'
        ]
        read_only_fields = ['id', 'reference_number', 'created_at']


class CreatePaymentLinkSerializer(serializers.Serializer):
    """Serializer for creating payment links"""
    
    # Payment details
    context = serializers.ChoiceField(choices=PaymentContext.choices)
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    description = serializers.CharField(max_length=255)
    
    # Partner and related entities
    partner_id = serializers.UUIDField()
    precheckin_id = serializers.UUIDField(required=False, allow_null=True)
    guest_id = serializers.UUIDField(required=False, allow_null=True)
    
    # Customer details
    customer_name = serializers.CharField(max_length=100)
    customer_email = serializers.EmailField(required=False, allow_blank=True)
    customer_phone = serializers.CharField(max_length=15)
    
    # Payment link settings
    callback_url = serializers.URLField(required=False, allow_blank=True)
    expire_hours = serializers.IntegerField(default=24, min_value=1, max_value=168)  # Max 7 days
    
    # Notification preferences
    send_sms = serializers.BooleanField(default=True)
    send_whatsapp = serializers.BooleanField(default=True)
    
    # Additional data
    notes = serializers.JSONField(required=False, default=dict)
    metadata = serializers.JSONField(required=False, default=dict)
    
    def validate_total_amount(self, value):
        """Validate payment amount"""
        if value <= 0:
            raise serializers.ValidationError("Amount must be greater than zero")
        if value > Decimal('100000'):  # Max 1 lakh
            raise serializers.ValidationError("Amount cannot exceed ₹1,00,000")
        return value
    
    def validate_partner_id(self, value):
        """Validate partner exists"""
        try:
            partner = PartnerProfile.objects.get(id=value)
            if not partner.is_active:
                raise serializers.ValidationError("Partner is not active")
            # Store partner for potential reuse in view using _validated_objects cache
            if not hasattr(self, "_validated_objects") or self._validated_objects is None:
                self._validated_objects = {}
            self._validated_objects["partner"] = partner
        except PartnerProfile.DoesNotExist:
            raise serializers.ValidationError("Partner not found")
        return value

    def validate_precheckin_id(self, value):
        """Validate precheckin exists if provided"""
        if value:
            try:
                precheckin = PreCheckin.objects.get(id=value)
            except PreCheckin.DoesNotExist:
                raise serializers.ValidationError("Precheckin not found")
            # cache for reuse
            if not hasattr(self, "_validated_objects") or self._validated_objects is None:
                self._validated_objects = {}
            self._validated_objects["precheckin"] = precheckin
        return value
    
    def validate_guest_id(self, value):
        """Validate guest exists if provided"""
        if value:
            try:
                guest = Guest.objects.get(id=value)
            except Guest.DoesNotExist:
                raise serializers.ValidationError("Guest not found")
            # cache for reuse
            if not hasattr(self, "_validated_objects") or self._validated_objects is None:
                self._validated_objects = {}
            self._validated_objects["guest"] = guest
        return value
    
    def validate_customer_phone(self, value):
        """Validate customer phone number"""
        # Remove non-numeric characters
        phone_digits = ''.join(filter(str.isdigit, value))
        if len(phone_digits) < 10:
            raise serializers.ValidationError("Phone number must have at least 10 digits")
        elif len(phone_digits) > 15:
            raise serializers.ValidationError("Phone number cannot exceed 15 digits")
        return value
    
    def validate(self, data):
        """Cross-field validation"""
        context = data.get('context')
        precheckin_id = data.get('precheckin_id')
        guest_id = data.get('guest_id')
        
        # Context-specific validation
        if context == PaymentContext.PRECHECKIN and not precheckin_id:
            raise serializers.ValidationError({
                'precheckin_id': 'Precheckin ID is required for precheckin payments'
            })
        
        if context == PaymentContext.CHECKOUT and not guest_id:
            raise serializers.ValidationError({
                'guest_id': 'Guest ID is required for checkout payments'
            })
        
        # Validate customer details
        customer_name = data.get('customer_name')
        customer_email = data.get('customer_email')
        customer_phone = data.get('customer_phone')

        if not customer_name or len(customer_name.strip()) < 2:
            raise serializers.ValidationError({'customer_name': 'Name must be at least 2 characters'})

        if customer_email and '@' not in customer_email:
            raise serializers.ValidationError({'customer_email': 'Invalid email format'})

        
        return data


class PaymentLinkResponseSerializer(serializers.Serializer):
    """Serializer for payment link creation responses"""
    
    # Payment intent details
    payment_intent_id = serializers.UUIDField()
    reference_number = serializers.CharField()
    
    # Razorpay payment link details
    payment_link_id = serializers.CharField()
    payment_link_url = serializers.URLField()
    short_url = serializers.URLField()
    
    # Payment details
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_amount_display = serializers.CharField()
    description = serializers.CharField()
    status = serializers.CharField()
    
    # Timing
    created_at = serializers.DateTimeField()
    expires_at = serializers.DateTimeField()
    
    # Customer details
    customer_name = serializers.CharField()
    customer_email = serializers.CharField(allow_null=True)
    customer_phone = serializers.CharField()
    
    # Notification status
    notifications_sent = serializers.DictField()
    
    # Split preview (for transparency)
    split_preview = serializers.DictField()


class PaymentLinkStatusSerializer(serializers.Serializer):
    """Serializer for payment link status responses"""
    
    payment_link_id = serializers.CharField()
    payment_intent_id = serializers.UUIDField()
    reference_number = serializers.CharField()
    
    # Status information
    status = serializers.ChoiceField(choices=PaymentStatus.choices)
    status_display = serializers.CharField()
    
    # Payment details
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_amount_display = serializers.CharField()
    paid_amount = serializers.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # Timing
    created_at = serializers.DateTimeField()
    expires_at = serializers.DateTimeField()
    paid_at = serializers.DateTimeField(allow_null=True)
    
    # Payment method (if paid)
    payment_method = serializers.CharField(allow_null=True)
    razorpay_payment_id = serializers.CharField(allow_null=True)
    
    # Customer details
    customer_name = serializers.CharField()
    customer_email = serializers.CharField(allow_null=True)
    customer_phone = serializers.CharField()
    
    # Transfer status (if paid)
    transfers_status = serializers.DictField(required=False)


class CancelPaymentLinkSerializer(serializers.Serializer):
    """Serializer for cancelling payment links"""
    
    reason = serializers.CharField(max_length=255, required=False, allow_blank=True)
    notify_customer = serializers.BooleanField(default=False)


class PaymentLinkListSerializer(serializers.Serializer):
    """Serializer for listing payment links"""
    
    payment_link_id = serializers.CharField()
    payment_intent_id = serializers.UUIDField()
    reference_number = serializers.CharField()
    
    # Basic details
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_amount_display = serializers.CharField()
    description = serializers.CharField()
    context = serializers.CharField()
    context_display = serializers.CharField()
    
    # Status
    status = serializers.CharField()
    status_display = serializers.CharField()
    
    # Customer
    customer_name = serializers.CharField()
    customer_phone = serializers.CharField()
    
    # Partner
    partner_name = serializers.CharField()
    
    # Timing
    created_at = serializers.DateTimeField()
    expires_at = serializers.DateTimeField()
    paid_at = serializers.DateTimeField(allow_null=True)
    
    # Quick actions
    can_cancel = serializers.BooleanField()
    can_resend = serializers.BooleanField()


class ResendPaymentLinkSerializer(serializers.Serializer):
    """Serializer for resending payment link notifications"""
    
    send_sms = serializers.BooleanField(default=True)
    send_whatsapp = serializers.BooleanField(default=True)
    custom_message = serializers.CharField(max_length=500, required=False, allow_blank=True)
    
    def validate(self, data):
        """Validate that at least one notification method is selected"""
        if not data.get('send_sms') and not data.get('send_whatsapp'):
            raise serializers.ValidationError(
                'At least one notification method (SMS or WhatsApp) must be selected'
            )
        return data
