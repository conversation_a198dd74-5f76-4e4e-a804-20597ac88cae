from django.contrib.auth import authenticate, logout
from django.http import JsonResponse
from django.core.cache import cache
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from ..utils import TOTPVerification, Messages, MessageTemplates
from core.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework import serializers


class SignupSerializer(serializers.Serializer):
    phone = serializers.CharField()
    name = serializers.CharField()
    password = serializers.Char<PERSON>ield()
    email = serializers.CharField()
    is_partner = serializers.BooleanField()


# Create your views here.
@csrf_exempt
@require_POST
def login_view(request):
    try:
        phone = request.POST.get("phone")
        password = request.POST.get("password")
        otp = request.POST.get("otp")
        token_type = request.POST.get("type")
        user = authenticate(request, phone=phone, password=password, otp=otp)
        if user is not None:
            response_data = {
                "success": True,
                "is_partner": user.is_partner,
                "user_id": user.id,
            }
            if token_type and token_type.lower() == "token":
                refresh = RefreshToken.for_user(user)
                response_data.update(
                    {
                        "refresh": str(refresh),
                        "access": str(refresh.access_token),
                    }
                )
            return JsonResponse(response_data)
        else:
            return JsonResponse({"error": "Login Failed"}, status=401)
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


@csrf_exempt
@require_POST
def signup_view(request):
    try:
        serializer = SignupSerializer(data=request.POST)
        if not serializer.is_valid():
            return JsonResponse({"error": serializer.errors}, status=400)
        User.objects.create_user(
            phone=serializer.data.get("phone"),
            name=serializer.data.get("name"),
            email=serializer.data.get("email"),
            password=serializer.data.get("password"),
            partner=serializer.data.get("is_partner"),
        )
        return JsonResponse({"success": True})
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


@csrf_exempt
@require_POST
def logout_view(request):
    try:
        logout(request)
        return JsonResponse({"success": True})
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


@csrf_exempt
@require_POST
def generate_otp(request):
    try:
        otp = TOTPVerification().generate_token()
        phone = request.POST.get("phone")
        if phone == "9999999999":
            return generate_otp_dev(request)
        msg = Messages(phone)
        User.objects.get(phone=phone)
        if cache.set(phone, otp, 500):
            msg.send_bulk_sms(MessageTemplates.OTP.value, otp)
            return JsonResponse({"success": True})
        else:
            return JsonResponse({"error": "OTP setting failed"}, status=400)
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


@csrf_exempt
@require_POST
def generate_otp_dev(request):
    try:
        otp = "123456"
        phone = request.POST.get("phone")
        User.objects.get(phone=phone)
        if cache.set(phone, otp, 500):
            return JsonResponse({"success": True})
        else:
            return JsonResponse({"error": "OTP setting failed"}, status=400)
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


@csrf_exempt
@require_POST
def test_view(request):
    try:
        return JsonResponse({"success": True}, status=200)
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)
