# Generated by Django 4.2.7 on 2025-10-02 05:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('stay', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0002_initial'),
        ('notification', '0001_initial'),
        ('service', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='weeklyreport',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='weekly_reports', to='stay.property'),
        ),
        migrations.AddField(
            model_name='usernotificationprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_profile', to='core.userprofile'),
        ),
        migrations.AddField(
            model_name='partnernotificationprofile',
            name='partner',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_profile', to='core.partnerprofile'),
        ),
        migrations.AddField(
            model_name='onboardingstatus',
            name='partner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='onboarding_statuses', to='core.partnerprofile'),
        ),
        migrations.AddField(
            model_name='onboardingstatus',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='onboarding_statuses', to='stay.property'),
        ),
        migrations.AddField(
            model_name='notificationsubscription',
            name='notification',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to='notification.notification'),
        ),
        migrations.AddField(
            model_name='notificationsubscription',
            name='partner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.partnerprofile'),
        ),
        migrations.AddField(
            model_name='notificationsubscription',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.userprofile'),
        ),
        migrations.AddField(
            model_name='notificationlog',
            name='notification',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='notification.notification'),
        ),
        migrations.AddField(
            model_name='notificationlog',
            name='partner_notification_profile',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='notification.partnernotificationprofile'),
        ),
        migrations.AddField(
            model_name='notificationlog',
            name='service_partner',
            field=models.ForeignKey(blank=True, help_text='For notifications sent to service partners', null=True, on_delete=django.db.models.deletion.CASCADE, to='service.servicepartner'),
        ),
        migrations.AddField(
            model_name='notificationlog',
            name='user_notification_profile',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='notification.usernotificationprofile'),
        ),
        migrations.AddField(
            model_name='firebasedevicetoken',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='firebase_device_tokens', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='whatsappcontact',
            unique_together={('user', 'phone_number')},
        ),
        migrations.AlterUniqueTogether(
            name='weeklyreport',
            unique_together={('partner', 'property', 'week_start')},
        ),
        migrations.AlterUniqueTogether(
            name='onboardingstatus',
            unique_together={('partner', 'property')},
        ),
        migrations.AddIndex(
            model_name='notificationlog',
            index=models.Index(fields=['message_id'], name='notificatio_message_4dba6d_idx'),
        ),
        migrations.AddIndex(
            model_name='notificationlog',
            index=models.Index(fields=['message_status'], name='notificatio_message_2d2f82_idx'),
        ),
        migrations.AddIndex(
            model_name='notificationlog',
            index=models.Index(fields=['created_at'], name='notificatio_created_f441eb_idx'),
        ),
    ]
