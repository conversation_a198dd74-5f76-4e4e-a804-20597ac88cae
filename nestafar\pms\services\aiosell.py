"""
AioSell Integration Service for rate restrictions and inventory restrictions.

This service handles communication with AioSell's Rate Restrictions API and
Inventory Restrictions API, providing a comprehensive integration layer between
the PMS and AioSell's channel management platform.
"""

import logging
import requests
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, date, timedelta
from decimal import Decimal
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.conf import settings
from django.core.exceptions import ValidationError

from ..models import HotelOTAIntegration, RoomType, RatePlan, Calendar, RoomBlock
from stay.models import Property, Room

logger = logging.getLogger(__name__)


class AioSellAPIError(Exception):
    """Custom exception for AioSell API errors."""

    pass


class AioSellService:
    """
    Service class for AioSell API integration.

    Handles both rate restrictions and inventory restrictions push APIs
    according to AioSell's API documentation.
    """

    def __init__(self, hotel_integration: HotelOTAIntegration):
        """
        Initialize the AioSell service with hotel integration.

        Args:
            hotel_integration: HotelOTAIntegration instance for AioSell
        """
        self.integration = hotel_integration
        self.hotel = hotel_integration.hotel
        self.credentials = hotel_integration.credentials
        self.base_url = "https://live.aiosell.com/api/v2/cm"

        # Validate required credentials
        if not self.credentials.get("identifier"):
            raise ValidationError("AioSell API token is required")

        # Get hotel-specific configuration
        self.config = self.hotel.get_channel_manager_config("aiosell") or {}

    def _get_headers(self) -> Dict[str, str]:
        """Get HTTP headers for AioSell API requests."""
        headers = {
            "Content-Type": "application/json",
            # 'Authorization': f"Bearer {self.credentials.get('identifier')}",
        }

        # # Add API key if provided
        # if self.credentials.get('api_key'):
        #     headers['X-API-Key'] = self.credentials.get('identifier')

        return headers

    def _get_hotel_code(self) -> str:
        """Get the hotel code for AioSell API requests."""
        hotel_code = (
            self.integration.external_hotel_id
            or self.config.get("hotel_code")
            or (
                self.hotel.get_shortened_id()
                if hasattr(self.hotel, "get_shortened_id")
                else str(self.hotel.id)
            )
        )

        if not hotel_code:
            raise ValidationError(
                "Hotel code could not be resolved for AioSell integration"
            )

        return hotel_code

    def _get_identifier(self) -> str:
        """Get the API identifier (token or API key) for AioSell API requests."""
        return self.credentials.get("identifier") or self.credentials.get("api_key")

    def _get_target_channels(self) -> List[str]:
        """Get the target channels for distribution."""
        return self.config.get("target_channels", ["agoda", "booking.com"])

    def _get_room_code_mapping(
        self, room: Room = None, room_type: RoomType = None
    ) -> str:
        """
        Get AioSell room code for a room or room type.

        Args:
            room: Room instance (optional)
            room_type: RoomType instance (optional)

        Returns:
            AioSell room code string
        """
        room_mapping = self.config.get("room_mapping", {})

        # Try room-specific mapping first
        if room and room.room_no:
            mapped_code = room_mapping.get(room.room_no)
            if mapped_code:
                return mapped_code

        # Try room type mapping
        if room_type and room_type.name:
            mapped_code = room_mapping.get(room_type.name)
            if mapped_code:
                return mapped_code

        # Default mapping based on room number or room type name
        if room and room.room_no:
            return room.room_no.upper().replace(" ", "_")
        elif room_type and room_type.name:
            return room_type.name.upper().replace(" ", "_")
        else:
            raise ValidationError(
                "Cannot determine room code - neither room nor room_type provided valid identifiers"
            )

    def _get_rate_plan_codes(
        self, room_code: str, rate_plan: RatePlan = None
    ) -> List[str]:
        """
        Get AioSell rate plan codes for a room.

        Args:
            room_code: AioSell room code
            rate_plan: RatePlan instance (optional)

        Returns:
            List of rate plan codes
        """
        rate_plans_config = self.config.get("rate_plans", {})

        # Try to get configured rate plans for this room
        if room_code in rate_plans_config:
            return rate_plans_config[room_code]

        # Try to get from specific rate plan
        if rate_plan:
            # Use rate plan name as base for code
            rate_plan_code = f"{room_code}-{rate_plan.name.upper().replace(' ', '_')}"
            return [rate_plan_code]

        # Default rate plan codes
        return [f"{room_code}-S-101", f"{room_code}-D-101"]

    def _make_api_request(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make an HTTP request to AioSell API.

        Args:
            endpoint: API endpoint (without base URL)
            data: Request payload

        Returns:
            Response data dictionary

        Raises:
            AioSellAPIError: If API request fails
        """
        url = f"{self.base_url}/{endpoint}"
        headers = self._get_headers()

        try:
            logger.info(f"Making AioSell API request to {url}")
            logger.debug(f"Request data: {json.dumps(data, indent=2)}")

            response = requests.post(url, json=data, headers=headers, timeout=30)

            # Try to parse JSON response
            try:
                response_data = response.json()
            except json.JSONDecodeError:
                response_data = {"raw_response": response.text}

            logger.debug(f"Response status: {response.status_code}")
            logger.debug(f"Response data: {json.dumps(response_data, indent=2)}")

            if response.status_code == 200 and response_data.get("success", False):
                return response_data
            else:
                error_msg = response_data.get("message", f"HTTP {response.status_code}")
                raise AioSellAPIError(f"AioSell API error: {error_msg}")

        except requests.exceptions.RequestException as e:
            logger.error(f"Network error in AioSell API request: {str(e)}")
            raise AioSellAPIError(f"Network error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error in AioSell API request: {str(e)}")
            raise AioSellAPIError(f"Unexpected error: {str(e)}")

    def push_rate_restrictions(
        self,
        room_type: RoomType,
        rate_plan: RatePlan,
        start_date: date,
        end_date: date,
        restrictions: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Push rate restrictions to AioSell.

        Args:
            room_type: RoomType instance
            rate_plan: RatePlan instance
            start_date: Start date for restrictions
            end_date: End date for restrictions
            restrictions: Dictionary containing restriction values

        Returns:
            API response data
        """
        room_code = self._get_room_code_mapping(room_type=room_type)
        rate_plan_codes = self._get_rate_plan_codes(room_code, rate_plan)

        # Build rates array for each rate plan
        rates = []
        for rate_plan_code in rate_plan_codes:
            rates.append(
                {
                    "roomCode": room_code,
                    "rateplanCode": rate_plan_code,
                    "restrictions": {
                        "stopSell": restrictions.get("stop_sell", False),
                        "exactStayArrival": restrictions.get("exact_stay_arrival"),
                        "maximumStayArrival": restrictions.get("maximum_stay_arrival"),
                        "minimumAdvanceReservation": restrictions.get(
                            "minimum_advance_reservation"
                        ),
                        "minimumStay": restrictions.get("minimum_stay", 1),
                        "closeOnArrival": restrictions.get("close_on_arrival", False),
                        "maximumStay": restrictions.get("maximum_stay"),
                        "maximumAdvanceReservation": restrictions.get(
                            "maximum_advance_reservation"
                        ),
                        "closeOnDeparture": restrictions.get(
                            "close_on_departure", False
                        ),
                    },
                }
            )

        # Build request payload
        payload = {
            "hotelCode": self._get_hotel_code(),
            "toChannels": self._get_target_channels(),
            "updates": [
                {
                    "startDate": start_date.isoformat(),
                    "endDate": end_date.isoformat(),
                    "rates": rates,
                }
            ],
        }

        # Make API request
        endpoint = f"update-rates/{self._get_identifier()}"
        return self._make_api_request(endpoint, payload)

    def push_inventory_restrictions(
        self,
        room_type: RoomType,
        start_date: date,
        end_date: date,
        restrictions: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Push inventory restrictions to AioSell.

        Args:
            room_type: RoomType instance
            start_date: Start date for restrictions
            end_date: End date for restrictions
            restrictions: Dictionary containing restriction values

        Returns:
            API response data
        """
        room_code = self._get_room_code_mapping(room_type=room_type)

        # Build request payload
        payload = {
            "hotelCode": self._get_hotel_code(),
            "toChannels": self._get_target_channels(),
            "updates": [
                {
                    "startDate": start_date.isoformat(),
                    "endDate": end_date.isoformat(),
                    "rooms": [
                        {
                            "roomCode": room_code,
                            "restrictions": {
                                "stopSell": restrictions.get("stop_sell", False),
                                "exactStayArrival": restrictions.get(
                                    "exact_stay_arrival"
                                ),
                                "maximumStayArrival": restrictions.get(
                                    "maximum_stay_arrival"
                                ),
                                "minimumAdvanceReservation": restrictions.get(
                                    "minimum_advance_reservation"
                                ),
                                "minimumStay": restrictions.get("minimum_stay", 1),
                                "closeOnArrival": restrictions.get(
                                    "close_on_arrival", False
                                ),
                                "minimumStayArrival": restrictions.get(
                                    "minimum_stay_arrival"
                                ),
                                "maximumStay": restrictions.get("maximum_stay"),
                                "maximumAdvanceReservation": restrictions.get(
                                    "maximum_advance_reservation"
                                ),
                                "closeOnDeparture": restrictions.get(
                                    "close_on_departure", False
                                ),
                            },
                        }
                    ],
                }
            ],
        }

        # Make API request
        endpoint = f"update/{self._get_identifier()}"
        return self._make_api_request(endpoint, payload)

    def push_inventory_update(
        self,
        room_inventories: List[Dict[str, Any]],
        start_date: date,
        end_date: date,
    ) -> Dict[str, Any]:
        """
        Push direct inventory updates to AioSell using the inventory push API.
        
        This method follows the AioSell API documentation for inventory updates:
        POST https://live.aiosell.com/api/v2/cm/update/{identifier}

        Args:
            room_inventories: List of room inventory data with format:
                [{"room_code": "SUITE", "available": 3}, ...]
            start_date: Start date for inventory update
            end_date: End date for inventory update

        Returns:
            API response data

        Raises:
            AioSellAPIError: If API request fails
        """
        logger.info(f"Pushing inventory update to AioSell for {len(room_inventories)} room types from {start_date} to {end_date}")
        
        # Validate room inventories
        if not room_inventories:
            raise ValidationError("At least one room inventory must be provided")
        
        # Validate and format room inventory data
        validated_rooms = []
        for room_inv in room_inventories:
            if not isinstance(room_inv.get("available"), int) or room_inv.get("available") < 0:
                raise ValidationError(f"Invalid available count for room {room_inv.get('room_code', 'unknown')}: {room_inv.get('available')}")
            
            if not room_inv.get("room_code"):
                raise ValidationError("Room code is required for inventory update")
                
            validated_rooms.append({
                "available": room_inv["available"],
                "roomCode": room_inv["room_code"]
            })

        # Build request payload according to AioSell API specification
        payload = {
            "hotelCode": self._get_hotel_code(),
            "updates": [
                {
                    "startDate": start_date.isoformat(),
                    "endDate": end_date.isoformat(),
                    "rooms": validated_rooms
                }
            ]
        }

        logger.debug(f"Inventory update payload: {json.dumps(payload, indent=2)}")

        # Make API request using the correct endpoint
        endpoint = f"update/{self._get_identifier()}"
        
        try:
            result = self._make_api_request(endpoint, payload)
            
            # Log successful inventory update
            logger.info(f"Successfully pushed inventory update to AioSell. Updated {len(validated_rooms)} room types.")
            for room in validated_rooms:
                logger.debug(f"Room {room['roomCode']}: {room['available']} available")
                
            return result
            
        except AioSellAPIError as e:
            logger.error(f"Failed to push inventory update to AioSell: {str(e)}")
            logger.error(f"Failed payload: {json.dumps(payload, indent=2)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during inventory update: {str(e)}")
            raise AioSellAPIError(f"Unexpected error during inventory update: {str(e)}")

    def bulk_inventory_update(
        self,
        room_type_ids: List[str],
        start_date: date,
        end_date: date,
        date_specific_inventory: Dict[str, Dict[str, int]] = None
    ) -> Dict[str, Any]:
        """
        Bulk update inventory for multiple room types across a date range.
        
        Args:
            room_type_ids: List of RoomType UUIDs to update
            start_date: Start date for inventory update
            end_date: End date for inventory update
            date_specific_inventory: Optional dict with date-specific inventory
                Format: {"2023-01-24": {"room_type_id": available_count}}

        Returns:
            API response data
        """
        logger.info(f"Starting bulk inventory update for {len(room_type_ids)} room types")
        
        # Get room types and build inventory data
        room_types = RoomType.objects.filter(
            id__in=room_type_ids,
            hotel=self.hotel
        )
        
        if not room_types.exists():
            raise ValidationError("No valid room types found for the hotel")
            
        room_inventories = []
        for room_type in room_types:
            # Get available count for each room type
            available_count = room_type.rooms.filter(
                occupied=False,
                checked_in=False
            ).count()
            
            # Override with date-specific inventory if provided
            if date_specific_inventory:
                current_date = start_date
                while current_date <= end_date:
                    date_str = current_date.isoformat()
                    if date_str in date_specific_inventory and str(room_type.id) in date_specific_inventory[date_str]:
                        available_count = date_specific_inventory[date_str][str(room_type.id)]
                        break
                    current_date += timedelta(days=1)
            
            room_code = self._get_room_code_mapping(room_type=room_type)
            room_inventories.append({
                "room_code": room_code,
                "available": available_count
            })
            
            logger.debug(f"Room type {room_type.name} ({room_code}): {available_count} available")
        
        return self.push_inventory_update(room_inventories, start_date, end_date)

    def sync_room_block(
        self, room_block: RoomBlock, action: str = "create"
    ) -> Dict[str, Any]:
        """
        Sync a room block to AioSell using inventory restrictions.

        Args:
            room_block: RoomBlock instance
            action: Action type ('create', 'update', 'delete')

        Returns:
            API response data
        """
        if not room_block.room:
            raise ValidationError("Room block must have an associated room")

        # Resolve room type using the explicit Room.type_of_room field (Room model verified).
        room_type = getattr(room_block.room, "type_of_room", None)
        if not room_type:
            raise ValidationError(
                f"Cannot determine room type (type_of_room) for room id={room_block.room.id} number={room_block.room.room_no}. "
                "Set the room's type_of_room before syncing blocks."
            )
        # Convert datetime to date
        start_date = timezone.localtime(room_block.blocked_from).date()
        end_date = (
            timezone.localtime(room_block.blocked_until).date()
            if room_block.blocked_until
            else start_date
        )

        # Determine stop sell based on action and block status
        stop_sell = True
        if action == "delete" or not room_block.is_active:
            stop_sell = False

        restrictions = {
            "stop_sell": stop_sell,
            "minimum_stay": 1,
            "close_on_arrival": False,
            "close_on_departure": False,
        }

        return self.push_inventory_restrictions(
            room_type=room_type,
            start_date=start_date,
            end_date=end_date,
            restrictions=restrictions,
        )

    def sync_calendar_entry(self, calendar_entry: Calendar) -> Dict[str, Any]:
        """
        Sync a calendar entry to AioSell using rate restrictions.

        Args:
            calendar_entry: Calendar instance

        Returns:
            API response data
        """
        # Extract restrictions from calendar entry
        entry_restrictions = calendar_entry.restrictions or {}

        restrictions = {
            "stop_sell": entry_restrictions.get("stopSell", False),
            "minimum_stay": entry_restrictions.get("minimumStay", 1),
            "maximum_stay": entry_restrictions.get("maximumStay"),
            "close_on_arrival": entry_restrictions.get("closeOnArrival", False),
            "close_on_departure": entry_restrictions.get("closeOnDeparture", False),
            "minimum_advance_reservation": entry_restrictions.get(
                "minimumAdvanceReservation"
            ),
            "maximum_advance_reservation": entry_restrictions.get(
                "maximumAdvanceReservation"
            ),
        }

        return self.push_rate_restrictions(
            room_type=calendar_entry.room_type,
            rate_plan=calendar_entry.rate_plan,
            start_date=calendar_entry.date,
            end_date=calendar_entry.date,
            restrictions=restrictions,
        )

    def sync_rate_plan(
        self, rate_plan: RatePlan, action: str = "create"
    ) -> Dict[str, Any]:
        """
        Sync an entire rate plan date range to AioSell at once.

        This method syncs the rate plan's full valid_from to valid_to date range
        in a single API call, eliminating race conditions and matching AioSell's
        API design for bulk updates.

        Args:
            rate_plan: RatePlan instance
            action: Action type ('create', 'update', 'delete')

        Returns:
            API response data
        """
        if not rate_plan.is_active and action != "delete":
            logger.debug(f"Skipping inactive rate plan {rate_plan.id}")
            return {"status": "skipped", "reason": "inactive"}

        # Get room code for this rate plan's room type
        room_code = self._get_room_code_mapping(room_type=rate_plan.room_type)
        if not room_code:
            raise ValidationError(
                f"No room code mapping found for room type {rate_plan.room_type.name}"
            )

        # Build restrictions based on rate plan properties
        restrictions = {
            "stopSell": not rate_plan.is_active if action != "delete" else True,
            "minimumStay": rate_plan.min_stay_days,
            "closeOnArrival": False,
            "closeOnDeparture": False,
            "exactStayArrival": None,
            "maximumStayArrival": None,
            "minimumAdvanceReservation": None,
            "maximumStay": None,
            "maximumAdvanceReservation": None,
        }

        # Use rate plan's date range
        start_date = rate_plan.valid_from
        end_date = rate_plan.valid_to

        # Ensure we don't sync past dates
        today = timezone.now().date()
        if start_date < today:
            start_date = today

        if end_date < today:
            logger.warning(
                f"Rate plan {rate_plan.id} end date is in the past, skipping sync"
            )
            return {"status": "skipped", "reason": "past_dates"}

        logger.info(
            f"Syncing rate plan {rate_plan.name} range {start_date} to {end_date} "
            f"for room {room_code} (action: {action})"
        )

        return self.push_rate_restrictions(
            room_type=rate_plan.room_type,
            rate_plan=rate_plan,
            start_date=start_date,
            end_date=end_date,
            restrictions=restrictions,
        )

    def bulk_sync_calendar_entries(
        self, calendar_entries: List[Calendar]
    ) -> List[Dict[str, Any]]:
        """
        Bulk sync multiple calendar entries to AioSell.

        Args:
            calendar_entries: List of Calendar instances

        Returns:
            List of API response data
        """
        results = []

        # Group entries by room type and rate plan for efficient batching
        grouped_entries = {}
        for entry in calendar_entries:
            key = (entry.room_type.id, entry.rate_plan.id)
            if key not in grouped_entries:
                grouped_entries[key] = []
            grouped_entries[key].append(entry)

        # Process each group
        for (room_type_id, rate_plan_id), entries in grouped_entries.items():
            try:
                # Get room type and rate plan
                room_type = RoomType.objects.get(id=room_type_id)
                rate_plan = RatePlan.objects.get(id=rate_plan_id)

                # Group entries by their restriction sets deterministically
                def normalize_restrictions(raw: Optional[Dict[str, Any]]):
                    raw = raw or {}
                    # Only keep known keys to avoid noise; default values inserted where needed
                    keys = [
                        "stopSell",
                        "minimumStay",
                        "maximumStay",
                        "closeOnArrival",
                        "closeOnDeparture",
                        "minimumAdvanceReservation",
                        "maximumAdvanceReservation",
                    ]
                    norm = {k: raw.get(k) for k in keys if k in raw}
                    return norm

                restriction_groups: Dict[str, Dict[str, Any]] = {}
                entries_by_restriction: Dict[str, List[Calendar]] = {}
                for entry in entries:
                    norm = normalize_restrictions(entry.restrictions)
                    # Create a stable key (sorted items) so identical sets cluster
                    key = json.dumps(sorted(norm.items()))
                    restriction_groups[key] = norm
                    entries_by_restriction.setdefault(key, []).append(entry)

                # Process each restriction cluster separately
                for r_key, clustered_entries in entries_by_restriction.items():
                    clustered_dates = sorted(e.date for e in clustered_entries)
                    # Split into consecutive ranges
                    consecutive_ranges: List[Tuple[date, date]] = []
                    range_start = prev = clustered_dates[0]
                    for dt in clustered_dates[1:]:
                        if dt == prev + timedelta(days=1):
                            prev = dt
                            continue
                        # close previous range
                        consecutive_ranges.append((range_start, prev))
                        range_start = prev = dt
                    consecutive_ranges.append((range_start, prev))

                    entry_restrictions = restriction_groups[r_key]
                    # Build internal restrictions dict expected by push_rate_restrictions
                    base_restrictions = {
                        "stop_sell": entry_restrictions.get("stopSell", False),
                        "minimum_stay": entry_restrictions.get("minimumStay", 1),
                        "maximum_stay": entry_restrictions.get("maximumStay"),
                        "close_on_arrival": entry_restrictions.get(
                            "closeOnArrival", False
                        ),
                        "close_on_departure": entry_restrictions.get(
                            "closeOnDeparture", False
                        ),
                        "minimum_advance_reservation": entry_restrictions.get(
                            "minimumAdvanceReservation"
                        ),
                        "maximum_advance_reservation": entry_restrictions.get(
                            "maximumAdvanceReservation"
                        ),
                    }

                    for r_start, r_end in consecutive_ranges:
                        result = self.push_rate_restrictions(
                            room_type=room_type,
                            rate_plan=rate_plan,
                            start_date=r_start,
                            end_date=r_end,
                            restrictions=base_restrictions,
                        )
                        results.append(
                            {
                                "room_type_id": room_type_id,
                                "rate_plan_id": rate_plan_id,
                                "date_range": f"{r_start} to {r_end}",
                                "restrictions_hash": r_key,
                                "result": result,
                            }
                        )

            except Exception as e:
                logger.error(
                    f"Error syncing calendar entries for room type {room_type_id}, "
                    f"rate plan {rate_plan_id}: {str(e)}"
                )
                results.append(
                    {
                        "room_type_id": room_type_id,
                        "rate_plan_id": rate_plan_id,
                        "error": str(e),
                    }
                )

        return results

    def push_rates_update(
        self,
        rate_updates: List[Dict[str, Any]],
        start_date: date,
        end_date: date,
        to_channels: List[str] = None,
    ) -> Dict[str, Any]:
        """
        Push room rates and rate plans to AioSell.

        This method implements the Rates Push API as specified in the AioSell documentation:
        POST https://live.aiosell.com/api/v2/cm/update-rates/sample-pms

        Args:
            rate_updates: List of rate update dictionaries with format:
                [{"room_code": "EXECUTIVE", "rate": 1749.0, "rateplan_code": "EXECUTIVE-S-101"}, ...]
            start_date: Start date for rate updates
            end_date: End date for rate updates
            to_channels: Optional list of target channels (defaults to configured channels)

        Returns:
            API response data

        Raises:
            AioSellAPIError: If API request fails
        """
        logger.info(f"Pushing rates update to AioSell for {len(rate_updates)} rate entries from {start_date} to {end_date}")

        # Validate rate updates
        if not rate_updates:
            raise ValidationError("At least one rate update must be provided")

        # Validate and format rate data
        validated_rates = []
        for rate_update in rate_updates:
            if not isinstance(rate_update.get("rate"), (int, float, Decimal)) or rate_update.get("rate") <= 0:
                raise ValidationError(f"Invalid rate for room {rate_update.get('room_code', 'unknown')}: {rate_update.get('rate')}")

            if not rate_update.get("room_code"):
                raise ValidationError("Room code is required for rate update")

            if not rate_update.get("rateplan_code"):
                raise ValidationError("Rate plan code is required for rate update")

            validated_rates.append({
                "roomCode": rate_update["room_code"],
                "rate": float(rate_update["rate"]),
                "rateplanCode": rate_update["rateplan_code"]
            })

        # Build request payload according to AioSell API specification
        payload = {
            "hotelCode": self._get_hotel_code(),
            "updates": [
                {
                    "startDate": start_date.isoformat(),
                    "endDate": end_date.isoformat(),
                    "rates": validated_rates
                }
            ]
        }

        # Add toChannels if specified
        if to_channels:
            payload["toChannels"] = to_channels

        logger.debug(f"Rates update payload: {json.dumps(payload, indent=2)}")

        # Make API request using the correct endpoint
        endpoint = f"update-rates/{self._get_identifier()}"

        try:
            result = self._make_api_request(endpoint, payload)

            # Log successful rates update
            logger.info(f"Successfully pushed rates update to AioSell. Updated {len(validated_rates)} rate entries.")
            for rate in validated_rates:
                logger.debug(f"Room {rate['roomCode']} - Rate Plan {rate['rateplanCode']}: {rate['rate']}")

            return result

        except AioSellAPIError as e:
            logger.error(f"Failed to push rates update to AioSell: {str(e)}")
            logger.error(f"Failed payload: {json.dumps(payload, indent=2)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during rates update: {str(e)}")
            raise AioSellAPIError(f"Unexpected error during rates update: {str(e)}")

    def push_inventory_restrictions_update(
        self,
        room_restrictions: List[Dict[str, Any]],
        start_date: date,
        end_date: date,
        to_channels: List[str] = None,
    ) -> Dict[str, Any]:
        """
        Push room-specific booking restrictions to AioSell.

        This method implements the Inventory Restrictions Push API as specified:
        POST https://live.aiosell.com/api/v2/cm/update/sample-pms

        Args:
            room_restrictions: List of room restriction dictionaries with format:
                [{"room_code": "SUITE", "restrictions": {...}}, ...]
            start_date: Start date for restrictions
            end_date: End date for restrictions
            to_channels: Optional list of target channels (defaults to configured channels)

        Returns:
            API response data

        Raises:
            AioSellAPIError: If API request fails
        """
        logger.info(f"Pushing inventory restrictions to AioSell for {len(room_restrictions)} rooms from {start_date} to {end_date}")

        # Validate room restrictions
        if not room_restrictions:
            raise ValidationError("At least one room restriction must be provided")

        # Validate and format room restriction data
        validated_rooms = []
        for room_restriction in room_restrictions:
            if not room_restriction.get("room_code"):
                raise ValidationError("Room code is required for inventory restriction")

            restrictions = room_restriction.get("restrictions", {})

            # Build AioSell restrictions format
            aiosell_restrictions = {
                "stopSell": restrictions.get("stop_sell", False),
                "exactStayArrival": restrictions.get("exact_stay_arrival"),
                "maximumStayArrival": restrictions.get("maximum_stay_arrival"),
                "minimumAdvanceReservation": restrictions.get("minimum_advance_reservation"),
                "minimumStay": restrictions.get("minimum_stay", 1),
                "closeOnArrival": restrictions.get("close_on_arrival", False),
                "minimumStayArrival": restrictions.get("minimum_stay_arrival"),
                "maximumStay": restrictions.get("maximum_stay"),
                "maximumAdvanceReservation": restrictions.get("maximum_advance_reservation"),
                "closeOnDeparture": restrictions.get("close_on_departure", False),
            }

            # Remove None values
            aiosell_restrictions = {k: v for k, v in aiosell_restrictions.items() if v is not None}

            validated_rooms.append({
                "roomCode": room_restriction["room_code"],
                "restrictions": aiosell_restrictions
            })

        # Build request payload according to AioSell API specification
        payload = {
            "hotelCode": self._get_hotel_code(),
            "updates": [
                {
                    "startDate": start_date.isoformat(),
                    "endDate": end_date.isoformat(),
                    "rooms": validated_rooms
                }
            ]
        }

        # Add toChannels if specified
        if to_channels:
            payload["toChannels"] = to_channels

        logger.debug(f"Inventory restrictions payload: {json.dumps(payload, indent=2)}")

        # Make API request using the correct endpoint
        endpoint = f"update/{self._get_identifier()}"

        try:
            result = self._make_api_request(endpoint, payload)

            # Log successful inventory restrictions update
            logger.info(f"Successfully pushed inventory restrictions to AioSell. Updated {len(validated_rooms)} rooms.")
            for room in validated_rooms:
                logger.debug(f"Room {room['roomCode']}: {room['restrictions']}")

            return result

        except AioSellAPIError as e:
            logger.error(f"Failed to push inventory restrictions to AioSell: {str(e)}")
            logger.error(f"Failed payload: {json.dumps(payload, indent=2)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during inventory restrictions update: {str(e)}")
            raise AioSellAPIError(f"Unexpected error during inventory restrictions update: {str(e)}")

    def push_rate_restrictions_update(
        self,
        rate_restrictions: List[Dict[str, Any]],
        start_date: date,
        end_date: date,
        to_channels: List[str] = None,
    ) -> Dict[str, Any]:
        """
        Push rate-specific booking restrictions to AioSell.

        This method implements the Rate Restrictions Push API as specified:
        POST https://live.aiosell.com/api/v2/cm/update-rates/sample-pms

        Args:
            rate_restrictions: List of rate restriction dictionaries with format:
                [{"room_code": "EXECUTIVE", "rateplan_code": "EXECUTIVE-S-101", "restrictions": {...}}, ...]
            start_date: Start date for restrictions
            end_date: End date for restrictions
            to_channels: Optional list of target channels (defaults to configured channels)

        Returns:
            API response data

        Raises:
            AioSellAPIError: If API request fails
        """
        logger.info(f"Pushing rate restrictions to AioSell for {len(rate_restrictions)} rate plans from {start_date} to {end_date}")

        # Validate rate restrictions
        if not rate_restrictions:
            raise ValidationError("At least one rate restriction must be provided")

        # Validate and format rate restriction data
        validated_rates = []
        for rate_restriction in rate_restrictions:
            if not rate_restriction.get("room_code"):
                raise ValidationError("Room code is required for rate restriction")

            if not rate_restriction.get("rateplan_code"):
                raise ValidationError("Rate plan code is required for rate restriction")

            restrictions = rate_restriction.get("restrictions", {})

            # Build AioSell restrictions format
            aiosell_restrictions = {
                "stopSell": restrictions.get("stop_sell", False),
                "exactStayArrival": restrictions.get("exact_stay_arrival"),
                "maximumStayArrival": restrictions.get("maximum_stay_arrival"),
                "minimumAdvanceReservation": restrictions.get("minimum_advance_reservation"),
                "minimumStay": restrictions.get("minimum_stay", 1),
                "closeOnArrival": restrictions.get("close_on_arrival", False),
                "maximumStay": restrictions.get("maximum_stay"),
                "maximumAdvanceReservation": restrictions.get("maximum_advance_reservation"),
                "closeOnDeparture": restrictions.get("close_on_departure", False),
            }

            # Remove None values
            aiosell_restrictions = {k: v for k, v in aiosell_restrictions.items() if v is not None}

            validated_rates.append({
                "roomCode": rate_restriction["room_code"],
                "rateplanCode": rate_restriction["rateplan_code"],
                "restrictions": aiosell_restrictions
            })

        # Build request payload according to AioSell API specification
        payload = {
            "hotelCode": self._get_hotel_code(),
            "updates": [
                {
                    "startDate": start_date.isoformat(),
                    "endDate": end_date.isoformat(),
                    "rates": validated_rates
                }
            ]
        }

        # Add toChannels if specified
        if to_channels:
            payload["toChannels"] = to_channels

        logger.debug(f"Rate restrictions payload: {json.dumps(payload, indent=2)}")

        # Make API request using the correct endpoint
        endpoint = f"update-rates/{self._get_identifier()}"

        try:
            result = self._make_api_request(endpoint, payload)

            # Log successful rate restrictions update
            logger.info(f"Successfully pushed rate restrictions to AioSell. Updated {len(validated_rates)} rate plans.")
            for rate in validated_rates:
                logger.debug(f"Room {rate['roomCode']} - Rate Plan {rate['rateplanCode']}: {rate['restrictions']}")

            return result

        except AioSellAPIError as e:
            logger.error(f"Failed to push rate restrictions to AioSell: {str(e)}")
            logger.error(f"Failed payload: {json.dumps(payload, indent=2)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during rate restrictions update: {str(e)}")
            raise AioSellAPIError(f"Unexpected error during rate restrictions update: {str(e)}")

    def test_connection(self) -> Dict[str, Any]:
        """
        Test the connection to AioSell API.

        Returns:
            Dictionary with test results
        """
        try:
            # Create a minimal test payload
            test_payload = {
                "hotelCode": self._get_hotel_code(),
                "toChannels": ["test"],
                "updates": [],
            }

            # Try both endpoints to test connectivity
            results = {}

            for endpoint_type, endpoint in [
                ("rate_restrictions", f"update-rates/{self._get_identifier()}"),
                ("inventory_restrictions", f"update/{self._get_identifier()}"),
            ]:
                try:
                    headers = self._get_headers()
                    url = f"{self.base_url}/{endpoint}"

                    response = requests.post(
                        url, json=test_payload, headers=headers, timeout=10
                    )

                    results[endpoint_type] = {
                        "status_code": response.status_code,
                        "accessible": response.status_code
                        in [200, 400],  # 400 is OK for empty payload
                        "response_time": response.elapsed.total_seconds(),
                    }

                except Exception as e:
                    results[endpoint_type] = {"accessible": False, "error": str(e)}

            return {
                "success": True,
                "hotel_code": self._get_hotel_code(),
                "endpoints": results,
            }

        except Exception as e:
            return {"success": False, "error": str(e)}


def get_aiosell_service(hotel: Property) -> Optional[AioSellService]:
    """
    Get AioSell service instance for a hotel.

    Args:
        hotel: Property instance

    Returns:
        AioSellService instance or None if not configured
    """
    try:
        integration = HotelOTAIntegration.objects.get(
            hotel=hotel, ota_platform__name="aiosell", is_active=True
        )
        return AioSellService(integration)
    except HotelOTAIntegration.DoesNotExist:
        logger.warning(f"No active AioSell integration found for hotel {hotel.id}")
        return None
    except Exception as e:
        logger.error(f"Error getting AioSell service for hotel {hotel.id}: {str(e)}")
        return None
