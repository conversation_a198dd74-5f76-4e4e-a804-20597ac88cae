from rest_framework.permissions import BasePermission, SAFE_METHODS


class PartnerPermission(BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_partner

    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)


class PropertyPermission(BasePermission):
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated
            and getattr(request, "property", None) is not None
        )

    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)


class ServicePermission(BasePermission):
    def has_permission(self, request, view):
        service_type = view.kwargs.get("service_type", None)
        user = request.user

        if service_type == "all":
            return True
        elif service_type and hasattr(user, "partner_profile"):
            return getattr(user.partner_profile, f"has_{service_type}", False)
        elif service_type and hasattr(request, "property"):
            return request.property.staffs.filter(
                **{f"has_{service_type}": True}
            ).exists()
        else:
            return False

    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)


class RolePermission(BasePermission):
    def has_permission(self, request, view):
        user = request.user
        property_id = view.kwargs.get("pk", None)

        if user.is_authenticated:
            user_permissions = user.get_perms(property_id)
            required_permissions = getattr(view, "permission_required", [])
            # to handle cases where property_id is null in UserRole
            if property_id is None or user_permissions is None:
                return False

            return all(perm in user_permissions for perm in required_permissions)

        return False

    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)


class ReadOnlyViewPermission(BasePermission):
    """
    Permission class for read-only views.
    """

    def has_permission(self, request, view):
        return request.method in SAFE_METHODS

    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)


class FullAccessViewPermission(BasePermission):
    """
    Permission class for views that require full access.
    """

    def has_permission(self, request, view):
        return True

    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)


def check_precheckin_access(user, precheckin) -> bool:
    """
    Determine whether `user` may access the given `precheckin`.

    Rules:
    - Unauthenticated users: no access (return False)
    - Partner users: allowed when the partner's profile is associated with the
      property that owns the precheckin (property.staffs includes partner_profile)
      or when the partner's active_property matches the precheckin property.
    - Regular users: allowed when they are the reservation owner or when they
      appear as a PreCheckinGuest for the given precheckin.

    Returns True when access is allowed, False otherwise. (Do not raise.)
    """

    if not user or not getattr(user, "is_authenticated", False):
        return False

    # Partner access: check partner_profile membership for the property
    if getattr(user, "is_partner", False):
        partner_profile = getattr(user, "partner_profile", None)
        if not partner_profile:
            return False
        try:
            # property.staffs is a ManyToMany to PartnerProfile
            if getattr(precheckin, "property", None) is not None:
                if precheckin.property.staffs.filter(id=partner_profile.id).exists():
                    return True
                if getattr(partner_profile, "active_property", None) and (
                    partner_profile.active_property_id == precheckin.property_id
                ):
                    return True
        except Exception:
            return False

    # Reservation owner (guest who created the reservation)
    try:
        if getattr(precheckin, "reservation", None) is not None:
            if getattr(precheckin.reservation, "user", None) == user:
                return True
    except Exception:
        pass

    # Any guest listed against the precheckin
    try:
        from booking.models import PreCheckinGuest

        if PreCheckinGuest.objects.filter(pre_checkin=precheckin, user=user).exists():
            return True
    except Exception:
        # In case booking app not available or query fails, deny access
        return False

    return False
