from django.contrib import admin
from .models import (
    LaundryService,
    LaundryServiceItem,
    LaundryCart,
    LaundryCartItems,
    LaundryOrder,
    LaundryOrderItem,
)


@admin.register(LaundryService)
class LaundryServiceAdmin(admin.ModelAdmin):
    list_display = ("name", "partner", "created_at", "updated_at")
    list_filter = ("partner", "created_at", "updated_at")
    search_fields = ("name", "partner__name")
    ordering = ("-updated_at",)


@admin.register(LaundryServiceItem)
class LaundryServiceItemAdmin(admin.ModelAdmin):
    list_display = ("name", "price", "service", "created_at", "updated_at")
    list_filter = ("service", "created_at", "updated_at")
    search_fields = ("name", "service__name")
    ordering = ("-updated_at",)


@admin.register(LaundryCart)
class LaundryCartAdmin(admin.ModelAdmin):
    list_display = (
        "guest",
        "status",
        "subtotal",
        "taxes",
        "charges",
        "total",
        "created_at",
        "updated_at",
    )
    list_filter = ("status", "created_at", "updated_at")
    search_fields = ("guest__user__name",)
    ordering = ("-updated_at",)


@admin.register(LaundryCartItems)
class LaundryCartItemsAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "cart",
        "item",
        "quantity",
        "price",
        "created_at",
        "updated_at",
    )
    list_filter = ("cart", "item")
    search_fields = ("name", "cart__guest__user__name", "item__name")
    ordering = ("-updated_at",)


@admin.register(LaundryOrder)
class LaundryOrderAdmin(admin.ModelAdmin):
    list_display = (
        "guest",
        "service",
        "cart",
        "status",
        "subtotal",
        "commissions",
        "taxes",
        "charges",
        "total",
        "created_at",
        "updated_at",
    )
    list_filter = ("status", "service", "guest", "created_at", "updated_at")
    search_fields = ("guest__user__name", "service__name")
    ordering = ("-updated_at",)


@admin.register(LaundryOrderItem)
class LaundryOrderItemAdmin(admin.ModelAdmin):
    list_display = (
        "item",
        "order",
        "quantity",
        "price",
        "rating",
        "created_at",
        "updated_at",
    )
    list_filter = ("order", "item")
    search_fields = ("item__name", "order__guest__user__name", "order__service__name")
    ordering = ("-updated_at",)
