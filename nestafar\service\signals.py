from django.dispatch import receiver
from django.db.models.signals import post_save, pre_save
from .subapps.food.models import FoodOrder
from .subapps.laundry.models import LaundryOrder
from .subapps.rental.models import RentalOrder
from .subapps.transport.models import TransportOrder
from .subapps.shop.models import ShopOrder
from .subapps.tourism.models import TourismOrder
from notification.tasks import send_notification, send_service_partner_notification
from notification.models import NotificationCategory
from django.dispatch import receiver
from service.models import Job
import logging

logger = logging.getLogger(__name__)

# List of order models to listen to
ORDER_MODELS = [
    FoodOrder,
    LaundryOrder,
    RentalOrder,
    TransportOrder,
    ShopOrder,
    TourismOrder,
]

# Store previous status to detect changes
_order_previous_status = {}


@receiver(pre_save, sender=FoodOrder)
@receiver(pre_save, sender=LaundryOrder)
@receiver(pre_save, sender=RentalOrder)
@receiver(pre_save, sender=TransportOrder)
@receiver(pre_save, sender=ShopOrder)
@receiver(pre_save, sender=TourismOrder)
def capture_previous_status(sender, instance, **kwargs):
    """Capture the previous status before saving to detect changes"""
    if instance.pk:  # Only for existing instances
        try:
            # Get the previous instance from database
            previous_instance = sender.objects.get(pk=instance.pk)
            _order_previous_status[f"{sender.__name__}_{instance.pk}"] = (
                previous_instance.status
            )
        except sender.DoesNotExist:
            # Instance doesn't exist yet, so it's being created
            _order_previous_status[f"{sender.__name__}_{instance.pk}"] = None
    else:
        # New instance being created
        _order_previous_status[f"{sender.__name__}_{instance.pk}"] = None


def send_order_notifications(instance, created=False, previous_status=None):
    """Send notifications for order creation and status changes"""
    try:

        if created:
            # Order was just created - notify guest and partner
            if created:
                # Order was just created - notify guest and partner
                if (
                    not instance.guest
                    or not hasattr(instance.guest, "user")
                    or not instance.guest.user
                ):
                    logger.warning(
                        f"Order {instance.id} has no guest or user associated"
                    )
                    return

                # Additional safety check
                username = getattr(instance.guest.user, "name", "Guest")

                guest_data = {
                    "username": instance.guest.user.name,
                    "order_id": str(instance.id),
                }

                send_notification.delay(
                    str(instance.guest.user.id),
                    NotificationCategory.USER_ORDER_PLACED.name,
                    guest_data,
                )

                # Notify service partner if assigned
                if instance.service_partner:
                    partner_data = {
                        "partner_name": instance.service_partner.name,
                        "order_id": str(instance.id),
                        "room_no": (
                            str(instance.guest.room.room_no)
                            if instance.guest.room
                            else "N/A"
                        ),
                        "guest_name": instance.guest.user.name,
                    }
                    send_service_partner_notification.delay(
                        str(instance.service_partner.id),
                        NotificationCategory.PARTNER_ORDER_PLACED.name,
                        partner_data,
                    )

        elif previous_status is not None and previous_status != instance.status:
            # Status changed - notify both guest and partner
            status_to_user_category = {
                instance.OrderStatus.ACCEPTED: NotificationCategory.USER_ORDER_ACCEPTED.name,
                instance.OrderStatus.ONGOING: NotificationCategory.USER_ORDER_ONGOING.name,
                instance.OrderStatus.REJECTED: NotificationCategory.USER_ORDER_REJECTED.name,
                instance.OrderStatus.CANCELLED: NotificationCategory.USER_ORDER_CANCELLED.name,
                instance.OrderStatus.COMPLETED: NotificationCategory.USER_ORDER_COMPLETED.name,
            }

            status_to_partner_category = {
                instance.OrderStatus.ACCEPTED: NotificationCategory.PARTNER_ORDER_ACCEPTED.name,
                instance.OrderStatus.ONGOING: NotificationCategory.PARTNER_ORDER_ONGOING.name,
                instance.OrderStatus.REJECTED: NotificationCategory.PARTNER_ORDER_REJECTED.name,
                instance.OrderStatus.CANCELLED: NotificationCategory.PARTNER_ORDER_CANCELLED.name,
                instance.OrderStatus.COMPLETED: NotificationCategory.PARTNER_ORDER_COMPLETED.name,
            }

            # Notify guest
            if instance.status in status_to_user_category:
                # Base guest data
                base_guest_data = {
                    "username": instance.guest.user.name,
                    "order_id": str(instance.id),
                }
                
                # Add status-specific data
                if instance.status == instance.OrderStatus.ONGOING:
                    guest_data = {
                        **base_guest_data,
                        "vendor_name": instance.service_partner.name,
                        "estimated_time": "30 minutes",
                        "contact_number": "<EMAIL>",
                    }
                elif instance.status == instance.OrderStatus.REJECTED:
                    guest_data = {
                        **base_guest_data,
                        "service_type": instance.__class__.__name__.replace("Order", ""),  # e.g., 'Food', 'Laundry'
                        "rejection_reason": "Unavailable at this time",  # Default reason
                        "refund_amount": f"{instance.total:,.2f}",
                    }
                else:
                    # For other statuses (ACCEPTED, CANCELLED, COMPLETED) use base data
                    guest_data = base_guest_data

                send_notification.delay(
                    str(instance.guest.user.id),
                    status_to_user_category[instance.status],
                    guest_data,
                )

            # Send order confirmation when order is accepted
            if instance.status == instance.OrderStatus.ACCEPTED:
                # Get order details for confirmation
                order_items = "Order items"  # This should be dynamically generated based on order type
                service_type = instance.__class__.__name__.replace(
                    "Order", ""
                )  # e.g., 'Food', 'Laundry'

                confirmation_data = {
                    "guest_name": instance.guest.user.name,
                    "service_type": service_type,
                    "order_id": str(instance.id),
                    "order_items": order_items,
                    "total_amount": f"₹{instance.total:,.2f}",
                    "delivery_time": "30 minutes",  # This should be dynamic based on service type
                }

                send_notification.delay(
                    str(instance.guest.user.id), "ORDER_CONFIRMED", confirmation_data
                )

            # Notify service partner
            if (
                instance.service_partner
                and instance.status in status_to_partner_category
            ):
                # Base partner data
                base_partner_data = {
                    "partner_name": instance.service_partner.name,
                    "order_id": str(instance.id),
                    "room_no": (
                        str(instance.guest.room.room_no)
                        if instance.guest.room
                        else "N/A"
                    ),
                    "guest_name": instance.guest.user.name,
                }
                
                # Add status-specific data
                if instance.status == instance.OrderStatus.ONGOING:
                    partner_data = {
                        **base_partner_data,
                        "estimated_completion": "30 minutes",
                    }
                elif instance.status == instance.OrderStatus.REJECTED:
                    partner_data = {
                        **base_partner_data,
                        "rejection_reason": "Unavailable at this time",
                        "refund_amount": f"{instance.total:,.2f}",
                        "dashboard_link": "https://dashboard.nestafar.com",
                    }
                else:
                    # For other statuses (PLACED, ACCEPTED, CANCELLED, COMPLETED) use base data
                    partner_data = base_partner_data

                send_service_partner_notification.delay(
                    str(instance.service_partner.id),
                    status_to_partner_category[instance.status],
                    partner_data,
                )

        logger.info(
            f"Order notifications sent for order {instance.id} (created: {created}, status change: {previous_status} -> {instance.status})"
        )

    except Exception as e:
        logger.error(f"Error sending order notifications: {str(e)}", exc_info=True)


def update_cart_status_on_rejected(instance):
    """Reset the cart if all orders in it are rejected."""
    instance.remove_from_cart()
    orders = instance.__class__.objects.filter(cart=instance.cart).values_list(
        "status", flat=True
    )
    if all(order == instance.OrderStatus.REJECTED for order in orders):
        instance.cart.reset_cart()
        instance.cart.status = instance.cart.CartStatus.REJECTED
        instance.cart.save()


def update_cart_status_on_accepted(instance):
    """Update cart status to ACCEPTED if all orders are accepted, otherwise PARTIALLY_ACCEPTED."""
    orders = instance.__class__.objects.filter(cart=instance.cart).values_list(
        "status", flat=True
    )
    if all(order == instance.OrderStatus.ACCEPTED for order in orders):
        instance.cart.status = instance.cart.CartStatus.ACCEPTED
    else:
        instance.cart.status = instance.cart.CartStatus.PARTIALLY_ACCEPTED
    instance.cart.save()


def update_cart_status_on_completed(instance):
    """Update cart status to COMPLETED if all orders are completed, rejected, or cancelled."""
    orders = instance.__class__.objects.filter(cart=instance.cart).values_list(
        "status", flat=True
    )
    if all(
        order
        in {
            instance.OrderStatus.COMPLETED,
            instance.OrderStatus.REJECTED,
            instance.OrderStatus.CANCELLED,
        }
        for order in orders
    ):
        instance.cart.status = instance.cart.CartStatus.COMPLETED
        instance.cart.save()


def update_cart_status_on_cancelled(instance):
    """Update cart status to CANCELLED if all orders are cancelled."""
    instance.remove_from_cart()
    orders = instance.__class__.objects.filter(cart=instance.cart).values_list(
        "status", flat=True
    )
    if all(order == instance.OrderStatus.CANCELLED for order in orders):
        instance.cart.status = instance.cart.CartStatus.CANCELLED
        instance.cart.save()


@receiver(post_save, sender=FoodOrder)
@receiver(post_save, sender=LaundryOrder)
@receiver(post_save, sender=RentalOrder)
@receiver(post_save, sender=TransportOrder)
@receiver(post_save, sender=ShopOrder)
@receiver(post_save, sender=TourismOrder)
def update_cart_status(sender, instance, created, **kwargs):
    """Signal to update the cart status based on the order's status and send notifications."""

    # Get the previous status from our cache
    status_key = f"{sender.__name__}_{instance.pk}"
    previous_status = _order_previous_status.get(status_key)

    try:
        # Send notifications
        if created:
            send_order_notifications(instance, created=True)
        elif previous_status is not None and previous_status != instance.status:
            # Status changed, send notifications
            send_order_notifications(
                instance, created=False, previous_status=previous_status
            )
    except Exception as e:
        logger.error(f"Error in notification handling: {str(e)}", exc_info=True)

    # Clean up the cached status
    if status_key in _order_previous_status:
        del _order_previous_status[status_key]

    # Update cart status
    try:
        if instance.status == instance.OrderStatus.PENDING:
            instance.cart.status = instance.cart.CartStatus.ORDERED
            instance.cart.save()

        elif instance.status == instance.OrderStatus.ACCEPTED:
            update_cart_status_on_accepted(instance)

        elif instance.status == instance.OrderStatus.REJECTED:
            update_cart_status_on_rejected(instance)

        elif instance.status == instance.OrderStatus.COMPLETED:
            update_cart_status_on_completed(instance)

        elif instance.status == instance.OrderStatus.CANCELLED:
            update_cart_status_on_cancelled(instance)
    except Exception as e:
        logger.error(f"Error updating cart status: {str(e)}", exc_info=True)


_previous_job_status = {}


@receiver(pre_save, sender=Job)
def capture_previous_job_status(sender, instance, **kwargs):
    if instance.pk:
        try:
            prev = sender.objects.get(pk=instance.pk)
            _previous_job_status[str(instance.pk)] = prev.status
        except sender.DoesNotExist:
            _previous_job_status[str(instance.pk)] = None
    else:
        _previous_job_status[str(instance.pk)] = None


@receiver(post_save, sender=Job)
def job_post_save(sender, instance: Job, created, **kwargs):
    try:
        prev_status = _previous_job_status.pop(str(instance.pk), None)
        if created:
            # Notify assigned staff if any
            if instance.staff and instance.staff.staff and instance.staff.staff.user:
                data = {
                    "staff_name": instance.staff.staff.user.name,
                    "job_id": str(instance.id),
                    "job_type": instance.type,
                    "room": instance.room,
                    "pickup_location": instance.pickup_location,
                    "dropoff_location": instance.dropoff_location,
                    "earnings": (
                        str(instance.earnings) if instance.earnings is not None else ""
                    ),
                }
                event = (
                    "DELIVERY_ASSIGNED"
                    if instance.type == Job.JobType.DELIVERY
                    else "JOB_ASSIGNED"
                )
                send_notification.delay(str(instance.staff.staff.user.id), event, data)
        elif prev_status and prev_status != instance.status:
            # Notify on status change
            if instance.staff and instance.staff.staff and instance.staff.staff.user:
                data = {
                    "staff_name": instance.staff.staff.user.name,
                    "job_id": str(instance.id),
                    "new_status": instance.status,
                }
                send_notification.delay(
                    str(instance.staff.staff.user.id), "JOB_STATUS_CHANGED", data
                )
    except Exception as e:
        logger.error(f"Error handling job notifications: {e}")
