import uuid
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator, MaxValueValidator


class OTAPlatform(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    api_endpoint = models.URLField(blank=True, null=True)
    configuration = models.JSONField(default=dict, blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name


class HotelOTAIntegration(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    hotel = models.ForeignKey(
        "stay.Property", on_delete=models.CASCADE, related_name="ota_integrations"
    )
    ota_platform = models.ForeignKey(
        OTAPlatform, on_delete=models.CASCADE, related_name="hotel_integrations"
    )
    external_hotel_id = models.Char<PERSON><PERSON>(max_length=150, blank=True, null=True)
    credentials = models.J<PERSON><PERSON>ield(default=dict, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.hotel} - {self.ota_platform}"

    class Meta:
        unique_together = ("hotel", "ota_platform")
        db_table = "pms_hotel_ota_integration"


class RoomType(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    hotel = models.ForeignKey(
        "stay.Property", on_delete=models.CASCADE, related_name="room_types"
    )
    ota_code = models.CharField(max_length=100, blank=True, null=True)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    max_occupancy = models.PositiveIntegerField(default=2)
    amenities = models.JSONField(default=list, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        unique_together = ("hotel", "name")


class RatePlan(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    room_type = models.ForeignKey(
        RoomType, on_delete=models.CASCADE, related_name="rate_plans"
    )
    name = models.CharField(max_length=100)
    ota_code = models.CharField(max_length=100, blank=True, null=True)
    base_rate = models.DecimalField(max_digits=10, decimal_places=2)
    surge_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
    )
    group_discount_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
    )
    long_term_discount_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
    )
    occupancy = models.PositiveIntegerField(default=2)
    tags = models.JSONField(default=list, blank=True)
    includes_breakfast = models.BooleanField(default=False)
    includes_lunch = models.BooleanField(default=False)
    includes_dinner = models.BooleanField(default=False)
    min_stay_days = models.PositiveIntegerField(default=1)
    valid_from = models.DateField()
    valid_to = models.DateField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def clean(self):
        super().clean()
        if self.base_rate is not None and self.base_rate <= 0:
            raise ValidationError({"base_rate": "Base rate must be a positive value."})

        # Allow same-day (valid_to == valid_from) while preventing ranges where end < start
        if self.valid_from and self.valid_to and self.valid_to < self.valid_from:
            raise ValidationError(
                {"valid_to": "Valid to date must be on or after the valid from date."}
            )

        # Validate percentage fields are within 0-100 range
        percentage_fields = {
            "surge_percentage": self.surge_percentage,
            "group_discount_percentage": self.group_discount_percentage,
            "long_term_discount_percentage": self.long_term_discount_percentage,
        }

        for field_name, value in percentage_fields.items():
            if value is not None and not (0 <= value <= 100):
                field_label = field_name.replace("_", " ").title()
                raise ValidationError(
                    {field_name: f"{field_label} must be between 0 and 100."}
                )

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)


class Calendar(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    room_type = models.ForeignKey(
        RoomType, on_delete=models.CASCADE, related_name="calendar_entries"
    )
    rate_plan = models.ForeignKey(
        RatePlan, on_delete=models.CASCADE, related_name="calendar_entries", null=True, blank=True
    )
    date = models.DateField()
    available_rooms = models.PositiveIntegerField(default=0)
    daily_rate = models.DecimalField(max_digits=10, decimal_places=2)
    restrictions = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("room_type", "rate_plan", "date")
        indexes = [
            models.Index(fields=["room_type", "rate_plan", "date"]),
        ]


class AvailabilityLog(models.Model):
    SYNC_STATUS_CHOICES = [
        ("pending", "Pending"),
        ("in_progress", "In Progress"),
        ("success", "Success"),
        ("failed", "Failed"),
        ("cancelled", "Cancelled"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    hotel = models.ForeignKey(
        "stay.Property", on_delete=models.CASCADE, related_name="availability_logs"
    )
    room_type = models.ForeignKey(
        RoomType, on_delete=models.CASCADE, related_name="availability_logs"
    )
    ota_platform = models.ForeignKey(
        OTAPlatform, on_delete=models.CASCADE, related_name="availability_logs"
    )
    date = models.DateField()
    available_rooms = models.PositiveIntegerField()
    sync_status = models.CharField(
        max_length=30, choices=SYNC_STATUS_CHOICES, default="pending"
    )
    synced_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "pms_availability_log"
        indexes = [
            models.Index(fields=["hotel", "sync_status", "date"]),
            models.Index(fields=["created_at"]),
        ]


class RoomBlock(models.Model):
    BLOCK_REASON_CHOICES = [
        ("maintenance", "Maintenance"),
        ("cleaning", "Cleaning"),
        ("reservation", "Reservation"),
        ("inspection", "Inspection"),
        ("other", "Other"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    hotel = models.ForeignKey(
        "stay.Property", on_delete=models.CASCADE, related_name="room_blocks"
    )
    room = models.ForeignKey(
        "stay.Room", on_delete=models.CASCADE, related_name="blocks"
    )
    reservation = models.ForeignKey(
        "booking.Reservation",
        on_delete=models.CASCADE,
        related_name="room_blocks",
        blank=True,
        null=True,
    )
    reason = models.CharField(
        max_length=200, choices=BLOCK_REASON_CHOICES, blank=True, null=True
    )
    blocked_from = models.DateTimeField(default=timezone.now)
    blocked_until = models.DateTimeField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def clean(self):
        super().clean()

        # Validate that blocked_until is after blocked_from
        if (
            self.blocked_until
            and self.blocked_from
            and self.blocked_until <= self.blocked_from
        ):
            raise ValidationError(
                {"blocked_until": "Block end time must be after start time."}
            )

        # Validate reservation field based on reason
        if self.reason == "reservation":
            if not self.reservation:
                raise ValidationError(
                    {
                        "reservation": 'Reservation is required when reason is "reservation".'
                    }
                )
        else:
            if self.reservation:
                raise ValidationError(
                    {
                        "reservation": "Reservation must not be set for non-reservation block reasons."
                    }
                )

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    class Meta:
        indexes = [
            models.Index(fields=["hotel", "is_active"]),
        ]
        unique_together = ("hotel", "reservation", "reason")


class RoomBlockSyncLog(models.Model):
    SYNC_STATUS_CHOICES = [
        ("pending", "Pending"),
        ("in_progress", "In Progress"),
        ("success", "Success"),
        ("failed", "Failed"),
        ("cancelled", "Cancelled"),
    ]

    ACTION_CHOICES = [
        ("create", "Create Block"),
        ("update", "Update Block"),
        ("delete", "Delete Block"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    room_block = models.ForeignKey(
        RoomBlock,
        on_delete=models.CASCADE,
        related_name="sync_logs",
        blank=True,
        null=True,
    )
    hotel = models.ForeignKey(
        "stay.Property", on_delete=models.CASCADE, related_name="room_block_sync_logs"
    )
    ota_platform = models.ForeignKey(
        OTAPlatform, on_delete=models.CASCADE, related_name="room_block_sync_logs"
    )
    action = models.CharField(max_length=20, choices=ACTION_CHOICES, default="create")
    sync_status = models.CharField(
        max_length=30, choices=SYNC_STATUS_CHOICES, default="pending"
    )
    request_data = models.JSONField(default=dict, blank=True)
    response_data = models.JSONField(default=dict, blank=True)
    error_message = models.TextField(blank=True, null=True)
    retry_count = models.PositiveIntegerField(default=0)
    max_retries = models.PositiveIntegerField(default=3)
    synced_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "pms_room_block_sync_log"
        indexes = [
            models.Index(fields=["hotel", "sync_status", "created_at"]),
            models.Index(fields=["room_block", "ota_platform"]),
            models.Index(fields=["sync_status", "retry_count"]),
        ]

    def __str__(self):
        # room_block can be None for delete operations
        rb_id = getattr(self.room_block, "id", "deleted")
        return f"RoomBlock {rb_id} - {self.ota_platform.name} - {self.sync_status}"
