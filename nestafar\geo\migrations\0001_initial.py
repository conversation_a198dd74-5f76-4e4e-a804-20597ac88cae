# Generated by Django 4.2.7 on 2025-10-02 05:11

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AdministrationArea',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('pincode', models.CharField(max_length=6)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name_plural': 'Administration Areas',
            },
        ),
        migrations.CreateModel(
            name='City',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
            ],
            options={
                'verbose_name_plural': 'Cities',
            },
        ),
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('code', models.CharField(max_length=2, unique=True)),
                ('phone_code', models.CharField(blank=True, max_length=5, null=True)),
            ],
            options={
                'verbose_name_plural': 'Countries',
            },
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('address', models.TextField()),
                ('location_type', models.CharField(choices=[('HS', 'Home Stay'), ('HT', 'Hotel'), ('TR', 'Transport Hub'), ('RS', 'Restaurant'), ('TS', 'Tourist Spot'), ('AR', 'Area'), ('OT', 'Other')], default='OT', max_length=2)),
                ('latitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('place_id', models.CharField(blank=True, default=None, max_length=255, null=True)),
                ('timezone', models.CharField(default='Asia/Kolkata', help_text='Timezone for the location (defaults to IST)', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('administrative_area', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='locations', to='geo.administrationarea')),
            ],
        ),
        migrations.CreateModel(
            name='State',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=2)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='states', to='geo.country')),
            ],
        ),
        migrations.CreateModel(
            name='LocationMetadata',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overall_rating', models.PositiveSmallIntegerField(choices=[(1, 'Poor'), (2, 'Fair'), (3, 'Good'), (4, 'Very Good'), (5, 'Excellent')], default=2)),
                ('weather_rating', models.PositiveSmallIntegerField(choices=[(1, 'Poor'), (2, 'Fair'), (3, 'Good'), (4, 'Very Good'), (5, 'Excellent')], default=2)),
                ('air_quality_index', models.PositiveSmallIntegerField(choices=[(1, 'Poor'), (2, 'Fair'), (3, 'Good'), (4, 'Very Good'), (5, 'Excellent')], default=2)),
                ('internet_speed', models.PositiveIntegerField(help_text='Speed in Mbps', null=True)),
                ('accessibility_rating', models.PositiveSmallIntegerField(choices=[(1, 'Poor'), (2, 'Fair'), (3, 'Good'), (4, 'Very Good'), (5, 'Excellent')], default=2)),
                ('safety_rating', models.PositiveSmallIntegerField(choices=[(1, 'Poor'), (2, 'Fair'), (3, 'Good'), (4, 'Very Good'), (5, 'Excellent')], default=2)),
                ('healthcare_rating', models.PositiveSmallIntegerField(choices=[(1, 'Poor'), (2, 'Fair'), (3, 'Good'), (4, 'Very Good'), (5, 'Excellent')], default=2)),
                ('primary_language', models.CharField(blank=True, max_length=100)),
                ('additional_languages', models.JSONField(default=list)),
                ('amenities', models.JSONField(default=dict)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('location', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='metadata', to='geo.location')),
            ],
            options={
                'verbose_name_plural': 'Location metadata',
            },
        ),
        migrations.AddIndex(
            model_name='country',
            index=models.Index(fields=['code'], name='geo_country_code_cbc6db_idx'),
        ),
        migrations.AddField(
            model_name='city',
            name='state',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cities', to='geo.state'),
        ),
        migrations.AddField(
            model_name='administrationarea',
            name='city',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='areas', to='geo.city'),
        ),
        migrations.AddIndex(
            model_name='state',
            index=models.Index(fields=['code'], name='geo_state_code_bc0136_idx'),
        ),
        migrations.AddIndex(
            model_name='state',
            index=models.Index(fields=['name'], name='geo_state_name_45f869_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='state',
            unique_together={('country', 'name')},
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['place_id'], name='geo_locatio_place_i_866ed2_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['location_type'], name='geo_locatio_locatio_c6226b_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['latitude', 'longitude'], name='geo_locatio_latitud_ab8676_idx'),
        ),
        migrations.AddIndex(
            model_name='city',
            index=models.Index(fields=['name'], name='geo_city_name_5efbe9_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='city',
            unique_together={('state', 'name')},
        ),
        migrations.AddIndex(
            model_name='administrationarea',
            index=models.Index(fields=['pincode'], name='geo_adminis_pincode_7e93eb_idx'),
        ),
        migrations.AddIndex(
            model_name='administrationarea',
            index=models.Index(fields=['name', 'pincode'], name='geo_adminis_name_cf430c_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='administrationarea',
            unique_together={('city', 'pincode', 'name')},
        ),
    ]
