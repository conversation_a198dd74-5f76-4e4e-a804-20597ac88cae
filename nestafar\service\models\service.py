import copy
import uuid
from django.db import models
from geo.models import Location
from phonenumber_field.modelfields import PhoneNumberField


class ServicePartner(models.Model):
    class PartnerTypes(models.IntegerChoices):
        FOOD = 1
        LAUNDRY = 2
        TRANSPORT = 3
        RENTAL = 4
        OTHERS = 5
        SHOP = 6
        TOURISM = 7
        # add more here (in house food, in house laundry, etc)

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    location = models.ForeignKey(Location, on_delete=models.CASCADE)
    type_of_service = models.PositiveSmallIntegerField(
        choices=PartnerTypes.choices, default=PartnerTypes.OTHERS
    )
    description = models.TextField(null=True, blank=True)
    phone_number = PhoneNumberField(null=True, blank=True)
    is_visible = models.BooleanField(default=True)

    # Razorpay Route Integration Fields
    razorpay_linked_account_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
    # remove db_index since UniqueConstraint with condition will create an index
        help_text="Razorpay linked account identifier for vendor payouts",
    )
    razorpay_account_verified = models.BooleanField(
        default=False,
        help_text="Whether the Razorpay linked account is verified and active",
    )
    kyc_completed = models.BooleanField(
        default=False, help_text="Whether KYC verification is completed for compliance"
    )
    kyc_completed_at = models.DateTimeField(
        null=True, blank=True, help_text="Timestamp when KYC was completed"
    )
    # Account status choices
    class AccountStatus(models.TextChoices):
        PENDING = 'pending', 'Pending Verification'
        VERIFIED = 'verified', 'Verified'
        SUSPENDED = 'suspended', 'Suspended'
        REJECTED = 'rejected', 'Rejected'

    # Convenience literal derived from enum to use in class-level constraints
    ACCOUNT_STATUS_VERIFIED = AccountStatus.VERIFIED.value

    account_status = models.CharField(
        max_length=20,
        choices=AccountStatus.choices,
        default=AccountStatus.PENDING,
        db_index=True,
        help_text="Current status of the vendor account",
    )

    # Partner preference -> Enum int wise
    @property
    def service(self):
        from service.subapps.food.models import FoodService
        from service.subapps.transport.models import TransportService
        from service.subapps.rental.models import RentalService
        from service.subapps.laundry.models import LaundryService
        from service.subapps.shop.models import ShopService
        from service.subapps.tourism.models import TourismService

        mapping = {
            ServicePartner.PartnerTypes.FOOD: FoodService,
            ServicePartner.PartnerTypes.TRANSPORT: TransportService,
            ServicePartner.PartnerTypes.LAUNDRY: LaundryService,
            ServicePartner.PartnerTypes.RENTAL: RentalService,
            ServicePartner.PartnerTypes.SHOP: ShopService,
            ServicePartner.PartnerTypes.TOURISM: TourismService,
        }
        return mapping.get(self.type_of_service)

    def services(self):
        return self.service.objects.filter(partner=self)

    @property
    def service_item(self):
        from service.subapps.food.models import FoodServiceItem
        from service.subapps.transport.models import TransportServiceItem
        from service.subapps.rental.models import RentalServiceItem
        from service.subapps.laundry.models import LaundryServiceItem
        from service.subapps.shop.models import ShopServiceItem
        from service.subapps.tourism.models import TourismServiceItem

        mapping = {
            ServicePartner.PartnerTypes.FOOD: FoodServiceItem,
            ServicePartner.PartnerTypes.TRANSPORT: TransportServiceItem,
            ServicePartner.PartnerTypes.LAUNDRY: LaundryServiceItem,
            ServicePartner.PartnerTypes.RENTAL: RentalServiceItem,
            ServicePartner.PartnerTypes.SHOP: ShopServiceItem,
            ServicePartner.PartnerTypes.TOURISM: TourismServiceItem,
        }
        return mapping.get(self.type_of_service)

    def service_items(self):
        return self.service_item.objects.filter(service__partner=self)

    def can_receive_payouts(self):
        """Check if vendor can receive Razorpay payouts"""
        checks = [
            bool(self.razorpay_linked_account_id),
            bool(self.razorpay_account_verified),
            bool(self.kyc_completed),
            self.account_status == ServicePartner.AccountStatus.VERIFIED,
        ]

        return bool(all(checks))

    def get_payout_account_id(self):
        """Get the Razorpay account ID for payouts"""
        if self.can_receive_payouts():
            return self.razorpay_linked_account_id
        return None

    def update_account_status(self, status, save=True):
        """Update account status with timestamp tracking"""
        from django.db import transaction

        self.account_status = status
        if status == ServicePartner.AccountStatus.VERIFIED and not self.kyc_completed:
            from django.utils import timezone

            self.kyc_completed = True
            self.kyc_completed_at = timezone.now()
        if save:
            with transaction.atomic():
                # Lock the row and update in a single call to avoid races
                ServicePartner.objects.select_for_update().filter(id=self.id).update(
                    account_status=self.account_status,
                    kyc_completed=self.kyc_completed,
                    kyc_completed_at=self.kyc_completed_at
                )
                # Reload the instance so self reflects the DB state
                self.refresh_from_db()    
                return self
            
    def __str__(self):
        return self.name + " - " + str(self.type_of_service)
    
    class Meta:
        # constraints are attached after class definition to allow referencing
        # class-level enums like AccountStatus without name-resolution issues
        constraints = []


# Attach constraints after class creation to allow referencing class-level enums
ServicePartner._meta.constraints = [
    models.UniqueConstraint(
        fields=["razorpay_linked_account_id"],
        condition=(
            ~models.Q(razorpay_linked_account_id=None)
            & ~models.Q(razorpay_linked_account_id="")
        ),
        name="unique_razorpay_linked_account",
    ),
    models.CheckConstraint(
        check=(
            ~models.Q(account_status=ServicePartner.AccountStatus.VERIFIED.value)
            | (
                models.Q(razorpay_linked_account_id__isnull=False)
                & ~models.Q(razorpay_linked_account_id="")
                & models.Q(razorpay_account_verified=True)
                & models.Q(kyc_completed=True)
            )
        ),
        name="check_verified_requires_account_and_kyc",
    ),
]


class BaseService(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    partner = models.ForeignKey(ServicePartner, on_delete=models.CASCADE)
    charges = models.FloatField(null=True, blank=True)
    tax_rate = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True


# addons format - {$add_on_name: $price }


class BaseServiceItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    service = models.ForeignKey(
        BaseService, on_delete=models.CASCADE, related_name="service_items"
    )
    image = models.ImageField(upload_to="service_item_images", blank=True, null=True)
    addon = models.JSONField(null=True, blank=True)
    name = models.CharField(max_length=100)
    description = models.TextField(null=True, blank=True)
    price = models.FloatField()
    is_active = models.BooleanField(default=True)
    rating = models.FloatField(blank=True, null=True, default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

    def get_catalog_item(self, commission_rate=0):
        ob = copy.deepcopy(self)
        ob.price = round(ob.price * (1 + commission_rate / 100))
        if ob.addon:
            ob.addon = {
                k: round(v * (1 + commission_rate / 100)) for k, v in ob.addon.items()
            }
        return ob
