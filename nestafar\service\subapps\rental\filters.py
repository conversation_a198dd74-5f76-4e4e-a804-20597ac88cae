from .models import *
from django_filters import rest_framework as django_filters
from service.filters import BaseCartFilter


class RentalItemFilterSet(django_filters.FilterSet):
    class Meta:
        model = RentalServiceItem
        fields = ["service", "price", "is_active", "rating"]


class RentalOrderFilter(django_filters.FilterSet):
    class Meta:
        model = RentalOrder
        fields = ["status", "guest", "cart"]


class RentalCartFilter(BaseCartFilter):
    class Meta:
        model = RentalCart
        fields = ["status", "guest"]
