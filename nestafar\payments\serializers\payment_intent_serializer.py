"""
PaymentIntent Serializers

Serializers for PaymentIntent model and related operations.
"""

from rest_framework import serializers
from decimal import Decimal
from django.utils import timezone

from payments.models.payment_intent import PaymentIntent
from payments.constants import PaymentStatus, PaymentContext, PaymentMethod
from payments.utils.validation_helpers import validate_payment_amount, validate_payment_context


class PaymentIntentSerializer(serializers.ModelSerializer):
    """Serializer for PaymentIntent model"""
    
    # Read-only computed fields
    razorpay_amount = serializers.SerializerMethodField()
    is_paid = serializers.SerializerMethodField()
    can_be_refunded = serializers.SerializerMethodField()
    
    # Display fields
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    context_display = serializers.CharField(source='get_context_display', read_only=True)
    partner_name = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = PaymentIntent
        fields = [
            'id', 'reference_number', 'context', 'context_display',
            'partner', 'partner_name', 'precheckin', 'guest',
            'total_amount', 'platform_commission', 'partner_amount', 'vendor_amount',
            'status', 'status_display', 'payment_method',
            'razorpay_order_id', 'razorpay_payment_id', 'razorpay_payment_link_id',
            'razorpay_amount', 'is_paid', 'can_be_refunded',
            'created_at', 'updated_at', 'paid_at', 'expires_at',
            'split_details', 'metadata'
        ]
        read_only_fields = [
            'id', 'reference_number', 'platform_commission', 'partner_amount', 'vendor_amount',
            'razorpay_order_id', 'razorpay_payment_id', 'razorpay_payment_link_id',
            'created_at', 'updated_at', 'paid_at', 'split_details'
        ]
    
    def get_razorpay_amount(self, obj):
        """Get amount in paise for Razorpay"""
        return obj.get_razorpay_amount()
    
    def get_is_paid(self, obj):
        """Check if payment is completed"""
        return obj.is_paid()
    
    def get_can_be_refunded(self, obj):
        """Check if payment can be refunded"""
        return obj.can_be_refunded()
    
    def validate_total_amount(self, value):
        """Validate payment amount"""
        is_valid, error_message = validate_payment_amount(value)
        if not is_valid:
            raise serializers.ValidationError(error_message)
        return value
    
    def validate_context(self, value):
        """Validate payment context"""
        is_valid, error_message = validate_payment_context(value)
        if not is_valid:
            raise serializers.ValidationError(error_message)
        return value

    def get_partner_name(self, obj):
        """Safely return partner.user.name or None if not available"""
        partner = getattr(obj, 'partner', None)
        if not partner:
            return None
        user = getattr(partner, 'user', None)
        if not user:
            return None
        # Safely get name attribute
        return getattr(user, 'name', None)


class CreatePaymentLinkSerializer(serializers.Serializer):
    """Serializer for creating payment links"""
    
    context = serializers.ChoiceField(choices=PaymentContext.choices)
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=Decimal('0.01'))
    description = serializers.CharField(max_length=255)
    
    # Customer details
    customer_name = serializers.CharField(max_length=100)
    customer_email = serializers.EmailField(required=False, allow_blank=True)
    customer_phone = serializers.CharField(max_length=15)
    
    # Optional fields
    precheckin_id = serializers.UUIDField(required=False, allow_null=True)
    guest_id = serializers.UUIDField(required=False, allow_null=True)
    callback_url = serializers.URLField(required=False, allow_blank=True)
    expire_hours = serializers.IntegerField(default=24, min_value=1, max_value=168)  # Max 7 days
    
    # Notification preferences
    send_sms = serializers.BooleanField(default=True)
    send_whatsapp = serializers.BooleanField(default=True)
    
    # Additional metadata
    notes = serializers.JSONField(required=False, default=dict)
    
    def validate_total_amount(self, value):
        """Validate payment amount"""
        is_valid, error_message = validate_payment_amount(value)
        if not is_valid:
            raise serializers.ValidationError(error_message)
        return value
    
    def validate_customer_phone(self, value):
        """Validate customer phone number"""
        # Remove non-numeric characters
        phone_digits = ''.join(filter(str.isdigit, value))
        if len(phone_digits) < 10:
            raise serializers.ValidationError("Phone number must have at least 10 digits")
        if len(phone_digits) > 15:  # ITU-T E.164 max length
            raise serializers.ValidationError("Phone number must not exceed 15 digits")
        return value
    
    def validate(self, data):
        """Cross-field validation"""
        context = data.get('context')
        precheckin_id = data.get('precheckin_id')
        guest_id = data.get('guest_id')
        
        # Ensure mutual exclusivity
        if precheckin_id and guest_id:
            raise serializers.ValidationError({
                'non_field_errors': 'Cannot provide both precheckin_id and guest_id'
            })
        
        # Validate context-specific requirements
        if context == PaymentContext.PRECHECKIN and not precheckin_id:
            raise serializers.ValidationError({
                'precheckin_id': 'Precheckin ID is required for precheckin payments'
            })
        elif context == PaymentContext.PRECHECKIN and guest_id:
            raise serializers.ValidationError({
                'guest_id': 'Guest ID should not be provided for precheckin payments'
            })
        
        if context == PaymentContext.CHECKOUT and not guest_id:
            raise serializers.ValidationError({
                'guest_id': 'Guest ID is required for checkout payments'
            })
        elif context == PaymentContext.CHECKOUT and precheckin_id:
            raise serializers.ValidationError({
                'precheckin_id': 'Precheckin ID should not be provided for checkout payments'
            })
        
        return data


class PaymentStatusSerializer(serializers.Serializer):
    """Serializer for payment status responses"""
    
    payment_id = serializers.UUIDField()
    reference_number = serializers.CharField()
    status = serializers.ChoiceField(choices=PaymentStatus.choices)
    status_display = serializers.CharField()
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    paid_amount = serializers.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    payment_method = serializers.ChoiceField(choices=PaymentMethod.choices, allow_null=True)
    created_at = serializers.DateTimeField()
    paid_at = serializers.DateTimeField(allow_null=True)
    expires_at = serializers.DateTimeField(allow_null=True)
    
    # Razorpay details
    razorpay_payment_id = serializers.CharField(allow_null=True)
    razorpay_payment_link_id = serializers.CharField(allow_null=True)
    
    # Split details
    platform_commission = serializers.DecimalField(max_digits=10, decimal_places=2)
    partner_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    vendor_amount = serializers.DecimalField(max_digits=10, decimal_places=2)


class PaymentSplitSerializer(serializers.Serializer):
    """Serializer for payment split details"""
    
    recipient_type = serializers.CharField()
    recipient_name = serializers.CharField()
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    percentage = serializers.DecimalField(max_digits=5, decimal_places=2, allow_null=True)
    status = serializers.CharField()
    razorpay_transfer_id = serializers.CharField(allow_null=True)
    transfer_completed_at = serializers.DateTimeField(allow_null=True)


class WebhookEventSerializer(serializers.Serializer):
    """Serializer for webhook event processing"""
    
    event = serializers.CharField()
    payload = serializers.JSONField()
    
    def validate_event(self, value):
        """Validate webhook event type"""
        from payments.utils.razorpay_helpers import is_webhook_event_supported
        if not is_webhook_event_supported(value):
            raise serializers.ValidationError(f"Unsupported webhook event: {value}")
        return value
