"""
Razorpay Integration Service

Service abstraction for all Razorpay API interactions including payment links,
transfers, webhook verification, and error handling.
"""

import hashlib
import hmac
import json
import logging
import requests
import random
import decimal
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from django.utils import timezone

from .payment_config_service import payment_config
from ..exceptions import (
    RazorpayException,
    PaymentLinkException,
    TransferException,
    SignatureVerificationException,
    ConfigurationException,
)

from django.conf import settings

logger = logging.getLogger(__name__)


class RazorpayService:
    """
    Service for Razorpay API integration.

    Handles all interactions with Razorpay APIs including payment links,
    transfers, webhook verification, and error handling with retry logic.
    """

    def __init__(self):
        self.config = payment_config
        self.base_url = self.config.base_url
        self.auth = self.config.get_auth_tuple()
        self.timeout = self.config.api_timeout

        if not all(self.auth):
            raise ConfigurationException("Razorpay API credentials not configured")

    def _to_paise(self, amount: Decimal) -> int:
        """
        Convert a Decimal amount in rupees to paise as int safely.

        Validates that the Decimal has at most two decimal places. If it has more
        than two decimals, quantize according to business rules or raise ValueError.

        Returns:
            int: amount in paise
        """
        if not isinstance(amount, Decimal):
            try:
                amount = Decimal(str(amount))
            except (TypeError, decimal.InvalidOperation) as e:
                raise ValueError(f"Amount must be a Decimal or numeric convertible to Decimal: {e}") from e

        # Quantize to 2 decimal places (paise) using ROUND_HALF_UP behavior from Decimal
        quantized = amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

        # Reject negative amounts explicitly
        if quantized < Decimal("0.00"):
            raise ValueError("Amount must be non-negative")

        # If original has more precision than 2 decimals and business rules want to forbid it,
        # uncomment the following check to raise instead of rounding:
        # if amount != quantized:
        #     raise ValueError(f"Amount {amount} has more than 2 decimal places")

        # Convert to paise
        paise = int(quantized * 100)
        return paise

    def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Dict = None,
        params: Dict = None,
        retry_count: int = 0,
        extra_headers: Dict = None,
    ) -> Dict:
        """
        Make HTTP request to Razorpay API with retry logic.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request payload
            params: Query parameters
            retry_count: Current retry attempt

        Returns:
            Response data as dictionary

        Raises:
            RazorpayException: For API errors
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Nestafar-Platform/1.0",
        }
        if extra_headers:
            headers.update(extra_headers)

        try:
            if self.config.log_api_requests:
                logger.info(f"Razorpay API Request: {method} {url}")

            response = requests.request(
                method=method,
                url=url,
                auth=self.auth,
                headers=headers,
                json=data,
                params=params,
                timeout=self.timeout,
            )

            # Parse response
            try:
                response_data = response.json()
            except json.JSONDecodeError:
                response_data = {"raw_response": response.text}

            if response.status_code >= 400:
                error_message = self._extract_error_message(response_data)
                error_code = self._extract_error_code(response_data)

                # Retry for certain error codes
                if retry_count < self.config.max_retries and self._should_retry_error(
                    response.status_code, error_code
                ):

                    # exponential backoff with jitter
                    delay = self.config.retry_delay * (2 ** retry_count)
                    delay *= random.uniform(0.5, 1.5)
                    time.sleep(delay)
                    return self._make_request(
                        method, endpoint, data, params, retry_count + 1, extra_headers=extra_headers
                    )

                raise RazorpayException(
                    message=error_message,
                    error_code=error_code,
                    status_code=response.status_code,
                    response_data=response_data,
                )

            return response_data

        except requests.RequestException as e:
            if retry_count < self.config.max_retries:
                # exponential backoff with jitter
                import time

                delay = self.config.retry_delay * (2 ** retry_count)
                delay *= random.uniform(0.5, 1.5)
                time.sleep(delay)
                return self._make_request(
                    method, endpoint, data, params, retry_count + 1, extra_headers=extra_headers
                )

            raise RazorpayException(f"Request failed: {str(e)}")

    def _extract_error_message(self, response_data: Dict) -> str:
        """Extract error message from Razorpay response"""
        if "error" in response_data:
            error = response_data["error"]
            return error.get("description", error.get("reason", "Unknown error"))
        return response_data.get("message", "Unknown error")

    def _extract_error_code(self, response_data: Dict) -> Optional[str]:
        """Extract error code from Razorpay response"""
        if "error" in response_data:
            return response_data["error"].get("code")
        return response_data.get("code")

    def _should_retry_error(self, status_code: int, error_code: Optional[str]) -> bool:
        """Determine if error should be retried"""
        # Retry on server errors and rate limits
        if status_code >= 500:
            return True
        if status_code == 429:  # Rate limit
            return True
        if error_code in ["GATEWAY_ERROR", "SERVER_ERROR"]:
            return True
        return False

    def create_order(
        self,
        amount: Decimal,
        currency: str = "INR",
        receipt: str = None,
        notes: Dict = None,
    ) -> Dict:
        """
        Create a Razorpay order.

        Args:
            amount: Order amount in rupees
            currency: Currency code
            receipt: Receipt identifier
            notes: Additional notes

        Returns:
            Order data from Razorpay
        """
        data = {
            "amount": self._to_paise(amount),
            "currency": currency,
            "receipt": receipt,
            "notes": notes or {},
        }

        try:
            return self._make_request("POST", "orders", data)
        except RazorpayException as e:
            logger.error(f"Failed to create order: {e.message}")
            raise

    def create_payment_link(
        self,
        amount: Decimal,
        description: str,
        customer_details: Dict,
        callback_url: str = None,
        expire_by: datetime = None,
        notes: Dict = None,
    ) -> Dict:
        """
        Create a Razorpay payment link.

        Args:
            amount: Payment amount in rupees
            description: Payment description
            customer_details: Customer information
            callback_url: Callback URL after payment
            expire_by: Link expiry datetime
            notes: Additional notes

        Returns:
            Payment link data from Razorpay
        """
        if not expire_by:
            expire_by = timezone.now() + timedelta(
                hours=self.config.payment_link_expire_hours
            )

        data = {
            "amount": self._to_paise(amount),
            "currency": "INR",
            "description": description,
            "customer": customer_details,
            "expire_by": int(expire_by.timestamp()),
            "notes": notes or {},
        }

        if callback_url:
            data["callback_url"] = callback_url

        # Add notification settings - Disable SMS, use WhatsApp instead
        link_config = self.config.get_payment_link_config()
        data["notify"] = {
            "sms": False,  # Disabled: Use WhatsApp via unified notification system
            "email": link_config.get("send_email", False),
        }
        data["reminder_enable"] = link_config.get("reminder_enable", True)

        try:
            response = self._make_request("POST", "payment_links", data)
            logger.info(f"Created payment link: {response.get('id')}")
            return response
        except RazorpayException as e:
            logger.error(f"Failed to create payment link: {e.message}")
            raise PaymentLinkException(f"Failed to create payment link: {e.message}")

    def get_payment_link(self, payment_link_id: str) -> Dict:
        """Get payment link details"""
        try:
            return self._make_request("GET", f"payment_links/{payment_link_id}")
        except RazorpayException as e:
            logger.error(f"Failed to get payment link {payment_link_id}: {e.message}")
            raise

    def cancel_payment_link(self, payment_link_id: str) -> Dict:
        """Cancel a payment link"""
        try:
            return self._make_request("POST", f"payment_links/{payment_link_id}/cancel")
        except RazorpayException as e:
            logger.error(
                f"Failed to cancel payment link {payment_link_id}: {e.message}"
            )
            raise

    def create_transfer(self, payment_id: str, transfers: List[Dict]) -> List[Dict]:
        """
        Create transfers for a payment using Razorpay Route.

        Args:
            payment_id: Razorpay payment ID
            transfers: List of transfer details

        Returns:
            List of created transfers
        """
        try:
            # Build idempotency header from deterministic reference_id values if present
            reference_ids = [t.get('reference_id') for t in transfers if t.get('reference_id')]
            extra_headers = {}
            if reference_ids:
                # Use a stable, order-independent idempotency key: sort reference IDs,
                # join with a delimiter and hash the result to produce a fixed-length key.
                try:
                    sorted_ids = sorted([str(r) for r in reference_ids])
                    key_material = "|".join(sorted_ids)
                    digest = hashlib.sha256(key_material.encode('utf-8')).hexdigest()
                    extra_headers['Idempotency-Key'] = digest
                except Exception:
                    # Fallback to original behavior if hashing fails
                    idempotency_key = ";".join(reference_ids)
                    extra_headers['Idempotency-Key'] = idempotency_key

            response = self._make_request(
                "POST",
                f"payments/{payment_id}/transfers",
                {"transfers": transfers},
                extra_headers=extra_headers,
            )

            items = response.get("items", [])
            if len(items) != len(transfers):
                logger.warning(
                    f"Transfer creation returned {len(items)} items for {len(transfers)} requests; provider may have deduplicated or failed some items"
                )

            logger.info(f"Created {len(items)} transfers for payment {payment_id}")
            return items
        except RazorpayException as e:
            logger.error(
                f"Failed to create transfers for payment {payment_id}: {e.message}"
            )
            raise TransferException(f"Failed to create transfers: {e.message}")

    def get_transfer(self, transfer_id: str) -> Dict:
        """Get transfer details"""
        try:
            return self._make_request("GET", f"transfers/{transfer_id}")
        except RazorpayException as e:
            logger.error(f"Failed to get transfer {transfer_id}: {e.message}")
            raise

    def reverse_transfer(self, transfer_id: str, amount: Optional[Decimal] = None) -> Dict:
        """Reverse a transfer"""
        data = {}
        if amount is not None:
            # Convert amount in rupees to paise int
            paise = self._to_paise(amount)
            data["amount"] = paise

        try:
            return self._make_request(
                "POST", f"transfers/{transfer_id}/reversals", data
            )
        except RazorpayException as e:
            logger.error(f"Failed to reverse transfer {transfer_id}: {e.message}")
            raise

    def verify_webhook_signature(self, payload: str, signature: str) -> bool:
        """
        Verify Razorpay webhook signature.

        Args:
            payload: Raw webhook payload
            signature: Signature from webhook headers

        Returns:
            True if signature is valid

        Raises:
            SignatureVerificationException: If verification fails
        """

        if not self.config.webhook_signature_verification:
            logger.error("SECURITY WARNING: Webhook signature verification is disabled")
            return True

        webhook_secret = self.config.razorpay_webhook_secret
        if not webhook_secret:
            raise SignatureVerificationException("Webhook secret not configured")

        try:
            expected_signature = hmac.new(
                webhook_secret.encode("utf-8"), payload.encode("utf-8"), hashlib.sha256
            ).hexdigest()

            return hmac.compare_digest(expected_signature, signature)
        except Exception as e:
            logger.error(f"Webhook signature verification failed: {str(e)}")
            raise SignatureVerificationException(
                f"Signature verification failed: {str(e)}"
            )

    def get_payment(self, payment_id: str) -> Dict:
        """Get payment details"""
        try:
            return self._make_request("GET", f"payments/{payment_id}")
        except RazorpayException as e:
            logger.error(f"Failed to get payment {payment_id}: {e.message}")
            raise

    def capture_payment(self, payment_id: str, amount: Decimal) -> Dict:
        """Capture an authorized payment. Amount is provided as a Decimal in rupees and converted to paise"""
        # Convert amount in rupees (Decimal) to paise integer
        paise = self._to_paise(amount)
        data = {"amount": paise}
        try:
            return self._make_request("POST", f"payments/{payment_id}/capture", data)
        except RazorpayException as e:
            logger.error(f"Failed to capture payment {payment_id}: {e.message}")
            raise

    def refund_payment(
        self, payment_id: str, amount: Optional[Decimal] = None, notes: Dict = None
    ) -> Dict:
        """Create a refund for a payment"""
        data = {"notes": notes or {}}
        if amount:
            data["amount"] = self._to_paise(amount)

        try:
            return self._make_request("POST", f"payments/{payment_id}/refund", data)
        except RazorpayException as e:
            logger.error(f"Failed to refund payment {payment_id}: {e.message}")
            raise
