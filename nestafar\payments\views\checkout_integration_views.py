"""
Checkout Integration Views

API views for integrating payment processing into the checkout flow.
"""

import logging
from decimal import Decimal, InvalidOperation
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from ..constants import PaymentStatus

from stay.models import Room
from ..services.checkout_payment_service import CheckoutPaymentService
from ..exceptions import PaymentException
from core.permissions import PartnerPermission
from ..models import PaymentIntent
from ..constants import PaymentContext

logger = logging.getLogger(__name__)

class PartnerGuestAccessMixin:
    """
    Mixin to check that a partner user can only access guests belonging to their property.
    """
    def check_partner_guest_access(self, request, guest):
        """
        Enforce partner-specific access:

        - If the requesting user does not have a `partner_profile`, deny access (returns False).
        - If the user has a `partner_profile`, allow only when the partner is associated with the guest's property.

        This prevents non-partner users from gaining unintended access via this check.
        """
        # Deny by default for non-partner users
        if not hasattr(request.user, 'partner_profile'):
            return False

        partner_profile = request.user.partner_profile
        # Check if partner is associated with the guest's property
        return guest.room.property.staffs.filter(id=partner_profile.id).exists()


class CreateCheckoutPaymentView(PartnerGuestAccessMixin, APIView):
    """
    Create payment link for guest checkout.
    
    This endpoint is called when a guest is ready to checkout
    and needs to pay their bill.
    """
    
    permission_classes = [IsAuthenticated, PartnerPermission]
    
    def post(self, request, room_id):
        """
        Create checkout payment link for guest.
        
        Args:
            room_id: Room UUID

        Request body:
            {
                "custom_amount": 1500.00,  # Optional: override calculated amount
                "description": "Custom description",  # Optional
                "include_services": true,  # Optional: include service orders
                "include_room_charges": true  # Optional: include room charges
            }
        """
        try:
            # Get room
            room = get_object_or_404(Room, id=room_id)
            guest = room.guest.filter(checked_in=True).last()

            # Check if guest exists
            if not guest:
                return Response(
                    {'error': 'No checked-in guest found for this room'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check partner access permission
            if not self.check_partner_guest_access(request, guest):
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Initialize checkout payment service
            checkout_service = CheckoutPaymentService()
            
            # Calculate or get custom amount
            custom_amount = request.data.get('custom_amount')
            if custom_amount is not None:
                # Sanitize input: convert to string, strip whitespace, remove commas
                raw = str(custom_amount).strip()
                raw = raw.replace(',', '')
                try:
                    # Use Decimal for robust numeric parsing; Decimal('') will raise InvalidOperation
                    total_amount = Decimal(raw)
                except (InvalidOperation, TypeError, ValueError):
                    return Response(
                        {'error': 'Invalid custom_amount'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                bill_breakdown = None
            else:
                # Calculate guest bill
                bill_breakdown = checkout_service.calculate_guest_bill(guest)
                total_amount = bill_breakdown['total_amount']
            
            # Validate amount
            if total_amount <= 0:
                return Response(
                    {'error': 'Payment amount must be greater than zero'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get custom description
            description = request.data.get('description')
            
            # Create payment link
            payment_data = checkout_service.create_checkout_payment_link(
                guest=guest,
                total_amount=total_amount,
                description=description
            )
            
            # Build response
            response_data = {
                'payment_link': payment_data,
                'guest_info': {
                    'id': guest.id,
                    'name': getattr(guest.user, 'name', 'Guest'),
                    'room_number': guest.room.room_no,
                    'property_name': guest.room.property.name,
                },
                'bill_breakdown': bill_breakdown,
            }
            
            return Response(response_data, status=status.HTTP_201_CREATED)
            
        except PaymentException as e:
            logger.error(f"Payment error for guest: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error creating checkout payment: {str(e)}")
            return Response(
                {'error': 'Failed to create checkout payment'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class CheckoutPaymentStatusView(PartnerGuestAccessMixin, APIView):
    """
    Check payment status for guest checkout.
    
    This endpoint allows checking if a guest has completed
    their checkout payment.
    """
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request, room_id):
        """
        Get checkout payment status for guest.
        
        Args:
            room_id: Room UUID
        """
        try:
            # Get room
            room = get_object_or_404(Room, id=room_id)
            guest = room.guest.filter(checked_in=True).last()

            # Check if guest exists
            if not guest:
                return Response(
                    {'error': 'No checked-in guest found for this room'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check partner access permission
            if not self.check_partner_guest_access(request, guest):
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )
            # Check guest user access for non-partner users
            if not hasattr(request.user, 'partner_profile') and request.user != guest.user:
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Find checkout payment intents for this guest    
            payment_intents = PaymentIntent.objects.filter(
                guest=guest,
                context=PaymentContext.CHECKOUT
            ).order_by('-created_at')
            
            payment_status = {
                'guest_id': guest.id,
                'has_pending_payment': False,
                'has_completed_payment': False,
                'total_paid': Decimal('0.00'),
                'payments': [],
            }
            
            for payment_intent in payment_intents:
                payment_info = {
                    'payment_id': payment_intent.id,
                    'reference_number': payment_intent.reference_number,
                    'amount': payment_intent.total_amount,
                    'status': payment_intent.status,
                    'created_at': payment_intent.created_at,
                    'paid_at': payment_intent.paid_at,
                }
                
                payment_status['payments'].append(payment_info)
                
                if payment_intent.status == PaymentStatus.PENDING:
                    payment_status['has_pending_payment'] = True
                elif payment_intent.is_paid():
                    payment_status['has_completed_payment'] = True
                    payment_status['total_paid'] += payment_intent.total_amount
            
            return Response(payment_status)
            
        except Exception as e:
            logger.error(f"Error getting checkout payment status: {str(e)}")
            return Response(
                {'error': 'Failed to get payment status'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
