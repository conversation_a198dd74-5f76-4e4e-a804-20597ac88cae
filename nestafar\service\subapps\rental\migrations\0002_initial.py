# Generated by Django 4.2.7 on 2025-10-02 05:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('service', '0001_initial'),
        ('geo', '0001_initial'),
        ('stay', '0001_initial'),
        ('rental', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='rentalservice',
            name='partner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='service.servicepartner'),
        ),
        migrations.AddField(
            model_name='rentalorderitem',
            name='item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='rental.rentalserviceitem'),
        ),
        migrations.AddField(
            model_name='rentalorderitem',
            name='order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='rental.rentalorder'),
        ),
        migrations.AddField(
            model_name='rentalorderitem',
            name='pickup_location',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rental_order_pickup_locations', to='geo.location'),
        ),
        migrations.AddField(
            model_name='rentalorder',
            name='cart',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='rental.rentalcart'),
        ),
        migrations.AddField(
            model_name='rentalorder',
            name='guest',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.guest'),
        ),
        migrations.AddField(
            model_name='rentalorder',
            name='service',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='rental.rentalservice'),
        ),
        migrations.AddField(
            model_name='rentalorder',
            name='service_partner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='service.servicepartner'),
        ),
        migrations.AddField(
            model_name='rentalcartitems',
            name='cart',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='rental.rentalcart'),
        ),
        migrations.AddField(
            model_name='rentalcartitems',
            name='item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='rental.rentalserviceitem'),
        ),
        migrations.AddField(
            model_name='rentalcartitems',
            name='pickup_location',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rental_cart_pickup_locations', to='geo.location'),
        ),
        migrations.AddField(
            model_name='rentalcart',
            name='guest',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.guest'),
        ),
        migrations.AddIndex(
            model_name='rentalorder',
            index=models.Index(fields=['guest', 'cart'], name='rental_rent_guest_i_c6c878_idx'),
        ),
    ]
