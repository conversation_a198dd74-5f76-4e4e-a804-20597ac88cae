"""
Partner Account Serializers

Serializers for partner Razorpay account management APIs.
"""

from rest_framework import serializers
from django.core.validators import RegexValidator
import re
import mimetypes
from ..models import (
    PartnerRazorpayAccount,
    PartnerBankAccount,
    PartnerKYCDocument,
    AccountVerificationLog
)
from ..constants import (
    BankAccountType,
    DocumentType
)


class PartnerRazorpayAccountSerializer(serializers.ModelSerializer):
    """Serializer for PartnerRazorpayAccount model"""

    # Read-only fields
    account_status_display = serializers.CharField(source='get_account_status_display', read_only=True)
    kyc_status_display = serializers.CharField(source='get_kyc_status_display', read_only=True)
    bank_verification_status_display = serializers.CharField(source='get_bank_verification_status_display', read_only=True)
    is_active = serializers.BooleanField(read_only=True)
    can_receive_transfers = serializers.BooleanField(read_only=True)

    class Meta:
        model = PartnerRazorpayAccount
        fields = [
            'id',
            'razorpay_account_id',
            'stakeholder_id',
            'product_config_id',
            'product_activation_status',
            'product_requirements',
            'account_status',
            'account_status_display',
            'kyc_status',
            'kyc_status_display',
            'bank_verification_status',
            'bank_verification_status_display',
            'business_name',
            'business_type',
            'contact_name',
            'contact_email',
            'contact_phone',
            'address_line1',
            'address_line2',
            'city',
            'state',
            'postal_code',
            'country',
            'activation_form_milestone',
            'is_active',
            'can_receive_transfers',
            'created_at',
            'updated_at',
            'activated_at',
            'last_error',
            'error_count'
        ]
        read_only_fields = [
            'id',
            'razorpay_account_id',
            'stakeholder_id',
            'product_config_id',
            'product_activation_status',
            'product_requirements',
            'account_status',
            'kyc_status',
            'bank_verification_status',
            'activation_form_milestone',
            'created_at',
            'updated_at',
            'activated_at',
            'last_error',
            'error_count'
        ]

    def validate_contact_phone(self, value):
        """Validate phone number format"""
        if not value.startswith('+'):
            value = '+91' + value  # Default to India
        return value

    def validate_postal_code(self, value):
        """Validate postal code format"""
        if len(value) != 6 or not value.isdigit():
            raise serializers.ValidationError("Postal code must be 6 digits")
        return value


class CreatePartnerAccountSerializer(serializers.Serializer):
    """Serializer for creating partner Razorpay account"""

    business_name = serializers.CharField(max_length=200)
    business_type = serializers.ChoiceField(choices=[
        ('individual', 'Individual'),
        ('proprietorship', 'Proprietorship'),
        ('partnership', 'Partnership'),
        ('private_limited', 'Private Limited'),
        ('public_limited', 'Public Limited'),
        ('llp', 'Limited Liability Partnership'),
        ('trust', 'Trust'),
        ('society', 'Society'),
        ('ngo', 'NGO'),
    ])
    contact_name = serializers.CharField(max_length=100)
    contact_email = serializers.EmailField()
    contact_phone = serializers.CharField(
        max_length=15,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
            )
        ]
    )
    address_line1 = serializers.CharField(max_length=200)
    address_line2 = serializers.CharField(max_length=200, required=False, allow_blank=True)
    city = serializers.CharField(max_length=100)
    state = serializers.CharField(max_length=100)
    postal_code = serializers.CharField(
        max_length=6,
        validators=[
            RegexValidator(
                regex=r'^\d{6}$',
                message="Postal code must be 6 digits"
            )
        ]
    )
    country = serializers.CharField(max_length=2, default='IN')
    # Optional Razorpay business categorization
    category = serializers.CharField(max_length=50, required=False, allow_blank=True)
    subcategory = serializers.CharField(max_length=50, required=False, allow_blank=True)
    # Optional: trigger Step 2 & 3 right after account creation
    auto_onboard = serializers.BooleanField(required=False, default=False)

    def validate_contact_phone(self, value):
        """Validate and format phone number"""
        if not value.startswith('+'):
            value = '+91' + value  # Default to India
        return value


class PartnerBankAccountSerializer(serializers.ModelSerializer):
    """Serializer for PartnerBankAccount model"""
    
    # Read-only fields
    account_type_display = serializers.CharField(source='get_account_type_display', read_only=True)
    verification_status_display = serializers.CharField(source='get_verification_status_display', read_only=True)
    is_verified = serializers.BooleanField(read_only=True)
    masked_account_number = serializers.SerializerMethodField()
    
    class Meta:
        model = PartnerBankAccount
        fields = [
            'id',
            'account_holder_name',
            'account_number',
            'masked_account_number',
            'ifsc_code',
            'bank_name',
            'branch_name',
            'account_type',
            'account_type_display',
            'verification_status',
            'verification_status_display',
            'is_primary',
            'is_verified',
            'verification_attempts',
            'last_verification_attempt',
            'verification_error',
            'created_at',
            'updated_at',
            'verified_at'
        ]
        read_only_fields = [
            'id',
            'verification_status',
            'verification_attempts',
            'last_verification_attempt',
            'verification_error',
            'created_at',
            'updated_at',
            'verified_at'
        ]
        extra_kwargs = {
            'account_number': {'write_only': True}  # Don't expose full account number in responses
        }
    
    def get_masked_account_number(self, obj):
        """Return masked account number for security"""
        if obj.account_number:
            return f"****{obj.account_number[-4:]}"
        return None
    
    def validate_ifsc_code(self, value):
        """Validate IFSC code format"""
        import re
        if not re.match(r'^[A-Z]{4}0[A-Z0-9]{6}$', value.upper()):
            raise serializers.ValidationError("Invalid IFSC code format")
        return value.upper()
    
    def validate_account_number(self, value):
        """Validate account number"""
        if not value.isdigit():
            raise serializers.ValidationError("Account number must contain only digits")
        if len(value) < 9:
            raise serializers.ValidationError("Account number must be at least 9 digits")
        return value


class AddBankAccountSerializer(serializers.Serializer):
    """Serializer for adding bank account"""
    
    account_holder_name = serializers.CharField(max_length=100)
    account_number = serializers.CharField(
        max_length=20,
        validators=[
            RegexValidator(
                regex=r'^\d{9,20}$',
                message="Account number must be 9-20 digits"
            )
        ]
    )
    ifsc_code = serializers.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r'^[A-Z]{4}0[A-Z0-9]{6}$',
                message="Invalid IFSC code format"
            )
        ]
    )
    bank_name = serializers.CharField(max_length=100)
    branch_name = serializers.CharField(max_length=100)
    account_type = serializers.ChoiceField(
        choices=BankAccountType.choices,
        default=BankAccountType.SAVINGS
    )
    is_primary = serializers.BooleanField(default=False)
    
    def validate_ifsc_code(self, value):
        """Validate and format IFSC code"""
        return value.upper()


class PartnerKYCDocumentSerializer(serializers.ModelSerializer):
    """Serializer for PartnerKYCDocument model"""
    
    # Read-only fields
    document_type_display = serializers.CharField(source='get_document_type_display', read_only=True)
    verification_status_display = serializers.CharField(source='get_verification_status_display', read_only=True)
    is_verified = serializers.BooleanField(read_only=True)
    file_url = serializers.SerializerMethodField()
    
    class Meta:
        model = PartnerKYCDocument
        fields = [
            'id',
            'document_type',
            'document_type_display',
            'document_number',
            'file_url',
            'file_size',
            'file_type',
            'verification_status',
            'verification_status_display',
            'is_verified',
            'verification_notes',
            'verified_by',
            'created_at',
            'updated_at',
            'verified_at'
        ]
        read_only_fields = [
            'id',
            'file_size',
            'file_type',
            'verification_status',
            'verification_notes',
            'verified_by',
            'created_at',
            'updated_at',
            'verified_at'
        ]
    
    def get_file_url(self, obj):
        """Return secure file URL"""
        if obj.document_file:
            # DO NOT expose the raw storage URL. Return a backend proxy endpoint
            # which should perform permission checks and stream the file.
            # Example proxy path: /api/partners/kyc-files/{pk}/download/
            return f"/api/partners/kyc-files/{obj.pk}/download/"
        return None


class UploadKYCDocumentSerializer(serializers.Serializer):
    """Serializer for uploading KYC documents"""
    
    document_type = serializers.ChoiceField(choices=DocumentType.choices)
    document_number = serializers.CharField(max_length=50)
    document_file = serializers.FileField()
    
    def validate_document_file(self, value):
        """Validate uploaded file"""
        # Check file size (max 5MB)
        if value.size > 5 * 1024 * 1024:
            raise serializers.ValidationError("File size cannot exceed 5MB")
        
        # Check file type
        allowed_types = ['pdf', 'jpg', 'jpeg', 'png']
        file_extension = value.name.split('.')[-1].lower()
        if file_extension not in allowed_types:
            raise serializers.ValidationError(f"File type must be one of: {', '.join(allowed_types)}")
        
        return value
    
    def validate_document_number(self, value):
        """Validate document number based on type"""
        document_type = self.initial_data.get('document_type')
        
        if document_type == DocumentType.PAN_CARD:
           
            if not re.match(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$', value.upper()):
                raise serializers.ValidationError("Invalid PAN card format")
            return value.upper()
        
        elif document_type == DocumentType.AADHAAR_CARD:
            if not value.isdigit() or len(value) != 12:
                raise serializers.ValidationError("Aadhaar number must be 12 digits")
        
        return value


class AccountVerificationLogSerializer(serializers.ModelSerializer):
    """Serializer for AccountVerificationLog model"""
    
    activity_type_display = serializers.CharField(source='get_activity_type_display', read_only=True)
    performed_by_name = serializers.CharField(source='performed_by.name', read_only=True, default=None)
    
    class Meta:
        model = AccountVerificationLog
        fields = [
            'id',
            'activity_type',
            'activity_type_display',
            'description',
            'performed_by_name',
            'metadata',
            'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class AccountStatusSerializer(serializers.Serializer):
    """Serializer for account status response"""

    account_status = serializers.CharField()
    kyc_status = serializers.CharField()
    bank_verification_status = serializers.CharField()
    can_receive_transfers = serializers.BooleanField()
    activation_form_milestone = serializers.CharField(allow_null=True)

    # Account details
    business_name = serializers.CharField()
    business_type = serializers.CharField()
    contact_email = serializers.EmailField()

    # Statistics
    total_bank_accounts = serializers.IntegerField()
    verified_bank_accounts = serializers.IntegerField()
    total_kyc_documents = serializers.IntegerField()
    verified_kyc_documents = serializers.IntegerField()

    # Recent activity
    recent_activities = AccountVerificationLogSerializer(many=True)

    # Error information
    last_error = serializers.CharField(allow_null=True)
    error_count = serializers.IntegerField()


class CompleteOnboardingSerializer(serializers.Serializer):
    """Serializer for complete onboarding (account + bank)"""
    account = CreatePartnerAccountSerializer(required=False)
    bank = AddBankAccountSerializer(required=True)
