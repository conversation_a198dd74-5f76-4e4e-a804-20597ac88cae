import logging
from django.dispatch import receiver
from django.db.models.signals import post_save
from django.db import transaction

from booking.models import Reservation
from notification.tasks import send_notification
from core.models import PartnerProfile

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Reservation)
def external_reservation_created_handler(sender, instance, created, **kwargs):
    """
    Handle external reservation creation notifications to property staff
    
    Triggers when a new reservation is created from external sources (OTAs)
    and sends WhatsApp notification to property staff members.
    """
    if not created:
        return  # Only process new reservations
    
    try:
        # Check if this is an external booking (has external_booking_id)
        if not instance.external_booking_id:
            logger.debug(f"Reservation {instance.id} is not external booking, skipping staff notification")
            return
            
        # Get property staff members
        property_staff = instance.property.staffs.all()
        
        if not property_staff.exists():
            logger.warning(f"No staff found for property {instance.property.name} (ID: {instance.property.id})")
            return
        
        # Prepare notification data
        notification_data = {
            "guest_name": getattr(instance.user, "name", "Guest"),
            "property_name": instance.property.name,
            "checkin_date": instance.check_in.strftime("%d %b %Y") if instance.check_in else "TBD",
            "checkout_date": instance.check_out.strftime("%d %b %Y") if instance.check_out else "TBD", 
            "guest_count": str(instance.guests),
            "room_type": get_room_type_from_reservation(instance),
            "booking_source": get_booking_source(instance),
            "total_amount": f"{instance.total:,.2f}" if instance.total else "0.00",
            "external_booking_id": instance.external_booking_id or "N/A",
        }
        
        # Send notifications to all property staff
        for staff_member in property_staff:
            if staff_member.user and staff_member.user.id:
                # Use transaction.on_commit to ensure the reservation is fully saved
                transaction.on_commit(
                    lambda staff_id=str(staff_member.user.id): send_notification.delay(
                        staff_id,
                        "EXTERNAL_RESERVATION_ALERT",
                        notification_data,
                    )
                )
                
                logger.info(
                    f"Queued external reservation alert for staff {staff_member.user.name} "
                    f"about reservation {instance.id} from {notification_data['booking_source']}"
                )
        
    except Exception as e:
        logger.error(
            f"Error sending external reservation notification for reservation {instance.id}: {str(e)}",
            exc_info=True
        )


def get_room_type_from_reservation(reservation):
    """
    Extract room type information from reservation data
    """
    try:
        # Try to get room type from room_details
        if reservation.room_details and isinstance(reservation.room_details, dict):
            rooms = reservation.room_details.get("rooms", [])
            if rooms and isinstance(rooms, list) and len(rooms) > 0:
                first_room = rooms[0]
                if isinstance(first_room, dict):
                    room_type = first_room.get("roomType") or first_room.get("room_type")
                    if room_type:
                        return str(room_type)
            
            # Try mappings
            mappings = reservation.room_details.get("mappings", [])
            if mappings and isinstance(mappings, list) and len(mappings) > 0:
                first_mapping = mappings[0]
                if isinstance(first_mapping, dict):
                    mapped_room = first_mapping.get("mapped_room")
                    if mapped_room and isinstance(mapped_room, dict):
                        room_type = mapped_room.get("room_type")
                        if room_type:
                            return str(room_type)
        
        # Fallback to generic room type based on guest count
        guest_count = reservation.guests or 1
        if guest_count == 1:
            return "Single Room"
        elif guest_count == 2:
            return "Double Room"
        elif guest_count <= 4:
            return "Family Room"
        else:
            return "Group Room"
            
    except Exception as e:
        logger.warning(f"Error extracting room type from reservation {reservation.id}: {e}")
        return "Standard Room"


def get_booking_source(reservation):
    """
    Extract booking source/channel from reservation data
    """
    try:
        # Check channel field first
        if reservation.channel:
            return str(reservation.channel).title()
        
        # Check segment
        if reservation.segment and reservation.segment != "DIRECT":
            return str(reservation.segment).replace("_", " ").title()
        
        # Check booking details for source information
        if reservation.booking_details and isinstance(reservation.booking_details, dict):
            # Look for various channel indicators
            source_fields = ["channel", "source", "platform", "ota"]
            for field in source_fields:
                if field in reservation.booking_details:
                    source = reservation.booking_details[field]
                    if source:
                        return str(source).title()
        
        # If it has external_booking_id but no clear source, assume it's from an OTA
        if reservation.external_booking_id:
            return "Online Travel Agency"
        
        return "External Source"
        
    except Exception as e:
        logger.warning(f"Error extracting booking source from reservation {reservation.id}: {e}")
        return "External Source"
