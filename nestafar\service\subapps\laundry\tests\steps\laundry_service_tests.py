from pytest_bdd import scenario, given, then, when
from models import LaundryOrder, LaundryCart


@scenario("features/laundry_order_test.feature")
def test_laundry_order(client, user, laundry_cart_factory):
    pass


@given("a laundry cart exists for the user")
def get_user_cart(user):
    cart = LaundryCart.objects.get(guest=user.guest)
    return cart


@when("I create a laundry order")
def create_laundry_order(client, cart):
    response = client.post(
        f"/api/laundry-orders/", data={"cart": cart.id}, format="json"
    )
    response.json()  # trigger data parsing


@then("the response status code is 201")
def check_order_creation_status(response):
    assert response.status_code == 201


@then("And a new laundry order is created with the cart items")
def check_order_creation(client, cart):
    order = LaundryOrder.objects.get(cart=cart)
