import uuid
from django.db import models, transaction
from stay.models.guest import Guest

from service.models import BaseServiceItem


class BaseCart(models.Model):
    class CartStatus(models.IntegerChoices):
        PENDING = 0
        ORDERED = 1
        PARTIALLY_ACCEPTED = 2
        ACCEPTED = 3
        ONGOING = 4
        PARTIALLY_REJECTED = 5
        REJECTED = 6
        CANCELLED = 7
        COMPLETED = 8
        INCOMPLETE = 9

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    guest = models.ForeignKey(Guest, on_delete=models.CASCADE)
    status = models.PositiveSmallIntegerField(
        choices=CartStatus.choices, default=CartStatus.PENDING.value
    )
    subtotal = models.FloatField(default=0)
    taxes = models.FloatField(default=0)
    charges = models.FloatField(default=0)
    total = models.FloatField(default=0)
    order_created_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

    def apply_charges(self, charges):
        self.charges = round(charges, 2)
        self.total = self.subtotal + self.charges + self.taxes
        self.save(update_fields=["subtotal", "charges", "taxes", "total"])
        return self

    def apply_taxes(self, taxes):
        self.taxes = round(taxes, 2)
        self.total = round(self.subtotal + self.charges + self.taxes, 2)
        self.save(update_fields=["subtotal", "charges", "taxes", "total"])
        return self

    def reset_cart(self):
        with transaction.atomic():
            self.cart_items.all().delete()
            self.subtotal = 0
            self.taxes = 0
            self.charges = 0
            self.total = 0
            self.save(update_fields=["subtotal", "charges", "taxes", "total"])


class BaseCartItems(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    cart = models.ForeignKey(
        BaseCart, on_delete=models.CASCADE, related_name="cart_items"
    )
    item = models.ForeignKey(
        BaseServiceItem, on_delete=models.CASCADE, related_name="cart_items"
    )
    quantity = models.IntegerField(default=1)
    price = models.FloatField(default=0)
    ordered = models.BooleanField(default=False)
    add_ons = models.JSONField(default=list, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

    def add_item(self, commission=0, charges=0):
        self.name = self.item.name
        self.price = round(self.item.price * (1 + commission / 100))
        add_on_price = (
            sum(self.item.addon[add_on] for add_on in self.add_ons)
            if self.add_ons
            else 0
        )
        add_on_price += round(add_on_price * commission / 100)
        self.price += add_on_price
        item_total = self.price * self.quantity
        self.cart.subtotal += item_total
        self.cart.taxes += item_total * (self.item.service.tax_rate / 100)
        self.cart.taxes = round(self.cart.taxes, 2)
        self.cart.charges += charges
        self.cart.total = round(
            self.cart.subtotal + self.cart.taxes + self.cart.charges, 2
        )
        self.cart.save(update_fields=["subtotal", "taxes", "charges", "total"])
        self.save()
        return self

    def add_quantity(self, quantity):
        self.quantity += quantity
        quantity_total = self.price * quantity
        self.cart.subtotal += quantity_total
        charges = self.cart.charges + (self.item.service.charges * quantity)
        new_tax = self.cart.taxes + (
            quantity_total * (self.item.service.tax_rate / 100)
        )
        self.cart.apply_charges(charges)
        self.cart.apply_taxes(new_tax)
        self.save(update_fields=["quantity"])
        return self

    def remove_quantity(self, quantity):
        self.quantity -= quantity
        self.quantity = self.quantity if self.quantity > 0 else 0
        quantity_total = self.price * quantity
        self.cart.subtotal -= quantity_total
        new_tax = self.cart.taxes - (
            quantity_total * (self.item.service.tax_rate / 100)
        )
        charges = self.cart.charges - (self.item.service.charges * quantity)
        self.cart.apply_charges(charges)
        self.cart.apply_taxes(new_tax)
        self.save(update_fields=["quantity"])
        return self
