"""
Unit tests for AioSell API endpoints.
"""

import json
from datetime import date, timedelta
from decimal import Decimal
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth.models import User
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch, MagicMock

from stay.models import Property, Room
from pms.models import RoomType, HotelOTAIntegration, OTAPlatform
from core.models import User as CoreUser, PartnerProfile


class AioSellAPIEndpointsTestCase(TestCase):
    """Test case for AioSell API endpoints."""

    def setUp(self):
        """Set up test data."""
        # Create test user and partner
        self.user = CoreUser.objects.create_user(
            phone="1234567890",
            name="Test Partner"
        )
        self.partner = PartnerProfile.objects.create(
            user=self.user,
            business_name="Test Hotel Business"
        )
        
        # Create test property
        self.property = Property.objects.create(
            name="Test Hotel",
            rooms=10,
            owner=self.partner,
            channel_managers={
                "aiosell": {
                    "enabled": True,
                    "config": {
                        "sync_enabled": True,
                        "target_channels": ["agoda", "booking.com"],
                        "room_mapping": {"DELUXE": "DELUXE_ROOM"},
                        "rate_plans": {"DELUXE_ROOM": ["DELUXE-S-101", "DELUXE-D-101"]},
                    },
                }
            },
        )
        
        # Create OTA platform
        self.ota_platform = OTAPlatform.objects.create(
            name="aiosell",
            api_endpoint="https://live.aiosell.com/api/v2/cm",
            is_active=True,
        )
        
        # Create hotel integration
        self.integration = HotelOTAIntegration.objects.create(
            hotel=self.property,
            ota_platform=self.ota_platform,
            external_hotel_id="TEST-HOTEL-001",
            credentials={"identifier": "test-api-key"},
            is_active=True,
        )
        
        # Create room type
        self.room_type = RoomType.objects.create(
            hotel=self.property,
            name="Deluxe Room",
            max_occupancy=2,
        )
        
        # Create test rooms
        for i in range(5):
            Room.objects.create(
                property=self.property,
                room_type=self.room_type,
                room_no=f"10{i}",
                max_guests=2,
                type_of_room="Deluxe Room"
            )
        
        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # Mock property permission
        self.client.defaults['HTTP_X_PROPERTY_ID'] = str(self.property.id)

    @patch('pms.services.aiosell.requests.post')
    def test_rates_update_success(self, mock_post):
        """Test successful rates update."""
        # Mock successful AioSell response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "message": "Rates Updated Successfully"
        }
        mock_post.return_value = mock_response
        
        url = reverse('pms-aiosell-rates-update')
        data = {
            "start_date": "2023-12-01",
            "end_date": "2023-12-03",
            "rate_updates": [
                {
                    "room_code": "DELUXE",
                    "rate": 1500.0,
                    "rateplan_code": "DELUXE-S-101"
                },
                {
                    "room_code": "DELUXE",
                    "rate": 1700.0,
                    "rateplan_code": "DELUXE-D-101"
                }
            ],
            "to_channels": ["agoda", "booking.com"]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['updated_rates'], 2)
        self.assertIn('aiosell_response', response.data['data'])

    def test_rates_update_validation_error(self):
        """Test rates update with validation errors."""
        url = reverse('pms-aiosell-rates-update')
        data = {
            "start_date": "2023-12-01",
            "end_date": "2023-12-03",
            "rate_updates": []  # Empty rate updates should fail
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('rate_updates is required', response.data['message'])

    @patch('pms.services.aiosell.requests.post')
    def test_inventory_restrictions_success(self, mock_post):
        """Test successful inventory restrictions update."""
        # Mock successful AioSell response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "message": "Inventory Updated Successfully"
        }
        mock_post.return_value = mock_response
        
        url = reverse('pms-aiosell-inventory-restrictions')
        data = {
            "start_date": "2023-12-01",
            "end_date": "2023-12-03",
            "room_restrictions": [
                {
                    "room_code": "DELUXE",
                    "restrictions": {
                        "stop_sell": False,
                        "minimum_stay": 2,
                        "close_on_arrival": False,
                        "close_on_departure": False
                    }
                }
            ]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['updated_rooms'], 1)

    @patch('pms.services.aiosell.requests.post')
    def test_rate_restrictions_success(self, mock_post):
        """Test successful rate restrictions update."""
        # Mock successful AioSell response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "message": "Rates Updated Successfully"
        }
        mock_post.return_value = mock_response
        
        url = reverse('pms-aiosell-rate-restrictions')
        data = {
            "start_date": "2023-12-01",
            "end_date": "2023-12-03",
            "rate_restrictions": [
                {
                    "room_code": "DELUXE",
                    "rateplan_code": "DELUXE-S-101",
                    "restrictions": {
                        "stop_sell": False,
                        "minimum_stay": 1,
                        "close_on_arrival": False,
                        "close_on_departure": False
                    }
                }
            ]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['updated_rate_plans'], 1)

    def test_invalid_date_format(self):
        """Test API with invalid date format."""
        url = reverse('pms-aiosell-rates-update')
        data = {
            "start_date": "invalid-date",
            "end_date": "2023-12-03",
            "rate_updates": [
                {
                    "room_code": "DELUXE",
                    "rate": 1500.0,
                    "rateplan_code": "DELUXE-S-101"
                }
            ]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('Invalid date format', response.data['message'])

    def test_end_date_before_start_date(self):
        """Test API with end date before start date."""
        url = reverse('pms-aiosell-rates-update')
        data = {
            "start_date": "2023-12-03",
            "end_date": "2023-12-01",  # End before start
            "rate_updates": [
                {
                    "room_code": "DELUXE",
                    "rate": 1500.0,
                    "rateplan_code": "DELUXE-S-101"
                }
            ]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('end_date must be on or after start_date', response.data['message'])

    @patch('pms.services.aiosell.requests.post')
    def test_aiosell_api_error(self, mock_post):
        """Test handling of AioSell API errors."""
        # Mock AioSell API error response
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "success": False,
            "message": "Invalid hotel code"
        }
        mock_post.return_value = mock_response
        
        url = reverse('pms-aiosell-rates-update')
        data = {
            "start_date": "2023-12-01",
            "end_date": "2023-12-03",
            "rate_updates": [
                {
                    "room_code": "DELUXE",
                    "rate": 1500.0,
                    "rateplan_code": "DELUXE-S-101"
                }
            ]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('AioSell API error', response.data['message'])

    def test_no_aiosell_integration(self):
        """Test API when AioSell integration is not configured."""
        # Disable the integration
        self.integration.is_active = False
        self.integration.save()
        
        url = reverse('pms-aiosell-rates-update')
        data = {
            "start_date": "2023-12-01",
            "end_date": "2023-12-03",
            "rate_updates": [
                {
                    "room_code": "DELUXE",
                    "rate": 1500.0,
                    "rateplan_code": "DELUXE-S-101"
                }
            ]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('AioSell integration not configured', response.data['message'])

    def test_missing_required_fields(self):
        """Test API with missing required fields."""
        url = reverse('pms-aiosell-rates-update')
        data = {
            "start_date": "2023-12-01",
            # Missing end_date
            "rate_updates": [
                {
                    "room_code": "DELUXE",
                    "rate": 1500.0,
                    "rateplan_code": "DELUXE-S-101"
                }
            ]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('start_date and end_date are required', response.data['message'])


class AioSellServiceTestCase(TestCase):
    """Test case for AioSell service methods."""

    def setUp(self):
        """Set up test data."""
        # Create test property and integration (similar to above)
        self.property = Property.objects.create(
            name="Test Hotel",
            rooms=10,
        )
        
        self.ota_platform = OTAPlatform.objects.create(
            name="aiosell",
            api_endpoint="https://live.aiosell.com/api/v2/cm",
            is_active=True,
        )
        
        self.integration = HotelOTAIntegration.objects.create(
            hotel=self.property,
            ota_platform=self.ota_platform,
            external_hotel_id="TEST-HOTEL-001",
            credentials={"identifier": "test-api-key"},
            is_active=True,
        )

    @patch('pms.services.aiosell.requests.post')
    def test_push_rates_update(self, mock_post):
        """Test push_rates_update method."""
        from pms.services.aiosell import AioSellService
        
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "message": "Rates Updated Successfully"
        }
        mock_post.return_value = mock_response
        
        service = AioSellService(self.integration)
        
        rate_updates = [
            {
                "room_code": "DELUXE",
                "rate": 1500.0,
                "rateplan_code": "DELUXE-S-101"
            }
        ]
        
        result = service.push_rates_update(
            rate_updates=rate_updates,
            start_date=date(2023, 12, 1),
            end_date=date(2023, 12, 3)
        )
        
        self.assertTrue(result['success'])
        self.assertEqual(result['message'], "Rates Updated Successfully")
        
        # Verify API call was made with correct data
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        payload = call_args[1]['json']
        
        self.assertEqual(payload['hotelCode'], "TEST-HOTEL-001")
        self.assertEqual(len(payload['updates']), 1)
        self.assertEqual(payload['updates'][0]['startDate'], "2023-12-01")
        self.assertEqual(payload['updates'][0]['endDate'], "2023-12-03")
        self.assertEqual(len(payload['updates'][0]['rates']), 1)
