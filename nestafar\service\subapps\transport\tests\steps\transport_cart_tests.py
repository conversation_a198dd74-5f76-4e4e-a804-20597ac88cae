from pytest_bdd import scenario, given, then, when
from transport.models import *
from transport.tests import transportest
import datetime


@scenario("features/transport_cart.feature")
def test_manage_transport_cart(client, user):
    pass


@given("I am an authenticated user with a transport cart (optional)")
def create_transport_cart(client, user, create_transport_cart=None):
    if not create_transport_cart:
        cart, _ = TransportCart.objects.get_or_create(
            guest=user.guest, defaults={"status": "OPEN"}
        )
        return cart
    return create_transport_cart


@when(
    "I add a transport service item '<item_name>' to the cart with quantity '<quantity>'"
)
def add_transport_item_to_cart(
    client,
    user,
    item_name,
    quantity,
    create_transport_service_item=transportest.create_transport_service_item,
):
    cart = user.guest.transport_carts.filter(status="OPEN").first()
    # Get the specific service item by name
    item = TransportServiceItem.objects.get(name=item_name)
    data = {"item": item.id, "quantity": quantity, "pickup_date": "2024-0"}


@then("Then the response status code is 201")
def check_add_item_status_code(response):
    assert response.status_code == 201


# Inside your step definition
@then(
    "And the response data contains the added cart item details with pickup & drop off dates, no of periods"
)
def check_add_item_response(response):
    data = response.json()

    # Convert pickup and drop-off dates from string to datetime
    pickup_date_str = data["pickup_date"]
    drop_date_str = data["drop_date"]

    # Convert to datetime objects
    pickup_date = datetime.datetime.strptime(pickup_date_str, "%Y-%m-%d")
    drop_date = datetime.datetime.strptime(drop_date_str, "%Y-%m-%d")

    # Calculate the number of periods
    delta = drop_date - pickup_date
    expected_no_of_periods = delta.days

    # Validate the response status code to ensure the item was added
    assert response.status_code == 201

    # Get the cart item from the response data
    rental_item_id = data.get("id")  # Check if 'id' is in the response data
    assert (
        rental_item_id is not None
    ), "The response does not contain the expected 'id' key"

    # Fetch the rental item using the retrieved ID
    rental_item = TransportCartItems.objects.get(
        id=rental_item_id
    )  # Make sure this is the correct model

    # Assertions for dates and periods
    assert rental_item.pickup_date == pickup_date
    assert rental_item.drop_date == drop_date
    assert rental_item.no_of_periods == expected_no_of_periods


@when("I remove a transport service item from the cart")
def remove_transport_item_from_cart(client, user, item_name):
    cart = user.guest.transport_carts.filter(status="OPEN").first()
    item = TransportServiceItem.objects.get(name=item_name)
    cart_item = TransportCartItems.objects.create(cart=cart, item=item, quantity=1)
    item_id = cart_item.id
    response = client.delete(f"/api/transport-carts/{cart.id}/items/{item_id}/")


@then("And the removed item is no longer present in the cart")
def check_remove_item_from_cart(client, user):
    cart = user.guest.transport_carts.filter(status="OPEN").first()
    removed_items = TransportCartItems.objects.filter(cart=cart)
    assert len(removed_items) == 0


@when("I try to add an invalid item or quantity to the cart")
def add_invalid_item_to_cart(client, user, item_name):
    # Get user's cart or create a new one
    cart, _ = TransportCart.objects.get_or_create(
        guest=user.guest, defaults={"status": "OPEN"}
    )
    # Invalid item name
    data = {
        "item": 9999,
        "quantity": 1,
        "pickup_date": "2024-05-12",
        "drop_date": "2024-05-15",
    }
    response = client.post(
        f"/api/transport-carts/{cart.id}/items/", data=data, format="json"
    )
    response.json()  # trigger data parsing


@then("Then the response status code is 400")
def check_invalid_item_status_code(response):
    assert response.status_code == 400


@then("And the response data contains error messages")
def check_invalid_item_error(response):
    data = response.json()
    assert "errors" in data
    # Assert specific error messages based on your API implementation
    # For example, for non-existent item ID:
    assert "item" in data["errors"]
    assert "does not exist" in data["errors"]["item"][0]


# To add steps for updating cart items, getting cart details, etc.
