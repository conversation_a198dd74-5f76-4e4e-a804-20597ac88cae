from django.db import models, transaction
from stay.models.guest import Guest
import uuid
from service.models import ServicePartner, BaseService
from . import BaseServiceItem
from .cart import BaseCart, BaseCartItems


class BaseOrder(models.Model):
    class OrderStatus(models.IntegerChoices):
        PENDING = 0  # when the guest creates the order
        ACCEPTED = 1  # when the partner accepts the order
        ONGOING = 2  # when the partner starts preparing the order
        REJECTED = 3
        CANCELLED = 4
        COMPLETED = 5

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    guest = models.ForeignKey(Guest, on_delete=models.CASCADE)
    status = models.PositiveSmallIntegerField(
        choices=OrderStatus.choices, default=OrderStatus.PENDING
    )
    service_partner = models.ForeignKey(
        ServicePartner, on_delete=models.SET_NULL, null=True
    )
    cart = models.ForeignKey(BaseCart, on_delete=models.CASCADE, related_name="order")
    service = models.ForeignKey(
        BaseService, on_delete=models.SET_NULL, related_name="service_orders"
    )
    subtotal = models.FloatField(default=0)
    commissions = models.FloatField(default=0)
    taxes = models.FloatField(default=0)
    charges = models.FloatField(default=0)
    total = models.FloatField(default=0)
    rating = models.FloatField(default=0)
    review = models.TextField(default="")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Reminder tracking fields
    last_reminder_sent = models.DateTimeField(null=True, blank=True)
    reminder_count = models.PositiveIntegerField(default=0)

    def __str__(self):
        return "Order {0} by {1} for {2}".format(self.id, self.guest.name, self.total)

    class Meta:
        abstract = True
        indexes = [
            models.Index(fields=["guest", "cart"]),
        ]

    def add_cart_item(self, cart_item):
        assert issubclass(
            type(cart_item), BaseCartItems
        ), "Only Cart Items are accepted"
        service_price = cart_item.item.price
        service_addons_price = sum(
            [cart_item.item.addon[addon] for addon in cart_item.add_ons]
        )
        catalog_price = cart_item.price
        tax_rate = cart_item.item.service.tax_rate
        charges = cart_item.item.service.charges
        self.commissions = round(
            (catalog_price - (service_price + service_addons_price))
            * cart_item.quantity,
            2,
        )
        self.subtotal += round(
            (service_price + service_addons_price) * cart_item.quantity, 2
        )
        self.charges += charges * cart_item.quantity
        self.taxes = round(self.subtotal * (tax_rate / 100), 2)
        self.total = round(self.subtotal + self.taxes + self.charges, 2)
        self.save(
            update_fields=["commissions", "subtotal", "taxes", "charges", "total"]
        )
        return self

    def remove_from_cart(self):
        with transaction.atomic():
            cart = self.cart
            cart_items = cart.cart_items.all()
            order_items = [item.item for item in self.order_items.all()]
            cancelled_cart_items = [
                item for item in cart_items if item.item in order_items
            ]
            for item in cancelled_cart_items:
                item.remove_quantity(item.quantity)
            service_partner = self.service_partner or self.service.partner
            property_partner = service_partner.partner.filter(
                property=self.guest.room.property
            ).get()
            charges = (
                self.service.charges
                + property_partner.delivery_charges
                + property_partner.pickup_charges
            )
            new_charges = cart.charges - charges
            cart.apply_charges(new_charges)


class BaseOrderItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.ForeignKey(
        BaseOrder, on_delete=models.CASCADE, related_name="order_items"
    )
    item = models.ForeignKey(
        BaseServiceItem, on_delete=models.CASCADE, related_name="order_items"
    )
    quantity = models.IntegerField(default=1)
    add_ons = models.JSONField(default=dict, null=True, blank=True)
    price = models.FloatField(default=0)
    rating = models.FloatField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

    def add_item(self, cart_item):
        return self.order.add_cart_item(cart_item)
