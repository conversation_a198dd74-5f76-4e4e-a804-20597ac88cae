from rest_framework import serializers
from .models import (
    RatePlan,
    RoomType,
    Calendar,
    OTAPlatform,
    HotelOTAIntegration,
    AvailabilityLog,
    RoomBlock,
    RoomBlockSyncLog,
)
from django.utils import timezone


class HotelContextMixin:
    """
    Mixin providing hotel context validation utilities for serializers.
    Ensures consistent hotel context retrieval and ownership validation.
    """

    def get_hotel_from_context(self):
        """
        Get hotel from serializer context with fallback to request user.

        Returns:
            Hotel instance from context or None if not available
        """
        hotel = self.context.get("hotel")
        if hotel is None and "request" in self.context:
            hotel = getattr(self.context["request"].user, "hotel", None)
        return hotel

    def validate_hotel_ownership(self, obj, field_name):
        """
        Validate that an object belongs to the current hotel context.

        Args:
            obj: Object to validate (should have hotel_id or property_id attribute)
            field_name: Name of the field being validated (for error messages)

        Raises:
            serializers.ValidationError: If object doesn't belong to hotel or context missing
        """
        if obj is None:
            return

        hotel = self.get_hotel_from_context()
        if hotel is None:
            raise serializers.ValidationError(
                f"Hotel context is required to validate {field_name}."
            )
        # Try different foreign key patterns for both object and hotel
        obj_hotel_id = getattr(obj, "hotel_id", None) or getattr(
            obj, "property_id", None
        )
        hotel_id = getattr(hotel, "id", None) or getattr(hotel, "property_id", None)
        if obj_hotel_id and hotel_id and obj_hotel_id != hotel_id:
            raise serializers.ValidationError(
                f"{field_name} does not belong to the current hotel."
            )

    def ensure_hotel_context(self):
        """
        Ensure hotel context is available, raising ValidationError if not.

        Returns:
            Hotel instance from context

        Raises:
            serializers.ValidationError: If hotel context is not available
        """
        hotel = self.get_hotel_from_context()
        if hotel is None:
            raise serializers.ValidationError({"hotel": "Hotel context is required."})
        return hotel


class RoomTypeSerializer(HotelContextMixin, serializers.ModelSerializer):
    class Meta:
        model = RoomType
        fields = [
            "id",
            "hotel",
            "name",
            "ota_code",
            "description",
            "max_occupancy",
            "amenities",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "hotel", "created_at", "updated_at"]

    def validate_max_occupancy(self, value):
        if value is None or value < 1:
            raise serializers.ValidationError("max_occupancy must be >= 1.")
        return value

    def validate(self, attrs):
        # Check if the room type already exists for same property
        hotel = self.ensure_hotel_context()
        return attrs

    def create(self, validated_data):
        try:
            hotel = self.ensure_hotel_context()
            validated_data["hotel"] = hotel
            return super().create(validated_data)
        except serializers.ValidationError:
            raise serializers.ValidationError("Hotel context is required.")
        except Exception as e:
            raise serializers.ValidationError(f"Error creating room type: {e}")
        


class RatePlanCreateSerializer(HotelContextMixin, serializers.ModelSerializer):
    """Serializer for creating rate plans without requiring room_type upfront"""
    
    class Meta:
        model = RatePlan
        fields = [
            "name",
            "base_rate",
            "occupancy",
            "tags",
            "includes_breakfast",
            "includes_lunch",
            "includes_dinner",
            "ota_code",
            "surge_percentage",
            "group_discount_percentage",
            "long_term_discount_percentage",
            "min_stay_days",
            "valid_from",
            "valid_to",
            "is_active",
        ]

    def create(self, validated_data):
        # Validate context
        self.ensure_hotel_context()
        return super().create(validated_data)

    def validate(self, attrs):
        base_rate = attrs.get("base_rate")
        if base_rate is not None and base_rate < 0:
            raise serializers.ValidationError({"base_rate": "Must be >= 0."})
        
        for field in (
            "surge_percentage",
            "group_discount_percentage",
            "long_term_discount_percentage",
        ):
            value = attrs.get(field)
            if value is not None and not (0 <= value <= 100):
                field_label = field.replace("_", " ").title()
                raise serializers.ValidationError(
                    {field: f"{field_label} must be between 0 and 100."}
                )

        valid_from = attrs.get("valid_from")
        valid_to = attrs.get("valid_to")
        if valid_from and valid_to and valid_to < valid_from:
            raise serializers.ValidationError(
                {"valid_to": "Valid to date must be on or after the valid from date."}
            )

        return attrs


class RatePlanSerializer(HotelContextMixin, serializers.ModelSerializer):
    # Expose hotel (property) as read-only derived from room_type
    hotel = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = RatePlan
        # Explicit public API fields only
        fields = [
            "id",
            "hotel",
            "room_type",
            "name",
            "occupancy",
            "tags",
            "includes_breakfast",
            "includes_lunch",
            "includes_dinner",
            "base_rate",
            "ota_code",
            "surge_percentage",
            "group_discount_percentage",
            "long_term_discount_percentage",
            "min_stay_days",
            "valid_from",
            "valid_to",
            "is_active",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "hotel", "created_at", "updated_at"]

    def create(self, validated_data):
        # Validate context
        self.ensure_hotel_context()
        return super().create(validated_data)

    def get_hotel(self, obj):  # pragma: no cover - simple accessor
        try:
            return str(obj.room_type.hotel_id)
        except Exception:
            return None

    def validate_room_type(self, value):
        self.validate_hotel_ownership(value, "room_type")
        return value

    def validate(self, attrs):
        base_rate = attrs.get("base_rate", getattr(self.instance, "base_rate", None))
        if base_rate is not None and base_rate < 0:
            raise serializers.ValidationError({"base_rate": "Must be >= 0."})
        for field in (
            "surge_percentage",
            "group_discount_percentage",
            "long_term_discount_percentage",
        ):
            v = attrs.get(field, getattr(self.instance, field, None))
            if v is not None and (v < 0 or v > 100):
                raise serializers.ValidationError({field: "Must be between 0 and 100."})
        min_days = attrs.get(
            "min_stay_days", getattr(self.instance, "min_stay_days", None)
        )
        if min_days is not None and min_days < 1:
            raise serializers.ValidationError({"min_stay_days": "Must be >= 1."})
        start = attrs.get("valid_from", getattr(self.instance, "valid_from", None))
        end = attrs.get("valid_to", getattr(self.instance, "valid_to", None))
        if start and end and start > end:
            raise serializers.ValidationError(
                {"valid_to": "Must be on or after valid_from."}
            )
        # Prevent past date ranges
        today = timezone.localdate()
        if start and start < today:
            raise serializers.ValidationError(
                {"valid_from": "valid_from cannot be in the past."}
            )
        if end and end < today:
            raise serializers.ValidationError(
                {"valid_to": "valid_to cannot be in the past."}
            )
        return attrs


class CalendarSerializer(HotelContextMixin, serializers.ModelSerializer):
    class Meta:
        model = Calendar
        fields = [
            "id",
            "room_type",
            "rate_plan",
            "date",
            "available_rooms",
            "daily_rate",
            "restrictions",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def validate_room_type(self, value):
        self.validate_hotel_ownership(value, "room_type")
        return value

    def validate(self, attrs):
        room_type = attrs.get("room_type", getattr(self.instance, "room_type", None))
        rate_plan = attrs.get("rate_plan", getattr(self.instance, "rate_plan", None))
        if rate_plan:
            self.validate_hotel_ownership(rate_plan, "rate_plan")
        if (
            rate_plan
            and room_type
            and getattr(rate_plan, "room_type_id", None)
            != getattr(room_type, "id", None)
        ):
            raise serializers.ValidationError(
                {"rate_plan": "rate_plan does not belong to the selected room_type."}
            )

        available = attrs.get(
            "available_rooms", getattr(self.instance, "available_rooms", None)
        )
        rate = attrs.get("daily_rate", getattr(self.instance, "daily_rate", None))
        if rate is not None and rate < 0:
            raise serializers.ValidationError({"daily_rate": "Must be >= 0."})

        date = attrs.get("date", getattr(self.instance, "date", None))
        if rate_plan and date:
            start = getattr(rate_plan, "valid_from", None)
            end = getattr(rate_plan, "valid_to", None)
            if (start and date < start) or (end and date > end):
                raise serializers.ValidationError(
                    {"date": "Date is outside the rate plan validity window."}
                )
        # Prevent creating calendar entries in the past
        if date:
            today = timezone.localdate()
            if date < today:
                raise serializers.ValidationError(
                    {"date": "date cannot be in the past."}
                )
        return attrs


class OTAPlatformSerializer(serializers.ModelSerializer):
    class Meta:
        model = OTAPlatform
        fields = ["id", "name", "api_endpoint", "configuration", "is_active"]
        read_only_fields = ["id"]


class HotelOTAIntegrationSerializer(HotelContextMixin, serializers.ModelSerializer):
    class Meta:
        model = HotelOTAIntegration
        fields = [
            "id",
            "hotel",
            "ota_platform",
            "external_hotel_id",
            "credentials",
            "is_active",
            "created_at",
        ]
        read_only_fields = ["id", "created_at", "hotel"]
        extra_kwargs = {
            "credentials": {"write_only": True},
        }

    def create(self, validated_data):
        hotel = self.ensure_hotel_context()
        validated_data["hotel"] = hotel
        return super().create(validated_data)


class AvailabilityLogSerializer(HotelContextMixin, serializers.ModelSerializer):
    class Meta:
        model = AvailabilityLog
        fields = [
            "id",
            "hotel",
            "room_type",
            "ota_platform",
            "date",
            "available_rooms",
            "sync_status",
            "synced_at",
            "created_at",
        ]
        read_only_fields = ["id", "hotel", "created_at", "synced_at"]

    def validate_room_type(self, value):
        """Validate that room_type belongs to the hotel from context."""
        self.validate_hotel_ownership(value, "room_type")
        return value

    def validate(self, attrs):
        """Validate hotel context and ensure consistency."""
        hotel = self.ensure_hotel_context()

        # Validate that provided hotel (if any) matches context hotel
        provided_hotel = attrs.get("hotel", getattr(self.instance, "hotel", None))
        if provided_hotel and getattr(provided_hotel, "id", None) != getattr(
            hotel, "id", None
        ):
            raise serializers.ValidationError(
                {"hotel": "Provided hotel does not match the current hotel context."}
            )

        # Validate room_type belongs to hotel
        room_type = attrs.get("room_type", getattr(self.instance, "room_type", None))
        if room_type:
            self.validate_hotel_ownership(room_type, "room_type")

        # Validate available_rooms is non-negative
        available_rooms = attrs.get(
            "available_rooms", getattr(self.instance, "available_rooms", None)
        )
        if available_rooms is not None and available_rooms < 0:
            raise serializers.ValidationError(
                {"available_rooms": "Available rooms must be non-negative."}
            )

        return attrs

    def create(self, validated_data):
        """Create availability log with enforced hotel context."""
        hotel = self.ensure_hotel_context()
        # Always use context hotel, ignore any provided hotel value
        validated_data["hotel"] = hotel
        return super().create(validated_data)


class RoomBlockSerializer(HotelContextMixin, serializers.ModelSerializer):
    class Meta:
        model = RoomBlock
        fields = [
            "id",
            "hotel",
            "room",
            "reservation",
            "blocked_from",
            "blocked_until",
            "is_active",
            "reason",
        ]
        read_only_fields = ["id", "hotel"]

    def validate_room(self, value):
        """Validate that room belongs to the hotel from context."""
        self.validate_hotel_ownership(value, "room")
        return value

    def validate_reservation(self, value):
        """Validate that reservation belongs to the hotel from context."""
        self.validate_hotel_ownership(value, "reservation")
        return value

    def validate(self, attrs):
        """Validate room block constraints."""
        blocked_from = attrs.get(
            "blocked_from", getattr(self.instance, "blocked_from", None)
        )
        blocked_until = attrs.get(
            "blocked_until", getattr(self.instance, "blocked_until", None)
        )
        # Reservation / room consistency
        reservation = attrs.get(
            "reservation", getattr(self.instance, "reservation", None)
        )
        room = attrs.get("room", getattr(self.instance, "room", None))
        if (
            reservation is not None
            and getattr(reservation, "room_id", None) is not None
            and room is not None
        ):
            room_id = getattr(room, "id", room)
            # Only flag if both ids are present and differ
            if room_id is not None and reservation.room_id != room_id:
                raise serializers.ValidationError(
                    {"reservation": "Reservation room does not match blocked room."}
                )

        if blocked_from and blocked_until and blocked_until < blocked_from:
            raise serializers.ValidationError(
                {
                    "blocked_until": "Block end time must be after or equal to start time."
                }
            )
        return attrs

    def create(self, validated_data):
        """Create room block with enforced hotel context."""
        hotel = self.ensure_hotel_context()
        # Always use context hotel
        validated_data["hotel"] = hotel
        return super().create(validated_data)


class RoomBlockSyncLogSerializer(HotelContextMixin, serializers.ModelSerializer):
    class Meta:
        model = RoomBlockSyncLog
        fields = [
            "id",
            "room_block",
            "hotel",
            "ota_platform",
            "action",
            "sync_status",
            "request_data",
            "response_data",
            "error_message",
            "retry_count",
            "max_retries",
            "synced_at",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "hotel",
            "sync_status",
            "response_data",
            "error_message",
            "retry_count",
            "synced_at",
            "created_at",
            "updated_at",
        ]

    def validate_room_block(self, value):
        """Validate that room_block belongs to the hotel from context."""
        self.validate_hotel_ownership(value, "room_block")
        return value

    def validate(self, attrs):
        """Validate sync log constraints."""
        hotel = self.ensure_hotel_context()

        # Validate max_retries is reasonable
        max_retries = attrs.get(
            "max_retries", getattr(self.instance, "max_retries", None)
        )
        if max_retries is not None and (max_retries < 0 or max_retries > 10):
            raise serializers.ValidationError(
                {"max_retries": "Max retries must be between 0 and 10."}
            )

        return attrs

    def create(self, validated_data):
        """Create sync log with enforced hotel context."""
        hotel = self.ensure_hotel_context()
        # Always use context hotel
        validated_data["hotel"] = hotel
        return super().create(validated_data)


class RoomTypeCreateSerializer(HotelContextMixin, serializers.ModelSerializer):
    """
    Serializer for creating RoomType with associated rooms and rate plan.
    
    Expected input:
    {
        "name": "Deluxe Room",
        "description": "A comfortable deluxe room with modern amenities",
        "max_occupancy": 2,
        "ota_code": "DELUXE",
        "amenities": ["AC", "TV", "WIFI"],
        "roomNumbers": ["101", "102", "103"]
    }
    """
    roomNumbers = serializers.ListField(
        child=serializers.CharField(max_length=100),
        write_only=True,
        help_text="List of room numbers to create for this room type"
    )
    rooms_created = serializers.SerializerMethodField(read_only=True)
    ota_code = serializers.CharField(required=False)
    
    class Meta:
        model = RoomType
        fields = [
            "id",
            "name", 
            "description",
            "max_occupancy",
            "ota_code",
            "amenities",
            "roomNumbers",
            "rooms_created",
            "created_at",
            "updated_at"
        ]
        read_only_fields = ["id", "created_at", "updated_at", "rooms_created"]
    
    def get_rooms_created(self, obj):
        """Return count of rooms created for this room type"""
        return obj.rooms.count() if hasattr(obj, 'rooms') else 0
    
    def validate_roomNumbers(self, value):
        """Validate room numbers list"""
        if not value or len(value) == 0:
            raise serializers.ValidationError("At least one room number must be provided.")
        
        # Check for duplicates in the list
        if len(value) != len(set(value)):
            duplicates = [x for x in value if value.count(x) > 1]
            raise serializers.ValidationError(
                f"Duplicate room numbers found: {list(set(duplicates))}. Room numbers must be unique."
            )
        
        # Validate room number format
        for room_no in value:
            if not room_no.strip():
                raise serializers.ValidationError("Room numbers cannot be empty or contain only whitespace.")
            if len(room_no.strip()) > 100:
                raise serializers.ValidationError("Room numbers cannot exceed 100 characters.")
        
        # Check if room numbers already exist for this property
        hotel = self.get_hotel_from_context()
        if hotel:
            from stay.models import Room
            existing_rooms = Room.objects.filter(
                property=hotel, 
                room_no__in=value
            ).values_list('room_no', flat=True)
            
            if existing_rooms:
                raise serializers.ValidationError(
                    f"Room numbers {list(existing_rooms)} already exist for this property."
                )
        
        return value
    
    def validate_name(self, value):
        """Validate room type name"""
        if not value.strip():
            raise serializers.ValidationError("Room type name cannot be empty.")
        
        # Check for duplicate room type name in the same hotel
        hotel = self.get_hotel_from_context()
        if hotel:
            existing = RoomType.objects.filter(hotel=hotel, name=value.strip()).exists()
            if existing:
                raise serializers.ValidationError(
                    f"Room type with name '{value.strip()}' already exists for this property."
                )
        
        return value.strip()
    
    def validate_max_occupancy(self, value):
        """Validate max occupancy"""
        if value is None or value < 1:
            raise serializers.ValidationError("max_occupancy must be >= 1.")
        if value > 20:  # Reasonable upper limit
            raise serializers.ValidationError("max_occupancy cannot exceed 20.")
        return value
    
    def create(self, validated_data):
        from stay.models import Room
        from django.db import transaction
        import logging
        
        logger = logging.getLogger(__name__)
        
        # Get hotel from context
        hotel = self.ensure_hotel_context()
        
        # Extract nested data
        room_numbers = validated_data.pop('roomNumbers')
        
        # Set hotel for room type
        validated_data['hotel'] = hotel
        
        try:
            with transaction.atomic():
                # Create the room type
                room_type = super().create(validated_data)
                logger.info(f"Created room type '{room_type.name}' with ID {room_type.id}")
                
                # Create individual rooms
                rooms_created = []
                for i, room_no in enumerate(room_numbers):
                    try:
                        room = Room.objects.create(
                            room_no=room_no.strip(),
                            property=hotel,
                            room_type=room_type,
                            description=validated_data.get('description', ''),
                            max_guests=validated_data.get('max_occupancy', 1),
                            amenities=validated_data.get('amenities', [])
                        )
                        rooms_created.append(room)
                        logger.debug(f"Created room {room_no} for room type {room_type.id}")
                        
                    except Exception as e:
                        logger.error(f"Failed to create room {room_no}: {str(e)}")
                        raise serializers.ValidationError({
                            'roomNumbers': f"Failed to create room '{room_no}': {str(e)}"
                        })
                
                # Store created rooms and rate plan for the response
                room_type._created_rooms = rooms_created
                
                logger.info(f"Successfully created room type '{room_type.name}' with {len(rooms_created)} rooms")
                
        except serializers.ValidationError:
            # Re-raise validation errors as-is
            raise
        except Exception as e:
            logger.error(f"Unexpected error during room type creation: {str(e)}", exc_info=True)
            raise serializers.ValidationError({
                'non_field_errors': f"Failed to create room type: {str(e)}"
            })
            
        return room_type
