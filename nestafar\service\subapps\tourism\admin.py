from django.contrib import admin
from .models import (
    TourismService,
    TourismServiceItem,
    ItinerarySpot,
    TourismCart,
    TourismCartItems,
    TourismOrder,
    TourismOrderItem,
)


@admin.register(TourismService)
class TourismServiceAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "local",
        "per_person",
        "vehicle_type",
        "disclaimer",
        "created_at",
        "updated_at",
    )
    list_filter = ("local", "per_person", "vehicle_type", "created_at", "updated_at")
    search_fields = ("name", "disclaimer")
    ordering = ("-updated_at",)


@admin.register(TourismServiceItem)
class TourismServiceItemAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "service",
        "start_time",
        "duration",
        "duration_type",
        "created_at",
        "updated_at",
    )
    list_filter = ("service", "duration_type", "created_at", "updated_at")
    search_fields = ("name", "service__name")
    ordering = ("-updated_at",)


@admin.register(ItinerarySpot)
class ItinerarySpotAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "location",
        "package",
        "stop_no",
        "wait_time",
        "duration_type",
    )
    list_filter = ("location", "package", "duration_type")
    search_fields = ("name", "location__name", "package__name")
    ordering = ("name",)


@admin.register(TourismCart)
class TourismCartAdmin(admin.ModelAdmin):
    list_display = (
        "guest",
        "pickup_time",
        "special_request",
        "subtotal",
        "taxes",
        "charges",
        "total",
        "created_at",
        "updated_at",
    )
    list_filter = ("pickup_time", "created_at", "updated_at")
    search_fields = ("guest__user__name",)
    ordering = ("-updated_at",)


@admin.register(TourismCartItems)
class TourismCartItemsAdmin(admin.ModelAdmin):
    list_display = ("item", "cart", "quantity", "price", "created_at", "updated_at")
    list_filter = ("cart", "item", "created_at", "updated_at")
    search_fields = ("item__name", "cart__guest__user__name")
    ordering = ("-updated_at",)


@admin.register(TourismOrder)
class TourismOrderAdmin(admin.ModelAdmin):
    list_display = (
        "guest",
        "service",
        "cart",
        "pickup_time",
        "special_request",
        "subtotal",
        "commissions",
        "taxes",
        "charges",
        "total",
        "created_at",
        "updated_at",
    )
    list_filter = ("service", "guest", "pickup_time", "created_at", "updated_at")
    search_fields = ("guest__user__name", "service__name")
    ordering = ("-updated_at",)


@admin.register(TourismOrderItem)
class TourismOrderItemAdmin(admin.ModelAdmin):
    list_display = ("item", "order", "quantity", "price", "created_at", "updated_at")
    list_filter = ("order", "item", "created_at", "updated_at")
    search_fields = ("item__name", "order__guest__user__name", "order__service__name")
    ordering = ("-updated_at",)
