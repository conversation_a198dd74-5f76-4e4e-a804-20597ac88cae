import logging
import requests
from django.conf import settings
from .base_channel import BaseChannel
from core.models import User
from ..models import WhatsAppContact

logger = logging.getLogger(__name__)


class WhatsAppChannel(BaseChannel):
    """
    WhatsApp Business API Channel for sending notifications
    Uses Meta's WhatsApp Business API v22.0 to send messages
    """

    def __init__(self):
        try:
            self.access_token = settings.WHATSAPP_ACCESS_TOKEN
            self.phone_number_id = settings.WHATSAPP_PHONE_NUMBER_ID
            self.api_version = getattr(settings, "WHATSAPP_API_VERSION", "v22.0")
            self.base_url = f"https://graph.facebook.com/{self.api_version}"

            if not self.access_token:
                logger.error("WHATSAPP_ACCESS_TOKEN is missing in settings")
                raise ValueError("WHATSAPP_ACCESS_TOKEN is missing in settings")

            if not self.phone_number_id:
                logger.error("WHATSAPP_PHONE_NUMBER_ID is missing in settings")
                raise ValueError("WHATSAPP_PHONE_NUMBER_ID is missing in settings")

            logger.info(
                f"WhatsApp channel initialized with API version {self.api_version}"
            )
            logger.debug(f"Phone Number ID: {'*' * min(len(self.phone_number_id), 8)}")
            logger.debug("Access Token: [REDACTED]")
        except AttributeError as e:
            logger.error(f"WhatsApp settings missing: {str(e)}")
            raise ValueError(f"WhatsApp credentials are missing in settings: {str(e)}")
        except Exception as e:
            logger.error(f"Error initializing WhatsApp channel: {str(e)}")
            raise

    def get_user_whatsapp_number(self, user_id):
        """Get the WhatsApp contact for a user"""
        try:
            user = User.objects.get(id=user_id)
            # First try to get from WhatsApp contacts
            whatsapp_contact = WhatsAppContact.objects.filter(
                user=user, is_active=True
            ).first()

            if whatsapp_contact:
                return str(whatsapp_contact.phone_number)

            # Fallback to user's primary phone number
            return str(user.phone)
        except User.DoesNotExist:
            logger.error(f"User with ID {user_id} not found")
            return None

    def format_phone_number(self, phone_number):
        """Format phone number for WhatsApp API

        WhatsApp API requires phone numbers in international format without + sign
        For example: +2347088212727 becomes 2347088212727
        """
        try:
            if not phone_number:
                logger.error("Empty phone number provided")
                return None

            # Convert to string and strip whitespace
            phone_str = str(phone_number).strip()

            # If it starts with +, keep the rest as is (already in international format)
            if phone_str.startswith("+"):
                # Remove the + but keep country code
                formatted = phone_str[1:]
                logger.debug(f"Phone number formatted from {phone_str} to {formatted}")
                return formatted

            # Remove any non-digit characters
            cleaned = "".join(filter(str.isdigit, phone_str))

            # Add country code if not present
            if len(cleaned) == 10:  # Local number without country code
                default_country_code = getattr(settings, "DEFAULT_COUNTRY_CODE", "91")
                formatted = default_country_code + cleaned
                logger.debug(
                    f"Added country code {default_country_code} to {cleaned}: {formatted}"
                )
                return formatted
            elif len(cleaned) >= 11:  # Already has country code
                logger.debug(f"Phone number already has country code: {cleaned}")
                return cleaned
            else:
                logger.error(f"Invalid phone number length: {phone_str}")
                return None

        except Exception as e:
            logger.error(f"Error formatting phone number {phone_number}: {str(e)}")
            return None

    def create_message(self, user_id, title, body):
        """Create WhatsApp message payload"""
        try:
            logger.info(f"Creating WhatsApp message for user {user_id}")
            phone_number = self.get_user_whatsapp_number(user_id)

            if not phone_number:
                logger.error(f"No WhatsApp number found for user {user_id}")
                return False

            logger.info(f"Raw phone number from database: {phone_number}")
            formatted_number = self.format_phone_number(phone_number)

            if not formatted_number:
                logger.error(f"Failed to format phone number: {phone_number}")
                return False

            logger.info(
                f"Sending WhatsApp message to formatted number: {formatted_number}"
            )

            # Combine title and body for WhatsApp message
            message_text = f"*{title}*\n\n{body}"

            message_payload = {
                "messaging_product": "whatsapp",
                "to": formatted_number,
                "type": "text",
                "text": {"body": message_text},
            }
            logger.debug(f"WhatsApp payload created: {message_payload}")
            return message_payload

        except Exception as e:
            logger.error(
                f"Error creating WhatsApp message for user {user_id}: {str(e)}",
                exc_info=True,
            )
            return False

    def send(self, message_payload):
        """Send WhatsApp message using Meta's API"""
        url = f"{self.base_url}/{self.phone_number_id}/messages"

        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }

        try:
            logger.info(f"Sending WhatsApp API request to {url}")
            logger.info(
                f"Headers: Authorization: Bearer ***[REDACTED]***, Content-Type: application/json"
            )
            logger.info(f"Payload: {message_payload}")

            response = requests.post(
                url, headers=headers, json=message_payload, timeout=30
            )
            logger.info(f"WhatsApp API response status: {response.status_code}")
            logger.info(f"WhatsApp API response headers: {dict(response.headers)}")
            logger.info(f"WhatsApp API response body: {response.text}")

            if response.status_code == 200:
                response_data = response.json()
                messages = response_data.get("messages", [])
                message_id = messages[0].get("id") if messages else None
                if not message_id:
                    logger.warning("No message ID returned in successful response")
                logger.info(
                    f"WhatsApp message sent successfully. Message ID: {message_id}"
                )
                logger.info(f"Full response: {response_data}")
                return True, message_id
            else:
                error_msg = (
                    f"WhatsApp API error: {response.status_code} - {response.text}"
                )
                logger.error(error_msg)
                logger.error(f"Response headers: {response.headers}")
                return False, error_msg

        except requests.exceptions.Timeout as e:
            error_msg = f"Timeout error while sending WhatsApp message: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        except requests.exceptions.ConnectionError as e:
            error_msg = f"Connection error while sending WhatsApp message: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        except requests.exceptions.RequestException as e:
            error_msg = f"Network error while sending WhatsApp message: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"Unexpected error while sending WhatsApp message: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return False, error_msg

    def send_message(self, user_id, message, title=None, category=None, **kwargs):
        """Main method to send WhatsApp message with support for templates

        WhatsApp Business API requires template messages for business communications.
        This method prioritizes template messages and only falls back to text for testing purposes.
        """
        try:
            # Handle User object or user_id
            if hasattr(user_id, "id"):
                actual_user_id = user_id.id
            else:
                actual_user_id = user_id

            logger.info(f"Attempting to send WhatsApp message to user {actual_user_id}")

            # ALWAYS try to send as template first if category is provided
            if category:
                template_name = self._get_template_name(category)
                if template_name:
                    components = self._build_template_components(category, **kwargs)
                    success, result = self.send_template_message(
                        actual_user_id, template_name, components
                    )
                    if success:
                        logger.info(
                            f"✅ Template message sent successfully to user {actual_user_id}"
                        )
                        return True, "Template message sent successfully", result
                    else:
                        logger.error(
                            f"❌ Template message failed for user {actual_user_id}: {result}"
                        )
                        # For WhatsApp Business API, template failure means the message won't be delivered
                        # Do NOT fall back to text messages as they won't be delivered for business accounts
                        return False, f"Template message failed: {result}", None
                else:
                    logger.error(
                        f"❌ No template mapping found for category: {category}"
                    )
                    return (
                        False,
                        f"No template mapping found for category: {category}",
                        None,
                    )

            # Only send regular text messages for testing or if no category is provided
            # WARNING: Regular text messages are NOT delivered by WhatsApp Business API
            # unless sent within 24 hours of receiving a message from the user
            logger.warning(
                f"⚠️ Sending regular text message to user {actual_user_id} - may not be delivered!"
            )
            logger.warning(
                "⚠️ WhatsApp Business API requires template messages for business communications"
            )

            message_payload = self.create_message(
                actual_user_id, title or "Notification", message
            )
            if not message_payload:
                error_msg = (
                    f"Failed to create WhatsApp message for user {actual_user_id}"
                )
                logger.error(error_msg)
                return False, error_msg, None

            success, result = self.send(message_payload)

            if success:
                logger.warning(
                    f"📤 Text message sent to API for user {actual_user_id}, but delivery NOT guaranteed: {result}"
                )
                return True, "Text message sent (delivery not guaranteed)", result
            else:
                logger.error(
                    f"Failed to send text message to user {actual_user_id}: {result}"
                )
                return False, result, None
        except Exception as e:
            error_msg = f"Error in send_message for user {user_id}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return False, error_msg, None

    def _get_template_name(self, category):
        """Map category to WhatsApp template name

        Maps notification categories to exact WhatsApp template names.
        Only templates that exist and are approved in WhatsApp Business Manager will work.
        """
        template_mapping = {
            # Working templates (✅ ACTIVE in WhatsApp Business Manager)
            "USER_CHECKIN_INITIATED": "checkin_initiated",
            "USER_ORDER_COMPLETED": "order_completed",
            "USER_ORDER_PLACED": "order_placed",
            "PRECHECKIN_CREATED": "precheckin_created",
            # Templates that need to be created in WhatsApp Business Manager (❌ NOT CONFIGURED)
            "USER_CHECKOUT": "checkout",
            "USER_ORDER_ACCEPTED": "order_accepted",
            "USER_ORDER_CANCELLED": "order_cancelled",
            "USER_ORDER_ONGOING": "order_ongoing",
            "USER_ORDER_REJECTED": "order_rejected",
            # Partner templates
            "PARTNER_ORDER_PLACED": "partner_order_placed",
            "PARTNER_ORDER_ACCEPTED": "partner_order_accepted",
            "PARTNER_ORDER_ONGOING": "partner_order_ongoing",
            "PARTNER_ORDER_COMPLETED": "partner_order_completed",
            "PARTNER_ORDER_CANCELLED": "partner_order_cancelled",
            "PARTNER_ORDER_REJECTED": "partner_order_rejected",
            # Checkin flow templates
            "PRECHECKIN_CONFIRMED": "precheckin_confirmed",
            "PRECHECKIN_STATUS_CHANGED": "precheckin_status_changed",
            "SIGNUP_SUCCESSFUL": "signup_successful",
            "ONBOARDING_REMINDER": "onboarding_reminder",
            "ONBOARDING_COMPLETED": "onboarding_completed",
            "PRECHECKIN_REMINDER": "precheckin_reminder",
            "PRECHECKIN_CANCELLATION_WARNING": "precheckin_cancellation_warning",
            "ROOM_ALLOTMENT": "room_allotment",
            "GUEST_ARRIVED_WELCOME": "guest_arrived_welcome",
            "CHECKIN_SUCCESSFUL": "checkin_successful",
            "CHECKOUT_BILLS": "checkout_bills",
            "REVIEW_REQUEST": "review_request",
            # External reservation alert template
            "EXTERNAL_RESERVATION_ALERT": "external_reservation_alert",
            # Service templates
            "NEW_SERVICE_AVAILABLE": "new_service_available",
            "DINNER_REMINDER": "dinner_reminder",
            "VENDOR_ORDER_REMINDER": "vendor_order_reminder",
            "ORDER_CONFIRMED": "order_confirmed",
            "ORDER_READY": "order_ready",
            "VENDOR_NEW_ORDER": "vendor_new_order",
            "SERVICE_HIDDEN_NOTIFICATION": "service_hidden_notification",
            "SERVICE_RESTORED_NOTIFICATION": "service_restored_notification",
            # Additional service templates
            "WELCOME_MESSAGE": "welcome_message",
            "CHECKOUT_REMINDER": "checkout_reminder",
            "CHECKOUT_SUCCESSFUL": "checkout_successful",
            "SERVICE_REQUEST_RECEIVED": "service_request_received",
            "SERVICE_IN_PROGRESS": "service_in_progress",
            "SERVICE_COMPLETED": "service_completed",
            "FOOD_ORDER_PLACED": "food_order_placed",
            "FOOD_ORDER_CONFIRMED": "food_order_confirmed",
            "FOOD_ORDER_READY": "food_order_ready",
            # Job management templates
            "JOB_ASSIGNED": "job_assigned",
            "JOB_STATUS_CHANGED": "job_status_changed",
            "DELIVERY_ASSIGNED": "delivery_assigned",
            # Summary and reporting templates
            "DAILY_SUMMARY_GUEST": "daily_summary_guest",
            "DAILY_SUMMARY_PARTNER": "daily_summary_partner",
            "WEEKLY_REPORT": "weekly_report",
            "RATING_REQUEST": "rating_request",
            "DAILY_SUMMARY": "daily_summary",
            "WEEKLY_SUMMARY": "weekly_summary",
            "MONTHLY_SUMMARY": "monthly_summary",
            # Payment system templates (New - Need to be created in WhatsApp Business Manager)
            "PAYMENT_LINK_CREATED": "payment_link_created",
            "PAYMENT_SUCCESS": "payment_success",
            "PAYMENT_FAILED": "payment_failed",
            "PAYMENT_REMINDER": "payment_reminder",
            "TRANSFER_SUCCESS": "transfer_success",
            "TRANSFER_FAILED": "transfer_failed",
            # Account management templates (New - Need to be created in WhatsApp Business Manager)
            "ACCOUNT_ACTIVATED": "account_activated",
            "ACCOUNT_SUSPENDED": "account_suspended",
            "ACCOUNT_UNDER_REVIEW": "account_under_review",
            "KYC_VERIFIED": "kyc_verified",
            "KYC_REJECTED": "kyc_rejected",
            "KYC_UNDER_REVIEW": "kyc_under_review",
        }

        template_name = template_mapping.get(category)
        if template_name:
            logger.info(
                f"📋 Mapped category '{category}' to template '{template_name}'"
            )
        else:
            logger.warning(f"⚠️ No template mapping found for category '{category}'")

        return template_name

    def _build_template_components(self, category, **kwargs):
        """Build template components based on category and kwargs"""
        components = []

        # Build header parameters if supported
        header_params = self._build_header_parameters(category, **kwargs)
        if header_params:
            components.append({"type": "header", "parameters": header_params})

        # Build body parameters
        body_params = self._build_body_parameters(category, **kwargs)
        if body_params:
            components.append({"type": "body", "parameters": body_params})

        # Build interactive buttons if supported
        buttons = self._build_interactive_buttons(category, **kwargs)
        if buttons:
            # For button components, each button needs to be a separate component with its own index
            for i, button in enumerate(buttons):
                components.append({
                    "type": "button",
                    "sub_type": "quick_reply",
                    "index": str(i),
                    "parameters": [button]
                })

        # Always return components list, even if empty
        # WhatsApp API expects this structure
        return components

    def _build_header_parameters(self, category, **kwargs):
        """Build header parameters for templates that need them"""
        header_params = []

        # Define header parameter mappings for templates that need them
        # Only include templates that actually have header variables in WhatsApp Business Manager
        header_configs = {
            "PRECHECKIN_REMINDER": ["guest_name"],  # Header with guest name
            # Remove CHECKOUT_BILLS, WEEKLY_REPORT, ONBOARDING_REMINDER until confirmed they have headers
        }

        param_names = header_configs.get(category, [])
        
        for param_name in param_names:
            if param_name in kwargs and kwargs[param_name] is not None:
                # Format the parameter value
                value = self._format_parameter_value(param_name, kwargs[param_name])
                header_params.append({"type": "text", "text": str(value)})

        return header_params

    def _build_body_parameters(self, category, **kwargs):
        """Build body parameters for template"""
        body_params = []

        # Define parameter order for each template category
        parameter_order = {
            "USER_CHECKIN_INITIATED": ["username", "room_no", "property_name"],
            "USER_ORDER_COMPLETED": ["username", "order_id"],
            "USER_ORDER_PLACED": ["username", "order_id"],
            "PRECHECKIN_CREATED": [
                "guest_name",
                "property_owner_name",
                "expected_date",
                "room_number",
                "precheckin_link",
            ],
            "PRECHECKIN_CONFIRMED": [
                "guest_name",
                "property_owner_name", 
                "expected_date",
                "room_number",
            ],
            "SIGNUP_SUCCESSFUL": ["partner_name", "property_name"],
            "ONBOARDING_REMINDER": [
                "partner_name",
                "property_name",
                "missing_steps",
                "completion_percentage",
            ],
            "ONBOARDING_COMPLETED": ["partner_name", "property_name"],
            "PRECHECKIN_REMINDER": [
                "guest_name",
                "property_name",
                "checkin_date",
                "hours_remaining",
            ],
            "PRECHECKIN_CANCELLATION_WARNING": [
                "guest_name",
                "property_name",
                "hours_remaining",
            ],
            "ROOM_ALLOTMENT": [
                "guest_name",
                "property_name",
                "room_number",
                "checkin_date",
            ],
            "GUEST_ARRIVED_WELCOME": ["guest_name", "property_name", "room_number"],
            "CHECKOUT_BILLS": [
                "guest_name",
                "property_name",
                "checkout_date",
                "total_amount",
            ],
            "REVIEW_REQUEST": ["guest_name", "property_name"],
            # External reservation alert for property staff
            "EXTERNAL_RESERVATION_ALERT": [
                "guest_name",
                "property_name",
                "checkin_date",
                "checkout_date",
                "guest_count",
                "room_type",
                "booking_source",
                "total_amount",
                "external_booking_id",
            ],
            "NEW_SERVICE_AVAILABLE": [
                "guest_name",
                "service_name",
                "service_type",
                "property_name",
            ],
            "DINNER_REMINDER": ["guest_name", "property_name"],
            "VENDOR_ORDER_REMINDER": [
                "vendor_name",
                "order_id", 
                "minutes_pending",
            ],
            "SERVICE_HIDDEN_NOTIFICATION": [
                "guest_name",
                "service_name",
                "property_name",
            ],
            "SERVICE_RESTORED_NOTIFICATION": [
                "guest_name",
                "service_name",
                "property_name",
            ],
            "WEEKLY_REPORT": [
                "partner_name",
                "property_name",
                "week_start",
                "week_end",
                "reservations",
                "occupancy_rate",
                "avg_orders",
                "gmv",
                "commission",
                "recommendations",
            ],
            "USER_ORDER_ACCEPTED": [
                "username",
                "order_id",
                "vendor_name",
                "estimated_time",
                "total_amount",
                "vendor_contact",
            ],
            "USER_ORDER_CANCELLED": [
                "username",
                "order_id",
                "service_type",
                "reason",
                "refund_amount",
                "additional_info",
            ],
            "USER_ORDER_ONGOING": [
                "username",
                "order_id",
                "vendor_name",
                "estimated_time",
                "contact_number",
            ],
            "USER_ORDER_REJECTED": [
                "username",
                "order_id",
                "service_type",
                "rejection_reason",
                "refund_amount",
            ],
            # Partner order templates
            "PARTNER_ORDER_PLACED": [
                "partner_name",
                "order_id",
                "room_no",
                "guest_name",
            ],
            "PARTNER_ORDER_ACCEPTED": [
                "partner_name",
                "order_id",
                "room_no",
                "guest_name",
            ],
            "PARTNER_ORDER_ONGOING": [
                "partner_name",
                "order_id",
                "room_no",
                "guest_name",
                "estimated_completion",
            ],
            "PARTNER_ORDER_COMPLETED": [
                "partner_name",
                "order_id",
                "room_no",
                "guest_name",
            ],
            "PARTNER_ORDER_CANCELLED": [
                "partner_name",
                "order_id",
                "room_no",
                "guest_name",
            ],
            "PARTNER_ORDER_REJECTED": [
                "partner_name",
                "order_id",
                "room_no",
                "guest_name",
                "rejection_reason",
                "refund_amount",
                "dashboard_link",
            ],
            # Summary templates
            "DAILY_SUMMARY_GUEST": [
                "username",
                "total_orders",
                "total_spent",
                "property_name",
                "most_ordered_service",
            ],
            "DAILY_SUMMARY_PARTNER": [
                "partner_name",
                "total_orders",
                "total_revenue",
                "property_name",
            ],
            # Additional service templates
            "WELCOME_MESSAGE": [
                "guest_name",
                "property_name",
                "room_number",
                "wifi_password",
            ],
            "CHECKOUT_REMINDER": [
                "guest_name",
                "property_name",
                "checkout_time",
                "extension_available",
            ],
            "CHECKOUT_SUCCESSFUL": [
                "guest_name",
                "property_name",
                "total_amount",
                "payment_method",
            ],
            "SERVICE_REQUEST_RECEIVED": [
                "guest_name",
                "service_name",
                "estimated_time",
                "property_name",
            ],
            "SERVICE_IN_PROGRESS": [
                "guest_name",
                "service_name",
                "eta",
                "property_name",
            ],
            "SERVICE_COMPLETED": [
                "guest_name",
                "service_name",
                "completion_time",
                "property_name",
            ],
            "FOOD_ORDER_PLACED": [
                "guest_name",
                "property_name",
                "order_items",
                "total_amount",
            ],
            "FOOD_ORDER_CONFIRMED": [
                "guest_name",
                "property_name",
                "order_items",
                "delivery_time",
            ],
            "FOOD_ORDER_READY": [
                "guest_name",
                "property_name",
                "order_items",
                "total_amount",
            ],
            # Job management templates
            "JOB_ASSIGNED": [
                "staff_name",
                "job_id",
                "job_type",
                "room",
                "pickup_location",
                "dropoff_location",
                "earnings",
            ],
            "JOB_STATUS_CHANGED": ["staff_name", "job_id", "new_status"],
            "DELIVERY_ASSIGNED": [
                "staff_name",
                "job_id",
                "pickup_location",
                "dropoff_location",
                "earnings",
            ],
            "RATING_REQUEST": ["guest_name", "property_name"],
            "DAILY_SUMMARY": [
                "property_name",
                "total_checkins",
                "total_checkouts",
                "occupancy_rate",
                "total_revenue",
            ],
            "WEEKLY_SUMMARY": [
                "property_name",
                "week_start",
                "total_guests",
                "average_occupancy",
                "total_revenue",
            ],
            "MONTHLY_SUMMARY": [
                "property_name",
                "month",
                "total_bookings",
                "total_revenue",
                "top_service",
            ],
            # Payment system templates - NEW
            "PAYMENT_LINK_CREATED": [
                "customer_name",
                "context_type", 
                "amount",
                "property_name",
                "reference_number",
                "payment_url",
            ],
            "PAYMENT_SUCCESS": [
                "customer_name",
                "amount",
                "property_name",
                "payment_method",
                "reference_number", 
                "payment_id",
            ],
            "PAYMENT_FAILED": [
                "customer_name",
                "amount",
                "property_name",
                "reference_number",
                "error_reason",
                "retry_url",
            ],
            "PAYMENT_REMINDER": [
                "customer_name",
                "amount",
                "property_name",
                "reference_number",
                "hours_remaining",
                "payment_url",
            ],
            "TRANSFER_SUCCESS": [
                "recipient_name",
                "amount",
                "payment_reference",
                "transfer_id",
                "settlement_date",
                "recipient_type",
            ],
            "TRANSFER_FAILED": [
                "recipient_name",
                "amount",
                "payment_reference",
                "recipient_type",
                "error_message",
            ],
            # Account management templates - NEW
            "ACCOUNT_ACTIVATED": [
                "partner_name",
                "account_id",
                "activation_date",
                "bank_account_details",
                "dashboard_url",
                "support_contact",
            ],
            "ACCOUNT_SUSPENDED": [
                "partner_name",
                "account_id",
                "suspension_reason",
                "suspension_date",
                "appeal_process",
                "support_contact",
            ],
            "ACCOUNT_UNDER_REVIEW": [
                "partner_name",
                "account_id",
                "review_reason",
                "estimated_duration",
                "review_date",
                "support_contact",
            ],
            "KYC_VERIFIED": [
                "partner_name",
                "document_type",
                "verification_date",
                "account_status",
                "dashboard_url",
                "next_steps",
            ],
            "KYC_REJECTED": [
                "partner_name",
                "document_type",
                "rejection_reason",
                "resubmission_url",
                "support_contact",
                "deadline",
            ],
            "KYC_UNDER_REVIEW": [
                "partner_name",
                "document_type",
                "submission_date",
                "estimated_duration",
                "reference_id",
                "support_contact",
            ],
        }

        # Get ordered parameters for this category
        ordered_params = parameter_order.get(category, [])

        # If no predefined order, use all provided kwargs in alphabetical order
        if not ordered_params:
            ordered_params = sorted(kwargs.keys())

        # Build parameters in the correct order
        for param_name in ordered_params:
            if param_name in kwargs and kwargs[param_name] is not None:
                # Handle special formatting
                value = self._format_parameter_value(param_name, kwargs[param_name])
                body_params.append({"type": "text", "text": str(value)})

        return body_params

    def _format_parameter_value(self, param_name, value):
        """Format parameter values for WhatsApp templates"""
        # Handle list parameters (like missing_steps, recommendations)
        if isinstance(value, list):
            if param_name in ["missing_steps", "recommendations"]:
                # Use comma separation instead of newlines for WhatsApp compatibility
                return ", ".join([f"• {item}" for item in value])
            else:
                return ", ".join(str(item) for item in value)

        # Handle currency formatting
        if param_name in ["total_amount", "refund_amount", "gmv", "commission"]:
            try:
                # Ensure proper currency formatting with ₹ symbol
                amount = float(str(value).replace(",", "").replace("₹", ""))
                return f"₹{amount:,.2f}"
            except (ValueError, TypeError):
                return str(value)

        # Handle percentage formatting
        if param_name in ["completion_percentage", "occupancy_rate"]:
            try:
                percentage = float(str(value).replace("%", ""))
                return f"{percentage:.1f}%"
            except (ValueError, TypeError):
                return str(value)

        return str(value)

    def _build_interactive_buttons(self, category, **kwargs):
        """Build interactive buttons for templates"""
        button_configs = {
            # Only include templates that actually have button components in WhatsApp Business Manager
            # Remove CHECKOUT_BILLS, ONBOARDING_REMINDER, VENDOR_ORDER_REMINDER, WEEKLY_REPORT until confirmed
            "REVIEW_REQUEST": [
                {"type": "text", "text": "Give Review"},
                {"type": "text", "text": "Rate Stay"},
            ],
        }

        return button_configs.get(category, [])

    def send_template_message(self, user_id, template_name, template_components=None):
        """Send WhatsApp template message"""
        phone_number = self.get_user_whatsapp_number(user_id)

        if not phone_number:
            return False, f"No WhatsApp number found for user {user_id}"

        formatted_number = self.format_phone_number(phone_number)

        payload = {
            "messaging_product": "whatsapp",
            "to": formatted_number,
            "type": "template",
            "template": {"name": template_name, "language": {"code": "en"}},
        }

        # Only add components if they exist and are not None
        if template_components is not None:
            payload["template"]["components"] = template_components

        url = f"{self.base_url}/{self.phone_number_id}/messages"

        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
        }

        try:
            logger.info(
                f"Sending WhatsApp template message: {template_name} to {formatted_number}"
            )
            logger.debug(f"Template payload: {payload}")

            response = requests.post(url, headers=headers, json=payload, timeout=30)

            if response.status_code == 200:
                response_data = response.json()
                message_id = response_data.get("messages", [{}])[0].get("id")
                logger.info(
                    f"WhatsApp template message sent successfully. Message ID: {message_id}"
                )
                return True, message_id
            else:
                error_msg = f"WhatsApp template message error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return False, error_msg

        except requests.exceptions.RequestException as e:
            error_msg = (
                f"Network error while sending WhatsApp template message: {str(e)}"
            )
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = (
                f"Unexpected error while sending WhatsApp template message: {str(e)}"
            )
            logger.error(error_msg)
            return False, error_msg

    def get_message_status(self, message_id):
        """Get the status of a sent message"""
        url = f"{self.base_url}/{message_id}"

        headers = {
            "Authorization": f"Bearer {self.access_token}",
        }

        try:
            response = requests.get(url, headers=headers, timeout=30)

            if response.status_code == 200:
                return True, response.json()
            else:
                error_msg = f"Error getting message status: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return False, error_msg

        except requests.exceptions.RequestException as e:
            error_msg = f"Network error while getting message status: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"Unexpected error while getting message status: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def send_template_message_to_phone(
        self, phone_number, template_name, template_components=None
    ):
        """Send WhatsApp template message directly to a phone number (for testing)"""
        if not phone_number:
            return False, "No phone number provided"

        # Create a mock user object with the phone number
        class MockUser:
            def __init__(self, phone):
                self.id = "test_phone_user"
                self.phone = phone

        # Override get_user_whatsapp_number to return the provided phone
        original_method = self.get_user_whatsapp_number
        self.get_user_whatsapp_number = lambda user_id: str(phone_number)

        try:
            return self.send_template_message(
                "test_phone_user", template_name, template_components
            )
        finally:
            self.get_user_whatsapp_number = original_method

    def send_message_to_phone(
        self, phone_number, message, title=None, category=None, **kwargs
    ):
        """Send WhatsApp message directly to a phone number

        Prioritizes template messages for business communications.
        Falls back to text only for testing purposes.
        """

        if not phone_number:
            logger.error("No phone number provided")
            return False, "No phone number provided", None

        logger.info(f"Attempting to send WhatsApp message to phone {phone_number}")

        # ALWAYS try to send as template first if category is provided
        if category:
            template_name = self._get_template_name(category)
            if template_name:
                components = self._build_template_components(category, **kwargs)
                success, result = self.send_template_message_to_phone(
                    phone_number, template_name, components
                )
                if success:
                    logger.info(
                        f"✅ Template message sent successfully to {phone_number}"
                    )
                    return True, "Template message sent successfully", result
                else:
                    logger.error(
                        f"❌ Template message failed for {phone_number}: {result}"
                    )
                    return False, f"Template message failed: {result}", None
            else:
                logger.error(f"❌ No template mapping found for category: {category}")
                return (
                    False,
                    f"No template mapping found for category: {category}",
                    None,
                )

        # Store original method to restore later
        original_get_user_whatsapp_number = self.get_user_whatsapp_number

        try:
            # WARNING: Regular text messages are NOT delivered by WhatsApp Business API
            # unless sent within 24 hours of receiving a message from the user
            logger.warning(
                f"⚠️ Sending regular text message to {phone_number} - may not be delivered!"
            )
            logger.warning(
                "⚠️ WhatsApp Business API requires template messages for business communications"
            )

            # Temporarily override the phone number lookup method
            self.get_user_whatsapp_number = lambda user_id: str(phone_number)

            # Use existing create_message method with dummy user_id
            # The overridden method will return our target phone number
            message_payload = self.create_message(
                user_id="direct_phone", title=title or "Notification", body=message
            )

            if not message_payload:
                return False, "Failed to create message payload", None

            # Use existing send method
            success, result = self.send(message_payload)

            if success:
                formatted_number = self.format_phone_number(str(phone_number))
                logger.warning(
                    f"📤 Text message sent to API for {formatted_number}, but delivery NOT guaranteed: {result}"
                )
                return True, "Text message sent (delivery not guaranteed)", result
            else:
                return False, result, None

        except Exception as e:
            error_msg = (
                f"Unexpected error while sending WhatsApp message to phone: {str(e)}"
            )
            logger.error(error_msg)
            return False, error_msg, None
        finally:
            # Always restore the original method
            self.get_user_whatsapp_number = original_get_user_whatsapp_number
