"""
Payment System Test Runner

Comprehensive test runner for the payment system with coverage reporting
and test organization.
"""

import os
import sys
import django
from django.test.utils import get_runner
from django.conf import settings
from django.core.management import execute_from_command_line


def setup_test_environment():
    """Set up Django test environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nestafar.settings')
    django.setup()


def run_payment_tests():
    """Run all payment system tests"""
    setup_test_environment()
    
    # Test modules to run
    test_modules = [
        'payments.tests.test_models',
        'payments.tests.test_services',
        'payments.tests.test_views',
        'payments.tests.test_webhooks',
    ]
    
    print("🚀 Running Payment System Tests")
    print("=" * 50)
    
    # Run tests with coverage if available
    try:
        import coverage
        cov = coverage.Coverage()
        cov.start()
        
        # Run Django tests
        TestRunner = get_runner(settings)
        test_runner = TestRunner(verbosity=2, interactive=False, keepdb=True)
        failures = test_runner.run_tests(test_modules)
        
        cov.stop()
        cov.save()
        
        print("\n📊 Coverage Report")
        print("=" * 30)
        cov.report(show_missing=True, include='payments/*')
        
        # Generate HTML coverage report
        cov.html_report(directory='htmlcov', include='payments/*')
        print("\n📄 HTML coverage report generated in 'htmlcov/' directory")
        
    except ImportError:
        print("⚠️  Coverage not available. Install with: pip install coverage")
        
        # Run tests without coverage
        TestRunner = get_runner(settings)
        test_runner = TestRunner(verbosity=2, interactive=False, keepdb=True)
        failures = test_runner.run_tests(test_modules)
    
    if failures:
        print(f"\n❌ {failures} test(s) failed")
        return False
    else:
        print("\n✅ All tests passed!")
        return True


def run_specific_test_class(test_class):
    """Run a specific test class"""
    setup_test_environment()
    
    print(f"🎯 Running specific test: {test_class}")
    print("=" * 50)
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner(verbosity=2, interactive=False, keepdb=True)
    failures = test_runner.run_tests([test_class])
    
    return failures == 0


def run_integration_tests():
    """Run integration tests only"""
    setup_test_environment()
    
    integration_tests = [
        'payments.tests.test_services.PaymentServiceIntegrationTest',
        'payments.tests.test_webhooks.WebhookIntegrationTest',
    ]
    
    print("🔗 Running Integration Tests")
    print("=" * 40)
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner(verbosity=2, interactive=False, keepdb=True)
    failures = test_runner.run_tests(integration_tests)
    
    return failures == 0


def run_unit_tests():
    """Run unit tests only"""
    setup_test_environment()
    
    unit_tests = [
        'payments.tests.test_models',
        'payments.tests.test_services.RazorpayServiceTest',
        'payments.tests.test_services.PaymentSplitServiceTest',
        'payments.tests.test_webhooks.PaymentWebhookProcessorTest',
        'payments.tests.test_webhooks.TransferWebhookProcessorTest',
    ]
    
    print("🧪 Running Unit Tests")
    print("=" * 30)
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner(verbosity=2, interactive=False, keepdb=True)
    failures = test_runner.run_tests(unit_tests)
    
    return failures == 0


def run_api_tests():
    """Run API tests only"""
    setup_test_environment()
    
    api_tests = [
        'payments.tests.test_views',
    ]
    
    print("🌐 Running API Tests")
    print("=" * 25)
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner(verbosity=2, interactive=False, keepdb=True)
    failures = test_runner.run_tests(api_tests)
    
    return failures == 0


def main():
    """Main test runner function"""
    if len(sys.argv) < 2:
        print("Payment System Test Runner")
        print("=" * 30)
        print("Usage:")
        print("  python test_runner.py all          - Run all tests")
        print("  python test_runner.py unit         - Run unit tests only")
        print("  python test_runner.py integration  - Run integration tests only")
        print("  python test_runner.py api          - Run API tests only")
        print("  python test_runner.py <test_class> - Run specific test class")
        print("\nExamples:")
        print("  python test_runner.py all")
        print("  python test_runner.py unit")
        print("  python test_runner.py payments.tests.test_models.PaymentIntentModelTest")
        return
    
    command = sys.argv[1]
    
    if command == 'all':
        success = run_payment_tests()
    elif command == 'unit':
        success = run_unit_tests()
    elif command == 'integration':
        success = run_integration_tests()
    elif command == 'api':
        success = run_api_tests()
    else:
        # Assume it's a specific test class
        success = run_specific_test_class(command)
    
    if success:
        print("\n🎉 Test run completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Test run failed!")
        sys.exit(1)


if __name__ == '__main__':
    main()
