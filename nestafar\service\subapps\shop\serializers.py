from .models import (
    ShopCart,
    ShopCartItems,
    ShopOrder,
    ShopOrderItem,
    ShopService,
    ShopServiceItem,
)
from rest_framework.serializers import (
    ModelSerializer,
    SerializerMethodField,
    ValidationError,
)

from ...models import ServicePartner


class ShopServiceListSerializer(ModelSerializer):
    n_items = SerializerMethodField(required=False)

    def get_n_items(self, obj):
        return obj.service_items.count()

    class Meta:
        model = ShopService
        fields = "__all__"


class ShopServiceItemListSerializer(ModelSerializer):
    class Meta:
        model = ShopServiceItem
        fields = "__all__"


class ShopServiceItemCardSerializer(ShopServiceItemListSerializer):
    class Meta:
        model = ShopServiceItem
        fields = ["name", "description", "price", "addon", "is_active"]


class ShopServiceItemCreateSerializer(ShopServiceItemListSerializer):
    def validate(self, attrs):
        addon = attrs.get("addon")
        if addon:
            for addon_item in addon.keys():
                try:
                    addon_price = float(addon[addon_item])
                    addon[addon_item] = addon_price
                except ValueError:
                    raise ValidationError("Invalid price for addon")
        else:
            attrs["addon"] = None
        return attrs


class ShopOrderItemCardSerializer(ModelSerializer):
    item = ShopServiceItemCardSerializer()

    class Meta:
        model = ShopOrderItem
        fields = ["item", "quantity", "add_ons"]


class ShopCartItemSerializer(ModelSerializer):
    name = SerializerMethodField(required=False)

    def get_name(self, obj):
        return obj.name if obj.name else obj.item.name

    class Meta:
        model = ShopCartItems
        fields = [
            "id",
            "name",
            "item",
            "price",
            "quantity",
            "add_ons",
            "ordered",
            "updated_at",
            "created_at",
        ]


class ShopCartItemListSerializer(ModelSerializer):
    name = SerializerMethodField()

    def get_name(self, obj):
        return obj.item.name

    class Meta:
        model = ShopCartItems
        fields = "__all__"


class ShopCartSerializer(ModelSerializer):
    cart_items = ShopCartItemListSerializer(many=True)

    class Meta:
        model = ShopCart
        fields = "__all__"


class ShopCartGetSerializer(ModelSerializer):
    cart_items = ShopCartItemListSerializer(many=True)
    status = SerializerMethodField()

    def get_status(self, obj):
        return ShopCart.CartStatus(obj.status).name

    class Meta:
        model = ShopCart
        fields = "__all__"


class ServicePartnerNameSerializer(ModelSerializer):
    class Meta:
        model = ServicePartner
        fields = ["id", "name", "type_of_service", "phone_number"]


class ShopOrderSerializer(ModelSerializer):
    order_items = ShopOrderItemCardSerializer(many=True)
    guest = SerializerMethodField(required=False)
    service_partner = ServicePartnerNameSerializer()

    def get_guest(self, obj):
        return {
            "id": obj.guest.id,
            "phone_no": obj.guest.user.phone.as_e164,
            "room_no": obj.guest.room.room_no,
            "name": obj.guest.user.name,
        }

    class Meta:
        model = ShopOrder
        fields = "__all__"
