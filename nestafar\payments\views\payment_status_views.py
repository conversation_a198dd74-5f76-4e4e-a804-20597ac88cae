"""
Payment Status Views

API views for payment status inquiry and monitoring.
"""

import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db.models import Q

from ..models import PaymentIntent, PaymentSplit
from ..serializers.payment_intent_serializer import PaymentStatusSerializer
from ..services import RazorpayService
from ..exceptions import PaymentException
from payments.utils.payment_helpers import find_payment_intent_by_identifier

logger = logging.getLogger(__name__)


# Helper to check user permission for payment intents
def has_permission(user, payment_intent):
    """
    Check if a user can access a given payment_intent.
    """
    if user.is_staff or user.is_superuser:
        return True
    if hasattr(user, "partner_profile"):
        return payment_intent.partner == user.partner_profile
    return False


class PaymentStatusView(APIView):
    """
    View for checking payment status by various identifiers.

    Supports lookup by:
    - Payment Intent ID
    - Reference Number
    - Razorpay Payment ID
    - Razorpay Payment Link ID
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, identifier):
        """
        Get payment status by identifier.

        Args:
            identifier: Payment identifier (ID, reference, etc.)

        Returns:
            Detailed payment status information
        """
        try:
            # Try to find payment intent by various identifiers
            try:
                payment_intent = find_payment_intent_by_identifier(identifier)
            except Exception as lookup_error:
                # Catch ORM / DB or helper errors and return a graceful response
                logger.exception(f"Error looking up payment intent {identifier}: {str(lookup_error)}")
                return Response(
                    {"error": "Payment lookup failed"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            if not payment_intent:
                return Response(
                    {"error": "Payment not found"}, status=status.HTTP_404_NOT_FOUND
                )

            # Check user permissions
            if not has_permission(request.user, payment_intent):
                return Response(
                    {"error": "Permission denied"}, status=status.HTTP_403_FORBIDDEN
                )

            # Get split information
            splits = PaymentSplit.objects.filter(payment_intent=payment_intent)

            # Calculate transfer statistics
            transfers_completed = splits.filter(status="processed").count()
            transfers_pending = splits.filter(
                status__in=["pending", "processing"]
            ).count()
            transfers_failed = splits.filter(status="failed").count()

            # Build response data
            response_data = {
                "payment_id": payment_intent.id,
                "reference_number": payment_intent.reference_number,
                "status": payment_intent.status,
                "status_display": payment_intent.get_status_display(),
                "total_amount": payment_intent.total_amount,
                "paid_amount": (
                    payment_intent.total_amount if payment_intent.is_paid() else 0
                ),
                "payment_method": payment_intent.payment_method,
                "created_at": payment_intent.created_at,
                "paid_at": payment_intent.paid_at,
                "expires_at": payment_intent.expires_at,
                # Razorpay details
                "razorpay_payment_id": payment_intent.razorpay_payment_id,
                "razorpay_payment_link_id": payment_intent.razorpay_payment_link_id,
                # Split details
                "platform_commission": payment_intent.platform_commission,
                "partner_amount": payment_intent.partner_amount,
                "vendor_amount": payment_intent.vendor_amount,
                # Transfer status
                "transfers_completed": transfers_completed,
                "transfers_pending": transfers_pending,
                "transfers_failed": transfers_failed,
            }

            serializer = PaymentStatusSerializer(data=response_data)
            serializer.is_valid(raise_exception=True)

            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error getting payment status for {identifier}: {str(e)}")
            return Response(
                {"error": "Failed to get payment status"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _find_payment_intent(self, identifier):
        """
        Find payment intent by various identifiers.

        Args:
            identifier: Payment identifier

        Returns:
            PaymentIntent instance or None
        """
        # Delegated to shared helper
        return find_payment_intent_by_identifier(identifier)


class PaymentStatusBulkView(APIView):
    """
    View for bulk payment status checking.

    Allows checking status of multiple payments in a single request.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Get status for multiple payments.

        Request body should contain:
        {
            "identifiers": ["payment_id_1", "reference_1", ...]
        }
        """
        # Validate identifiers payload: must be a list of 1-50 non-empty strings
        identifiers = request.data.get("identifiers")
        if (
            not isinstance(identifiers, list)
            or not identifiers
            or len(identifiers) > 50
        ):
            return Response(
                {"error": "Please provide 1-50 payment identifiers"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        for identifier in identifiers:
            if not isinstance(identifier, str) or not identifier.strip():
                return Response(
                    {"error": "Please provide 1-50 payment identifiers"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        results = []

        for identifier in identifiers:
            try:
                try:
                    payment_intent = find_payment_intent_by_identifier(identifier)
                except Exception as lookup_error:
                    logger.exception(f"Error looking up payment intent {identifier} in bulk processing: {str(lookup_error)}")
                    results.append({
                        "identifier": identifier,
                        "found": False,
                        "error": "Payment lookup failed",
                    })
                    continue

                if not payment_intent:
                    results.append(
                        {
                            "identifier": identifier,
                            "found": False,
                            "error": "Payment not found",
                        }
                    )
                    continue

                if not has_permission(request.user, payment_intent):
                    results.append(
                        {
                            "identifier": identifier,
                            "found": True,
                            "error": "Permission denied",
                        }
                    )
                    continue

                # Get basic status info
                results.append(
                    {
                        "identifier": identifier,
                        "found": True,
                        "payment_id": payment_intent.id,
                        "reference_number": payment_intent.reference_number,
                        "status": payment_intent.status,
                        "status_display": payment_intent.get_status_display(),
                        "total_amount": payment_intent.total_amount,
                        "paid_at": payment_intent.paid_at,
                        "created_at": payment_intent.created_at,
                    }
                )

            except Exception as e:
                logger.error(f"Error processing identifier {identifier}: {str(e)}")
                results.append(
                    {
                        "identifier": identifier,
                        "found": False,
                        "error": "Processing failed",
                    }
                )

        return Response(
            {
                "results": results,
                "total_requested": len(identifiers),
                "found": len([r for r in results if r.get("found", False)]),
                "errors": len([r for r in results if "error" in r]),
            }
        )

    def _find_payment_intent(self, identifier):
        return find_payment_intent_by_identifier(identifier)


class PaymentSyncView(APIView):
    """
    View for syncing payment status with Razorpay.

    Useful for reconciliation and ensuring local status
    matches Razorpay's records.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request, identifier):
        """
        Sync payment status with Razorpay.

        Args:
            identifier: Payment identifier

        Returns:
            Updated payment status
        """
        try:
            # Find payment intent
            try:
                payment_intent = find_payment_intent_by_identifier(identifier)
            except Exception as lookup_error:
                logger.exception(
                    f"Error finding payment intent for sync {identifier}: {str(lookup_error)}"
                )
                return Response(
                    {"error": "Payment lookup failed"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            if not payment_intent:
                return Response(
                    {"error": "Payment not found"}, status=status.HTTP_404_NOT_FOUND
                )

            # Check permissions
            if not has_permission(request.user, payment_intent):
                return Response(
                    {"error": "Permission denied"}, status=status.HTTP_403_FORBIDDEN
                )

            # Sync with Razorpay
            razorpay_service = RazorpayService()
            updated = False

            # Check payment link status
            if payment_intent.razorpay_payment_link_id:
                try:
                    link_data = razorpay_service.get_payment_link(
                        payment_intent.razorpay_payment_link_id
                    )

                    # Update status if different
                    razorpay_status = link_data.get("status", "")
                    if razorpay_status == "paid" and not payment_intent.is_paid():
                        payments = link_data.get("payments", [])
                        razorpay_payment_id = None
                        if payments and isinstance(payments, list) and len(payments) > 0:
                            razorpay_payment_id = payments[0].get("payment_id")
                        payment_intent.mark_as_paid(
                            razorpay_payment_id=razorpay_payment_id
                        )
                        updated = True
                except Exception as e:
                    logger.error(f"Failed to sync payment link: {str(e)}")

            # Check payment status if we have payment ID
            if payment_intent.razorpay_payment_id:
                try:
                    payment_data = razorpay_service.get_payment(
                        payment_intent.razorpay_payment_id
                    )

                    # Update payment method if not set
                    if not payment_intent.payment_method and payment_data.get("method"):
                        payment_intent.payment_method = payment_data["method"]
                        payment_intent.save()
                        updated = True

                except Exception as e:
                    logger.error(f"Failed to sync payment: {str(e)}")

            return Response(
                {
                    "message": "Sync completed",
                    "updated": updated,
                    "payment_id": payment_intent.id,
                    "status": payment_intent.status,
                    "razorpay_payment_id": payment_intent.razorpay_payment_id,
                }
            )

        except Exception as e:
            logger.error(f"Error syncing payment {identifier}: {str(e)}")
            return Response(
                {"error": "Failed to sync payment status"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _find_payment_intent(self, identifier):
        return find_payment_intent_by_identifier(identifier)
