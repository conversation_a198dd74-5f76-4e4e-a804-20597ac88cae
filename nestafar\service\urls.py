from django.urls import path, include
from .views import *
from rest_framework.routers import SimpleRouter

router = SimpleRouter()
router.register("service-partner", ServicePartnerViewSet, basename="service-partner")
from service.views.job import StaffViewSet, JobViewSet

router.register("staff", StaffViewSet, basename="service-staff")
router.register("job", JobViewSet, basename="service-job")


urlpatterns = [
    path(r"", include(router.urls)),
    path("catalog/<service_type>/", CatalogView.as_view(), name="catalog"),
    path("cart/<service_type>/", CartView.as_view(), name="cart"),
    path("item/<service_type>/", ServiceItemView.as_view(), name="item"),
    path("request/<service_type>/", RequestView.as_view(), name="request"),
    path("order/<service_type>/", OrderView.as_view(), name="order"),
    path("partner/hide/", HideServiceView.as_view(), name="hide_service"),
    path(
        "tourism/",
        include(("service.subapps.tourism.urls", "tourism"), namespace="tourism_urls"),
    ),
    path("service-vendor/", ServiceVendorView.as_view(), name="service-vendor"),
]
