from rest_framework.serializers import (
    ModelSerializer,
    SerializerMethodField,
    CharField,
    JSONField,
    FloatField,
    BooleanField,
    UUIDField,
    Serializer,
    ValidationError,
)
from .models import (
    BaseService,
    BaseServiceItem,
    ServicePartner,
    Staff,
    StaffProfile,
    Job,
    JobStatusLog,
)
from stay.models import PropertyPartner
from .service_factory import *
from django.utils import timezone
import math


class BaseServiceSerializer(ModelSerializer):
    class Meta:
        model = BaseService
        fields = ["name", "partner", "charges", "tax_rate", "is_active"]


class BaseServiceItemSerializer(ModelSerializer):
    add_ons = SerializerMethodField()

    def get_add_ons(self, obj):
        if obj.addon is None:
            return None
        return obj.addon

    class Meta:
        model = BaseServiceItem
        fields = [
            "image",
            "add_ons",
            "name",
            "description",
            "price",
            "rating",
            "is_active",
        ]


class ServicePartnerListSerializer(ModelSerializer):
    services = SerializerMethodField()
    in_house = SerializerMethodField()
    commission = SerializerMethodField()
    delivery_charges = SerializerMethodField()
    pickup_charges = SerializerMethodField()

    def get_services(self, obj):
        services = obj.service.objects.filter(partner=obj)
        serializer = service_list_serializer.get(obj.type_of_service)
        return serializer(services, many=True).data

    def get_in_house(self, obj):
        return obj.partner.first().in_house

    def get_commission(self, obj):
        return obj.partner.first().commission

    def get_delivery_charges(self, obj):
        return obj.partner.first().delivery_charges

    def get_pickup_charges(self, obj):
        return obj.partner.first().pickup_charges

    class Meta:
        model = ServicePartner
        fields = "__all__"


class ServicePartnerRetrieveSerializer(ModelSerializer):
    commission = SerializerMethodField()
    delivery_charges = SerializerMethodField()
    pickup_charges = SerializerMethodField()
    services = SerializerMethodField()
    in_house = SerializerMethodField()

    def get_services(self, obj):
        services = obj.service.objects.filter(partner=obj)
        serializer = service_list_serializer.get(obj.type_of_service)
        return serializer(services, many=True).data

    def get_commission(self, obj):
        context = self.context
        _property = context.get("_property")
        _property_partner = PropertyPartner.objects.get(property=_property, partner=obj)
        return _property_partner.commission

    def get_delivery_charges(self, obj):
        context = self.context
        _property = context.get("_property")
        _property_partner = PropertyPartner.objects.get(property=_property, partner=obj)
        return _property_partner.delivery_charges

    def get_pickup_charges(self, obj):
        context = self.context
        _property = context.get("_property")
        _property_partner = PropertyPartner.objects.get(property=_property, partner=obj)
        return _property_partner.pickup_charges

    def get_in_house(self, obj):
        return obj.partner.first().in_house

    class Meta:
        model = ServicePartner
        fields = "__all__"


class ServicePartnerSerializer(ModelSerializer):
    class Meta:
        model = ServicePartner
        fields = [
            "id",
            "name",
            "location",
            "type_of_service",
            "description",
            "phone_number",
        ]


class ServicePartnerCreateSerializer(ModelSerializer):
    commission = CharField(required=True)
    delivery_charges = FloatField(required=False)
    pickup_charges = FloatField(required=False)
    charges = FloatField(required=False)
    tax_rate = FloatField(required=False)
    in_house = BooleanField(required=False)
    service = JSONField(required=False)
    is_visible = BooleanField(required=False)

    class Meta:
        model = ServicePartner
        fields = [
            "name",
            "type_of_service",
            "description",
            "phone_number",
            "commission",
            "charges",
            "tax_rate",
            "delivery_charges",
            "pickup_charges",
            "service",
            "in_house",
            "is_visible",
        ]


class PropertyPartnerSerializer(ModelSerializer):
    class Meta:
        model = PropertyPartner
        fields = "__all__"


class HideServiceSerializer(Serializer):
    partner_id = UUIDField(required=False)
    service_id = UUIDField(required=False)
    hide = BooleanField()

    def validate(self, data):
        if not data.get("partner_id") and not data.get("service_id"):
            raise ValidationError(
                "Either 'partner_id' or 'service_id' must be provided."
            )
        return data


class ServiceVendorSerializer(ModelSerializer):
    distance = SerializerMethodField()
    location_details = SerializerMethodField()
    services = SerializerMethodField()

    def get_services(self, obj):
        services = obj.service.objects.filter(partner=obj)
        serializer = service_list_serializer.get(obj.type_of_service)
        return serializer(services, many=True).data

    def get_distance(self, obj):
        if hasattr(obj, "distance"):
            distance = obj.distance
            # Check for invalid distance values (NaN, Inf, -Inf)
            if distance is None or (
                isinstance(distance, float)
                and (math.isnan(distance) or math.isinf(distance))
            ):
                return None
            return round(distance, 5)
        return None

    def get_location_details(self, obj):
        from geo.serializers import LocationSerializer

        return LocationSerializer(obj.location).data

    class Meta:
        model = ServicePartner
        fields = [
            "id",
            "name",
            "type_of_service",
            "description",
            "phone_number",
            "distance",
            "location_details",
            "services",
        ]


class StaffSerializer(ModelSerializer):
    user_name = SerializerMethodField()

    def get_user_name(self, obj):
        return getattr(obj.user, "name", None)

    class Meta:
        model = Staff
        fields = ["id", "user", "user_name", "created_at", "updated_at"]


class StaffProfileSerializer(ModelSerializer):
    staff_details = StaffSerializer(source="staff", read_only=True)
    active_job_count = SerializerMethodField()
    recent_jobs = SerializerMethodField()

    def get_active_job_count(self, obj):
        return obj.jobs.filter(
            status__in=[Job.JobStatus.PENDING, Job.JobStatus.IN_PROGRESS]
        ).count()

    def get_recent_jobs(self, obj):
        jobs = obj.jobs.order_by("-created_at")[:5]
        return JobSerializer(jobs, many=True).data if jobs else []

    class Meta:
        model = StaffProfile
        fields = [
            "id",
            "staff",
            "staff_details",
            "property",
            "role",
            "contact_email",
            "alt_phone",
            "is_active",
            "is_gig_worker",
            "earnings_rate",
            "meta",
            "active_job_count",
            "recent_jobs",
            "created_at",
            "updated_at",
        ]


class JobSerializer(ModelSerializer):
    staff_name = SerializerMethodField()

    def get_staff_name(self, obj):
        try:
            return (
                obj.staff.staff.user.name
                if obj.staff and obj.staff.staff and obj.staff.staff.user
                else None
            )
        except Exception:
            return None

    class Meta:
        model = Job
        fields = [
            "id",
            "staff",
            "staff_name",
            "property",
            "room",
            "status",
            "initiated_by",
            "type",
            "pickup_location",
            "dropoff_location",
            "earnings",
            "title",
            "description",
            "payload",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["created_at", "updated_at"]


class JobStatusLogSerializer(ModelSerializer):
    class Meta:
        model = JobStatusLog
        fields = [
            "id",
            "job",
            "previous_status",
            "new_status",
            "changed_by",
            "note",
            "created_at",
        ]
        read_only_fields = ["created_at"]
