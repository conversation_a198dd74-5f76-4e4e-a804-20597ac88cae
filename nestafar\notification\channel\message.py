from .base_channel import BaseChannel
from django.conf import settings
from core.models import User
import requests


class MessageChannel(BaseChannel):
    def __init__(self):
        self.__url__ = settings.FAST2SMS_API_ENDPOINT
        self.__token__ = settings.FAST2SMS_API_KEY

    def create_message(self, user_id, title, body):
        contact = User.objects.get(id=user_id).phone
        self.headers = {"Authorization": self.__token__}
        msg_body = f"{title} \n {body}"
        self.body = {
            "route": "q",
            "message": msg_body,
            "language": "english",
            "numbers": contact,
        }

    def send(self):
        try:
            response = requests.post(
                url=self.__url__, data=self.body, headers=self.headers
            )
            return True, response
        except Exception as e:
            return False, str(e)

    def send_message(self, user_id, title, body):
        self.create_message(user_id, title, body)
        return self.send()
