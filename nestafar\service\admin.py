from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.db import transaction
from .models import ServicePartner, Staff, StaffProfile, Job, JobStatusLog


class StaffProfileInline(admin.TabularInline):
    """Inline for managing staff profiles within Staff admin"""

    model = StaffProfile
    extra = 0
    readonly_fields = ("id", "created_at", "updated_at")
    fields = (
        "property",
        "role",
        "contact_email",
        "alt_phone",
        "is_active",
        "is_gig_worker",
        "earnings_rate",
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("property")


class JobStatusLogInline(admin.TabularInline):
    """Inline for viewing job status history within Job admin"""

    model = JobStatusLog
    extra = 0
    readonly_fields = (
        "id",
        "previous_status",
        "new_status",
        "changed_by",
        "created_at",
    )
    fields = ("previous_status", "new_status", "changed_by", "note", "created_at")
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(Staff)
class StaffAdmin(admin.ModelAdmin):
    list_display = [
        "user_name",
        "user_phone",
        "user_email",
        "profile_count",
        "active_jobs_count",
        "created_at",
    ]
    search_fields = ["user__name", "user__phone", "user__email"]
    list_filter = ["created_at"]
    readonly_fields = [
        "id",
        "profile_count",
        "active_jobs_count",
        "created_at",
        "updated_at",
    ]
    inlines = [StaffProfileInline]

    fieldsets = (
        (None, {"fields": ("user", "id", "created_at", "updated_at")}),
        (
            "Statistics",
            {
                "fields": ("profile_count", "active_jobs_count"),
                "classes": ("collapse",),
            },
        ),
    )

    def user_name(self, obj):
        return obj.user.name if obj.user else "-"

    user_name.short_description = "Name"
    user_name.admin_order_field = "user__name"

    def user_phone(self, obj):
        return obj.user.phone if obj.user else "-"

    user_phone.short_description = "Phone"
    user_phone.admin_order_field = "user__phone"

    def user_email(self, obj):
        return obj.user.email if obj.user else "-"

    user_email.short_description = "Email"
    user_email.admin_order_field = "user__email"

    def profile_count(self, obj):
        return obj.profiles.count()

    profile_count.short_description = "Profiles"

    def active_jobs_count(self, obj):
        return Job.objects.filter(
            staff__staff=obj,
            status__in=[Job.JobStatus.PENDING, Job.JobStatus.IN_PROGRESS],
        ).count()

    active_jobs_count.short_description = "Active Jobs"


@admin.register(StaffProfile)
class StaffProfileAdmin(admin.ModelAdmin):
    list_display = [
        "staff_name",
        "property_name",
        "role",
        "is_active",
        "is_gig_worker",
        "earnings_rate",
        "active_jobs_count",
    ]
    search_fields = [
        "staff__user__name",
        "property__name",
        "contact_email",
        "alt_phone",
    ]
    list_filter = ["role", "is_active", "is_gig_worker", "property", "created_at"]
    readonly_fields = [
        "id",
        "active_jobs_count",
        "recent_jobs_display",
        "created_at",
        "updated_at",
    ]

    fieldsets = (
        (None, {"fields": ("staff", "property", "role", "is_active")}),
        (
            "Contact Information",
            {
                "fields": ("contact_email", "alt_phone"),
            },
        ),
        (
            "Work Configuration",
            {
                "fields": ("is_gig_worker", "earnings_rate", "meta"),
            },
        ),
        (
            "Statistics",
            {
                "fields": ("active_jobs_count", "recent_jobs_display"),
                "classes": ("collapse",),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("id", "created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def staff_name(self, obj):
        return obj.staff.user.name if obj.staff and obj.staff.user else "-"

    staff_name.short_description = "Staff Name"
    staff_name.admin_order_field = "staff__user__name"

    def property_name(self, obj):
        return obj.property.name if obj.property else "-"

    property_name.short_description = "Property"
    property_name.admin_order_field = "property__name"

    def active_jobs_count(self, obj):
        return obj.jobs.filter(
            status__in=[Job.JobStatus.PENDING, Job.JobStatus.IN_PROGRESS]
        ).count()

    active_jobs_count.short_description = "Active Jobs"

    def recent_jobs_display(self, obj):
        jobs = obj.jobs.order_by("-created_at")[:3]
        if not jobs:
            return "No jobs"

        job_list = []
        for job in jobs:
            job_link = reverse("admin:service_job_change", args=[job.id])
            job_list.append(
                format_html(
                    '<a href="{}">{} - {}</a>',
                    job_link,
                    job.get_type_display(),
                    job.get_status_display(),
                )
            )
        return format_html("<br>".join(job_list))

    recent_jobs_display.short_description = "Recent Jobs"


@admin.register(Job)
class JobAdmin(admin.ModelAdmin):
    list_display = [
        "id_short",
        "type",
        "status",
        "staff_name",
        "property_name",
        "room",
        "earnings",
        "created_at",
    ]
    search_fields = [
        "id",
        "title",
        "description",
        "room",
        "pickup_location",
        "dropoff_location",
    ]
    list_filter = ["type", "status", "initiated_by", "property", "created_at"]
    readonly_fields = ["id", "status_logs_display", "created_at", "updated_at"]
    inlines = [JobStatusLogInline]
    actions = ["mark_in_progress", "mark_completed", "mark_cancelled"]

    fieldsets = (
        (
            None,
            {"fields": ("id", "property", "staff", "type", "status", "initiated_by")},
        ),
        (
            "Job Details",
            {
                "fields": ("title", "description", "room"),
            },
        ),
        (
            "Delivery Details",
            {
                "fields": ("pickup_location", "dropoff_location", "earnings"),
                "classes": ("collapse",),
            },
        ),
        (
            "Additional Information",
            {
                "fields": ("payload",),
                "classes": ("collapse",),
            },
        ),
        (
            "Status History",
            {
                "fields": ("status_logs_display",),
                "classes": ("collapse",),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def id_short(self, obj):
        return str(obj.id)[:8] + "..."

    id_short.short_description = "ID"

    def staff_name(self, obj):
        if obj.staff and obj.staff.staff and obj.staff.staff.user:
            return obj.staff.staff.user.name
        return "Unassigned"

    staff_name.short_description = "Staff"
    staff_name.admin_order_field = "staff__staff__user__name"

    def property_name(self, obj):
        return obj.property.name if obj.property else "-"

    property_name.short_description = "Property"
    property_name.admin_order_field = "property__name"

    def status_logs_display(self, obj):
        logs = obj.status_logs.order_by("-created_at")[:5]
        if not logs:
            return "No status changes"

        log_list = []
        for log in logs:
            changed_by = log.changed_by.name if log.changed_by else "System"
            log_list.append(
                f"{log.previous_status or 'None'} → {log.new_status} ({changed_by}) - {log.created_at.strftime('%Y-%m-%d %H:%M')}"
            )
        return format_html("<br>".join(log_list))

    status_logs_display.short_description = "Recent Status Changes"

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related("staff__staff__user", "property")
        )

    # Bulk actions for status updates
    def mark_in_progress(self, request, queryset):
        with transaction.atomic():
            count = 0
            for job in queryset:
                if job.status != Job.JobStatus.IN_PROGRESS:
                    JobStatusLog.objects.create(
                        job=job,
                        previous_status=job.status,
                        new_status=Job.JobStatus.IN_PROGRESS,
                        changed_by=request.user,
                        note="Bulk action via admin",
                    )
                    job.status = Job.JobStatus.IN_PROGRESS
                    job.save(update_fields=["status"])
                    count += 1
        self.message_user(request, f"{count} job(s) marked as in progress.")

    mark_in_progress.short_description = "Mark selected jobs as in progress"

    def mark_completed(self, request, queryset):
        with transaction.atomic():
            count = 0
            for job in queryset:
                if job.status != Job.JobStatus.COMPLETED:
                    JobStatusLog.objects.create(
                        job=job,
                        previous_status=job.status,
                        new_status=Job.JobStatus.COMPLETED,
                        changed_by=request.user,
                        note="Bulk action via admin",
                    )
                    job.status = Job.JobStatus.COMPLETED
                    job.save(update_fields=["status"])
                    count += 1
        self.message_user(request, f"{count} job(s) marked as completed.")

    mark_completed.short_description = "Mark selected jobs as completed"

    def mark_cancelled(self, request, queryset):
        with transaction.atomic():
            count = 0
            for job in queryset:
                if job.status != Job.JobStatus.CANCELLED:
                    JobStatusLog.objects.create(
                        job=job,
                        previous_status=job.status,
                        new_status=Job.JobStatus.CANCELLED,
                        changed_by=request.user,
                        note="Bulk action via admin",
                    )
                    job.status = Job.JobStatus.CANCELLED
                    job.save(update_fields=["status"])
                    count += 1
        self.message_user(request, f"{count} job(s) marked as cancelled.")

    mark_cancelled.short_description = "Mark selected jobs as cancelled"


@admin.register(JobStatusLog)
class JobStatusLogAdmin(admin.ModelAdmin):
    list_display = [
        "job_id_short",
        "previous_status",
        "new_status",
        "changed_by_name",
        "note",
        "created_at",
    ]
    search_fields = ["job__id", "note", "changed_by__name"]
    list_filter = ["previous_status", "new_status", "created_at"]
    readonly_fields = [
        "id",
        "job",
        "previous_status",
        "new_status",
        "changed_by",
        "created_at",
    ]

    fieldsets = (
        (
            None,
            {"fields": ("job", "previous_status", "new_status", "changed_by", "note")},
        ),
        (
            "Metadata",
            {
                "fields": ("id", "created_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def job_id_short(self, obj):
        return str(obj.job.id)[:8] + "..."

    job_id_short.short_description = "Job ID"

    def changed_by_name(self, obj):
        return obj.changed_by.name if obj.changed_by else "System"

    changed_by_name.short_description = "Changed By"
    changed_by_name.admin_order_field = "changed_by__name"

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(ServicePartner)
class ServicePartnerAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "type_of_service",
        "phone_number",
        "location",
        "service_count",
        "view_services_link",
        "is_visible",
    ]
    search_fields = ["name", "phone_number", "is_visible"]
    list_filter = ["type_of_service", "location"]
    ordering = ["name"]
    inlines = []
    readonly_fields = ["service_count", "view_services_link"]

    # Fieldset organization to make editing more intuitive
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "name",
                    "location",
                    "type_of_service",
                    "description",
                    "phone_number",
                    "is_visible",
                )
            },
        ),
        (
            "Services",
            {
                "fields": ("service_count", "view_services_link"),
                "classes": ("collapse",),
            },
        ),
    )

    # Custom property to display count of services
    def service_count(self, obj):
        return obj.services().count()

    service_count.short_description = "Number of Services"

    # Add a link to view services in the list display
    def view_services_link(self, obj):
        if obj.service:
            # Get the service's admin URL for the specific service type
            service_model_name = obj.service._meta.model_name
            app_label = obj.service._meta.app_label
            # Generate the correct link with the partner's UUID in the query string
            link = reverse(f"admin:{app_label}_{service_model_name}_changelist")
            return format_html(
                '<a href="{}?partner={}">View Services</a>', link, obj.id
            )
        return "-"

    view_services_link.short_description = "Services Link"
