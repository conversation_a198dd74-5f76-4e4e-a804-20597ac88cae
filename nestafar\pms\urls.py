from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>

from .views import *

router = DefaultRouter()
router.register(r"rateplan", RatePlanViewSet, basename="pms-rateplan")
router.register(r"roomtypes", RoomTypeViewSet, basename="pms-roomtypes")
router.register(r"availability", AvailabilityViewSet, basename="pms-availability")
router.register(r"inventory", InventoryViewSet, basename="pms-inventory")
router.register(r"aiosell", AioSellViewSet, basename="pms-aiosell")

urlpatterns = [
    path("", include(router.urls)),
    path(
        "policy/",
        PolicyViewSet.as_view(
            {
                "get": "retrieve",
                "put": "update",
            }
        ),
        name="pms-policy",
    ),

    # Channel Manager webhook endpoints
    path(
        "webhook/<str:channel_name>/",
        channel_manager_webhook,
        name="channel-manager-webhook",
    ),

    # Calendar endpoint
    path("calendar/", calendar_endpoint, name="booking-calendar"),
    
]
