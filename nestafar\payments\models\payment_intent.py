"""
PaymentIntent Model

Core payment tracking entity that manages the complete payment lifecycle
including Razorpay integration, split calculations, and audit trails.
"""

import uuid
import logging
from decimal import Decimal
from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
from django.db import IntegrityError, transaction

from ..constants import PaymentStatus, PaymentContext, PaymentMethod

logger = logging.getLogger(__name__)


class PaymentIntent(models.Model):
    """
    Comprehensive payment tracking entity for Razorpay Route integration.

    This model tracks the complete payment lifecycle from creation to completion,
    including split calculations, Razorpay identifiers, and audit trails.
    """

    # Primary identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    reference_number = models.CharField(
        max_length=50,
        unique=True,
        blank=True,
        editable=False,
        help_text="Unique reference number for this payment",
    )

    # Context and relationships
    context = models.CharField(
        max_length=20,
        choices=PaymentContext.choices,
        help_text="Payment context (checkout, precheckin, etc.)",
    )
    partner = models.ForeignKey(
        "core.PartnerProfile",
        on_delete=models.CASCADE,
        related_name="payment_intents",
        help_text="Partner associated with this payment",
    )

    # Related entities (nullable for flexibility)
    precheckin = models.ForeignKey(
        "booking.PreCheckin",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="payment_intents",
        help_text="Associated precheckin if applicable",
    )
    guest = models.ForeignKey(
        "stay.Guest",
        on_delete=models.CASCADE,
        related_name="payment_intents",
        help_text="Associated guest if applicable",
    )

    # Payment amounts (stored in rupees, converted to paise for Razorpay)
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal("0.01"))],
        help_text="Total payment amount in rupees",
    )
    platform_commission = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal("0.00"),
        help_text="Platform commission amount in rupees",
    )
    partner_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal("0.00"),
        help_text="Amount to be transferred to partner in rupees",
    )
    vendor_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal("0.00"),
        help_text="Amount to be transferred to vendors in rupees",
    )

    # Payment status and method
    status = models.CharField(
        max_length=20,
        choices=PaymentStatus.choices,
        default=PaymentStatus.PENDING,
        help_text="Current payment status",
    )
    payment_method = models.CharField(
        max_length=20,
        choices=PaymentMethod.choices,
        null=True,
        blank=True,
        help_text="Payment method used by customer",
    )

    # Razorpay identifiers
    razorpay_order_id = models.CharField(
        max_length=100, null=True, blank=True, help_text="Razorpay order ID"
    )
    razorpay_payment_id = models.CharField(
        max_length=100, null=True, blank=True, help_text="Razorpay payment ID"
    )
    razorpay_payment_link_id = models.CharField(
        max_length=100, null=True, blank=True, help_text="Razorpay payment link ID"
    )

    # Split calculation results (JSON for flexibility)
    split_details = models.JSONField(
        default=dict, help_text="Detailed split calculation results"
    )

    # Webhook processing
    idempotency_key = models.CharField(
        max_length=100,
        unique=True,
        blank=True,
        editable=False,
        help_text="Idempotency key for webhook processing",
    )
    webhook_processed_at = models.DateTimeField(
        null=True, blank=True, help_text="Timestamp when webhook was processed"
    )

    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        "core.User",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_payment_intents",
        help_text="User who created this payment intent",
    )

    # Payment completion tracking
    paid_at = models.DateTimeField(
        null=True, blank=True, help_text="Timestamp when payment was completed"
    )
    expires_at = models.DateTimeField(
        null=True, blank=True, help_text="Payment link expiry timestamp"
    )

    # Additional metadata
    metadata = models.JSONField(
        default=dict, help_text="Additional metadata for this payment"
    )

    class Meta:
        db_table = "payments_payment_intent"
        indexes = [
            models.Index(fields=["reference_number"]),
            models.Index(fields=["razorpay_payment_id"]),
            models.Index(fields=["razorpay_order_id"]),
            models.Index(fields=["status", "created_at"]),
            models.Index(fields=["partner", "status"]),
            models.Index(fields=["context", "status"]),
        ]
        ordering = ["-created_at"]

    def __str__(self):
        return f"Payment {self.reference_number} - {self.status}"

    def save(self, *args, **kwargs):
        # Generate reference number if not provided (independent of idempotency key)
        # Ensure we create a unique reference_number under concurrency by
        # attempting the save inside a DB transaction and retrying on
        # IntegrityError (unique constraint violation). This avoids race
        # conditions where two concurrent requests generate the same ref.
        max_attempts = 5

        # Generate idempotency key if not provided (independent of ref)
        if not self.idempotency_key:
            import uuid as _uuid
            self.idempotency_key = f"payment_{_uuid.uuid4().hex}_{int(timezone.now().timestamp())}"

        # If reference already set, just try saving once (still in transaction)
        if self.reference_number:
            try:
                with transaction.atomic():
                    super().save(*args, **kwargs)
                return
            except IntegrityError:
                # Fall through to retry path
                pass

        # Retry loop for generating unique reference_number
        for attempt in range(1, max_attempts + 1):
            if not self.reference_number:
                self.reference_number = self.generate_reference_number()

            try:
                with transaction.atomic():
                    super().save(*args, **kwargs)
                return
            except IntegrityError as e:
                # Assume IntegrityError due to unique ref_number collision; try again
                logger.warning(
                    "Reference number collision on attempt %s for payment creation: %s",
                    attempt,
                    str(e),
                )
                # Clear reference to force regeneration on next loop
                self.reference_number = None
                if attempt == max_attempts:
                    # Re-raise after exhausting retries
                    raise
                # otherwise retry

    @classmethod
    def generate_reference_number(cls):
        """Generate a unique reference number"""
        import secrets
        import string

        timestamp = timezone.now().strftime("%Y%m%d%H%M%S")
        # Use a cryptographically secure generator for the suffix.
        # Keep the suffix length at 4 characters and restrict to A-Z0-9.
        secure_chars = string.ascii_uppercase + string.digits
        secure_suffix = "".join(secrets.choice(secure_chars) for _ in range(4))
        return f"PAY{timestamp}{secure_suffix}"

    def get_razorpay_amount(self):
        """Convert amount to paise for Razorpay API"""
        return int(self.total_amount * 100)

    def is_paid(self):
        """Check if payment is completed"""
        return self.status == PaymentStatus.COMPLETED

    def can_be_refunded(self):
        """Check if payment can be refunded"""
        return self.status in [
            PaymentStatus.COMPLETED,
            PaymentStatus.PARTIALLY_REFUNDED,
        ]

    def mark_as_paid(self, razorpay_payment_id=None, payment_method=None):
        """Mark payment as completed (idempotent)."""
        now = timezone.now()
        update_kwargs = {
            "status": PaymentStatus.COMPLETED,
            "paid_at": now,
        }
        if razorpay_payment_id:
            update_kwargs["razorpay_payment_id"] = razorpay_payment_id
        if payment_method:
            update_kwargs["payment_method"] = payment_method

        updated = (
            type(self)
            .objects.filter(pk=self.pk)
            .exclude(status=PaymentStatus.COMPLETED)
            .update(**update_kwargs)
        )

        # Reflect state on instance
        if updated or self.status != PaymentStatus.COMPLETED:
            self.status = PaymentStatus.COMPLETED
            self.paid_at = now
            if razorpay_payment_id:
                self.razorpay_payment_id = razorpay_payment_id
            if payment_method:
                self.payment_method = payment_method
                self.status = PaymentStatus.COMPLETED
        self.paid_at = timezone.now()
        if razorpay_payment_id:
            self.razorpay_payment_id = razorpay_payment_id
        if payment_method:
            self.payment_method = payment_method
        self.save()

    def calculate_splits(self):
        """Calculate payment splits based on context and configuration"""
        from ..services import PaymentSplitService

        split_service = PaymentSplitService()
        return split_service.calculate_splits(self)

    def get_transfer_details(self):
        """Get transfer details for Razorpay Route"""
        if not self.split_details:
            try:
                result = self.calculate_splits()
            except Exception as exc:
                # Log with context and default to a safe structure without saving
                logger.exception(
                    "Failed to calculate splits for PaymentIntent %s: %s",
                    getattr(self, "reference_number", str(getattr(self, "id", "<unknown>"))),
                    str(exc),
                )
                # Do not persist an error result to the DB here; provide a safe default
                self.split_details = {"transfers": []}
                return self.split_details.get("transfers", [])

            # If the service returned an unexpected/falsy value, default safely
            if not result:
                logger.warning(
                    "calculate_splits returned falsy for PaymentIntent %s; defaulting transfers to empty list",
                    getattr(self, "reference_number", str(getattr(self, "id", "<unknown>"))),
                )
                self.split_details = {"transfers": []}
                return self.split_details.get("transfers", [])

            # Normal successful path: persist the calculated splits
            self.split_details = result
            try:
                self.save()
            except Exception:
                # If saving fails here, log but still return the computed transfers
                logger.exception(
                    "Failed to save split_details for PaymentIntent %s after successful calculation",
                    getattr(self, "reference_number", str(getattr(self, "id", "<unknown>"))),
                )

        return self.split_details.get("transfers", [])
