"""
Payment Notification Service

Service for sending payment-related notifications via the existing
notification system including payment links, success/failure notifications, and reminders.
"""

import logging
from typing import Dict
from django.conf import settings
from service.models import ServicePartner

# Helper to safely get nested attributes with default fallback
def _safe_attr(obj, attr_path: str, default=''):
    """
    Safely traverse nested attributes specified by dot-separated path on an object.
    Returns default if any attribute in the chain is missing or None.
    """
    try:
        for part in attr_path.split('.'):
            obj = getattr(obj, part, None)
            if obj is None:
                return default
        return obj
    except Exception:
        return default

logger = logging.getLogger(__name__)


class PaymentNotificationService:
    """
    Service for sending payment-related notifications.
    
    Integrates with existing notification system to send WhatsApp
    messages for payment links, status updates, and reminders.
    """
    
    def __init__(self):
        self.logger = logger
    
    def send_payment_link_notification(self, payment_intent, payment_url: str) -> Dict:
        """
        Send payment link notification via WhatsApp using existing notification system.
        
        Args:
            payment_intent: PaymentIntent instance
            payment_url: Payment link URL
            
        Returns:
            Dictionary with sending status
        """
        try:
            # Get user from payment intent
            user = self._get_user_from_payment_intent(payment_intent)
            
            if not user:
                return {'sent': False, 'error': 'No user found for payment intent'}
            
            # Prepare notification data
            notification_data = self._build_payment_link_notification_data(payment_intent, payment_url)
            
            # Send notification using existing system
            from notification.tasks.send_tasks import send_notification
            
            # Normalize send_notification return: can be (success, message), bool, dict, etc.
            raw_result = send_notification(
                user_id=str(user.id),
                event='PAYMENT_LINK_CREATED',
                data=notification_data
            )
            success, message = self._normalize_notification_result(raw_result)
            
            if success:
                self.logger.info(f"Sent payment link notification for payment {payment_intent.id}")
                return {
                    'sent': True,
                    'user_id': str(user.id),
                    'notification_data': notification_data
                }
            else:
                self.logger.error(f"Failed to send payment link notification: {message}")
                return {'sent': False, 'error': message}
            
        except Exception as e:
            self.logger.error(f"Failed to send payment link notification: {str(e)}")
            return {'sent': False, 'error': str(e)}
    
    def send_payment_success_notification(self, payment_intent) -> Dict:
        """
        Send payment success notification.
        
        Args:
            payment_intent: PaymentIntent instance
            
        Returns:
            Dictionary with sending status
        """
        try:
            # Get user from payment intent
            user = self._get_user_from_payment_intent(payment_intent)
            
            if not user:
                return {'sent': False, 'error': 'No user found for payment intent'}
            
            # Prepare notification data
            notification_data = self._build_payment_success_notification_data(payment_intent)
            
            # Send notification using existing system
            from notification.tasks.send_tasks import send_notification
            
            # Normalize send_notification return
            raw_result = send_notification(
                user_id=str(user.id),
                event='PAYMENT_SUCCESS',
                data=notification_data
            )
            success, message = self._normalize_notification_result(raw_result)
            
            if success:
                self.logger.info(f"Sent payment success notification for payment {payment_intent.id}")
                return {
                    'sent': True,
                    'user_id': str(user.id),
                    'notification_data': notification_data
                }
            else:
                self.logger.error(f"Failed to send payment success notification: {message}")
                return {'sent': False, 'error': message}
            
        except Exception as e:
            self.logger.error(f"Failed to send payment success notification: {str(e)}")
            return {'sent': False, 'error': str(e)}
    
    def send_payment_failure_notification(self, payment_intent, error_reason: str = '') -> Dict:
        """
        Send payment failure notification.
        
        Args:
            payment_intent: PaymentIntent instance
            error_reason: Reason for payment failure
            
        Returns:
            Dictionary with sending status
        """
        try:
            # Get user from payment intent
            user = self._get_user_from_payment_intent(payment_intent)
            
            if not user:
                return {'sent': False, 'error': 'No user found for payment intent'}
            
            # Prepare notification data
            notification_data = self._build_payment_failure_notification_data(payment_intent, error_reason)
            
            # Send notification using existing system
            from notification.tasks.send_tasks import send_notification
            
            # Normalize send_notification return
            raw_result = send_notification(
                user_id=str(user.id),
                event='PAYMENT_FAILED',
                data=notification_data
            )
            success, message = self._normalize_notification_result(raw_result)
            
            if success:
                self.logger.info(f"Sent payment failure notification for payment {payment_intent.id}")
                return {
                    'sent': True,
                    'user_id': str(user.id),
                    'notification_data': notification_data
                }
            else:
                self.logger.error(f"Failed to send payment failure notification: {message}")
                return {'sent': False, 'error': message}
            
        except Exception as e:
            self.logger.error(f"Failed to send payment failure notification: {str(e)}")
            return {'sent': False, 'error': str(e)}
    
    def send_payment_reminder_notification(self, payment_intent) -> Dict:
        """
        Send payment reminder notification.
        
        Args:
            payment_intent: PaymentIntent instance
            
        Returns:
            Dictionary with sending status
        """
        try:
            # Skip sending if payment link has expired
            from django.utils import timezone
            if payment_intent.expires_at and payment_intent.expires_at < timezone.now():
                return {'sent': False, 'error': 'Payment link expired'}
            # Only send reminders for pending payments
            if payment_intent.is_paid():
                return {'sent': False, 'error': 'Payment already completed'}
            
            # Get user from payment intent
            user = self._get_user_from_payment_intent(payment_intent)
            
            if not user:
                return {'sent': False, 'error': 'No user found for payment intent'}
            
            # Prepare notification data
            notification_data = self._build_payment_reminder_notification_data(payment_intent)
            
            # Send notification using existing system
            from notification.tasks.send_tasks import send_notification
            
            # Normalize send_notification return
            raw_result = send_notification(
                user_id=str(user.id),
                event='PAYMENT_REMINDER',
                data=notification_data
            )
            success, message = self._normalize_notification_result(raw_result)
            
            if success:
                self.logger.info(f"Sent payment reminder for payment {payment_intent.id}")
                return {
                    'sent': True,
                    'user_id': str(user.id),
                    'notification_data': notification_data
                }
            else:
                self.logger.error(f"Failed to send payment reminder: {message}")
                return {'sent': False, 'error': message}
            
        except Exception as e:
            self.logger.error(f"Failed to send payment reminder: {str(e)}")
            return {'sent': False, 'error': str(e)}
    
    def send_transfer_success_notification(self, payment_split) -> Dict:
        """
        Send transfer success notification to partner/vendor.
        
        Args:
            payment_split: PaymentSplit instance
            
        Returns:
            Dictionary with sending status
        """
        try:
            # Get recipient user
            recipient_user = self._get_recipient_user(payment_split)
            
            if not recipient_user:
                return {'sent': False, 'error': 'No recipient user found'}
            
            # Prepare notification data
            notification_data = self._build_transfer_success_notification_data(payment_split)
            
            # Send notification using existing system
            from notification.tasks.send_tasks import send_notification
            
            # Normalize send_notification return
            raw_result = send_notification(
                user_id=str(recipient_user.id),
                event='TRANSFER_SUCCESS',
                data=notification_data
            )
            success, message = self._normalize_notification_result(raw_result)
            
            if success:
                self.logger.info(f"Sent transfer success notification for split {payment_split.id}")
                return {
                    'sent': True,
                    'user_id': str(recipient_user.id),
                    'notification_data': notification_data
                }
            else:
                self.logger.error(f"Failed to send transfer success notification: {message}")
                return {'sent': False, 'error': message}
            
        except Exception as e:
            self.logger.error(f"Failed to send transfer success notification: {str(e)}")
            return {'sent': False, 'error': str(e)}
    
    def send_transfer_failure_notification(self, payment_split, error_message: str) -> Dict:
        """
        Send transfer failure notification to partner/vendor.
        
        Args:
            payment_split: PaymentSplit instance
            error_message: Error message from transfer failure
            
        Returns:
            Dictionary with sending status
        """
        try:
            # Get recipient user
            recipient_user = self._get_recipient_user(payment_split)
            
            if not recipient_user:
                return {'sent': False, 'error': 'No recipient user found'}
            
            # Prepare notification data
            notification_data = self._build_transfer_failure_notification_data(payment_split, error_message)
            
            # Send notification using existing system
            from notification.tasks.send_tasks import send_notification
            
            # Normalize send_notification return
            raw_result = send_notification(
                user_id=str(recipient_user.id),
                event='TRANSFER_FAILED',
                data=notification_data
            )
            success, message = self._normalize_notification_result(raw_result)
            
            if success:
                self.logger.info(f"Sent transfer failure notification for split {payment_split.id}")
                return {
                    'sent': True,
                    'user_id': str(recipient_user.id),
                    'notification_data': notification_data
                }
            else:
                self.logger.error(f"Failed to send transfer failure notification: {message}")
                return {'sent': False, 'error': message}
            
        except Exception as e:
            self.logger.error(f"Failed to send transfer failure notification: {str(e)}")
            return {'sent': False, 'error': str(e)}

    # Helper methods
    def _get_user_from_payment_intent(self, payment_intent):
        """Get user from payment intent based on context."""
        if payment_intent.context == 'checkout' and payment_intent.guest:
            return payment_intent.guest.user
        elif payment_intent.context == 'precheckin' and payment_intent.precheckin:
            # Get primary guest from precheckin
            primary_guest = payment_intent.precheckin.pre_checkin_guests.filter(is_primary=True).first()
            return primary_guest.user if primary_guest else None
        return None

    def _get_recipient_user(self, payment_split):
        """Get recipient user from payment split."""
        if payment_split.recipient_type == 'partner':
            return payment_split.payment_intent.partner.user
        elif payment_split.recipient_type == 'vendor':
            try:
                vendor = ServicePartner.objects.get(id=payment_split.recipient_id)
                return vendor.user if hasattr(vendor, 'user') else None
            except ServicePartner.DoesNotExist:
                return None
        return None

    def _normalize_notification_result(self, raw_result):
        """Normalize various shapes of send_notification return values.

        Returns a tuple (success: bool, message: Optional[str]).
        Supported shapes:
        - (success, message) tuple
        - bool
        - dict with keys 'sent', 'error' or 'message'
        - otherwise logs a warning and returns (False, 'Unexpected notification response')
        """
        if isinstance(raw_result, tuple) and len(raw_result) == 2:
            return raw_result[0], raw_result[1]
        if isinstance(raw_result, bool):
            return raw_result, None
        if isinstance(raw_result, dict):
            success = bool(raw_result.get('sent'))
            message = raw_result.get('error') or raw_result.get('message')
            return success, message

        # Unexpected shape
        self.logger.warning("Unexpected send_notification response: %r", raw_result)
        return False, 'Unexpected notification response'

    def _build_payment_link_notification_data(self, payment_intent, payment_url: str) -> Dict:
        """Build notification data for payment link."""
        context_name = "checkout" if payment_intent.context == 'checkout' else "advance payment"
        # Safely retrieve property name or fallback
        property_name = _safe_attr(payment_intent, 'partner.user.name', default='Unknown Property')

        return {
            'customer_name': self._get_customer_name(payment_intent),
            'property_name': property_name,
            # Preserve exact decimal value as string
            'amount': str(payment_intent.total_amount),
            'payment_url': payment_url,
            'reference_number': payment_intent.reference_number,
            'context_type': payment_intent.context,
            'context_name': context_name,
            'expires_at': payment_intent.expires_at.isoformat() if payment_intent.expires_at else None,
        }

    def _build_payment_success_notification_data(self, payment_intent) -> Dict:
        """Build notification data for payment success."""
        return {
            'customer_name': self._get_customer_name(payment_intent),
            'property_name': _safe_attr(payment_intent, 'partner.user.name', default=''),
            # Preserve exact decimal value as string
            'amount': str(payment_intent.total_amount),
            'payment_id': payment_intent.razorpay_payment_id or 'N/A',
            'reference_number': payment_intent.reference_number,
            'payment_method': payment_intent.payment_method or 'Unknown',
            'paid_at': payment_intent.paid_at.isoformat() if payment_intent.paid_at else None,
        }

    def _build_payment_failure_notification_data(self, payment_intent, error_reason: str) -> Dict:
        """Build notification data for payment failure."""
        # Get payment link URL for retry
        retry_url = self._get_payment_link_url(payment_intent)

        return {
            'customer_name': self._get_customer_name(payment_intent),
            'property_name': _safe_attr(payment_intent, 'partner.user.name', default=''),
            # Preserve exact decimal value as string
            'amount': str(payment_intent.total_amount),
            'reference_number': payment_intent.reference_number,
            'error_reason': error_reason,
            'retry_url': retry_url,
        }

    def _build_payment_reminder_notification_data(self, payment_intent) -> Dict:
        """Build notification data for payment reminder."""
        # Calculate hours remaining
        hours_remaining = 0
        if payment_intent.expires_at:
            from django.utils import timezone
            remaining = payment_intent.expires_at - timezone.now()
            hours_remaining = max(0, int(remaining.total_seconds() / 3600))

        # Get payment link URL
        payment_url = self._get_payment_link_url(payment_intent)

        return {
            'customer_name': self._get_customer_name(payment_intent),
            'property_name': _safe_attr(payment_intent, 'partner.user.name', default=''),
            # Preserve exact decimal value as string
            'amount': str(payment_intent.total_amount),
            'payment_url': payment_url,
            'reference_number': payment_intent.reference_number,
            'hours_remaining': hours_remaining,
        }

    def _build_transfer_success_notification_data(self, payment_split) -> Dict:
        """Build notification data for transfer success."""
        from django.utils import timezone

        return {
            'recipient_name': payment_split.recipient_name,
            # Preserve exact decimal value as string
            'amount': str(payment_split.amount),
            'payment_reference': payment_split.payment_intent.reference_number,
            'transfer_id': payment_split.razorpay_transfer_id or 'N/A',
            'settlement_date': timezone.now().strftime('%d %b %Y'),
            'recipient_type': payment_split.recipient_type,
        }

    def _build_transfer_failure_notification_data(self, payment_split, error_message: str) -> Dict:
        """Build notification data for transfer failure."""
        return {
            'recipient_name': payment_split.recipient_name,
            # Preserve exact decimal value as string
            'amount': str(payment_split.amount),
            'payment_reference': payment_split.payment_intent.reference_number,
            'error_message': error_message,
            'support_contact': getattr(settings, 'SUPPORT_CONTACT', '+91-9876543210'),
            'recipient_type': payment_split.recipient_type,
        }

    def _get_customer_name(self, payment_intent):
        """Get customer name from payment intent."""
        user = self._get_user_from_payment_intent(payment_intent)
        return user.name if user else 'Customer'

    def _get_payment_link_url(self, payment_intent):
        """Get payment link URL from Razorpay."""
        if not payment_intent.razorpay_payment_link_id:
            return None

        try:
            from .razorpay_service import RazorpayService
            razorpay_service = RazorpayService()
            link_data = razorpay_service.get_payment_link(payment_intent.razorpay_payment_link_id)
            return link_data.get('short_url', '')
        except Exception as e:
            self.logger.error(f"Failed to get payment link URL: {str(e)}")
            return None

    # Compatibility helpers expected by webhook processors/tests
    def send_sms(self, phone_number: str, message: str):
        """Send a plain SMS message to a phone number using configured handlers.

        This is a thin wrapper around the notification channel handlers so
        existing callers (and tests) can patch/mock this method.
        Returns a boolean indicating success.
        """
        try:
            # Import here to avoid circular imports at module import time
            from notification.tasks.send_tasks import load_handler_from_channel_name, send_message_via_channel
            from notification.models.main import NotificationChannel

            handler = load_handler_from_channel_name(NotificationChannel.MESSAGE.value)
            if handler is None:
                self.logger.error("No handler configured for SMS/MESSAGE channel")
                return False

            success, response, _ = send_message_via_channel(
                handler,
                NotificationChannel.MESSAGE.value,
                {'phone_number': phone_number},
                message,
                '',
            )
            return success
        except Exception as e:
            self.logger.exception(f"Failed to send SMS to {phone_number}: {e}")
            return False

    def send_whatsapp_message(self, phone_number: str, message: str):
        """Send a WhatsApp message to a phone number using configured handlers.

        Thin wrapper for compatibility so callers can patch/mock this method.
        """
        try:
            from notification.tasks.send_tasks import load_handler_from_channel_name, send_message_via_channel
            from notification.models.main import NotificationChannel

            handler = load_handler_from_channel_name(NotificationChannel.WHATSAPP.value)
            if handler is None:
                self.logger.error("No handler configured for WHATSAPP channel")
                return False

            success, response, _ = send_message_via_channel(
                handler,
                NotificationChannel.WHATSAPP.value,
                {'phone_number': phone_number},
                message,
                '',
            )
            return success
        except Exception as e:
            self.logger.exception(f"Failed to send WhatsApp message to {phone_number}: {e}")
            return False
