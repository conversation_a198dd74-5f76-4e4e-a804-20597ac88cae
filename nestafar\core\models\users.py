import uuid
from django.contrib.auth.models import <PERSON>bs<PERSON><PERSON><PERSON><PERSON><PERSON>, BaseUserManager
from phonenumber_field.modelfields import PhoneNumberField
from django.db import models, transaction
from django.apps import apps
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from decimal import Decimal


# User Manager. This is the core user manager for the platform.
class UserManager(BaseUserManager):
    def create_user(
        self, phone, name, email=None, partner=False, password=None, is_verified=False
    ):
        """
        Creates and saves a User with the given phone number, name and password.
        """
        if not phone:
            raise ValueError("Users must have a phone number")
        normalized_email = self.normalize_email(email) if email else None

        with transaction.atomic():
            user = self.model(
                phone=phone,
                email=normalized_email,
                name=name,
                is_partner=partner,
                is_verified=is_verified,
            )

            user.set_password(password)
            user.save(using=self._db)
            if partner:
                partner_profile = PartnerProfile.objects.create(user=user)
                partner_profile.save()
                # update the active_property if properties exists
                if partner_profile.properties.exists():
                    partner_profile.active_property = partner_profile.properties.first()
                    partner_profile.save()
            UserProfile.objects.create(user=user)
            return user

    def create_superuser(self, phone, name, email=None, password=None):
        """
        Creates and saves a superuser with the given email, date of
        birth and password.
        """
        if not phone:
            raise ValueError("Users must have a phone number")

        user = self.model(phone=phone, email=self.normalize_email(email), name=name)
        user.set_password(password)
        user.is_admin = True
        user.save(using=self._db)
        return user


# User Model. This is the core user model for the platform.
class User(AbstractBaseUser):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    phone = PhoneNumberField(unique=True)
    email = models.EmailField(
        verbose_name="email address",
        max_length=255,
        null=True,
        blank=True,
        unique=False,
    )
    name = models.CharField(max_length=255, blank=False, null=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_verified = models.BooleanField(
        default=False, editable=True, blank=False, null=False
    )
    is_partner = models.BooleanField(
        default=False, editable=True, blank=False, null=False
    )
    is_active = models.BooleanField(default=True, editable=True)
    is_admin = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)
    groups = models.ManyToManyField(to="core.role", blank=True, default=None)
    user_permissions = models.ManyToManyField(
        to="core.permission", max_length=255, blank=True, default=None
    )

    objects = UserManager()

    USERNAME_FIELD = "phone"
    REQUIRED_FIELDS = ["name"]

    def __str__(self):
        return self.name

    def get_perms(self, property_id=None):
        if self.is_superuser or self.is_admin:
            return set()
        UserRole = apps.get_model("core", "UserRole")

        user_roles = (
            UserRole.objects.filter(user=self, property_id=property_id)
            if property_id
            else UserRole.objects.filter(user=self)
        )
        permissions = set()

        for user_role in user_roles:
            for permission in user_role.role.permissions.all():
                permissions.add(permission.name)

        return permissions

    def has_perm(self, perm, obj=None):
        "Does the user have a specific permission?"
        if self.is_superuser or self.is_admin:
            return True

        # Check if the user has the specified permission
        return perm in self.get_perms(obj.id if obj else None)

    def has_perms(self, perms, property_id=None):
        if self.is_superuser or self.is_admin:
            return True  # Superusers have all permissions

        user_perms = self.get_perms(property_id)
        return all(perm in user_perms for perm in perms)

    def has_module_perms(self, app_label):
        "Does the user have permissions to view the app `app_label`?"
        if self.is_superuser or self.is_admin:
            return True

        # Simplest possible answer: Yes, always
        return True

    @property
    def is_staff(self):
        """Is the user a member of staff?"""
        return self.is_admin


class UserProfile(models.Model):
    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="user_profile"
    )
    trips = models.IntegerField(default=0)
    image = models.ImageField(upload_to="profile_images/", null=True, blank=True)

    def __str__(self):
        return self.user.name


class PartnerProfile(models.Model):
    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="partner_profile"
    )
    max_rooms = models.IntegerField(default=100)
    has_rental = models.BooleanField(default=True)
    has_transport = models.BooleanField(default=False)
    has_laundry = models.BooleanField(default=True)
    has_food = models.BooleanField(default=True)
    has_shop = models.BooleanField(default=True)
    has_tourism = models.BooleanField(default=True)

    active_property = models.ForeignKey(
        "stay.Property",
        on_delete=models.SET_NULL,
        related_name="active_partners",
        null=True,
        blank=True,
    )

    # Payment and Commission Configuration
    platform_commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal("5.00"),
        help_text="Platform commission rate as percentage (e.g., 5.00 for 5%)",
        validators=[MinValueValidator(0), MaxValueValidator(100)],
    )
    razorpay_linked_account_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="Razorpay linked account identifier for direct transfers",
    )
    razorpay_account_verified = models.BooleanField(
        default=False, help_text="Whether the Razorpay linked account is verified"
    )

    # Audit fields for commission rate changes
    commission_rate_updated_at = models.DateTimeField(
        null=True, blank=True, help_text="Last time commission rate was updated"
    )
    commission_rate_updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="commission_rate_updates",
        help_text="User who last updated the commission rate",
    )

    def __str__(self):
        return self.user.name

    def clean(self):
        """Validate commission rate bounds"""
        from django.core.exceptions import ValidationError

        if self.platform_commission_rate < 0:
            raise ValidationError("Commission rate cannot be negative")
        if self.platform_commission_rate > 100:
            raise ValidationError("Commission rate cannot exceed 100%")

    def save(self, *args, **kwargs):
        # Track commission rate changes
        if self.pk:
            try:
                old_instance = PartnerProfile.objects.get(pk=self.pk)
                if old_instance.platform_commission_rate != self.platform_commission_rate:
                    from django.utils import timezone
                    self.commission_rate_updated_at = timezone.now()
            except PartnerProfile.DoesNotExist:
                # Handle the case where the instance was deleted concurrently
                pass

        self.full_clean()  # Run validation
        super().save(*args, **kwargs)

        if not self.active_property and self.properties.exists():
            self.active_property = self.properties.first()
            super().save(*args, **kwargs)

    def has_valid_razorpay_account(self):
        """Check if partner has a valid Razorpay linked account"""
        return bool(self.razorpay_linked_account_id and self.razorpay_account_verified)


class Role(models.Model):
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True, null=True)
    permissions = models.ManyToManyField("Permission", related_name="roles")

    def __str__(self):
        return self.name


class Permission(models.Model):
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name


class UserRole(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="roles")
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    property = models.ForeignKey(
        to="stay.Property", on_delete=models.CASCADE, null=True, blank=True
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["user", "role", "property"], name="uniq_user_role_property"
            )
        ]

    def __str__(self):
        return f"{self.user.name} - {self.role.name}"
