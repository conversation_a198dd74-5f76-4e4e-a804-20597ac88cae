from django.contrib import admin
from .models import (
    RentalService,
    RentalServiceItem,
    RentalCart,
    RentalCartItems,
    RentalOrder,
    RentalOrderItem,
)


@admin.register(RentalService)
class RentalServiceAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "period",
        "min_period",
        "max_period",
        "type_of_rental",
        "created_at",
        "updated_at",
    )
    list_filter = ("period", "type_of_rental", "created_at", "updated_at")
    search_fields = ("name",)
    ordering = ("-updated_at",)


@admin.register(RentalServiceItem)
class RentalServiceItemAdmin(admin.ModelAdmin):
    list_display = ("name", "price", "service", "deposit", "created_at", "updated_at")
    list_filter = ("service", "created_at", "updated_at")
    search_fields = ("name", "service__name")
    ordering = ("-updated_at",)


@admin.register(RentalCart)
class RentalCartAdmin(admin.ModelAdmin):
    list_display = (
        "guest",
        "status",
        "subtotal",
        "taxes",
        "charges",
        "total",
        "created_at",
        "updated_at",
    )
    list_filter = ("status", "created_at", "updated_at")
    search_fields = ("guest__user__name",)
    ordering = ("-updated_at",)


@admin.register(RentalCartItems)
class RentalCartItemsAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "cart",
        "item",
        "pickup_date",
        "drop_date",
        "pickup_location",
        "no_of_periods",
        "quantity",
        "price",
        "created_at",
        "updated_at",
    )
    list_filter = ("cart", "item", "pickup_location", "created_at", "updated_at")
    search_fields = ("name", "cart__guest__user__name", "item__name")
    ordering = ("-updated_at",)


@admin.register(RentalOrder)
class RentalOrderAdmin(admin.ModelAdmin):
    list_display = (
        "guest",
        "service",
        "cart",
        "status",
        "subtotal",
        "commissions",
        "taxes",
        "charges",
        "total",
        "created_at",
        "updated_at",
    )
    list_filter = ("status", "service", "guest", "created_at", "updated_at")
    search_fields = ("guest__user__name", "service__name")
    ordering = ("-updated_at",)


@admin.register(RentalOrderItem)
class RentalOrderItemAdmin(admin.ModelAdmin):
    list_display = (
        "item",
        "order",
        "pickup_date",
        "drop_date",
        "no_of_periods",
        "pickup_location",
        "quantity",
        "price",
        "created_at",
        "updated_at",
    )
    list_filter = ("order", "item", "pickup_location", "created_at", "updated_at")
    search_fields = ("item__name", "order__guest__user__name", "order__service__name")
    ordering = ("-updated_at",)
