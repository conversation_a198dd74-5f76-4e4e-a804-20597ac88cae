from rest_framework import serializers
from decimal import Decimal


class MonthlyEarningsSerializer(serializers.Serializer):
    """Serializer for monthly earnings data"""

    month = serializers.CharField()
    year = serializers.IntegerField()
    total_earnings = serializers.DecimalField(max_digits=10, decimal_places=2)
    booking_earnings = serializers.DecimalField(max_digits=10, decimal_places=2)
    service_earnings = serializers.DecimalField(max_digits=10, decimal_places=2)
    commission_earned = serializers.DecimalField(max_digits=10, decimal_places=2)


class CommissionBreakdownSerializer(serializers.Serializer):
    """Serializer for commission breakdown data"""

    service_type = serializers.CharField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    commission_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    commission_earned = serializers.DecimalField(max_digits=10, decimal_places=2)


class DeliveryChargesAnalysisSerializer(serializers.Serializer):
    """Serializer for delivery charges analysis"""

    service_type = serializers.CharField()
    total_orders = serializers.IntegerField()
    total_delivery_charges = serializers.DecimalField(max_digits=10, decimal_places=2)
    average_delivery_charge = serializers.DecimalField(max_digits=8, decimal_places=2)


class PayoutSummarySerializer(serializers.Serializer):
    """Serializer for payout summary data"""

    month = serializers.CharField()
    year = serializers.IntegerField()
    vendor_payouts = serializers.DecimalField(max_digits=10, decimal_places=2)
    staff_payouts = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_payouts = serializers.DecimalField(max_digits=10, decimal_places=2)


class ProfitMetricsSerializer(serializers.Serializer):
    """Serializer for profit metrics"""

    month = serializers.CharField()
    year = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_costs = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_profit = serializers.DecimalField(max_digits=10, decimal_places=2)
    average_profit_per_order = serializers.DecimalField(max_digits=8, decimal_places=2)
    total_orders = serializers.IntegerField()


class FinancialReportSerializer(serializers.Serializer):
    """Main serializer for financial reports"""

    report_type = serializers.CharField(default="financial")
    generated_at = serializers.DateTimeField()
    partner_name = serializers.CharField()
    property_name = serializers.CharField()

    monthly_earnings = MonthlyEarningsSerializer(many=True)
    commission_breakdown = CommissionBreakdownSerializer(many=True)
    delivery_charges_analysis = DeliveryChargesAnalysisSerializer(many=True)
    payout_summary = PayoutSummarySerializer(many=True)
    profit_metrics = ProfitMetricsSerializer(many=True)

    # Summary totals
    total_revenue_ytd = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_profit_ytd = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_orders_ytd = serializers.IntegerField()


class VendorPerformanceSerializer(serializers.Serializer):
    """Serializer for vendor performance data"""

    vendor_id = serializers.UUIDField()
    vendor_name = serializers.CharField()
    service_type = serializers.CharField()
    total_orders = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    average_order_value = serializers.DecimalField(max_digits=8, decimal_places=2)
    commission_earned = serializers.DecimalField(max_digits=10, decimal_places=2)
    outstanding_dues = serializers.DecimalField(max_digits=10, decimal_places=2)


class VendorReportSerializer(serializers.Serializer):
    """Main serializer for vendor reports"""

    report_type = serializers.CharField(default="vendor")
    generated_at = serializers.DateTimeField()
    partner_name = serializers.CharField()
    property_name = serializers.CharField()

    total_vendors = serializers.IntegerField()
    vendors_by_service = serializers.DictField()
    top_vendors = VendorPerformanceSerializer(many=True)
    total_outstanding_dues = serializers.DecimalField(max_digits=12, decimal_places=2)


class GuestStayMetricsSerializer(serializers.Serializer):
    """Serializer for guest stay metrics"""

    month = serializers.CharField()
    year = serializers.IntegerField()
    total_guests = serializers.IntegerField()
    average_stay_duration = serializers.DecimalField(max_digits=5, decimal_places=2)
    occupancy_rate = serializers.DecimalField(max_digits=5, decimal_places=2)


class TopGuestSerializer(serializers.Serializer):
    """Serializer for top guest data"""

    guest_id = serializers.UUIDField()
    guest_name = serializers.CharField()
    total_stays = serializers.IntegerField()
    total_spending = serializers.DecimalField(max_digits=10, decimal_places=2)
    average_stay_duration = serializers.DecimalField(max_digits=5, decimal_places=2)
    last_visit = serializers.DateTimeField()


class GuestOrderingMetricsSerializer(serializers.Serializer):
    """Serializer for guest ordering behavior"""

    service_type = serializers.CharField()
    total_orders = serializers.IntegerField()
    unique_guests = serializers.IntegerField()
    ordering_rate = serializers.DecimalField(
        max_digits=5, decimal_places=2
    )  # percentage
    average_order_value = serializers.DecimalField(max_digits=8, decimal_places=2)


class GuestReportSerializer(serializers.Serializer):
    """Main serializer for guest reports"""

    report_type = serializers.CharField(default="guest")
    generated_at = serializers.DateTimeField()
    partner_name = serializers.CharField()
    property_name = serializers.CharField()

    stay_metrics = GuestStayMetricsSerializer(many=True)
    top_guests = TopGuestSerializer(many=True)
    ordering_metrics = GuestOrderingMetricsSerializer(many=True)

    # Summary totals
    total_unique_guests = serializers.IntegerField()
    average_occupancy_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    total_guest_orders = serializers.IntegerField()
