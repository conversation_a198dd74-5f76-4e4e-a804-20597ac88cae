# Generated by Django 4.2.7 on 2025-10-02 05:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('geo', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0001_initial'),
        ('pms', '0001_initial'),
        ('service', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Guest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('group_id', models.CharField(blank=True, max_length=100, null=True)),
                ('checkin_key', models.CharField(max_length=100)),
                ('checked_in', models.BooleanField(default=False)),
                ('check_in_date', models.DateTimeField(blank=True, null=True)),
                ('checked_out', models.BooleanField(default=False)),
                ('payment_completed', models.BooleanField(default=False)),
                ('check_out_date', models.DateTimeField(blank=True, null=True)),
                ('total_orders', models.IntegerField(blank=True, default=0, null=True)),
                ('total_spends', models.FloatField(blank=True, default=0, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Property',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('avg_price', models.FloatField(blank=True, null=True)),
                ('po_address', models.TextField(blank=True, null=True)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='property')),
                ('type', models.PositiveSmallIntegerField(choices=[(1, 'Family'), (2, 'Adventure'), (3, 'Lavish'), (4, 'Budget'), (5, 'Couple')], default=1)),
                ('rooms', models.IntegerField()),
                ('rating', models.PositiveSmallIntegerField(choices=[(1, 'One'), (2, 'Two'), (3, 'Three'), (4, 'Four'), (5, 'Five')], default=5)),
                ('description', models.TextField(blank=True, null=True)),
                ('meal_cost', models.IntegerField(blank=True, null=True)),
                ('directions', models.TextField(blank=True, null=True)),
                ('amenities', models.JSONField(blank=True, null=True)),
                ('policies', models.JSONField(blank=True, default=dict, help_text='Hotel policies for OTA compliance')),
                ('channel_managers', models.JSONField(blank=True, default=dict, help_text="Channel manager integrations configuration. Format: {'aiosell': {'enabled': True, 'config': {...}}, ...}")),
                ('primary_channel_manager', models.CharField(blank=True, help_text="Primary channel manager for this property (e.g., 'aiosell', 'booking_com')", max_length=50, null=True)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='property', to='geo.location')),
                ('staffs', models.ManyToManyField(related_name='properties', to='core.partnerprofile')),
            ],
            options={
                'verbose_name_plural': 'Properties',
            },
        ),
        migrations.CreateModel(
            name='Room',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('room_no', models.CharField(max_length=100)),
                ('type_of_room', models.CharField(blank=True, max_length=100, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('rate', models.FloatField(blank=True, null=True)),
                ('bed', models.IntegerField(default=1)),
                ('max_guests', models.IntegerField()),
                ('occupied', models.BooleanField(default=False)),
                ('checked_in', models.BooleanField(default=False)),
                ('running_total', models.FloatField(default=0.0)),
                ('amenities', models.JSONField(blank=True, null=True)),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='property_rooms', to='stay.property')),
                ('room_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='rooms', to='pms.roomtype')),
            ],
        ),
        migrations.CreateModel(
            name='RoomPhotos',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(blank=True, null=True, upload_to='property/rooms')),
                ('description', models.CharField(blank=True, max_length=100, null=True)),
                ('primary', models.BooleanField(default=False)),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='photos', to='stay.room')),
            ],
            options={
                'verbose_name_plural': 'Room Photos',
            },
        ),
        migrations.CreateModel(
            name='PropertyReview',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('review', models.TextField()),
                ('image', models.ImageField(blank=True, null=True, upload_to='review_images/')),
                ('rating', models.FloatField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='review', to='stay.property')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='property_review', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PropertyPartner',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('commission', models.FloatField(default=0)),
                ('delivery_charges', models.FloatField(default=0)),
                ('pickup_charges', models.FloatField(default=0)),
                ('name', models.CharField(max_length=100)),
                ('in_house', models.BooleanField(default=False)),
                ('partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='partner', to='service.servicepartner')),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='property_partner', to='stay.property')),
            ],
        ),
        migrations.CreateModel(
            name='PropertyMetaData',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100)),
                ('value', models.TextField()),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='metadata', to='stay.property')),
            ],
            options={
                'verbose_name_plural': 'Property metadata',
            },
        ),
        migrations.CreateModel(
            name='GuestReview',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)])),
                ('review_text', models.TextField(blank=True)),
                ('service_rating', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('cleanliness_rating', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('location_rating', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('guest', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='stay.guest')),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='guest_reviews', to='stay.property')),
            ],
        ),
        migrations.AddField(
            model_name='guest',
            name='room',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='guest', to='stay.room'),
        ),
        migrations.AddField(
            model_name='guest',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='guest', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='CheckinRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('room_no', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('COMPLETED', 'Completed'), ('REJECTED', 'Rejected')], default='PENDING', max_length=10)),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='checkin_requests', to='stay.property')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='checkin_requests', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
