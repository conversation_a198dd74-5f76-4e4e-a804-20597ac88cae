from django.contrib import admin
from .models import (
    FoodService,
    FoodServiceItem,
    FoodCart,
    FoodCartItems,
    FoodOrder,
    FoodOrderItem,
)


@admin.register(FoodService)
class FoodServiceAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "partner",
        "veg_only",
        "active_days",
        "opening_time",
        "closing_time",
        "created_at",
        "updated_at",
    )
    list_filter = ("partner", "veg_only", "active_days", "opening_time", "closing_time")
    search_fields = ("name", "partner__name")
    ordering = ("-updated_at",)


@admin.register(FoodServiceItem)
class FoodServiceItemAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "price",
        "service",
        "vegetarian",
        "category",
        "is_special",
        "created_at",
        "updated_at",
    )
    list_filter = ("service", "vegetarian", "category", "is_special")
    search_fields = ("name", "service__name")
    ordering = ("-updated_at",)


@admin.register(FoodCart)
class FoodCartAdmin(admin.ModelAdmin):
    list_display = (
        "guest",
        "status",
        "subtotal",
        "taxes",
        "charges",
        "total",
        "created_at",
        "updated_at",
    )
    list_filter = ("status", "created_at", "updated_at")
    search_fields = ("guest__user__name",)
    ordering = ("-updated_at",)


@admin.register(FoodCartItems)
class FoodCartItemsAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "cart",
        "item",
        "quantity",
        "price",
        "created_at",
        "updated_at",
    )
    list_filter = ("cart", "item")
    search_fields = ("name", "cart__guest__user__name", "item__name")
    ordering = ("-updated_at",)


@admin.register(FoodOrder)
class FoodOrderAdmin(admin.ModelAdmin):
    list_display = (
        "guest",
        "service",
        "cart",
        "status",
        "subtotal",
        "commissions",
        "taxes",
        "charges",
        "total",
        "created_at",
        "updated_at",
    )
    list_filter = ("status", "service", "guest", "created_at", "updated_at")
    search_fields = ("guest__user__name", "service__name")
    ordering = ("-updated_at",)


@admin.register(FoodOrderItem)
class FoodOrderItemAdmin(admin.ModelAdmin):
    list_display = (
        "item",
        "order",
        "quantity",
        "price",
        "rating",
        "created_at",
        "updated_at",
    )
    list_filter = ("order", "item")
    search_fields = ("item__name", "order__guest__user__name", "order__service__name")
    ordering = ("-updated_at",)
