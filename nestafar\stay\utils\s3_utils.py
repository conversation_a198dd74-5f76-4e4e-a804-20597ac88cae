import uuid
import boto3
from botocore.exceptions import ClientError
from django.conf import settings
from django.core.files.storage import default_storage
from io import BytesIO


def upload_pdf_to_s3_and_get_signed_url(pdf_buffer, filename_prefix="invoice"):
    """
    Upload PDF buffer to S3 and return a signed URL for download.
    
    Args:
        pdf_buffer: BytesIO object containing PDF content
        filename_prefix: Prefix for the filename
        
    Returns:
        tuple: (file_path, signed_url) or (None, None) if upload fails
    """
    try:
        # Generate unique filename
        unique_id = str(uuid.uuid4())[:8]
        filename = f"{filename_prefix}_{unique_id}.pdf"
        file_path = f"invoices/{filename}"
        
        # Upload to S3 using Django's default storage
        pdf_buffer.seek(0)
        default_storage.save(file_path, pdf_buffer)
        
        # Generate signed URL
        signed_url = generate_signed_url(file_path)
        
        return file_path, signed_url
        
    except Exception as e:
        print(f"Error uploading PDF to S3: {str(e)}")
        return None, None


def generate_signed_url(file_path, expiration=3600):
    """
    Generate a signed URL for S3 object.
    
    Args:
        file_path: Path to the file in S3 bucket
        expiration: URL expiration time in seconds (default: 1 hour)
        
    Returns:
        str: Signed URL or None if generation fails
    """
    try:
        # Initialize S3 client
        s3_client = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_S3_REGION_NAME
        )
        
        # Generate signed URL
        signed_url = s3_client.generate_presigned_url(
            'get_object',
            Params={
                'Bucket': settings.AWS_STORAGE_BUCKET_NAME,
                'Key': file_path
            },
            ExpiresIn=expiration
        )
        
        return signed_url
        
    except ClientError as e:
        print(f"Error generating signed URL: {str(e)}")
        return None
    except Exception as e:
        print(f"Unexpected error generating signed URL: {str(e)}")
        return None


def delete_pdf_from_s3(file_path):
    """
    Delete PDF file from S3.
    
    Args:
        file_path: Path to the file in S3 bucket
        
    Returns:
        bool: True if deletion successful, False otherwise
    """
    try:
        default_storage.delete(file_path)
        return True
    except Exception as e:
        print(f"Error deleting PDF from S3: {str(e)}")
        return False
