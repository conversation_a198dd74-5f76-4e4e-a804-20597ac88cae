"""
PaymentSplit Model

Tracks individual split calculations and transfers for each payment.
"""

import uuid
from decimal import Decimal
from django.db import models, transaction
from django.db.models import F
from django.core.validators import MinValueValidator, MaxValueValidator

from ..constants import TransferStatus


class PaymentSplit(models.Model):
    """
    Individual payment split tracking for Razorpay Route transfers.

    Each PaymentIntent can have multiple splits (platform, partner, vendors).
    This model tracks each individual transfer and its status.
    """

    # Primary identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    payment_intent = models.ForeignKey(
        "PaymentIntent",
        on_delete=models.CASCADE,
        related_name="splits",
        help_text="Associated payment intent",
    )

    # Split details
    recipient_type = models.Char<PERSON>ield(
        max_length=20,
        choices=[
            ("platform", "Platform Commission"),
            ("partner", "Partner Amount"),
            ("vendor", "Vendor Payout"),
        ],
        help_text="Type of recipient for this split",
    )
    recipient_id = models.Char<PERSON>ield(
        max_length=100,
        null=True,
        blank=True,
        help_text="ID of the recipient (partner ID, vendor ID, etc.)",
    )
    recipient_name = models.CharField(max_length=200, help_text="Name of the recipient")

    # Amount details
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal("0.01"))],
        help_text="Split amount in rupees",
    )
    percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Percentage of total amount (if applicable)",
    )

    # Razorpay transfer details
    razorpay_account_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="Razorpay linked account ID for transfer",
    )
    razorpay_transfer_id = models.CharField(
        max_length=100, null=True, blank=True, help_text="Razorpay transfer ID"
    )

    # Transfer status and tracking
    status = models.CharField(
        max_length=20,
        choices=TransferStatus.choices,
        default=TransferStatus.PENDING,
        help_text="Current transfer status",
    )
    transfer_initiated_at = models.DateTimeField(
        null=True, blank=True, help_text="When transfer was initiated"
    )
    transfer_completed_at = models.DateTimeField(
        null=True, blank=True, help_text="When transfer was completed"
    )

    # Settlement details
    settlement_id = models.CharField(
        max_length=100, null=True, blank=True, help_text="Settlement ID from Razorpay"
    )
    settled_at = models.DateTimeField(
        null=True, blank=True, help_text="When amount was settled to recipient"
    )

    # Error handling
    error_code = models.CharField(
        max_length=50, null=True, blank=True, help_text="Error code if transfer failed"
    )
    error_message = models.TextField(
        null=True, blank=True, help_text="Error message if transfer failed"
    )
    retry_count = models.PositiveIntegerField(
        default=0, help_text="Number of retry attempts"
    )

    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Additional metadata
    metadata = models.JSONField(
        default=dict, help_text="Additional metadata for this split"
    )

    class Meta:
        db_table = "payments_payment_split"
        indexes = [
            models.Index(fields=["payment_intent", "recipient_type"]),
            models.Index(fields=["razorpay_transfer_id"]),
            models.Index(fields=["status", "created_at"]),
            models.Index(fields=["recipient_type", "status"]),
        ]
        ordering = ["created_at"]

    def __str__(self):
        return f"Split {self.recipient_type} - ₹{self.amount} ({self.status})"

    def get_razorpay_amount(self):
        """Convert amount to paise for Razorpay API"""
        return int(self.amount * 100)

    def is_completed(self):
        """Check if transfer is completed"""
        return self.status == TransferStatus.PROCESSED

    def is_failed(self):
        """Check if transfer failed"""
        return self.status == TransferStatus.FAILED

    def can_retry(self):
        """Check if transfer can be retried"""
        from ..constants.payment_constants import MAX_WEBHOOK_RETRY_ATTEMPTS

        return self.is_failed() and self.retry_count < MAX_WEBHOOK_RETRY_ATTEMPTS

    def mark_as_initiated(self, razorpay_transfer_id):
        """Mark transfer as initiated"""
        from django.utils import timezone

        # Ensure the status, transfer id and initiation timestamp are saved atomically
        with transaction.atomic():
            self.status = TransferStatus.PROCESSING
            self.razorpay_transfer_id = razorpay_transfer_id
            self.transfer_initiated_at = timezone.now()
            self.save()

    def mark_as_completed(self, settlement_id=None):
        """Mark transfer as completed"""
        from django.utils import timezone

        # Persist the status and timestamps atomically and only update modified fields
        with transaction.atomic():
            self.status = TransferStatus.PROCESSED
            self.transfer_completed_at = timezone.now()
            update_fields = ["status", "transfer_completed_at"]

            if settlement_id:
                self.settlement_id = settlement_id
                self.settled_at = timezone.now()
                update_fields.extend(["settlement_id", "settled_at"])

            self.save(update_fields=update_fields)

    def mark_as_failed(self, error_code=None, error_message=None):
        """Mark transfer as failed"""
        # Use an atomic update and F-expression to avoid race conditions
        with transaction.atomic():
            self.status = TransferStatus.FAILED

            if error_code:
                self.error_code = error_code
            if error_message:
                self.error_message = error_message

            # Increment retry_count in the DB to avoid lost updates under concurrency
            self.retry_count = F('retry_count') + 1

            # Persist only the changed fields
            self.save(update_fields=['status', 'retry_count', 'error_code', 'error_message'])

            # Refresh the instance so retry_count is the actual integer value (not an F expression)
            try:
                self.refresh_from_db(fields=['retry_count'])
            except Exception:
                # If refresh fails for any reason, it's non-fatal; instance may hold F expression
                pass

    def get_transfer_payload(self):
        """Get payload for Razorpay transfer API"""
        if not self.razorpay_account_id:
            return None

        if not self.amount or self.amount <= 0:
            raise ValueError("Invalid amount for transfer")

        if not self.recipient_name:
            raise ValueError("Recipient name is required for transfer")

        return {
            "account": self.razorpay_account_id,
            "amount": self.get_razorpay_amount(),
            "currency": "INR",
            "notes": {
                "split_id": str(self.id),
                "payment_intent_id": str(self.payment_intent.id),
                "recipient_type": self.recipient_type,
                "recipient_name": self.recipient_name,
            },
        }
