"""
Payment Constants

Defines constants and enums used throughout the payment system.
"""

from django.db import models


class PaymentStatus(models.TextChoices):
    """Payment status choices"""

    PENDING = "pending", "Pending"
    PROCESSING = "processing", "Processing"
    COMPLETED = "completed", "Completed"
    FAILED = "failed", "Failed"
    CANCELLED = "cancelled", "Cancelled"
    REFUNDED = "refunded", "Refunded"
    PARTIALLY_REFUNDED = "partially_refunded", "Partially Refunded"


class PaymentContext(models.TextChoices):
    """Payment context types"""

    CHECKOUT = "checkout", "Guest Checkout"
    PRECHECKIN = "precheckin", "Precheckin Upfront"
    SERVICE_ORDER = "service_order", "Service Order"
    BOOKING = "booking", "Booking Payment"


class PaymentMethod(models.TextChoices):
    """Payment method choices"""

    CARD = "card", "Credit/Debit Card"
    UPI = "upi", "UPI"
    NETBANKING = "netbanking", "Net Banking"
    WALLET = "wallet", "Digital Wallet"
    EMI = "emi", "EMI"
    BANK_TRANSFER = "bank_transfer", "Bank Transfer"


class TransferStatus(models.TextChoices):
    """Transfer status choices"""

    PENDING = "pending", "Pending"
    PROCESSING = "processing", "Processing"
    PROCESSED = "processed", "Processed"
    REVERSED = "reversed", "Reversed"
    FAILED = "failed", "Failed"
    ON_HOLD = "on_hold", "On Hold"


class WebhookEventType(models.TextChoices):
    """Webhook event types"""

    PAYMENT_AUTHORIZED = "payment.authorized", "Payment Authorized"
    PAYMENT_CAPTURED = "payment.captured", "Payment Captured"
    PAYMENT_FAILED = "payment.failed", "Payment Failed"
    TRANSFER_PROCESSED = "transfer.processed", "Transfer Processed"
    TRANSFER_FAILED = "transfer.failed", "Transfer Failed"
    TRANSFER_REVERSED = "transfer.reversed", "Transfer Reversed"
    PAYMENT_LINK_PAID = "payment_link.paid", "Payment Link Paid"
    PAYMENT_LINK_CANCELLED = "payment_link.cancelled", "Payment Link Cancelled"


# Default commission rates
DEFAULT_PLATFORM_COMMISSION_RATE = 5.00  # 5%

# Amount limits (in paise for Razorpay)
MIN_PAYMENT_AMOUNT = 100  # ₹1.00
MAX_PAYMENT_AMOUNT = 10000000  # ₹1,00,000

# Timeout settings (in seconds)
PAYMENT_LINK_EXPIRY_HOURS = 24
WEBHOOK_PROCESSING_TIMEOUT = 30

# Retry settings
MAX_WEBHOOK_RETRY_ATTEMPTS = 3
WEBHOOK_RETRY_DELAY_SECONDS = 5


class RazorpayWebhookEvents(models.TextChoices):
    """Razorpay webhook event types"""
    PAYMENT_CAPTURED = 'payment.captured', 'Payment Captured'
    PAYMENT_FAILED = 'payment.failed', 'Payment Failed'
    PAYMENT_LINK_PAID = 'payment_link.paid', 'Payment Link Paid'
    PAYMENT_LINK_CANCELLED = 'payment_link.cancelled', 'Payment Link Cancelled'
    PAYMENT_LINK_EXPIRED = 'payment_link.expired', 'Payment Link Expired'
    TRANSFER_PROCESSED = 'transfer.processed', 'Transfer Processed'
    TRANSFER_FAILED = 'transfer.failed', 'Transfer Failed'
    TRANSFER_REVERSED = 'transfer.reversed', 'Transfer Reversed'

    # Account webhook events
    ACCOUNT_ACTIVATED = 'account.activated', 'Account Activated'
    ACCOUNT_SUSPENDED = 'account.suspended', 'Account Suspended'
    ACCOUNT_UNDER_REVIEW = 'account.under_review', 'Account Under Review'


class AccountStatus(models.TextChoices):
    """Partner Razorpay account status"""
    PENDING = 'pending', 'Pending'
    UNDER_REVIEW = 'under_review', 'Under Review'
    ACTIVE = 'active', 'Active'
    SUSPENDED = 'suspended', 'Suspended'
    REJECTED = 'rejected', 'Rejected'


class KYCStatus(models.TextChoices):
    """KYC verification status"""
    PENDING = 'pending', 'Pending'
    SUBMITTED = 'submitted', 'Submitted'
    UNDER_REVIEW = 'under_review', 'Under Review'
    VERIFIED = 'verified', 'Verified'
    REJECTED = 'rejected', 'Rejected'
    NEEDS_CLARIFICATION = 'needs_clarification', 'Needs Clarification'


class VerificationStatus(models.TextChoices):
    """General verification status"""
    PENDING = 'pending', 'Pending'
    IN_PROGRESS = 'in_progress', 'In Progress'
    VERIFIED = 'verified', 'Verified'
    FAILED = 'failed', 'Failed'
    REJECTED = 'rejected', 'Rejected'


class BankAccountType(models.TextChoices):
    """Bank account types"""
    SAVINGS = 'savings', 'Savings'
    CURRENT = 'current', 'Current'


class DocumentType(models.TextChoices):
    """KYC document types"""
    PAN_CARD = 'pan_card', 'PAN Card'
    AADHAAR_CARD = 'aadhaar_card', 'Aadhaar Card'
    BANK_STATEMENT = 'bank_statement', 'Bank Statement'
    CANCELLED_CHEQUE = 'cancelled_cheque', 'Cancelled Cheque'
    BUSINESS_REGISTRATION = 'business_registration', 'Business Registration'
    GST_CERTIFICATE = 'gst_certificate', 'GST Certificate'
    PARTNERSHIP_DEED = 'partnership_deed', 'Partnership Deed'
    MOA_AOA = 'moa_aoa', 'MOA/AOA'
    TRUST_DEED = 'trust_deed', 'Trust Deed'
    SOCIETY_REGISTRATION = 'society_registration', 'Society Registration'
