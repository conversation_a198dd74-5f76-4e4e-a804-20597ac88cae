from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import ModelViewSet
from rest_framework.decorators import action
from django.db import transaction
from core.permissions import PropertyPermission
from nestafar.responses import SuccessResponse, BadRequestResponse, CreateResponse
from service.serializers import (
    StaffProfileSerializer,
    JobSerializer,
    JobStatusLogSerializer,
)
from service.models import Staff, StaffProfile, Job, JobStatusLog
from django_filters import rest_framework as filters
from rest_framework.filters import OrderingFilter, SearchFilter


class StaffFilter(filters.FilterSet):
    role = filters.CharFilter(field_name="role")
    is_active = filters.BooleanFilter(field_name="is_active")

    class Meta:
        model = StaffProfile
        fields = ["role", "is_active"]


class StaffViewSet(ModelViewSet):
    permission_classes = [PropertyPermission, IsAuthenticated]
    filter_backends = [filters.DjangoFilterBackend, OrderingFilter, SearchFilter]
    filterset_class = StaffFilter

    def get_queryset(self):
        if not hasattr(self.request, "property"):
            return StaffProfile.objects.none()
        return StaffProfile.objects.select_related(
            "staff", "property", "staff__user"
        ).filter(property=self.request.property)

    def get_serializer_class(self):
        return StaffProfileSerializer

    def create(self, request, *args, **kwargs):
        try:
            property_obj = getattr(request, "property", None)
            if not property_obj:
                return BadRequestResponse(message="Property not found")
            data = request.data.copy()
            with transaction.atomic():
                # Create Staff from user if not exists
                user_id = data.get("user")
                if not user_id:
                    return BadRequestResponse(message="user is required")
                staff, _ = Staff.objects.get_or_create(user_id=user_id)
                profile = StaffProfile.objects.create(
                    staff=staff,
                    property=property_obj,
                    role=data.get("role"),
                    contact_email=data.get("contact_email"),
                    alt_phone=data.get("alt_phone"),
                    is_active=data.get("is_active", True),
                    is_gig_worker=data.get("is_gig_worker", False),
                    earnings_rate=data.get("earnings_rate"),
                )
            return CreateResponse(data=StaffProfileSerializer(profile).data)
        except Exception as e:
            return BadRequestResponse(message=str(e))


class JobFilter(filters.FilterSet):
    status = filters.CharFilter(field_name="status")
    type = filters.CharFilter(field_name="type")
    start_date = filters.DateTimeFilter(field_name="created_at", lookup_expr="gte")
    end_date = filters.DateTimeFilter(field_name="created_at", lookup_expr="lte")

    class Meta:
        model = Job
        fields = ["status", "type", "start_date", "end_date"]


class JobViewSet(ModelViewSet):
    permission_classes = [PropertyPermission, IsAuthenticated]
    filter_backends = [filters.DjangoFilterBackend, OrderingFilter, SearchFilter]
    filterset_class = JobFilter

    def get_queryset(self):
        if not hasattr(self.request, "property"):
            return Job.objects.none()
        qs = Job.objects.select_related("staff__staff__user").filter(
            property=self.request.property
        )

        user = self.request.user
        # Staff view: only their jobs if the user is staff
        if hasattr(user, "staff"):
            staff_profiles = user.staff.profiles.filter(property=self.request.property)
            if staff_profiles.exists():
                qs = qs.filter(staff__in=staff_profiles)
        # Partner/Manager view: partners can see all jobs for the property
        elif getattr(user, "is_partner", False):
            pass  # full qs
        else:
            qs = Job.objects.none()
        return qs

    def get_serializer_class(self):
        return JobSerializer

    def perform_create(self, serializer):
        serializer.save(property=self.request.property)

    def create(self, request, *args, **kwargs):
        try:
            data = request.data.copy()
            if not hasattr(request, "property"):
                return BadRequestResponse(message="Property not found")

            # Ensure housekeeping jobs include a room
            if data.get("type") == Job.JobType.HOUSEKEEPING and not data.get("room"):
                return BadRequestResponse(
                    message="room is required for housekeeping jobs"
                )

            with transaction.atomic():
                job = Job.objects.create(
                    property=request.property,
                    staff_id=data.get("staff"),
                    room=data.get("room"),
                    status=data.get("status", Job.JobStatus.PENDING),
                    initiated_by=data.get("initiated_by", Job.InitiatedBy.AUTO_CREATED),
                    type=data.get("type"),
                    pickup_location=data.get("pickup_location"),
                    dropoff_location=data.get("dropoff_location"),
                    earnings=data.get("earnings"),
                    title=data.get("title"),
                    description=data.get("description"),
                    payload=data.get("payload") or {},
                )
            return CreateResponse(data=JobSerializer(job).data)
        except Exception as e:
            return BadRequestResponse(message=str(e))

    def update(self, request, *args, **kwargs):
        try:
            job = self.get_object()
            prev_status = job.status
            partial = kwargs.pop("partial", True)
            serializer = JobSerializer(job, data=request.data, partial=partial)
            if not serializer.is_valid():
                return BadRequestResponse(data=serializer.errors)
            with transaction.atomic():
                job = serializer.save()
                if prev_status != job.status:
                    JobStatusLog.objects.create(
                        job=job,
                        previous_status=prev_status,
                        new_status=job.status,
                        changed_by=request.user,
                        note=request.data.get("note"),
                    )
            return SuccessResponse(data=JobSerializer(job).data)
        except Exception as e:
            return BadRequestResponse(message=str(e))

    @action(detail=True, methods=["get"])
    def logs(self, request, pk=None):
        job = self.get_object()
        logs = job.status_logs.order_by("-created_at")
        return SuccessResponse(data=JobStatusLogSerializer(logs, many=True).data)
