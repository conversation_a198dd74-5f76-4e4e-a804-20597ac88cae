"""
Payment Models Tests

Tests for payment system models including PaymentIntent,
PaymentSplit, and PaymentWebhookEvent.
"""

from decimal import Decimal
from datetime import timedelta
from django.utils import timezone

from ..models import PaymentWebhookEvent
from ..constants import PaymentContext, PaymentStatus, TransferStatus
from .test_base import PaymentTestBase


class PaymentIntentModelTest(PaymentTestBase):
    """Tests for PaymentIntent model"""
    
    def test_payment_intent_creation(self):
        """Test creating a payment intent"""
        payment_intent = self.create_payment_intent()
        
        self.assertIsNotNone(payment_intent.id)
        self.assertIsNotNone(payment_intent.reference_number)
        self.assertTrue(payment_intent.reference_number.startswith('PAY'))
        self.assertEqual(payment_intent.status, PaymentStatus.PENDING)
        self.assertEqual(payment_intent.context, PaymentContext.CHECKOUT)
        self.assertEqual(payment_intent.partner, self.partner_profile)
        self.assertEqual(payment_intent.guest, self.guest)
    
    def test_payment_intent_reference_number_unique(self):
        """Test that reference numbers are unique"""
        payment1 = self.create_payment_intent()
        payment2 = self.create_payment_intent()
        
        self.assertNotEqual(payment1.reference_number, payment2.reference_number)
    
    def test_payment_intent_mark_as_paid(self):
        """Test marking payment intent as paid"""
        payment_intent = self.create_payment_intent()
        razorpay_payment_id = 'pay_test123'
        
        payment_intent.mark_as_paid(razorpay_payment_id=razorpay_payment_id)
        
        self.assertEqual(payment_intent.status, PaymentStatus.COMPLETED)
        self.assertEqual(payment_intent.razorpay_payment_id, razorpay_payment_id)
        self.assertIsNotNone(payment_intent.paid_at)
    
    def test_payment_intent_is_paid(self):
        """Test is_paid method"""
        payment_intent = self.create_payment_intent()
        
        self.assertFalse(payment_intent.is_paid())
        
        payment_intent.mark_as_paid()
        self.assertTrue(payment_intent.is_paid())
    
    def test_payment_intent_is_expired(self):
        """Test is_expired method"""
        # Create expired payment intent
        expired_payment = self.create_payment_intent(
            expires_at=timezone.now() - timezone.timedelta(hours=1)
        )
        self.assertTrue(expired_payment.is_expired())
        
        # Create non-expired payment intent
        active_payment = self.create_payment_intent(
            expires_at=timezone.now() + timezone.timedelta(hours=1)
        )
        self.assertFalse(active_payment.is_expired())
    
    def test_payment_intent_can_be_cancelled(self):
        """Test can_be_cancelled method"""
        payment_intent = self.create_payment_intent()
        
        # Pending payment can be cancelled
        self.assertTrue(payment_intent.can_be_cancelled())
        
        # Paid payment cannot be cancelled
        payment_intent.mark_as_paid()
        self.assertFalse(payment_intent.can_be_cancelled())
    
    def test_payment_intent_precheckin_context(self):
        """Test payment intent with precheckin context"""
        payment_intent = self.create_payment_intent(
            context=PaymentContext.PRECHECKIN,
            guest=None  # Remove guest for precheckin
        )
        
        self.assertEqual(payment_intent.context, PaymentContext.PRECHECKIN)
        self.assertEqual(payment_intent.precheckin, self.precheckin)
        self.assertIsNone(payment_intent.guest)
    
    def test_payment_intent_str_representation(self):
        """Test string representation"""
        payment_intent = self.create_payment_intent()
        
        expected_str = f"Payment {payment_intent.reference_number} - ₹{payment_intent.total_amount}"
        self.assertEqual(str(payment_intent), expected_str)


class PaymentSplitModelTest(PaymentTestBase):
    """Tests for PaymentSplit model"""
    
    def test_payment_split_creation(self):
        """Test creating a payment split"""
        payment_intent = self.create_payment_intent()
        payment_split = self.create_payment_split(payment_intent)
        
        self.assertIsNotNone(payment_split.id)
        self.assertEqual(payment_split.payment_intent, payment_intent)
        self.assertEqual(payment_split.recipient_type, 'partner')
        self.assertEqual(payment_split.status, TransferStatus.PENDING)
    
    def test_payment_split_mark_as_initiated(self):
        """Test marking split as initiated"""
        payment_intent = self.create_payment_intent()
        payment_split = self.create_payment_split(payment_intent)
        
        transfer_id = 'trf_test123'
        payment_split.mark_as_initiated(transfer_id)
        
        self.assertEqual(payment_split.status, TransferStatus.PROCESSING)
        self.assertEqual(payment_split.razorpay_transfer_id, transfer_id)
        self.assertIsNotNone(payment_split.transfer_initiated_at)
    
    def test_payment_split_mark_as_completed(self):
        """Test marking split as completed"""
        payment_intent = self.create_payment_intent()
        payment_split = self.create_payment_split(payment_intent)
        
        payment_split.mark_as_initiated('trf_test123')
        payment_split.mark_as_completed(settlement_id='setl_test123')
        
        self.assertEqual(payment_split.status, TransferStatus.PROCESSED)
        self.assertIsNotNone(payment_split.transfer_completed_at)
    
    def test_payment_split_mark_as_failed(self):
        """Test marking split as failed"""
        payment_intent = self.create_payment_intent()
        payment_split = self.create_payment_split(payment_intent)
        
        error_message = 'Insufficient balance'
        payment_split.mark_as_failed(error_message=error_message)
        
        self.assertEqual(payment_split.status, TransferStatus.FAILED)
        self.assertEqual(payment_split.error_message, error_message)
        self.assertEqual(payment_split.retry_count, 1)
    
    def test_payment_split_can_retry(self):
        """Test can_retry method"""
        payment_intent = self.create_payment_intent()
        payment_split = self.create_payment_split(payment_intent)
        
        # Fresh split can be retried
        payment_split.mark_as_failed()
        self.assertTrue(payment_split.can_retry())
        
        # Split with max retries cannot be retried
        payment_split.retry_count = 3
        payment_split.save()
        self.assertFalse(payment_split.can_retry())
        
        # Completed split cannot be retried
        payment_split.status = TransferStatus.PROCESSED
        payment_split.retry_count = 0
        payment_split.save()
        self.assertFalse(payment_split.can_retry())
    
    def test_payment_split_is_completed(self):
        """Test is_completed method"""
        payment_intent = self.create_payment_intent()
        payment_split = self.create_payment_split(payment_intent)
        
        self.assertFalse(payment_split.is_completed())
        
        payment_split.mark_as_initiated('trf_test123')
        payment_split.mark_as_completed()
        self.assertTrue(payment_split.is_completed())
    
    def test_payment_split_get_transfer_payload(self):
        """Test get_transfer_payload method"""
        payment_intent = self.create_payment_intent()
        payment_split = self.create_payment_split(payment_intent, amount=Decimal('500.00'))
        
        payload = payment_split.get_transfer_payload()
        
        expected_payload = {
            'account': payment_split.razorpay_account_id,
            'amount': 50000,  # 500.00 in paise
            'currency': 'INR',
            'notes': {
                'recipient_type': payment_split.recipient_type,
                'recipient_id': payment_split.recipient_id,
                'recipient_name': payment_split.recipient_name,
                'payment_intent_id': str(payment_intent.id),
            }
        }
        
        self.assertEqual(payload, expected_payload)
    
    def test_payment_split_without_razorpay_account(self):
        """Test split without Razorpay account ID"""
        payment_intent = self.create_payment_intent()
        payment_split = self.create_payment_split(
            payment_intent, 
            razorpay_account_id=None
        )
        
        payload = payment_split.get_transfer_payload()
        self.assertIsNone(payload)
    
    def test_payment_split_str_representation(self):
        """Test string representation"""
        payment_intent = self.create_payment_intent()
        payment_split = self.create_payment_split(payment_intent)
        
        expected_str = f"Split ₹{payment_split.amount} to {payment_split.recipient_name}"
        self.assertEqual(str(payment_split), expected_str)


class PaymentWebhookEventModelTest(PaymentTestBase):
    """Tests for PaymentWebhookEvent model"""
    
    def test_webhook_event_creation(self):
        """Test creating a webhook event"""
        webhook_event = self.create_webhook_event()
        
        self.assertIsNotNone(webhook_event.id)
        self.assertIsNotNone(webhook_event.event_id)
        self.assertEqual(webhook_event.event_type, 'payment.captured')
        self.assertEqual(webhook_event.entity_type, 'payment')
        self.assertEqual(webhook_event.entity_id, 'pay_test123')
        self.assertFalse(webhook_event.processed)
    
    def test_webhook_event_create_from_webhook(self):
        """Test creating webhook event from webhook data"""
        webhook_data = {
            'event': 'payment.captured',
            'payload': {
                'payment': {
                    'entity': {
                        'id': 'pay_test456',
                        'status': 'captured',
                        'amount': 150000,
                    }
                }
            }
        }
        
        webhook_event = PaymentWebhookEvent.create_from_webhook(
            webhook_data,
            headers={'X-Razorpay-Signature': 'test_signature'},
            source_ip='127.0.0.1'
        )
        
        self.assertEqual(webhook_event.event_type, 'payment.captured')
        self.assertEqual(webhook_event.entity_type, 'payment')
        self.assertEqual(webhook_event.entity_id, 'pay_test456')
        self.assertEqual(webhook_event.source_ip, '127.0.0.1')
        self.assertIn('X-Razorpay-Signature', webhook_event.headers)
    
    def test_webhook_event_mark_as_processed(self):
        """Test marking webhook event as processed"""
        webhook_event = self.create_webhook_event()
        
        webhook_event.mark_as_processed()
        
        self.assertTrue(webhook_event.processed)
        self.assertIsNotNone(webhook_event.processed_at)
    
    def test_webhook_event_mark_processing_error(self):
        """Test marking webhook event with processing error"""
        webhook_event = self.create_webhook_event()
        
        error_message = 'Payment not found'
        webhook_event.mark_processing_error(error_message)
        
        self.assertFalse(webhook_event.processed)
        self.assertEqual(webhook_event.processing_error, error_message)
        self.assertIsNotNone(webhook_event.last_error_at)
        self.assertEqual(webhook_event.processing_attempts, 1)
    
    def test_webhook_event_can_retry_processing(self):
        """Test can_retry_processing method"""
        webhook_event = self.create_webhook_event()
        
        # Fresh event can be retried
        self.assertTrue(webhook_event.can_retry_processing())
        
        # Event with max attempts cannot be retried
        webhook_event.processing_attempts = 5
        webhook_event.save()
        self.assertFalse(webhook_event.can_retry_processing())
        
        # Processed event cannot be retried
        webhook_event.processed = True
        webhook_event.processing_attempts = 1
        webhook_event.save()
        self.assertFalse(webhook_event.can_retry_processing())
    
    def test_webhook_event_is_payment_event(self):
        """Test is_payment_event method"""
        payment_event = self.create_webhook_event(event_type='payment.captured')
        self.assertTrue(payment_event.is_payment_event())
        
        transfer_event = self.create_webhook_event(
            event_type='transfer.processed',
            entity_id='trf_test123'
        )
        transfer_event.entity_type = 'transfer'
        transfer_event.save()
        self.assertFalse(transfer_event.is_payment_event())
    
    def test_webhook_event_is_transfer_event(self):
        """Test is_transfer_event method"""
        transfer_event = self.create_webhook_event(
            event_type='transfer.processed',
            entity_id='trf_test123'
        )
        transfer_event.entity_type = 'transfer'
        transfer_event.save()
        self.assertTrue(transfer_event.is_transfer_event())
        
        payment_event = self.create_webhook_event(event_type='payment.captured')
        self.assertFalse(payment_event.is_transfer_event())
    
    def test_webhook_event_get_payment_data(self):
        """Test get_payment_data method"""
        webhook_event = self.create_webhook_event()
        payment_data = webhook_event.get_payment_data()
        
        self.assertEqual(payment_data['id'], 'pay_test123')
        self.assertEqual(payment_data['status'], 'captured')
        self.assertEqual(payment_data['amount'], 100000)
    
    def test_webhook_event_get_entity_data(self):
        """Test get_entity_data method"""
        webhook_event = self.create_webhook_event()
        entity_data = webhook_event.get_entity_data()
        
        self.assertEqual(entity_data['id'], 'pay_test123')
        self.assertEqual(entity_data['status'], 'captured')
    
    def test_webhook_event_str_representation(self):
        """Test string representation"""
        webhook_event = self.create_webhook_event()
        
        expected_str = f"Webhook {webhook_event.event_type} - {webhook_event.entity_id}"
        self.assertEqual(str(webhook_event), expected_str)
