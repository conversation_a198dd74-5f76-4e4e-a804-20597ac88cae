import logging
import json
import hashlib
import hmac
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.conf import settings
from django.utils.decorators import method_decorator
from django.views import View
from notification.tasks import update_message_status

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name="dispatch")
class WhatsAppWebhookView(View):
    """
    WhatsApp Webhook View to handle message status updates and incoming messages
    """

    def get(self, request):
        """
        Handle webhook verification from WhatsApp
        """
        verify_token = request.GET.get("hub.verify_token")
        challenge = request.GET.get("hub.challenge")
        mode = request.GET.get("hub.mode")

        if (
            mode == "subscribe"
            and verify_token
            and challenge
            and hmac.compare_digest(
                verify_token, getattr(settings, "WHATSAPP_WEBHOOK_VERIFY_TOKEN", "")
            )
        ):
            logger.info("WhatsApp webhook verified successfully")
            return HttpResponse(challenge, content_type="text/plain")
        else:
            logger.warning("WhatsApp webhook verification failed")
            return HttpResponse("Forbidden", status=403)

    def post(self, request):
        """
        Handle incoming webhook data from WhatsApp
        """
        try:
            # Verify webhook signature
            if not self._verify_signature(request):
                logger.warning("WhatsApp webhook signature verification failed")
                return HttpResponse("Forbidden", status=403)

            # Parse webhook data
            data = json.loads(request.body)

            # Process webhook data
            self._process_webhook_data(data)

            return JsonResponse({"status": "success"})

        except json.JSONDecodeError:
            logger.error("Invalid JSON in WhatsApp webhook")
            return JsonResponse({"error": "Invalid JSON"}, status=400)
        except Exception as e:
            logger.error(f"Error processing WhatsApp webhook: {str(e)}")
            return JsonResponse({"error": "Internal server error"}, status=500)

    def _verify_signature(self, request):
        """
        Verify the webhook signature from WhatsApp
        """
        signature = request.headers.get("X-Hub-Signature-256")
        if not signature:
            return False

        # Remove 'sha256=' prefix
        # Remove 'sha256=' prefix if present
        if signature.startswith("sha256="):
            signature = signature[7:]  # len('sha256=') = 7
        # Calculate expected signature
        expected_signature = hmac.new(
            settings.WHATSAPP_WEBHOOK_VERIFY_TOKEN.encode("utf-8"),
            request.body,
            hashlib.sha256,
        ).hexdigest()

        return hmac.compare_digest(signature, expected_signature)

    def _process_webhook_data(self, data):
        """
        Process the webhook data and update message statuses
        """
        try:
            # Handle different types of webhook events
            if "entry" in data:
                for entry in data["entry"]:
                    if "changes" in entry:
                        for change in entry["changes"]:
                            if change.get("field") == "messages":
                                self._process_message_changes(change.get("value", {}))

        except Exception as e:
            logger.error(f"Error processing webhook data: {str(e)}")

    def _process_message_changes(self, value):
        """
        Process message status changes
        """
        try:
            # Handle message status updates
            if "statuses" in value:
                for status in value["statuses"]:
                    message_id = status.get("id")
                    status_type = status.get("status")
                    timestamp = status.get("timestamp")

                    if (
                        message_id
                        and status_type
                        and isinstance(timestamp, (int, type(None)))
                    ):
                        # Update message status asynchronously
                        update_message_status.delay(message_id, status_type, timestamp)
                        logger.info(
                            f"Updated message {message_id} status to {status_type}"
                        )
                    else:
                        logger.warning(f"Invalid status data: {status}")

            # Handle incoming messages (optional - for future use)
            if "messages" in value:
                for message in value["messages"]:
                    self._handle_incoming_message(message)

        except Exception as e:
            logger.error(f"Error processing message changes: {str(e)}")

    def _handle_incoming_message(self, message):
        """
        Handle incoming messages from users (for future implementation)
        """
        # This can be implemented for handling user replies
        # For now, just log the incoming message
        logger.info(f"Received incoming message: {message.get('id')}")


# Function-based view for backward compatibility
@csrf_exempt
@require_http_methods(["GET", "POST"])
def whatsapp_webhook(request):
    """
    Function-based webhook handler
    """
    view = WhatsAppWebhookView()
    return view.dispatch(request)
