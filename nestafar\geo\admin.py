from django.contrib import admin
from .models import Country, State, City, AdministrationArea, Location, LocationMetadata


@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = ("name", "code", "phone_code")
    search_fields = ("name", "code", "phone_code")
    ordering = ("name",)
    list_filter = ("code",)


@admin.register(State)
class StateAdmin(admin.ModelAdmin):
    list_display = ("name", "code", "country")
    search_fields = ("name", "code", "country__name")
    list_filter = ("country",)
    ordering = ("country", "name")
    autocomplete_fields = ("country",)


@admin.register(City)
class CityAdmin(admin.ModelAdmin):
    list_display = ("name", "state")
    search_fields = ("name", "state__name", "state__country__name")
    list_filter = ("state__country", "state")
    ordering = ("state__country", "state", "name")
    autocomplete_fields = ("state",)


@admin.register(AdministrationArea)
class AdministrationAreaAdmin(admin.ModelAdmin):
    list_display = ("name", "pincode", "city", "is_active")
    search_fields = ("name", "pincode", "city__name", "city__state__name")
    list_filter = ("is_active", "city__state__country", "city__state")
    ordering = ("city__state__country", "city__state", "city", "name")
    autocomplete_fields = ("city",)


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "location_type",
        "latitude",
        "longitude",
        "is_verified",
        "administrative_area",
    )
    search_fields = ("name", "address", "place_id", "administrative_area__name")
    list_filter = (
        "is_verified",
        "location_type",
        "administrative_area__city__state__country",
    )
    ordering = ("name",)
    autocomplete_fields = ("administrative_area",)
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "name",
                    "description",
                    "address",
                    "location_type",
                    "place_id",
                    "is_verified",
                )
            },
        ),
        (
            "Geographic Information",
            {"fields": ("latitude", "longitude", "administrative_area", "timezone")},
        ),
        ("Timestamps", {"fields": ("created_at", "updated_at")}),
    )
    readonly_fields = ("created_at", "updated_at")


@admin.register(LocationMetadata)
class LocationMetadataAdmin(admin.ModelAdmin):
    list_display = (
        "location",
        "overall_rating",
        "weather_rating",
        "air_quality_index",
        "internet_speed",
    )
    search_fields = ("location__name", "primary_language")
    list_filter = (
        "overall_rating",
        "weather_rating",
        "safety_rating",
        "accessibility_rating",
    )
    ordering = ("location__name",)
    autocomplete_fields = ("location",)
    fieldsets = (
        (None, {"fields": ("location",)}),
        (
            "Ratings",
            {
                "fields": (
                    "overall_rating",
                    "weather_rating",
                    "air_quality_index",
                    "accessibility_rating",
                    "safety_rating",
                    "healthcare_rating",
                )
            },
        ),
        (
            "Additional Info",
            {
                "fields": (
                    "internet_speed",
                    "primary_language",
                    "additional_languages",
                    "amenities",
                )
            },
        ),
        ("Timestamps", {"fields": ("last_updated",)}),
    )
    readonly_fields = ("last_updated",)
