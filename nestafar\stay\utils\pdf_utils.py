import datetime
from io import BytesIO
import os
import platform
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.fonts import addMapping
from django.conf import settings
from service import service_factory
from service.models.order import BaseOrder


def register_unicode_font():
    """
    Register a font that supports Unicode characters including rupee symbol.
    Falls back to system fonts if available.
    """
    try:
        from reportlab.pdfbase.ttfonts import TTFont
        
        # Detect the current operating system and prioritize appropriate fonts
        current_os = platform.system().lower()
        
        # Define font paths based on operating system priority
        if current_os == 'linux':
            font_paths = [
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux - DejaVu (most common)
                '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',  # Linux - Liberation
                '/usr/share/fonts/truetype/noto/NotoSans-Regular.ttf',  # Linux - Noto
                '/usr/share/fonts/TTF/DejaVuSans.ttf',  # Some Linux distros
                '/System/Library/Fonts/Arial Unicode MS.ttf',  # macOS (fallback)
                'C:/Windows/Fonts/arial.ttf',  # Windows (fallback)
            ]
        elif current_os == 'darwin':  # macOS
            font_paths = [
                '/System/Library/Fonts/Arial Unicode MS.ttf',  # macOS
                '/System/Library/Fonts/Helvetica.ttc',  # macOS fallback
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux (fallback)
                'C:/Windows/Fonts/arial.ttf',  # Windows (fallback)
            ]
        elif current_os == 'windows':  # Windows
            font_paths = [
                'C:/Windows/Fonts/arial.ttf',  # Windows
                'C:/Windows/Fonts/calibri.ttf',  # Windows alternative
                '/System/Library/Fonts/Arial Unicode MS.ttf',  # macOS (fallback)
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux (fallback)
            ]
        else:
            # Unknown OS - try all paths
            font_paths = [
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
                '/System/Library/Fonts/Arial Unicode MS.ttf',  # macOS
                'C:/Windows/Fonts/arial.ttf',  # Windows
            ]
        
        for font_path in font_paths:
            # Check if font file exists before trying to register it
            if not os.path.exists(font_path):
                continue
                
            try:
                # Register the base font
                pdfmetrics.registerFont(TTFont('UnicodeFont', font_path))
                
                # For DejaVu fonts, we have separate bold files
                if 'DejaVu' in font_path:
                    bold_path = font_path.replace('.ttf', '-Bold.ttf')
                    if os.path.exists(bold_path):
                        pdfmetrics.registerFont(TTFont('UnicodeFont-Bold', bold_path))
                    else:
                        # Fallback to same font for bold
                        pdfmetrics.registerFont(TTFont('UnicodeFont-Bold', font_path))
                else:
                    # For other fonts, use the same file for bold (ReportLab will handle it)
                    pdfmetrics.registerFont(TTFont('UnicodeFont-Bold', font_path))
                
                # Register italic variant (same font file)
                pdfmetrics.registerFont(TTFont('UnicodeFont-Oblique', font_path))
                
                # Register bold italic variant
                pdfmetrics.registerFont(TTFont('UnicodeFont-BoldOblique', font_path))
                
                # Set up font mappings
                addMapping('UnicodeFont', 0, 0, 'UnicodeFont')  # normal
                addMapping('UnicodeFont', 0, 1, 'UnicodeFont-Oblique')  # italic
                addMapping('UnicodeFont', 1, 0, 'UnicodeFont-Bold')  # bold
                addMapping('UnicodeFont', 1, 1, 'UnicodeFont-BoldOblique')  # bold italic
                
                print(f"Successfully registered font: {font_path}")
                return True
            except Exception as e:
                # Only show error for fonts that exist but failed to load
                print(f"Failed to register existing font {font_path}: {e}")
                continue
        
        print("Warning: No suitable Unicode fonts found. Falling back to default fonts.")
        return False
    except Exception as e:
        print(f"Font registration failed: {e}")
        return False


def format_currency(amount, use_unicode_symbol=True):
    """
    Format currency with proper rupee symbol.
    Uses Unicode ₹ symbol if font supports it, otherwise falls back to Rs.
    """
    if use_unicode_symbol:
        # Try Unicode rupee symbol - should work with registered Unicode font
        return f"₹ {amount:,.2f}"
    else:
        # Fallback to Rs. for compatibility
        return f"Rs. {amount:,.2f}"


def generate_invoice_pdf(room, guests):
    """
    Generate a PDF invoice for the guest's stay and all their orders.
    
    Args:
        room: Room object
        guests: QuerySet or list of Guest objects
        
    Returns:
        BytesIO: PDF content as BytesIO object
    """
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)
    
    # Try to register Unicode font for proper rupee symbol support
    unicode_font_available = register_unicode_font()
    
    # Get styles
    styles = getSampleStyleSheet()
    
    # Create custom styles with Unicode font support
    # Use fallback fonts if Unicode font registration failed
    if unicode_font_available:
        font_name = 'UnicodeFont'
        use_unicode_symbol = True
    else:
        font_name = 'Helvetica'
        use_unicode_symbol = False
    
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue,
        fontName=font_name
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=12,
        textColor=colors.darkblue,
        fontName=font_name
    )
    
    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=6,
        fontName=font_name
    )
    
    # Build the story (content)
    story = []
    
    # Title
    story.append(Paragraph("INVOICE", title_style))
    story.append(Spacer(1, 20))
    
    # Property and Room Information
    story.append(Paragraph(f"<b>Property:</b> {room.property.name}", normal_style))
    story.append(Paragraph(f"<b>Room:</b> {room.room_no} - {room.type_of_room}", normal_style))
    story.append(Paragraph(f"<b>Invoice Date:</b> {datetime.date.today().strftime('%B %d, %Y')}", normal_style))
    story.append(Spacer(1, 20))
    
    # Guest Information
    story.append(Paragraph("Guest Information", heading_style))
    guest_names = [guest.user.name for guest in guests]
    if len(guest_names) == 1:
        story.append(Paragraph(f"<b>Guest:</b> {guest_names[0]}", normal_style))
    else:
        story.append(Paragraph(f"<b>Guests:</b> {', '.join(guest_names)}", normal_style))
    
    # Check-in/Check-out dates
    if guests:
        # Handle both QuerySet and list
        if hasattr(guests, 'values_list'):
            # It's a QuerySet
            earliest_checkin = min(guests.values_list('check_in_date', flat=True))
            latest_checkout = max(guests.values_list('check_out_date', flat=True))
        else:
            # It's a list
            earliest_checkin = min(guest.check_in_date for guest in guests if guest.check_in_date)
            latest_checkout = max(guest.check_out_date for guest in guests if guest.check_out_date)
        story.append(Paragraph(f"<b>Check-in Date:</b> {earliest_checkin.strftime('%B %d, %Y at %I:%M %p')}", normal_style))
        if latest_checkout:
            story.append(Paragraph(f"<b>Check-out Date:</b> {latest_checkout.strftime('%B %d, %Y at %I:%M %p')}", normal_style))
    
    story.append(Spacer(1, 20))
    
    # Stay Charges
    total_bill = 0
    if room.rate and room.rate > 0:
        story.append(Paragraph("Stay Charges", heading_style))
        # Handle both QuerySet and list
        if hasattr(guests, 'values_list'):
            earliest_checkin_dt = min(guests.values_list('check_in_date', flat=True))
        else:
            earliest_checkin_dt = min(guest.check_in_date for guest in guests if guest.check_in_date)
        stay_duration = datetime.date.today() - earliest_checkin_dt.date()
        stay_total = round(room.rate * stay_duration.days, 2)
        total_bill += stay_total
        
        stay_data = [
            ['Description', 'Rate per Day', 'Duration (Days)', 'Total'],
            ['Room Stay', format_currency(room.rate, use_unicode_symbol), str(stay_duration.days), format_currency(stay_total, use_unicode_symbol)]
        ]
        
        stay_table = Table(stay_data, colWidths=[3*inch, 1.5*inch, 1.5*inch, 1.5*inch])
        # Create table style with proper font handling
        table_style = [
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]
        
        # Add font styles based on availability
        if unicode_font_available:
            table_style.extend([
                ('FONTNAME', (0, 0), (-1, 0), 'UnicodeFont-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'UnicodeFont')
            ])
        else:
            table_style.extend([
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica')
            ])
        
        stay_table.setStyle(TableStyle(table_style))
        
        story.append(stay_table)
        story.append(Spacer(1, 20))
    
    # Service Orders
    story.append(Paragraph("Service Orders", heading_style))
    
    all_orders_data = [['Service', 'Order ID', 'Status', 'Items', 'Subtotal', 'Taxes', 'Charges', 'Total']]
    
    for service_name, service_type in service_factory.url_mappings.items():
        order_model = service_factory.service_order_model.get(service_type)
        orders = order_model.objects.filter(
            guest__in=guests,
            status__in=[
                BaseOrder.OrderStatus.COMPLETED,
                BaseOrder.OrderStatus.ACCEPTED,
                BaseOrder.OrderStatus.ONGOING
            ]
        ).order_by('-created_at')
        
        for order in orders:
            # Get order items
            order_items = order.order_items.all()
            items_summary = []
            for item in order_items:
                if item.quantity > 1:
                    items_summary.append(f"{item.item.name} (Qty: {item.quantity})")
                else:
                    items_summary.append(item.item.name)
            
            # Format items text with better line breaks for readability
            if items_summary:
                items_text = " • ".join(items_summary)
                # If text is too long, truncate but keep it readable
                if len(items_text) > 60:
                    items_text = items_text[:57] + "..."
            else:
                items_text = "No items"
            
            all_orders_data.append([
                service_name.title(),
                str(order.id)[:8] + "...",
                order.get_status_display(),
                items_text,
                format_currency(order.subtotal, use_unicode_symbol),
                format_currency(order.taxes, use_unicode_symbol),
                format_currency(order.charges, use_unicode_symbol),
                format_currency(order.total, use_unicode_symbol)
            ])
            total_bill += order.total
    
    # Create orders table
    if len(all_orders_data) > 1:  # More than just header
        orders_table = Table(all_orders_data, colWidths=[1*inch, 1*inch, 1*inch, 2*inch, 0.8*inch, 0.8*inch, 0.8*inch, 0.8*inch])
        # Create orders table style with proper font handling
        orders_table_style = [
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]
        
        # Add font styles based on availability
        if unicode_font_available:
            orders_table_style.extend([
                ('FONTNAME', (0, 0), (-1, 0), 'UnicodeFont-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'UnicodeFont')
            ])
        else:
            orders_table_style.extend([
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica')
            ])
        
        orders_table.setStyle(TableStyle(orders_table_style))
        
        story.append(orders_table)
    else:
        story.append(Paragraph("No service orders found.", normal_style))
    
    story.append(Spacer(1, 20))
    
    # Total Summary
    story.append(Paragraph("Total Summary", heading_style))
    total_data = [
        ['Description', 'Amount'],
        ['Total Bill', format_currency(round(total_bill, 2), use_unicode_symbol)]
    ]
    
    total_table = Table(total_data, colWidths=[4*inch, 2*inch])
    # Create total table style with proper font handling
    total_table_style = [
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('FONTSIZE', (0, 1), (-1, -1), 12),
    ]
    
    # Add font styles based on availability
    if unicode_font_available:
        total_table_style.extend([
            ('FONTNAME', (0, 0), (-1, 0), 'UnicodeFont-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'UnicodeFont')
        ])
    else:
        total_table_style.extend([
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica')
        ])
    
    total_table.setStyle(TableStyle(total_table_style))
    
    story.append(total_table)
    story.append(Spacer(1, 20))
    
    # Footer
    story.append(Paragraph("Thank you for staying with us!", normal_style))
    story.append(Paragraph(f"Generated on: {datetime.datetime.now().strftime('%B %d, %Y at %I:%M %p')}", normal_style))
    
    # Build PDF
    doc.build(story)
    buffer.seek(0)
    return buffer


def generate_multi_room_invoice_pdf(pre_checkin, alloted_rooms, guests_by_room):
    """
    Generate a PDF invoice for a pre-checkin with multiple rooms.
    
    Args:
        pre_checkin: PreCheckin object
        alloted_rooms: QuerySet or list of AllotedRoom objects
        guests_by_room: Dict mapping room_id to list of Guest objects
        
    Returns:
        BytesIO: PDF content as BytesIO object
    """
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)
    
    # Try to register Unicode font for proper rupee symbol support
    unicode_font_available = register_unicode_font()
    
    # Get styles
    styles = getSampleStyleSheet()
    
    # Create custom styles with Unicode font support
    if unicode_font_available:
        font_name = 'UnicodeFont'
        use_unicode_symbol = True
    else:
        font_name = 'Helvetica'
        use_unicode_symbol = False
    
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue,
        fontName=font_name
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=12,
        textColor=colors.darkblue,
        fontName=font_name
    )
    
    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=6,
        fontName=font_name
    )
    
    # Build the story (content)
    story = []
    
    # Title
    story.append(Paragraph("INVOICE", title_style))
    story.append(Spacer(1, 20))
    
    # Property Information
    story.append(Paragraph(f"<b>Property:</b> {pre_checkin.property.name}", normal_style))
    story.append(Paragraph(f"<b>Reservation ID:</b> {str(pre_checkin.id)[:8]}...", normal_style))
    story.append(Paragraph(f"<b>Invoice Date:</b> {datetime.date.today().strftime('%B %d, %Y')}", normal_style))
    story.append(Spacer(1, 20))
    
    # Guest Information
    story.append(Paragraph("Guest Information", heading_style))
    all_guests = []
    for room_guests in guests_by_room.values():
        all_guests.extend(room_guests)
    
    # Get unique guest names
    guest_names = list(set([guest.user.name for guest in all_guests if guest.user]))
    if len(guest_names) == 1:
        story.append(Paragraph(f"<b>Guest:</b> {guest_names[0]}", normal_style))
    else:
        story.append(Paragraph(f"<b>Guests:</b> {', '.join(guest_names)}", normal_style))
    
    # Check-in/Check-out dates
    if all_guests:
        earliest_checkin = min(guest.check_in_date for guest in all_guests if guest.check_in_date)
        latest_checkout = max(guest.check_out_date for guest in all_guests if guest.check_out_date)
        story.append(Paragraph(f"<b>Expected Check-in:</b> {pre_checkin.expected_checkin.strftime('%B %d, %Y at %I:%M %p')}", normal_style))
        story.append(Paragraph(f"<b>Expected Check-out:</b> {pre_checkin.get_expected_checkout().strftime('%B %d, %Y at %I:%M %p')}", normal_style))
        if latest_checkout:
            story.append(Paragraph(f"<b>Actual Check-out:</b> {latest_checkout.strftime('%B %d, %Y at %I:%M %p')}", normal_style))
    
    story.append(Spacer(1, 20))
    
    # Room-wise breakdown
    story.append(Paragraph("Room-wise Breakdown", heading_style))
    total_bill = 0
    
    for alloted_room in alloted_rooms:
        room = alloted_room.room
        room_guests = guests_by_room.get(room.id, [])
        
        # Room header
        story.append(Paragraph(f"<b>Room {room.room_no} - {room.type_of_room}</b>", normal_style))
        
        # Guests in this room
        if room_guests:
            room_guest_names = [guest.user.name for guest in room_guests if guest.user]
            story.append(Paragraph(f"<b>Guests:</b> {', '.join(room_guest_names)}", normal_style))
        
        # Stay charges for this room
        room_total = 0
        if room.rate and room.rate > 0:
            stay_duration = pre_checkin.stay_duration
            stay_total = round(room.rate * stay_duration, 2)
            room_total += stay_total
            
            stay_data = [
                ['Description', 'Rate per Day', 'Duration (Days)', 'Total'],
                ['Room Stay', format_currency(room.rate, use_unicode_symbol), str(stay_duration), format_currency(stay_total, use_unicode_symbol)]
            ]
            
            stay_table = Table(stay_data, colWidths=[3*inch, 1.5*inch, 1.5*inch, 1.5*inch])
            table_style = [
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]
            
            if unicode_font_available:
                table_style.extend([
                    ('FONTNAME', (0, 0), (-1, 0), 'UnicodeFont-Bold'),
                    ('FONTNAME', (0, 1), (-1, -1), 'UnicodeFont')
                ])
            else:
                table_style.extend([
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica')
                ])
            
            stay_table.setStyle(TableStyle(table_style))
            story.append(stay_table)
        
        # Service orders for guests in this room
        if room_guests:
            story.append(Paragraph(f"<b>Service Orders for Room {room.room_no}</b>", normal_style))
            
            orders_data = [['Service', 'Order ID', 'Status', 'Items', 'Subtotal', 'Taxes', 'Charges', 'Total']]
            
            for service_name, service_type in service_factory.url_mappings.items():
                order_model = service_factory.service_order_model.get(service_type)
                orders = order_model.objects.filter(
                    guest__in=room_guests,
                    status__in=[
                        BaseOrder.OrderStatus.COMPLETED,
                        BaseOrder.OrderStatus.ACCEPTED,
                        BaseOrder.OrderStatus.ONGOING
                    ]
                ).order_by('-created_at')
                
                for order in orders:
                    # Get order items
                    order_items = order.order_items.all()
                    items_summary = []
                    for item in order_items:
                        if item.quantity > 1:
                            items_summary.append(f"{item.item.name} (Qty: {item.quantity})")
                        else:
                            items_summary.append(item.item.name)
                    
                    # Format items text
                    if items_summary:
                        items_text = " • ".join(items_summary)
                        if len(items_text) > 60:
                            items_text = items_text[:57] + "..."
                    else:
                        items_text = "No items"
                    
                    orders_data.append([
                        service_name.title(),
                        str(order.id)[:8] + "...",
                        order.get_status_display(),
                        items_text,
                        format_currency(order.subtotal, use_unicode_symbol),
                        format_currency(order.taxes, use_unicode_symbol),
                        format_currency(order.charges, use_unicode_symbol),
                        format_currency(order.total, use_unicode_symbol)
                    ])
                    room_total += order.total
            
            # Create orders table for this room
            if len(orders_data) > 1:  # More than just header
                orders_table = Table(orders_data, colWidths=[1*inch, 1*inch, 1*inch, 2*inch, 0.8*inch, 0.8*inch, 0.8*inch, 0.8*inch])
                orders_table_style = [
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ]
                
                if unicode_font_available:
                    orders_table_style.extend([
                        ('FONTNAME', (0, 0), (-1, 0), 'UnicodeFont-Bold'),
                        ('FONTNAME', (0, 1), (-1, -1), 'UnicodeFont')
                    ])
                else:
                    orders_table_style.extend([
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica')
                    ])
                
                orders_table.setStyle(TableStyle(orders_table_style))
                story.append(orders_table)
            else:
                story.append(Paragraph("No service orders found for this room.", normal_style))
        
        # Room total
        story.append(Paragraph(f"<b>Room {room.room_no} Total: {format_currency(round(room_total, 2), use_unicode_symbol)}</b>", normal_style))
        total_bill += room_total
        
        story.append(Spacer(1, 20))
    
    # Overall Total Summary
    story.append(Paragraph("Total Summary", heading_style))
    total_data = [
        ['Description', 'Amount'],
        ['Total Bill', format_currency(round(total_bill, 2), use_unicode_symbol)]
    ]
    
    total_table = Table(total_data, colWidths=[4*inch, 2*inch])
    total_table_style = [
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('FONTSIZE', (0, 1), (-1, -1), 12),
    ]
    
    if unicode_font_available:
        total_table_style.extend([
            ('FONTNAME', (0, 0), (-1, 0), 'UnicodeFont-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'UnicodeFont')
        ])
    else:
        total_table_style.extend([
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica')
        ])
    
    total_table.setStyle(TableStyle(total_table_style))
    
    story.append(total_table)
    story.append(Spacer(1, 20))
    
    # Footer
    story.append(Paragraph("Thank you for staying with us!", normal_style))
    story.append(Paragraph(f"Generated on: {datetime.datetime.now().strftime('%B %d, %Y at %I:%M %p')}", normal_style))
    
    # Build PDF
    doc.build(story)
    buffer.seek(0)
    return buffer
