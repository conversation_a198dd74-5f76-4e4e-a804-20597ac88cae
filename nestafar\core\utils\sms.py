import requests
from django.conf import settings
from functools import reduce
from enum import Enum


# referred from https://docs.fast2sms.com/#otp-sms-api
class MessageTemplates(Enum):
    OTP = 166966
    ORDER_CONFIRMATION = 166969
    CHECKIN = 167675
    CHECKOUT = 166967
    ORDER_COMPLETE = 166968
    ORDER_UPDATE = 166970
    PRECHECKIN_INITIATED = 166976


class Messages:
    def __init__(self, contact):
        self.contact = contact
        self.__url__ = settings.FAST2SMS_API_ENDPOINT
        self.__token__ = settings.FAST2SMS_API_KEY
        self.headers = {"Authorization": self.__token__}
        self.sender_id = "NESTFR"

    def send_otp(self, otp):
        try:
            body = {
                "route": "otp",
                "variables_values": otp,
                "flash": 0,
                "numbers": self.contact,
            }
            response = requests.post(url=self.__url__, data=body, headers=self.headers)
            return response.status_code == 200
        except Exception as e:
            return False

    def send_bulk_sms(self, message_id, *args):
        try:
            body = {
                "route": "dlt",
                "sender_id": self.sender_id,
                "message": message_id,
                "variables_values": reduce(lambda x, y: f"{x}|{y}", args),
                "flash": 0,
                "numbers": self.contact,
            }
            response = requests.post(url=self.__url__, data=body, headers=self.headers)
            return response.json()
        except Exception as e:
            return False
