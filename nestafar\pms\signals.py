"""
Comprehensive Django signals for AioSell integration.

This module provides signals for automatic synchronization of PMS data
to AioSell when relevant model changes occur.
"""

import logging
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.db import transaction
from django.utils import timezone
from datetime import timedelta
from pms.models import (
    RoomType,
    RatePlan,
    Calendar,
    RoomBlock,
    HotelOTAIntegration,
    RoomBlockSyncLog,
)
from pms.tasks import (
    sync_hotel_initial_data_to_aiosell,
    sync_hotel_configuration_update,
    sync_calendar_to_aiosell,
    sync_room_block_to_aiosell,
    sync_room_type_to_aiosell,
    sync_rate_plan_to_aiosell,
)

logger = logging.getLogger(__name__)


def _has_aiosell_integration(hotel) -> bool:
    """Check if hotel has active AioSell integration."""
    if not hotel:
        return False

    return HotelOTAIntegration.objects.filter(
        hotel=hotel,
        ota_platform__name="aiosell",
        is_active=True,
        ota_platform__is_active=True,
    ).exists()


def _should_sync_to_aiosell(hotel) -> bool:
    """Check if hotel should sync to AioSell based on configuration."""
    if not _has_aiosell_integration(hotel):
        return False

    # Check hotel-specific sync settings
    try:
        config = hotel.get_channel_manager_config("aiosell") or {}
        # Support both flattened format {'sync_enabled': True, ...}
        # and nested format {'enabled': True, 'config': {'sync_enabled': True, ...}}
        if "sync_enabled" in config:
            return config.get("sync_enabled", True)
        inner = config.get("config") if isinstance(config, dict) else None
        if isinstance(inner, dict):
            return inner.get("sync_enabled", True)
        return True
    except Exception as e:
        logger.warning(
            f"Failed to retrieve AioSell config for hotel {hotel.id}: {str(e)}"
        )
        return False  # Default to no sync if config check fails


@receiver(post_save, sender=Calendar)
def sync_calendar_on_save(sender, instance, created, **kwargs):
    """
    Signal handler to sync calendar entries to AioSell when created or updated.

    This triggers rate restrictions sync for the affected date and rate plan.
    """
    try:
        hotel = instance.room_type.hotel

        if not _should_sync_to_aiosell(hotel):
            logger.debug(
                f"Skipping calendar sync for hotel {hotel.id} - AioSell not enabled"
            )
            return

        # Queue async task to sync calendar entry
        sync_calendar_to_aiosell.delay(
                calendar_id=str(instance.id), action="create" if created else "update")
            
        

        logger.info(
            f"Queued calendar sync for entry {instance.id} "
            f"(room type: {instance.room_type.name}, date: {instance.date})"
        )

    except Exception as e:
        logger.error(f"Error in calendar sync signal for entry {instance.id}: {str(e)}")


@receiver(post_delete, sender=Calendar)
def sync_calendar_on_delete(sender, instance, **kwargs):
    """
    Signal handler to sync calendar entry deletion to AioSell.

    This removes rate restrictions for the affected date and rate plan.
    """
    try:
        hotel = instance.room_type.hotel

        if not _should_sync_to_aiosell(hotel):
            logger.debug(
                f"Skipping calendar deletion sync for hotel {hotel.id} - AioSell not enabled"
            )
            return

        # Queue async task to remove calendar restrictions
        # IMPORTANT:
        # When a RatePlan is deleted, Django cascades and deletes related Calendar entries.
        # During that cascade the related RatePlan row is already (or about to be) gone, so
        # dereferencing instance.rate_plan will trigger a fresh DB lookup and raise
        # RatePlan.DoesNotExist. Using the raw foreign key value (rate_plan_id) avoids this.
        room_type_id = getattr(instance, "room_type_id", None)
        rate_plan_id = getattr(instance, "rate_plan_id", None)

        transaction.on_commit(
            lambda: sync_calendar_to_aiosell.delay(
                calendar_id=str(instance.id),
                action="delete",
                calendar_data={
                    "room_type_id": str(room_type_id) if room_type_id else None,
                    "rate_plan_id": str(rate_plan_id) if rate_plan_id else None,
                    "date": instance.date.isoformat(),
                    "hotel_id": str(getattr(hotel, "id", "")),
                },
            )
        )

        logger.info(f"Queued calendar deletion sync for entry {instance.id}")

    except Exception as e:
        logger.error(
            f"Error in calendar deletion sync signal for entry {instance.id}: {str(e)}"
        )


@receiver(post_save, sender=RoomType)
def sync_room_type_on_save(sender, instance, created, **kwargs):
    """
    Signal handler to sync room type changes to AioSell.

    This may affect inventory restrictions and room code mappings.
    """
    try:
        hotel = instance.hotel

        if not _should_sync_to_aiosell(hotel):
            logger.debug(
                f"Skipping room type sync for hotel {hotel.id} - AioSell not enabled"
            )
            return

        # Queue async task to sync room type
        sync_room_type_to_aiosell.delay(
                room_type_id=str(instance.id), action="create" if created else "update"
            )

        logger.info(f"Queued room type sync for {instance.name} (hotel: {hotel.name})")

    except Exception as e:
        logger.error(f"Error in room type sync signal for {instance.id}: {str(e)}")


@receiver(post_save, sender=RatePlan)
def sync_rate_plan_on_save(sender, instance, created, **kwargs):
    """
    Signal handler to sync rate plan changes to AioSell.

    This affects rate restrictions and rate plan code mappings.
    """
    try:
        hotel = instance.room_type.hotel

        if not _should_sync_to_aiosell(hotel):
            logger.debug(
                f"Skipping rate plan sync for hotel {hotel.id} - AioSell not enabled"
            )
            return
        
        sync_rate_plan_to_aiosell.delay(
            rate_plan_id=str(instance.id), action="create" if created else "update"
        )
        

        logger.info(
            f"Queued rate plan sync for {instance.name} "
            f"(room type: {instance.room_type.name}) using date range approach"
        )

    except Exception as e:
        logger.error(f"Error in rate plan sync signal for {instance.id}: {str(e)}")


@receiver(post_save, sender=RoomBlock)
def sync_room_block_on_save(sender, instance, created, **kwargs):
    """
    Enhanced signal handler to sync room blocks to AioSell.

    This uses inventory restrictions to block/unblock room availability.
    """
    try:
        hotel = instance.hotel
        ctx = {
            "room_block_id": str(instance.id),
            "hotel_id": str(hotel.id) if hotel else None,
            "room_id": (
                str(instance.room.id) if getattr(instance, "room", None) else None
            ),
            "room_no": (
                getattr(instance.room, "room_no", None)
                if getattr(instance, "room", None)
                else None
            ),
            "created": created,
        }

        if not hotel:
            logger.warning("Room block has no associated hotel", extra=ctx)
            return
        if not _should_sync_to_aiosell(hotel):
            logger.debug("Skipping room block sync - AioSell not enabled", extra=ctx)
            return
        # Check if auto-sync is enabled for room blocks
        config = hotel.get_channel_manager_config("aiosell") or {}
        if not config.get("auto_sync_on_block", True):
            logger.debug("Auto-sync disabled for room blocks", extra=ctx)
            return
        # Determine action
        action = "create" if created else "update"
        ctx["action"] = action

        # Wrap integration lookup and sync log creation in a transaction
        with transaction.atomic():
            try:
                integration = HotelOTAIntegration.objects.get(
                    hotel=hotel, ota_platform__name="aiosell", is_active=True
                )
            except HotelOTAIntegration.DoesNotExist:
                logger.warning("No active AioSell integration found", extra=ctx)
                return
            except HotelOTAIntegration.MultipleObjectsReturned:
                logger.warning(
                    "Multiple active AioSell integrations found - selecting most recent",
                    extra=ctx,
                )
                integration = (
                    HotelOTAIntegration.objects.filter(
                        hotel=hotel, ota_platform__name="aiosell", is_active=True
                    )
                    .order_by("-created_at")
                    .first()
                )
                if not integration:
                    logger.error(
                        "Failed to resolve an integration among multiples", extra=ctx
                    )
                    return

            ctx["integration_id"] = str(integration.id)
            ctx["ota_platform_id"] = str(integration.ota_platform.id)

            sync_log = RoomBlockSyncLog.objects.create(
                room_block=instance,
                hotel=hotel,
                ota_platform_id=integration.ota_platform.id,
                action=action,
                sync_status="pending",
            )
            ctx["sync_log_id"] = str(sync_log.id)

            # Queue async task after commit
            transaction.on_commit(
                lambda: sync_room_block_to_aiosell.delay(
                    room_block_id=str(instance.id),
                    sync_log_id=str(sync_log.id),
                    action=action,
                )
            )

        logger.info("Queued room block sync", extra=ctx)

    except Exception:
        # Include full stack trace and context
        logger.exception(
            "Error in room block sync signal",
            extra={"room_block_id": str(getattr(instance, "id", None))},
        )


@receiver(post_delete, sender=RoomBlock)
def sync_room_block_on_delete(sender, instance, **kwargs):
    """
    Enhanced signal handler to sync room block deletion to AioSell.

    This removes inventory restrictions to make the room available again.
    """
    try:
        hotel = instance.hotel

        if not hotel:
            logger.warning(f"Room block {instance.id} has no associated hotel")
            return

        if not _should_sync_to_aiosell(hotel):
            logger.debug(
                f"Skipping room block deletion sync for hotel {hotel.id} - AioSell not enabled"
            )
            return

        # Check if auto-sync is enabled for room unblocking
        config = hotel.get_channel_manager_config("aiosell") or {}
        if not config.get("auto_sync_on_unblock", True):
            logger.debug(f"Auto-sync disabled for room unblocking in hotel {hotel.id}")
            return

        # Create sync log entry for deletion
        try:
            ota_integration = HotelOTAIntegration.objects.get(
                hotel=hotel, ota_platform__name="aiosell", is_active=True
            )
            ota_platform_id = ota_integration.ota_platform.id
        except HotelOTAIntegration.DoesNotExist:
            logger.error(f"No active AioSell integration found for hotel {hotel.id}")
            return
        except HotelOTAIntegration.MultipleObjectsReturned:
            logger.warning(
                f"Multiple active AioSell integrations found for hotel {hotel.id}; selecting most recent for delete sync"
            )
            ota_integration = (
                HotelOTAIntegration.objects.filter(
                    hotel=hotel, ota_platform__name="aiosell", is_active=True
                )
                .order_by("-created_at")
                .first()
            )
            if not ota_integration:
                logger.error(
                    f"Failed to resolve an active AioSell integration among multiples for hotel {hotel.id}"
                )
                return
            ota_platform_id = ota_integration.ota_platform.id

        sync_log = RoomBlockSyncLog.objects.create(
            room_block=None,  # Block is deleted
            hotel=hotel,
            ota_platform_id=ota_platform_id,
            action="delete",
            sync_status="pending",
            request_data={
                "deleted_room_block_id": str(instance.id),
                "room_id": str(instance.room.id) if instance.room else None,
                "room_no": instance.room.room_no if instance.room else None,
                "blocked_from": (
                    instance.blocked_from.isoformat() if instance.blocked_from else None
                ),
                "blocked_until": (
                    instance.blocked_until.isoformat()
                    if instance.blocked_until
                    else None
                ),
                "reason": instance.reason,
            },
        )
        # Queue async task
        transaction.on_commit(
            lambda: sync_room_block_to_aiosell.delay(
                room_block_id=str(instance.id),
                sync_log_id=str(sync_log.id),
                action="delete",
                room_block_data=sync_log.request_data,
            )
        )

        logger.info(f"Queued room block deletion sync for block {instance.id}")

    except Exception as e:
        logger.error(
            f"Error in room block deletion sync signal for {instance.id}: {str(e)}"
        )


@receiver(post_save, sender=HotelOTAIntegration)
def sync_hotel_data_on_integration_setup(sender, instance, created, **kwargs):
    """
    Signal handler to sync existing hotel data when AioSell integration is set up.

    This performs an initial sync of room types, rate plans, and active calendar entries.
    """
    try:
        # Only process AioSell integrations
        if instance.ota_platform.name != "aiosell" or not instance.is_active:
            return

        if not created:
            return  # Only sync on new integration setup

        hotel = instance.hotel

        logger.info(
            f"New AioSell integration created for hotel {hotel.name}. Starting initial sync..."
        )

        # Queue initial sync tasks

        transaction.on_commit(
            lambda: sync_hotel_initial_data_to_aiosell.delay(
                hotel_id=str(hotel.id), integration_id=str(instance.id)
            )
        )

        logger.info(f"Queued initial data sync for hotel {hotel.name}")

    except Exception as e:
        logger.error(
            f"Error in hotel integration setup signal for {instance.id}: {str(e)}"
        )


# Rate and Inventory Update Signals for Property Configuration Changes
@receiver(post_save, sender="stay.Property")
def sync_property_config_changes(sender, instance, **kwargs):
    """
    Signal handler for property configuration changes that affect AioSell.

    This monitors changes to channel manager configurations and triggers re-sync if needed.
    """
    try:
        if not _has_aiosell_integration(instance):
            return

        # Check if channel manager configuration changed

        if (
            hasattr(instance, "_original_channel_managers")
            and instance._original_channel_managers is not None
        ):
            old_config = (instance._original_channel_managers or {}).get("aiosell", {})
            new_config = instance.get_channel_manager_config("aiosell") or {}

            # Check for significant configuration changes
            significant_changes = [
                "room_mapping",
                "rate_plans",
                "target_channels",
                "sync_enabled",
            ]

            config_changed = any(
                old_config.get(key) != new_config.get(key)
                for key in significant_changes
            )

            if config_changed:
                logger.info(
                    f"AioSell configuration changed for hotel {instance.name}. Triggering re-sync..."
                )

                transaction.on_commit(
                    lambda: sync_hotel_configuration_update.delay(
                        hotel_id=str(instance.id), config_changes=significant_changes
                    )
                )

    except Exception as e:
        logger.error(
            f"Error in property config sync signal for {instance.id}: {str(e)}"
        )


# Store original values for comparison
@receiver(pre_save, sender="stay.Property")
def store_original_property_values(sender, instance, **kwargs):
    if instance.pk:
        try:
            # Get the existing instance from the database
            existing = sender.objects.get(pk=instance.pk)
            instance._original_channel_managers = dict(existing.channel_managers or {})
        except sender.DoesNotExist:
            # Instance was deleted or doesn't exist yet
            instance._original_channel_managers = {}
    else:
        # New instance, set empty original values
        instance._original_channel_managers = {}


# Guest check-in signal for automatic inventory reduction
@receiver(pre_save, sender="stay.Guest")
def capture_guest_checkin_state(sender, instance, **kwargs):
    """Capture the original checked_in state before save to detect check-in events."""
    if instance.pk:  # Only for existing instances (updates)
        try:
            original_instance = sender.objects.get(pk=instance.pk)
            instance._original_checked_in = original_instance.checked_in
        except sender.DoesNotExist:
            instance._original_checked_in = None
    else:
        instance._original_checked_in = None


@receiver(post_save, sender="stay.Guest")
def guest_checkin_inventory_reduction(sender, instance, created, **kwargs):
    """
    Automatically reduce inventory when guests check in.

    This signal handler triggers when a guest's checked_in status changes from False to True,
    indicating an actual check-in event. It then reduces the available inventory for the
    corresponding room type in AioSell.
    """
    # Skip processing for new guest creation or if no room is assigned
    if created or not instance.room:
        return

    # Check if guest just checked in (checked_in status changed from False to True)
    if (
        hasattr(instance, "_original_checked_in")
        and instance._original_checked_in is not None
        and not instance._original_checked_in
        and instance.checked_in
    ):
        try:
            # Get the property/hotel
            hotel = instance.room.property

            # Check if hotel has AioSell integration
            if not _should_sync_to_aiosell(hotel):
                logger.debug(
                    f"Skipping inventory reduction for hotel {hotel.id} - AioSell not enabled"
                )
                return

            # Get room type for the room
            room_type = instance.room.room_type
            if not room_type:
                # Try to resolve room type from type_of_room field
                if instance.room.type_of_room:
                    try:
                        room_type = RoomType.objects.get(
                            hotel=hotel,
                            name__iexact=instance.room.type_of_room
                        )
                    except (RoomType.DoesNotExist, RoomType.MultipleObjectsReturned):
                        logger.warning(
                            f"Could not resolve room type for room {instance.room.id} "
                            f"with type_of_room '{instance.room.type_of_room}'"
                        )
                        return
                else:
                    logger.warning(
                        f"No room type found for room {instance.room.id} - skipping inventory reduction"
                    )
                    return

            # Calculate available rooms for this room type
            total_rooms = room_type.rooms.count()
            occupied_rooms = room_type.rooms.filter(
                checked_in=True
            ).count()
            available_rooms = max(0, total_rooms - occupied_rooms)

            logger.info(
                f"Guest {instance.user.name} checked into room {instance.room.room_no} "
                f"(type: {room_type.name}). Available rooms: {available_rooms}/{total_rooms}"
            )

            # Queue async task to update inventory in AioSell
            from pms.tasks.aiosell_tasks import async_push_inventory_to_aiosell

            # Get room code mapping
            from pms.services.aiosell import get_aiosell_service
            aiosell_service = get_aiosell_service(hotel=hotel)
            if aiosell_service:
                try:
                    room_code = aiosell_service._get_room_code_mapping(room_type=room_type)

                    # Prepare inventory data
                    room_inventories = [{
                        "room_code": room_code,
                        "available": available_rooms
                    }]

                    # Set date range for today and next few days
                    today = timezone.now().date()
                    end_date = today + timedelta(days=7)  # Update inventory for next 7 days

                    # Queue async inventory update
                    transaction.on_commit(
                        lambda: async_push_inventory_to_aiosell.delay(
                            hotel_id=str(hotel.id),
                            room_inventories=room_inventories,
                            start_date=today.isoformat(),
                            end_date=end_date.isoformat(),
                            user_id=str(instance.user.id)
                        )
                    )

                    logger.info(
                        f"Queued inventory reduction for room type {room_type.name} "
                        f"({room_code}) - new available count: {available_rooms}"
                    )

                except Exception as e:
                    logger.error(
                        f"Error preparing inventory reduction for room type {room_type.name}: {str(e)}"
                    )
            else:
                logger.warning(
                    f"AioSell service not available for hotel {hotel.id} - skipping inventory reduction"
                )

        except Exception as e:
            logger.error(
                f"Error processing inventory reduction for guest check-in {instance.id}: {str(e)}",
                exc_info=True
            )
