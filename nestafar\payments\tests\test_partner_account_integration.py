"""
Partner Account Integration Tests

Tests for the complete partner account management system including:
- Account creation and management
- Bank account operations
- KYC document handling
- Webhook processing
- Security and validation
"""

import json
from decimal import Decimal
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status

from core.models import PartnerProfile
from ..models import (
    PartnerRazorpayAccount,
    PartnerBankAccount,
    PartnerKYCDocument,
    AccountVerificationLog,
    PaymentWebhookEvent
)
from ..constants import (
    AccountStatus,
    KYCStatus,
    VerificationStatus,
    BankAccountType,
    DocumentType
)
from ..services.razorpay_account_service import RazorpayAccountService
from ..webhooks.account_webhook_processor import AccountWebhookProcessor

User = get_user_model()


class PartnerAccountModelTests(TestCase):
    """Test partner account models"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone='+************',
            name='Test Partner User',
            email='<EMAIL>',
            password='testpass123',
            partner=False,
        )
        self.partner_profile = PartnerProfile.objects.create(user=self.user)
    
    def test_partner_account_creation(self):
        """Test creating a partner Razorpay account"""
        account = PartnerRazorpayAccount.objects.create(
            partner=self.partner_profile,
            contact_name='Test Partner',
            contact_email='<EMAIL>',
            contact_phone='+************',
            business_type='individual'
        )
        
        self.assertEqual(account.partner, self.partner_profile)
        self.assertEqual(account.account_status, AccountStatus.PENDING)
        self.assertEqual(account.kyc_status, KYCStatus.PENDING)
        self.assertIsNone(account.razorpay_account_id)
    
    def test_bank_account_creation(self):
        """Test creating a bank account"""
        razorpay_account = PartnerRazorpayAccount.objects.create(
            partner=self.partner_profile,
            contact_name='Test Partner',
            contact_email='<EMAIL>',
            contact_phone='+************'
        )
        
        bank_account = PartnerBankAccount.objects.create(
            razorpay_account=razorpay_account,
            account_holder_name='Test Partner',
            account_number='**********',
            ifsc_code='HDFC0001234',
            bank_name='HDFC Bank',
            account_type=BankAccountType.SAVINGS,
            is_primary=True
        )
        
        self.assertEqual(bank_account.razorpay_account, razorpay_account)
        self.assertTrue(bank_account.is_primary)
        self.assertEqual(bank_account.verification_status, VerificationStatus.PENDING)
    
    def test_kyc_document_creation(self):
        """Test creating a KYC document"""
        razorpay_account = PartnerRazorpayAccount.objects.create(
            partner=self.partner_profile,
            contact_name='Test Partner',
            contact_email='<EMAIL>',
            contact_phone='+************'
        )
        
        kyc_document = PartnerKYCDocument.objects.create(
            razorpay_account=razorpay_account,
            document_type=DocumentType.PAN_CARD,
            document_number='**********'
        )
        
        self.assertEqual(kyc_document.razorpay_account, razorpay_account)
        self.assertEqual(kyc_document.document_type, DocumentType.PAN_CARD)
        self.assertEqual(kyc_document.verification_status, VerificationStatus.PENDING)


class PartnerAccountAPITests(APITestCase):
    """Test partner account API endpoints"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone='+************',
            name='Test Partner User',
            email='<EMAIL>',
            password='testpass123',
            partner=False,
        )
        self.partner_profile = PartnerProfile.objects.create(user=self.user)
        self.client.force_authenticate(user=self.user)
    
    @patch('payments.services.razorpay_account_service.RazorpayAccountService.create_partner_account')
    def test_create_partner_account_api(self, mock_create_account):
        """Test creating partner account via API"""
        mock_create_account.return_value = {
            'id': 'acc_test123',
            'status': 'created',
            'reference_id': 'test-ref-123'
        }
        
        url = reverse('payments:partner-account-create-account')
        data = {
            'contact_name': 'Test Partner',
            'contact_email': '<EMAIL>',
            'contact_phone': '+************',
            'business_type': 'individual',
            'business_name': 'Test Business'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('razorpay_account_id', response.data)
        
        # Verify account was created in database
        account = PartnerRazorpayAccount.objects.get(partner=self.partner_profile)
        self.assertEqual(account.razorpay_account_id, 'acc_test123')
    
    def test_add_bank_account_api(self):
        """Test adding bank account via API"""
        # Create partner account first
        razorpay_account = PartnerRazorpayAccount.objects.create(
            partner=self.partner_profile,
            contact_name='Test Partner',
            contact_email='<EMAIL>',
            contact_phone='+************',
            razorpay_account_id='acc_test123'
        )
        
        url = reverse('payments:partner-bank-account-list')
        data = {
            'account_holder_name': 'Test Partner',
            'account_number': '**********',
            'ifsc_code': 'HDFC0001234',
            'bank_name': 'HDFC Bank',
            'account_type': BankAccountType.SAVINGS,
            'is_primary': True
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify bank account was created
        bank_account = PartnerBankAccount.objects.get(razorpay_account=razorpay_account)
        self.assertEqual(bank_account.account_number, '**********')
        self.assertTrue(bank_account.is_primary)


class RazorpayAccountServiceTests(TestCase):
    """Test Razorpay account service"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone='+************',
            name='Test Partner User',
            email='<EMAIL>',
            password='testpass123',
            partner=False,
        )
        self.partner_profile = PartnerProfile.objects.create(user=self.user)
        self.service = RazorpayAccountService()
    
    @patch('payments.services.razorpay_account_service.razorpay.Client')
    def test_create_partner_account(self, mock_client):
        """Test creating partner account via Razorpay API"""
        mock_razorpay = MagicMock()
        mock_client.return_value = mock_razorpay
        mock_razorpay.account.create.return_value = {
            'id': 'acc_test123',
            'status': 'created',
            'reference_id': 'test-ref-123'
        }
        
        partner_account = PartnerRazorpayAccount.objects.create(
            partner=self.partner_profile,
            contact_name='Test Partner',
            contact_email='<EMAIL>',
            contact_phone='+************'
        )
        result = self.service.create_partner_account(partner_account, category='retail', subcategory='store')
        
        self.assertEqual(result['id'], 'acc_test123')
        mock_razorpay.account.create.assert_called_once()

    @patch('payments.services.razorpay_account_service.razorpay.Client')
    def test_create_partner_account_defaults(self, mock_client):
        """When category/subcategory not provided, defaults should be used"""
        mock_razorpay = MagicMock()
        mock_client.return_value = mock_razorpay
        mock_razorpay.account.create.return_value = {
            'id': 'acc_test_default',
            'status': 'created'
        }

        partner_account = PartnerRazorpayAccount.objects.create(
            partner=self.partner_profile,
            contact_name='Test Partner',
            contact_email='<EMAIL>',
            contact_phone='+************'
        )

        result = self.service.create_partner_account(partner_account)
        self.assertEqual(result['id'], 'acc_test_default')


class AccountWebhookProcessorTests(TestCase):
    """Test account webhook processing"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone='+************',
            name='Test Partner User',
            email='<EMAIL>',
            password='testpass123',
            partner=False,
        )
        self.partner_profile = PartnerProfile.objects.create(user=self.user)
        self.processor = AccountWebhookProcessor()
    
    def test_account_activated_webhook(self):
        """Test processing account.activated webhook"""
        # Create partner account
        partner_account = PartnerRazorpayAccount.objects.create(
            partner=self.partner_profile,
            contact_name='Test Partner',
            contact_email='<EMAIL>',
            contact_phone='+************',
            razorpay_account_id='acc_test123',
            account_status=AccountStatus.UNDER_REVIEW
        )
        
        # Create webhook event
        webhook_event = PaymentWebhookEvent.objects.create(
            event_id='evt_test123',
            event_type='account.activated',
            entity_type='account',
            entity_id='acc_test123',
            raw_payload={
                'payload': {
                    'account': {
                        'entity': {
                            'id': 'acc_test123',
                            'status': 'activated',
                            'activation_form_milestone': 'L2'
                        }
                    }
                }
            }
        )
        
        # Process webhook
        self.processor.process_event(webhook_event)
        
        # Verify account status updated
        partner_account.refresh_from_db()
        self.assertEqual(partner_account.account_status, AccountStatus.ACTIVE)
        self.assertIsNotNone(partner_account.activated_at)
        
        # Verify partner profile updated
        self.partner_profile.refresh_from_db()
        self.assertTrue(self.partner_profile.razorpay_account_verified)
    
    # Verify webhook marked as processed
        webhook_event.refresh_from_db()
        self.assertTrue(webhook_event.processed)

    def test_notifications_skipped_for_invalid_phone(self):
        """If partner contact_phone is None/empty/malformed, notifications should be skipped and a warning logged"""
        from unittest.mock import patch

        # Create partner account with no phone
        partner_account = PartnerRazorpayAccount.objects.create(
            partner=self.partner_profile,
            contact_name='Test Partner',
            contact_email='<EMAIL>',
            contact_phone='',
            razorpay_account_id='acc_test123',
            account_status=AccountStatus.UNDER_REVIEW
        )

        webhook_event = PaymentWebhookEvent.objects.create(
            event_id='evt_test_none',
            event_type='account.activated',
            entity_type='account',
            entity_id='acc_test123',
            raw_payload={'payload': {'account': {'entity': {'id': 'acc_test123'}}}}
        )

        with patch('payments.webhooks.account_webhook_processor.logger') as mock_logger:
            with patch('payments.services.payment_notification_service.PaymentNotificationService.send_sms') as mock_sms:
                with patch('payments.services.payment_notification_service.PaymentNotificationService.send_whatsapp_message') as mock_wa:
                    # Process - should not attempt to send
                    self.processor.process_event(webhook_event)

                    self.assertFalse(mock_sms.called)
                    self.assertFalse(mock_wa.called)
                    # The webhook processor logs a warning via logger.warning when phone is invalid
                    mock_logger.warning.assert_any_call(f"Invalid/empty contact phone for partner {partner_account.id}")

    def test_notifications_sent_for_valid_phone(self):
        """Valid phone should be normalized and used to send SMS/WhatsApp"""
        from unittest.mock import patch

        # Create partner account with valid local number
        partner_account = PartnerRazorpayAccount.objects.create(
            partner=self.partner_profile,
            contact_name='Test Partner',
            contact_email='<EMAIL>',
            contact_phone='+************',
            razorpay_account_id='acc_test_valid',
            account_status=AccountStatus.UNDER_REVIEW
        )

        webhook_event = PaymentWebhookEvent.objects.create(
            event_id='evt_test_valid',
            event_type='account.activated',
            entity_type='account',
            entity_id='acc_test_valid',
            raw_payload={'payload': {'account': {'entity': {'id': 'acc_test_valid'}}}}
        )

        with patch('payments.services.payment_notification_service.PaymentNotificationService.send_sms') as mock_sms:
            with patch('payments.services.payment_notification_service.PaymentNotificationService.send_whatsapp_message') as mock_wa:
                # Process - should attempt to send using normalized number
                self.processor.process_event(webhook_event)

                self.assertTrue(mock_sms.called)
                self.assertTrue(mock_wa.called)


class SecurityValidationTests(TestCase):
    """Test security and validation features"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_partner=True
        )
        self.partner_profile = PartnerProfile.objects.create(
            user=self.user,
            name='Test Partner',
            phone='+************'
        )
    
    def test_ifsc_code_validation(self):
        """Test IFSC code validation"""
        from ..serializers.partner_account_serializer import BankAccountSerializer
        
        # Valid IFSC codes
        valid_codes = ['HDFC0001234', 'ICIC0000123', 'SBIN0012345']
        for code in valid_codes:
            serializer = BankAccountSerializer()
            result = serializer.validate_ifsc_code(code)
            self.assertEqual(result, code)
        
        # Invalid IFSC codes
        invalid_codes = ['HDFC123', 'hdfc0001234', '**********', 'ABCD1234567']
        for code in invalid_codes:
            serializer = BankAccountSerializer()
            with self.assertRaises(Exception):
                serializer.validate_ifsc_code(code)
    
    def test_pan_number_validation(self):
        """Test PAN number validation"""
        from ..serializers.partner_account_serializer import KYCDocumentSerializer
        
        # Valid PAN numbers
        valid_pans = ['**********', '**********']
        for pan in valid_pans:
            serializer = KYCDocumentSerializer()
            result = serializer.validate_document_number(pan, DocumentType.PAN_CARD)
            self.assertEqual(result, pan)
        
        # Invalid PAN numbers
        invalid_pans = ['ABC123', 'abcde1234f', '**********']
        for pan in invalid_pans:
            serializer = KYCDocumentSerializer()
            with self.assertRaises(Exception):
                serializer.validate_document_number(pan, DocumentType.PAN_CARD)
