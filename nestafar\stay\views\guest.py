from core.permissions import PropertyPermission
from rest_framework.views import APIView
from stay.models import Guest, Property
from stay.serializers import GuestSerializer
from nestafar.responses import SuccessResponse, NotFoundResponse, BadRequestResponse


class GuestHistoryView(APIView):
    permission_classes = [PropertyPermission]

    def get(self, request):
        property = request.property

        if not property:
            return BadRequestResponse(message="Property ID header is missing")

        # property is already a Property object from request.property
        guests = Guest.objects.filter(
            room__property=property, checked_in=False, checked_out=True
        ).order_by("check_out_date")

        if not guests.exists():
            return NotFoundResponse(
                message="No checked-out guests found for this property"
            )

        serializer = GuestSerializer(guests, many=True)
        return SuccessResponse(
            data=serializer.data,
            message="Checked-out guests and their stay history retrieved successfully",
        )
