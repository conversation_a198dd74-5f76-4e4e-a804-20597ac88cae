from rest_framework.serializers import Model<PERSON>erializer, StringRelatedField
from core.models import User, PartnerProfile, Permission, UserRole, Role
from phonenumber_field.serializerfields import PhoneNumberField


class UserSerializer(ModelSerializer):
    phone = PhoneNumberField()
    roles = StringRelatedField(many=True, read_only=True)

    class Meta:
        model = User
        exclude = ["password"]


class PartnerSerializer(ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = PartnerProfile
        fields = "__all__"
        depth = 1


class RoleSerializer(ModelSerializer):
    permissions = StringRelatedField(many=True)

    class Meta:
        model = Role
        fields = "__all__"


class PermissionSerializer(ModelSerializer):
    class Meta:
        model = Permission
        fields = "__all__"


class UserRoleSerializer(ModelSerializer):
    class Meta:
        model = UserRole
        fields = "__all__"
