from pytest_bdd import scenario, given, then, when
from rental.models import *
from rental.tests import rentaltests


@scenario("features/rental_order.feature")
def test_manage_rental_order(client, user):
    pass


@given("I am an authenticated user with a rental cart containing items")
def create_rental_cart_with_items(
    client, user, service_factory=rentaltests.create_rental_service_item
):
    # Create a cart with some rental service items
    cart, _ = RentalCart.objects.get_or_create(
        guest=user.guest, defaults={"status": "OPEN"}
    )
    item1 = service_factory()
    item2 = service_factory()
    RentalCartItems.objects.create(
        cart=cart,
        item=item1,
        quantity=2,
        pickup_date="2024-05-12",
        drop_date="2024-05-15",
    )
    RentalCartItems.objects.create(
        cart=cart,
        item=item2,
        quantity=1,
        pickup_date="2024-05-10",
        drop_date="2024-05-13",
    )
    return cart


@when("I create a rental order")
def create_rental_order(client, user, cart):
    data = {"cart": cart.id}
    response = client.post("/api/rental-orders/", data=data, format="json")
    response.json()  # trigger data parsing


@then("Then the response status code is 201")
def check_create_order_status_code(response):
    assert response.status_code == 201


@then(
    "And a new rental order is created with the cart items details (including pickup/drop off dates, no of periods)"
)
def check_create_order_response(response):
    data = response.json()
    order_id = data["id"]
    order = RentalOrder.objects.get(pk=order_id)
    assert order.cart.id == data["cart"]
    # Check for ordered rental service items
    order_items = RentalOrderItem.objects.filter(order=order)
    assert len(order_items) == 2
    for item in order_items:
        cart_item = RentalCartItems.objects.get(id=item.id)
        assert item.pickup_date == cart_item.pickup_date
        assert item.drop_date == cart_item.drop_date
        assert item.no_of_periods == cart_item.no_of_periods


@given("a rental order exists with ID '<order_id>'")
def get_rental_order(client, order_id):
    order = RentalOrder.objects.get(pk=order_id)
    return order


@when("I retrieve the rental order details")
def retrieve_rental_order(client, order):
    response = client.get(f"/api/rental-orders/{order.id}/")
    response.json()  # trigger data parsing


@then("Then the response status code is 200")
def check_retrieve_order_status_code(response):
    assert response.status_code == 200


@then("And the response data contains the rental order details, including:")
def check_retrieve_order_details(response, order):
    data = response.json()
    assert order.id == data["id"]

    # Guest information assertions
    assert order.guest.user.name == data["guest"]["name"]
    assert order.guest.user.phone.as_e164 == data["guest"]["phone_no"]
    assert order.guest.room.room_no == data["guest"]["room_no"]

    # Service information assertions
    assert order.service.period == data["service"]["period"]
    assert order.service.type_of_rental == data["service"]["type"]

    # Ordered rental service items assertions
    assert len(data["order_items"]) == len(RentalOrderItem.objects.filter(order=order))
    for order_item_data in data["order_items"]:
        order_item = RentalOrderItem.objects.get(pk=order_item_data["id"])
        assert order_item.item.name == order
        assert order_item.item.name == order_item_data["name"]
        assert order_item.quantity == order_item_data["quantity"]
        # Assert  price and total amount
