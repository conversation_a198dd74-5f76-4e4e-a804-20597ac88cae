from .models import *
from django_filters import rest_framework as django_filters
from service.filters import BaseCartFilter


class TourismItemFilterSet(django_filters.FilterSet):
    class Meta:
        model = TourismServiceItem
        fields = ["service", "price", "is_active", "rating"]


class TourismOrderFilter(django_filters.FilterSet):
    class Meta:
        model = TourismOrder
        fields = ["status", "guest", "cart"]


class TourismCartFilter(BaseCartFilter):
    class Meta:
        model = TourismCart
        fields = ["status", "guest"]
