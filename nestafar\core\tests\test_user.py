from django.test import LiveServerTestCase
from rest_framework.test import APIClient
from django.urls import reverse


class TestUser(LiveServerTestCase):
    def get_authenticated_client(self, username, otp):
        client = APIClient()
        login_response = client.post(
            reverse("core:login"),
            {"username": username, "password": otp, "type": "token"},
        )
        if login_response.status_code != 200 or "access" not in login_response.data:
            raise Exception(f"Login failed: {login_response.data}")
        token = login_response.data["access"]
        client.credentials(HTTP_AUTHORIZATION="Bearer " + token)
        return client
