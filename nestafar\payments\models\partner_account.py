"""
Partner Account Models

Models for managing partner <PERSON><PERSON>pay Route accounts, bank details,
KYC documents, and verification status.
"""

import uuid
from decimal import Decimal
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import RegexValidator, MinLengthValidator
from django.utils import timezone
import os

from ..constants.payment_constants import (
    AccountStatus,
    KYCStatus,
    BankAccountType,
    DocumentType,
    VerificationStatus
)

User = get_user_model()


class PartnerRazorpayAccount(models.Model):
    """
    Razorpay Route account details for partners
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Partner relationship
    partner = models.OneToOneField(
        'core.PartnerProfile',
        on_delete=models.CASCADE,
        related_name='razorpay_account'
    )
    
    # Razorpay account details
    razorpay_account_id = models.CharField(
        max_length=100,
        unique=True,
        null=True,
        blank=True,
        help_text="Razorpay linked account ID"
    )
    
    # Account status and verification
    account_status = models.CharField(
        max_length=20,
        choices=AccountStatus.choices,
        default=AccountStatus.PENDING,
        help_text="Current account status"
    )
    
    kyc_status = models.CharField(
        max_length=20,
        choices=KYCStatus.choices,
        default=KYCStatus.PENDING,
        help_text="KYC verification status"
    )
    
    bank_verification_status = models.CharField(
        max_length=20,
        choices=VerificationStatus.choices,
        default=VerificationStatus.PENDING,
        help_text="Bank account verification status"
    )
    
    # Business details
    business_name = models.CharField(
        max_length=200,
        help_text="Legal business name"
    )
    business_type = models.CharField(
        max_length=50,
        choices=[
            ('individual', 'Individual'),
            ('proprietorship', 'Proprietorship'),
            ('partnership', 'Partnership'),
            ('private_limited', 'Private Limited'),
            ('public_limited', 'Public Limited'),
            ('llp', 'Limited Liability Partnership'),
            ('trust', 'Trust'),
            ('society', 'Society'),
            ('ngo', 'NGO'),
        ],
        default='individual',
        help_text="Type of business entity"
    )
    
    # Contact details
    contact_name = models.CharField(
        max_length=100,
        help_text="Primary contact person name"
    )
    contact_email = models.EmailField(
        help_text="Contact email for account communications"
    )
    contact_phone = models.CharField(
        max_length=15,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
            )
        ],
        help_text="Contact phone number"
    )
    
    # Address details
    address_line1 = models.CharField(max_length=200)
    address_line2 = models.CharField(max_length=200, blank=True)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    postal_code = models.CharField(
        max_length=10,
        validators=[
            RegexValidator(
                regex=r'^\d{6}$',
                message="Postal code must be 6 digits"
            )
        ]
    )
    country = models.CharField(max_length=2, default='IN')
    
    # Razorpay response data
    razorpay_response = models.JSONField(
        default=dict,
        blank=True,
        help_text="Raw response from Razorpay account creation"
    )

    # Route onboarding tracking (explicit fields)
    stakeholder_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="Razorpay Stakeholder ID for the linked account"
    )
    product_config_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="Razorpay Product Configuration ID for Route"
    )
    product_activation_status = models.CharField(
        max_length=30,
        null=True,
        blank=True,
        help_text="Route product activation status (e.g., created, activated, needs_clarification)"
    )
    product_requirements = models.JSONField(
        default=list,
        blank=True,
        help_text="Outstanding or returned requirements from Razorpay product configuration"
    )

    # Status tracking
    activation_form_milestone = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text="Current milestone in activation process"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    activated_at = models.DateTimeField(null=True, blank=True)
    
    # Error tracking
    last_error = models.TextField(
        null=True,
        blank=True,
        help_text="Last error encountered during account operations"
    )
    error_count = models.IntegerField(
        default=0,
        help_text="Number of consecutive errors"
    )
    
    class Meta:
        db_table = 'payments_partner_razorpay_account'
        verbose_name = 'Partner Razorpay Account'
        verbose_name_plural = 'Partner Razorpay Accounts'
    
    def __str__(self):
        partner_name = getattr(self.partner.user, 'name', 'Unknown') if self.partner.user else 'Unknown'
        return f"{partner_name} - {self.account_status}"    
    def is_active(self):
        """Check if account is fully activated"""
        return (
            self.account_status == AccountStatus.ACTIVE and
            self.kyc_status == KYCStatus.VERIFIED and
            self.bank_verification_status == VerificationStatus.VERIFIED
        )
    
    def is_pending_activation(self):
        """Check if account is pending activation"""
        return self.account_status in [AccountStatus.PENDING, AccountStatus.UNDER_REVIEW]
    
    def can_receive_transfers(self):
        """Check if account can receive transfers"""
        return (
            self.account_status == AccountStatus.ACTIVE and
            self.bank_verification_status == VerificationStatus.VERIFIED
        )
    
    def mark_as_activated(self):
        """Mark account as activated"""
        self.account_status = AccountStatus.ACTIVE
        self.activated_at = timezone.now()
        self.save(update_fields=['account_status', 'activated_at'])
    
    def record_error(self, error_message):
        """Record an error for this account"""
        self.last_error = error_message
        self.error_count += 1
        self.save(update_fields=['last_error', 'error_count'])
    
    def clear_errors(self):
        """Clear error tracking"""
        self.last_error = None
        self.error_count = 0
        self.save(update_fields=['last_error', 'error_count'])


class PartnerBankAccount(models.Model):
    """
    Bank account details for partner payments
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Account relationship
    razorpay_account = models.ForeignKey(
        PartnerRazorpayAccount,
        on_delete=models.CASCADE,
        related_name='bank_accounts'
    )
    
    # Bank account details
    account_holder_name = models.CharField(
        max_length=100,
        help_text="Name as per bank account"
    )
    account_number = models.CharField(
        max_length=20,
        validators=[
            MinLengthValidator(9),
            RegexValidator(
                regex=r'^\d+$',
                message="Account number must contain only digits"
            )
        ],
        help_text="Bank account number"
    )
    ifsc_code = models.CharField(
        max_length=11,
        validators=[
            RegexValidator(
                regex=r'^[A-Z]{4}0[A-Z0-9]{6}$',
                message="Invalid IFSC code format"
            )
        ],
        help_text="IFSC code of the bank branch"
    )
    
    # Bank details
    bank_name = models.CharField(max_length=100)
    branch_name = models.CharField(max_length=100)
    
    # Account type
    account_type = models.CharField(
        max_length=20,
        choices=BankAccountType.choices,
        default=BankAccountType.SAVINGS,
        help_text="Type of bank account"
    )
    
    # Verification status
    verification_status = models.CharField(
        max_length=20,
        choices=VerificationStatus.choices,
        default=VerificationStatus.PENDING,
        help_text="Bank account verification status"
    )
    
    # Primary account flag
    is_primary = models.BooleanField(
        default=False,
        help_text="Whether this is the primary bank account"
    )
    
    # Razorpay bank account ID
    razorpay_bank_account_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="Razorpay bank account identifier"
    )
    
    # Verification details
    verification_attempts = models.IntegerField(
        default=0,
        help_text="Number of verification attempts"
    )
    last_verification_attempt = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Last verification attempt timestamp"
    )
    verification_error = models.TextField(
        null=True,
        blank=True,
        help_text="Last verification error message"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    verified_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'payments_partner_bank_account'
        verbose_name = 'Partner Bank Account'
        verbose_name_plural = 'Partner Bank Accounts'
        unique_together = ['razorpay_account', 'account_number']
    
    def __str__(self):
        return f"{self.account_holder_name} - {self.account_number[-4:]}"
    
    def save(self, *args, **kwargs):
        # Ensure only one primary account per partner
        if self.is_primary:
            PartnerBankAccount.objects.filter(
                razorpay_account=self.razorpay_account,
                is_primary=True
            ).exclude(pk=self.pk).update(is_primary=False)
        
        super().save(*args, **kwargs)
    
    def is_verified(self):
        """Check if bank account is verified"""
        return self.verification_status == VerificationStatus.VERIFIED
    
    def mark_as_verified(self):
        """Mark bank account as verified"""
        self.verification_status = VerificationStatus.VERIFIED
        self.verified_at = timezone.now()
        self.save(update_fields=['verification_status', 'verified_at'])
    
    def record_verification_attempt(self, error_message=None):
        """Record a verification attempt"""
        self.verification_attempts += 1
        self.last_verification_attempt = timezone.now()
        if error_message:
            self.verification_error = error_message
            self.verification_status = VerificationStatus.FAILED
        self.save(update_fields=[
            'verification_attempts',
            'last_verification_attempt',
            'verification_error',
            'verification_status'
        ])


class PartnerKYCDocument(models.Model):
    """
    KYC documents uploaded by partners
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Account relationship
    razorpay_account = models.ForeignKey(
        PartnerRazorpayAccount,
        on_delete=models.CASCADE,
        related_name='kyc_documents'
    )
    
    # Document details
    document_type = models.CharField(
        max_length=30,
        choices=DocumentType.choices,
        help_text="Type of KYC document"
    )
    document_number = models.CharField(
        max_length=50,
        help_text="Document number (PAN, Aadhaar, etc.)"
    )
    
    # File upload
    document_file = models.FileField(
        upload_to='kyc_documents/%Y/%m/',
        help_text="Uploaded document file"
    )
    file_size = models.IntegerField(
        null=True,
        blank=True,
        help_text="File size in bytes"
    )
    file_type = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        help_text="File type (pdf, jpg, png)"
    )
    
    # Verification status
    verification_status = models.CharField(
        max_length=20,
        choices=VerificationStatus.choices,
        default=VerificationStatus.PENDING,
        help_text="Document verification status"
    )
    
    # Razorpay document ID
    razorpay_document_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="Razorpay document identifier"
    )
    
    # Verification details
    verification_notes = models.TextField(
        null=True,
        blank=True,
        help_text="Verification notes or rejection reasons"
    )
    verified_by = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="Who verified the document"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    verified_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'payments_partner_kyc_document'
        verbose_name = 'Partner KYC Document'
        verbose_name_plural = 'Partner KYC Documents'
        unique_together = ['razorpay_account', 'document_type']
    
    def __str__(self):
        # Defensive resolution of partner identifier. The relationships may be
        # missing (e.g., razorpay_account, partner, user) so walk the chain
        # using getattr and fall back to email/username/partner name or a
        # constant when nothing is available.
        partner_identifier = "Unknown Partner"

        try:
            rp_account = getattr(self, 'razorpay_account', None)
            if rp_account is not None:
                partner = getattr(rp_account, 'partner', None)
                if partner is not None:
                    user = getattr(partner, 'user', None)
                    if user is not None:
                        partner_identifier = (
                            getattr(user, 'name', None)
                            or getattr(user, 'email', None)
                            or getattr(user, 'username', None)
                            or partner_identifier
                        )
                    else:
                        # partner object might have an identifying field
                        partner_identifier = (
                            getattr(partner, 'name', None)
                            or getattr(partner, 'organization_name', None)
                            or partner_identifier
                        )
        except Exception:
            # Avoid failing __str__; keep the fallback identifier
            partner_identifier = partner_identifier

        return f"{partner_identifier} - {self.document_type}"
    
    def save(self, *args, **kwargs):
        # Set file metadata
        if self.document_file:
            self.file_size = self.document_file.size
            # Safely extract file extension. Use os.path.splitext which handles
            # filenames without a dot correctly. If there's no extension, set
            # file_type to an empty string.
            _, ext = os.path.splitext(self.document_file.name)
            if ext:
                # ext includes the leading dot, e.g. '.pdf'
                self.file_type = ext[1:].lower()
            else:
                self.file_type = ''
        
        super().save(*args, **kwargs)
    
    def is_verified(self):
        """Check if document is verified"""
        return self.verification_status == VerificationStatus.VERIFIED
    
    def mark_as_verified(self, verified_by=None, notes=None):
        """Mark document as verified"""
        self.verification_status = VerificationStatus.VERIFIED
        self.verified_at = timezone.now()
        if verified_by:
            self.verified_by = verified_by
        if notes:
            self.verification_notes = notes
        self.save(update_fields=[
            'verification_status',
            'verified_at',
            'verified_by',
            'verification_notes'
        ])
    
    def mark_as_rejected(self, reason, verified_by=None):
        """Mark document as rejected"""
        self.verification_status = VerificationStatus.FAILED
        self.verification_notes = reason
        if verified_by:
            self.verified_by = verified_by
        self.save(update_fields=[
            'verification_status',
            'verification_notes',
            'verified_by'
        ])


class AccountVerificationLog(models.Model):
    """
    Log of account verification activities
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Account relationship
    razorpay_account = models.ForeignKey(
        PartnerRazorpayAccount,
        on_delete=models.CASCADE,
        related_name='verification_logs'
    )
    
    # Log details
    activity_type = models.CharField(
        max_length=50,
        choices=[
            ('account_created', 'Account Created'),
            ('kyc_submitted', 'KYC Submitted'),
            ('kyc_verified', 'KYC Verified'),
            ('kyc_rejected', 'KYC Rejected'),
            ('bank_added', 'Bank Account Added'),
            ('bank_verified', 'Bank Account Verified'),
            ('bank_rejected', 'Bank Account Rejected'),
            ('account_activated', 'Account Activated'),
            ('account_suspended', 'Account Suspended'),
            ('webhook_received', 'Webhook Received'),
        ],
        help_text="Type of verification activity"
    )
    
    description = models.TextField(
        help_text="Detailed description of the activity"
    )
    
    # Related objects
    related_document = models.ForeignKey(
        PartnerKYCDocument,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Related KYC document if applicable"
    )
    related_bank_account = models.ForeignKey(
        PartnerBankAccount,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Related bank account if applicable"
    )
    
    # Metadata
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional metadata for the activity"
    )
    
    # User tracking
    performed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="User who performed the activity"
    )
    
    # Timestamp
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'payments_account_verification_log'
        verbose_name = 'Account Verification Log'
        verbose_name_plural = 'Account Verification Logs'
        ordering = ['-created_at']
    
    def __str__(self):
        # One-line defensive resolution to avoid AttributeError when related
        # objects or attributes are missing. Prefer user.name, then user.email,
        # then username, then str(user); fall back to '<Unknown User>'.
        user_display = (
            (getattr(getattr(getattr(self, 'razorpay_account', None), 'partner', None), 'user', None) and
             (getattr(getattr(getattr(self, 'razorpay_account', None), 'partner', None), 'user', None).name or
              getattr(getattr(getattr(self, 'razorpay_account', None), 'partner', None), 'user', None).email or
              getattr(getattr(getattr(self, 'razorpay_account', None), 'partner', None), 'user', None).username or
              str(getattr(getattr(getattr(self, 'razorpay_account', None), 'partner', None), 'user', None))))
            or '<Unknown User>'
        )
        return f"{user_display} - {self.activity_type}"
