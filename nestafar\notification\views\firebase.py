from rest_framework.views import APIView
from nestafar.responses import CreateResponse, BadRequestResponse
from rest_framework.permissions import IsAuthenticated
from notification.models import FirebaseDeviceToken
from django.db import IntegrityError
import logging

logger = logging.getLogger(__name__)


class FirebaseTokenView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        firebase_token = request.data.get("firebase_token")
        if not firebase_token:
            return BadRequestResponse(message="firebase_token is required")
        try:
            # FCM tokens vary in length (up to 255 chars) and are URL-safe strings
            if not isinstance(firebase_token, str):
                return BadRequestResponse(message="Invalid Firebase token format")

            # Optional: enforce max length to avoid overly long inputs
            if len(firebase_token) > 255:
                return BadRequestResponse(message="Firebase token too long")

            # Optional: validate allowed characters
            # import re at module top if using this
            # if not re.fullmatch(r'[A-Za-z0-9\-_]+', firebase_token):
            #     return BadRequestResponse(message="Invalid Firebase token format")
            # if not isinstance(firebase_token, str) or len(firebase_token) < 150:
            #     return BadRequestResponse(message="Invalid firebase token format")
            # Handle duplicate tokens
            # Handle duplicate tokens atomically
            token_obj, created = FirebaseDeviceToken.objects.get_or_create(
                user=request.user, token=firebase_token
            )
            if not created:
                return BadRequestResponse(
                    message="Token already registered for this user"
                )
            return CreateResponse(message="Firebase token added successfully")
        except IntegrityError as e:
            logger.error(
                f"Database constraint error when saving Firebase token: {str(e)}"
            )
            return BadRequestResponse(
                message="Failed to save token due to database constraint"
            )
        except Exception as e:
            logger.error(f"Unexpected error when processing Firebase token: {str(e)}")
            return BadRequestResponse(message="Failed to process firebase token")
