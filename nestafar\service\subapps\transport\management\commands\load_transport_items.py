from django.core.management import BaseCommand
from django.db import transaction
import pandas as pd
import math
import json
from django.core.files import File
import urllib
from service.subapps.transport.models import TransportService, TransportServiceItem
from openai import OpenAI


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument("filepath", type=str)
        parser.add_argument("service_id", type=str)

    def handle(self, *args, **kwargs):
        try:

            def create_record(chunk, ts):
                with transaction.atomic():
                    if math.isnan(chunk["addon"]):
                        chunk["addon"] = None
                    else:
                        chunk["addon"] = json.loads(chunk["addon"])
                        print(chunk["addon"])
                    item = TransportServiceItem.objects.filter(name=chunk["name"])
                    if not item.exists():
                        item = TransportServiceItem.objects.create(
                            service=ts,
                            name=chunk["name"],
                            price=chunk["price"],
                            deposit=chunk["deposit"],
                            description=chunk["description"],
                            addon=chunk["addon"],
                            stop_location=chunk["stop_location"],
                        )
                        print("Item created")
                    else:
                        ts_item = TransportServiceItem.objects.filter(
                            name=chunk["name"], service=ts
                        )
                        if ts_item.exists():
                            print("Skipping ", chunk["name"])
                            return chunk
                        TransportServiceItem.objects.create(
                            service=ts,
                            name=chunk["name"],
                            price=chunk["price"],
                            deposit=chunk["deposit"],
                            description=chunk["description"],
                            addon=chunk["addon"],
                            stop_location=chunk["stop_location"],
                            image=item.last().image,
                        )
                        print("Reusing from: ", item.last().name)
                        return chunk
                    if not item.image:
                        try:
                            image_url = chunk["image_url"]
                            result = urllib.request.urlretrieve(image_url)
                            item.image.save(
                                "onboarding/transport/" + ts.pk,
                                File(open(result[0], "rb")),
                            )
                            item.save()
                        except Exception as e:
                            print("Exception occurred:", e)
                            prompt = chunk["name"] + " - " + chunk["description"]
                            print("Generating from Open AI: ", prompt)
                            try:
                                client = OpenAI()
                                response = client.images.generate(
                                    model="dall-e-2",
                                    prompt=prompt,
                                    size="512x512",
                                    quality="standard",
                                    n=1,
                                )
                                image_url = response.data[0].url
                                result = urllib.request.urlretrieve(image_url)
                                item.image.save(
                                    "onboarding/transport/{}/{}.png".format(
                                        str(ts.pk), chunk["name"]
                                    ),
                                    File(open(result[0], "rb")),
                                )
                                item.save()
                            except Exception as e:
                                print(e)
                                return chunk
                        else:
                            print("Item already has image: ", chunk["name"])
                return chunk

            path = kwargs.get("filepath")
            service_id = kwargs.get("service_id")
            service = TransportService.objects.get(pk=service_id)
            df = pd.read_csv(path)
            df = df.apply(lambda x: create_record(x, service), axis=1)
        except Exception as e:
            print(e)
