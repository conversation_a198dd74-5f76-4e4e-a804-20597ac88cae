# Generated by Django 4.2.7 on 2025-10-02 05:11

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountVerificationLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('activity_type', models.CharField(choices=[('account_created', 'Account Created'), ('kyc_submitted', 'KYC Submitted'), ('kyc_verified', 'KYC Verified'), ('kyc_rejected', 'KYC Rejected'), ('bank_added', 'Bank Account Added'), ('bank_verified', 'Bank Account Verified'), ('bank_rejected', 'Bank Account Rejected'), ('account_activated', 'Account Activated'), ('account_suspended', 'Account Suspended'), ('webhook_received', 'Webhook Received')], help_text='Type of verification activity', max_length=50)),
                ('description', models.TextField(help_text='Detailed description of the activity')),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='Additional metadata for the activity')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Account Verification Log',
                'verbose_name_plural': 'Account Verification Logs',
                'db_table': 'payments_account_verification_log',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PartnerBankAccount',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('account_holder_name', models.CharField(help_text='Name as per bank account', max_length=100)),
                ('account_number', models.CharField(help_text='Bank account number', max_length=20, validators=[django.core.validators.MinLengthValidator(9), django.core.validators.RegexValidator(message='Account number must contain only digits', regex='^\\d+$')])),
                ('ifsc_code', models.CharField(help_text='IFSC code of the bank branch', max_length=11, validators=[django.core.validators.RegexValidator(message='Invalid IFSC code format', regex='^[A-Z]{4}0[A-Z0-9]{6}$')])),
                ('bank_name', models.CharField(max_length=100)),
                ('branch_name', models.CharField(max_length=100)),
                ('account_type', models.CharField(choices=[('savings', 'Savings'), ('current', 'Current')], default='savings', help_text='Type of bank account', max_length=20)),
                ('verification_status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('verified', 'Verified'), ('failed', 'Failed'), ('rejected', 'Rejected')], default='pending', help_text='Bank account verification status', max_length=20)),
                ('is_primary', models.BooleanField(default=False, help_text='Whether this is the primary bank account')),
                ('razorpay_bank_account_id', models.CharField(blank=True, help_text='Razorpay bank account identifier', max_length=100, null=True)),
                ('verification_attempts', models.IntegerField(default=0, help_text='Number of verification attempts')),
                ('last_verification_attempt', models.DateTimeField(blank=True, help_text='Last verification attempt timestamp', null=True)),
                ('verification_error', models.TextField(blank=True, help_text='Last verification error message', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Partner Bank Account',
                'verbose_name_plural': 'Partner Bank Accounts',
                'db_table': 'payments_partner_bank_account',
            },
        ),
        migrations.CreateModel(
            name='PartnerKYCDocument',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('document_type', models.CharField(choices=[('pan_card', 'PAN Card'), ('aadhaar_card', 'Aadhaar Card'), ('bank_statement', 'Bank Statement'), ('cancelled_cheque', 'Cancelled Cheque'), ('business_registration', 'Business Registration'), ('gst_certificate', 'GST Certificate'), ('partnership_deed', 'Partnership Deed'), ('moa_aoa', 'MOA/AOA'), ('trust_deed', 'Trust Deed'), ('society_registration', 'Society Registration')], help_text='Type of KYC document', max_length=30)),
                ('document_number', models.CharField(help_text='Document number (PAN, Aadhaar, etc.)', max_length=50)),
                ('document_file', models.FileField(help_text='Uploaded document file', upload_to='kyc_documents/%Y/%m/')),
                ('file_size', models.IntegerField(blank=True, help_text='File size in bytes', null=True)),
                ('file_type', models.CharField(blank=True, help_text='File type (pdf, jpg, png)', max_length=10, null=True)),
                ('verification_status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('verified', 'Verified'), ('failed', 'Failed'), ('rejected', 'Rejected')], default='pending', help_text='Document verification status', max_length=20)),
                ('razorpay_document_id', models.CharField(blank=True, help_text='Razorpay document identifier', max_length=100, null=True)),
                ('verification_notes', models.TextField(blank=True, help_text='Verification notes or rejection reasons', null=True)),
                ('verified_by', models.CharField(blank=True, help_text='Who verified the document', max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Partner KYC Document',
                'verbose_name_plural': 'Partner KYC Documents',
                'db_table': 'payments_partner_kyc_document',
            },
        ),
        migrations.CreateModel(
            name='PartnerRazorpayAccount',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('razorpay_account_id', models.CharField(blank=True, help_text='Razorpay linked account ID', max_length=100, null=True, unique=True)),
                ('account_status', models.CharField(choices=[('pending', 'Pending'), ('under_review', 'Under Review'), ('active', 'Active'), ('suspended', 'Suspended'), ('rejected', 'Rejected')], default='pending', help_text='Current account status', max_length=20)),
                ('kyc_status', models.CharField(choices=[('pending', 'Pending'), ('submitted', 'Submitted'), ('under_review', 'Under Review'), ('verified', 'Verified'), ('rejected', 'Rejected'), ('needs_clarification', 'Needs Clarification')], default='pending', help_text='KYC verification status', max_length=20)),
                ('bank_verification_status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('verified', 'Verified'), ('failed', 'Failed'), ('rejected', 'Rejected')], default='pending', help_text='Bank account verification status', max_length=20)),
                ('business_name', models.CharField(help_text='Legal business name', max_length=200)),
                ('business_type', models.CharField(choices=[('individual', 'Individual'), ('proprietorship', 'Proprietorship'), ('partnership', 'Partnership'), ('private_limited', 'Private Limited'), ('public_limited', 'Public Limited'), ('llp', 'Limited Liability Partnership'), ('trust', 'Trust'), ('society', 'Society'), ('ngo', 'NGO')], default='individual', help_text='Type of business entity', max_length=50)),
                ('contact_name', models.CharField(help_text='Primary contact person name', max_length=100)),
                ('contact_email', models.EmailField(help_text='Contact email for account communications', max_length=254)),
                ('contact_phone', models.CharField(help_text='Contact phone number', max_length=15, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')])),
                ('address_line1', models.CharField(max_length=200)),
                ('address_line2', models.CharField(blank=True, max_length=200)),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=100)),
                ('postal_code', models.CharField(max_length=10, validators=[django.core.validators.RegexValidator(message='Postal code must be 6 digits', regex='^\\d{6}$')])),
                ('country', models.CharField(default='IN', max_length=2)),
                ('razorpay_response', models.JSONField(blank=True, default=dict, help_text='Raw response from Razorpay account creation')),
                ('stakeholder_id', models.CharField(blank=True, help_text='Razorpay Stakeholder ID for the linked account', max_length=100, null=True)),
                ('product_config_id', models.CharField(blank=True, help_text='Razorpay Product Configuration ID for Route', max_length=100, null=True)),
                ('product_activation_status', models.CharField(blank=True, help_text='Route product activation status (e.g., created, activated, needs_clarification)', max_length=30, null=True)),
                ('product_requirements', models.JSONField(blank=True, default=list, help_text='Outstanding or returned requirements from Razorpay product configuration')),
                ('activation_form_milestone', models.CharField(blank=True, help_text='Current milestone in activation process', max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('activated_at', models.DateTimeField(blank=True, null=True)),
                ('last_error', models.TextField(blank=True, help_text='Last error encountered during account operations', null=True)),
                ('error_count', models.IntegerField(default=0, help_text='Number of consecutive errors')),
            ],
            options={
                'verbose_name': 'Partner Razorpay Account',
                'verbose_name_plural': 'Partner Razorpay Accounts',
                'db_table': 'payments_partner_razorpay_account',
            },
        ),
        migrations.CreateModel(
            name='PaymentIntent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('reference_number', models.CharField(blank=True, editable=False, help_text='Unique reference number for this payment', max_length=50, unique=True)),
                ('context', models.CharField(choices=[('checkout', 'Guest Checkout'), ('precheckin', 'Precheckin Upfront'), ('service_order', 'Service Order'), ('booking', 'Booking Payment')], help_text='Payment context (checkout, precheckin, etc.)', max_length=20)),
                ('total_amount', models.DecimalField(decimal_places=2, help_text='Total payment amount in rupees', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('platform_commission', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Platform commission amount in rupees', max_digits=10)),
                ('partner_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Amount to be transferred to partner in rupees', max_digits=10)),
                ('vendor_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Amount to be transferred to vendors in rupees', max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('refunded', 'Refunded'), ('partially_refunded', 'Partially Refunded')], default='pending', help_text='Current payment status', max_length=20)),
                ('payment_method', models.CharField(blank=True, choices=[('card', 'Credit/Debit Card'), ('upi', 'UPI'), ('netbanking', 'Net Banking'), ('wallet', 'Digital Wallet'), ('emi', 'EMI'), ('bank_transfer', 'Bank Transfer')], help_text='Payment method used by customer', max_length=20, null=True)),
                ('razorpay_order_id', models.CharField(blank=True, help_text='Razorpay order ID', max_length=100, null=True)),
                ('razorpay_payment_id', models.CharField(blank=True, help_text='Razorpay payment ID', max_length=100, null=True)),
                ('razorpay_payment_link_id', models.CharField(blank=True, help_text='Razorpay payment link ID', max_length=100, null=True)),
                ('split_details', models.JSONField(default=dict, help_text='Detailed split calculation results')),
                ('idempotency_key', models.CharField(blank=True, editable=False, help_text='Idempotency key for webhook processing', max_length=100, unique=True)),
                ('webhook_processed_at', models.DateTimeField(blank=True, help_text='Timestamp when webhook was processed', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('paid_at', models.DateTimeField(blank=True, help_text='Timestamp when payment was completed', null=True)),
                ('expires_at', models.DateTimeField(blank=True, help_text='Payment link expiry timestamp', null=True)),
                ('metadata', models.JSONField(default=dict, help_text='Additional metadata for this payment')),
                ('created_by', models.ForeignKey(blank=True, help_text='User who created this payment intent', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_payment_intents', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'payments_payment_intent',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PaymentWebhookEvent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('event_id', models.CharField(help_text='Razorpay event ID', max_length=100, unique=True)),
                ('event_type', models.CharField(choices=[('payment.authorized', 'Payment Authorized'), ('payment.captured', 'Payment Captured'), ('payment.failed', 'Payment Failed'), ('transfer.processed', 'Transfer Processed'), ('transfer.failed', 'Transfer Failed'), ('transfer.reversed', 'Transfer Reversed'), ('payment_link.paid', 'Payment Link Paid'), ('payment_link.cancelled', 'Payment Link Cancelled')], help_text='Type of webhook event', max_length=50)),
                ('entity_type', models.CharField(help_text='Type of entity (payment, transfer, etc.)', max_length=50)),
                ('entity_id', models.CharField(help_text='ID of the entity', max_length=100)),
                ('raw_payload', models.JSONField(help_text='Complete webhook payload from Razorpay')),
                ('headers', models.JSONField(default=dict, help_text='HTTP headers from webhook request')),
                ('processed', models.BooleanField(default=False, help_text='Whether this event has been processed')),
                ('processed_at', models.DateTimeField(blank=True, help_text='When this event was processed', null=True)),
                ('processing_attempts_count', models.PositiveIntegerField(default=0, help_text='Number of processing attempts')),
                ('processing_error', models.TextField(blank=True, help_text='Error message if processing failed', null=True)),
                ('last_error_at', models.DateTimeField(blank=True, help_text='When last error occurred', null=True)),
                ('signature_verified', models.BooleanField(default=False, help_text='Whether webhook signature was verified')),
                ('signature', models.CharField(blank=True, help_text='Webhook signature from Razorpay', max_length=500, null=True)),
                ('received_at', models.DateTimeField(auto_now_add=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('source_ip', models.GenericIPAddressField(blank=True, help_text='IP address of webhook source', null=True)),
                ('payment_intent', models.ForeignKey(blank=True, help_text='Associated payment intent', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='webhook_events', to='payments.paymentintent')),
            ],
            options={
                'db_table': 'payments_webhook_event',
                'ordering': ['-received_at'],
            },
        ),
        migrations.CreateModel(
            name='WebhookProcessingAttempt',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('attempt_number', models.PositiveIntegerField(help_text='Sequential attempt number')),
                ('status', models.CharField(help_text='Status of this attempt', max_length=20)),
                ('attempted_at', models.DateTimeField(help_text='When attempt occurred')),
                ('error_message', models.TextField(blank=True, help_text='Error message if failed', null=True)),
                ('event', models.ForeignKey(help_text='Associated webhook event', on_delete=django.db.models.deletion.CASCADE, related_name='processing_attempts', to='payments.paymentwebhookevent')),
            ],
            options={
                'db_table': 'payments_webhook_processing_attempt',
                'ordering': ['-attempted_at'],
            },
        ),
        migrations.CreateModel(
            name='PaymentSplit',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('recipient_type', models.CharField(choices=[('platform', 'Platform Commission'), ('partner', 'Partner Amount'), ('vendor', 'Vendor Payout')], help_text='Type of recipient for this split', max_length=20)),
                ('recipient_id', models.CharField(blank=True, help_text='ID of the recipient (partner ID, vendor ID, etc.)', max_length=100, null=True)),
                ('recipient_name', models.CharField(help_text='Name of the recipient', max_length=200)),
                ('amount', models.DecimalField(decimal_places=2, help_text='Split amount in rupees', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, help_text='Percentage of total amount (if applicable)', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('razorpay_account_id', models.CharField(blank=True, help_text='Razorpay linked account ID for transfer', max_length=100, null=True)),
                ('razorpay_transfer_id', models.CharField(blank=True, help_text='Razorpay transfer ID', max_length=100, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('processed', 'Processed'), ('reversed', 'Reversed'), ('failed', 'Failed'), ('on_hold', 'On Hold')], default='pending', help_text='Current transfer status', max_length=20)),
                ('transfer_initiated_at', models.DateTimeField(blank=True, help_text='When transfer was initiated', null=True)),
                ('transfer_completed_at', models.DateTimeField(blank=True, help_text='When transfer was completed', null=True)),
                ('settlement_id', models.CharField(blank=True, help_text='Settlement ID from Razorpay', max_length=100, null=True)),
                ('settled_at', models.DateTimeField(blank=True, help_text='When amount was settled to recipient', null=True)),
                ('error_code', models.CharField(blank=True, help_text='Error code if transfer failed', max_length=50, null=True)),
                ('error_message', models.TextField(blank=True, help_text='Error message if transfer failed', null=True)),
                ('retry_count', models.PositiveIntegerField(default=0, help_text='Number of retry attempts')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metadata', models.JSONField(default=dict, help_text='Additional metadata for this split')),
                ('payment_intent', models.ForeignKey(help_text='Associated payment intent', on_delete=django.db.models.deletion.CASCADE, related_name='splits', to='payments.paymentintent')),
            ],
            options={
                'db_table': 'payments_payment_split',
                'ordering': ['created_at'],
            },
        ),
    ]
