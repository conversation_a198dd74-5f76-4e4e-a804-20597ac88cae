def checkin_initiated_template(username, property_name, room_no, **kwargs):
    return (
        f"Checkin Initiated for Room {room_no}",
        f"""Greetings {username}! 🌟
    We're delighted to inform you that your check-in at {property_name} has been successfully processed, and you've been assigned to Room {room_no}. 🏨
    To enhance your stay and explore our exclusive services, please click on the following link to complete your check-in: Complete Check-In.
    If you have any questions or need assistance during your stay, our friendly staff is here to help. We hope you have a wonderful and comfortable experience at {property_name}. Enjoy your stay! 🌟✨
    """,
    )


def checkout_template(username, property_name, **kwargs):
    return (
        f"Checkout Processed for Property {property_name}",
        f"""Hello, {username}! 🌟
    We trust your stay at {property_name} was fantastic! As you get ready to check out, we hope it was a memorable experience. 🏨
    Looking forward to welcoming you back soon! 🌟✨""",
    )


def order_accepted_template(
    username,
    order_id,
    vendor_name,
    estimated_time,
    total_amount,
    vendor_contact,
    **kwargs,
):
    """Extended template to match WhatsApp Business Manager requirements (6 parameters)"""
    subject = f"Your Order {order_id} has been Accepted."
    message = (
        f"Hello, {username}! 🌟\n"
        f"Great news! {vendor_name} has accepted your order {order_id}.\n"
        f"Estimated delivery: {estimated_time}\n"
        f"Total: ₹{total_amount}\n"
        f"Your order is now being prepared. We'll notify you when it's ready for delivery.\n"
        f"Questions? Contact vendor: {vendor_contact} 🛍️"
    )
    return subject, message


def order_cancelled_template(
    username, order_id, service_type, reason, refund_amount, additional_info, **kwargs
):
    """Extended template to match WhatsApp Business Manager requirements (6 parameters)"""
    subject = f"Your Order {order_id} has been Cancelled."
    message = (
        f"Hello, {username}! 🌟\n"
        f"Unfortunately, your {service_type} order {order_id} has been cancelled.\n"
        f"Reason: {reason}\n"
        f"Refund: ₹{refund_amount}\n"
        f"{additional_info}\n"
        f"We apologize for the inconvenience. Please try ordering again or contact us for assistance. 🛍️"
    )
    return subject, message


def order_completed_template(username, order_id, **kwargs):
    subject = f"Your Order {order_id} has been Completed."
    message = (
        f"Hello, {username}! 🌟\n"
        f"We're delighted to inform you that your order with ID {order_id} has been successfully completed. 🛍️"
    )
    return subject, message


def partner_order_placed_template(
    partner_name, order_id, room_no, guest_name, **kwargs
):
    subject = f"New Order {order_id} Placed by {guest_name}."
    message = (
        f"Hello, {partner_name}! 🌟\n"
        f"We're thrilled to inform you that an order with ID {order_id} has been placed by {guest_name} in Room {room_no}. 🛍️\n"
        f"Please ensure that the order is delivered to the guest's room promptly. 🌟✨"
    )
    return subject, message


def pre_checkin_confirmed_template(
    guest_name, property_owner_name, expected_date, room_number, **kwargs
):
    subject = f"Pre-Checkin Confirmed for {guest_name}."
    message = (
        f"Hello {property_owner_name}, 🌟\n"
        f"We're pleased to inform you that the pre-checkin for {guest_name} has been successfully completed in room(s) {room_number}. 🏨\n"
        f"The guest is expected to arrive on {expected_date}. Please ensure that everything is prepared for their arrival.\n"
        f"If you have any questions or need further assistance, feel free to reach out. 🌟✨"
    )
    return subject, message


def pre_checkin_created_template(
    guest_name,
    property_owner_name,
    expected_date,
    room_number,
    precheckin_link,
    **kwargs,
):
    subject = f"Pre-Checkin Created for {guest_name}."
    message = (
        f"Hello {property_owner_name}, 🌟\n"
        f"We're pleased to inform you that the pre-checkin for {guest_name} has been successfully created in room(s) {room_number}. 🏨\n"
        f"The guest is expected to arrive on {expected_date}. Please ensure that everything is prepared for their arrival.\n"
        f"Complete pre-checkin here: {precheckin_link}\n"
        f"If you have any questions or need further assistance, feel free to reach out. 🌟✨"
    )
    return subject, message


def order_placed_template(username, order_id, **kwargs):
    subject = f"Order {order_id} Placed Successfully."
    message = (
        f"Hello, {username}! 🌟\n"
        f"Your order {order_id} has been placed successfully! 🛍️\n"
        f"We'll keep you updated on the status of your order."
    )
    return subject, message


def order_ongoing_template(
    username, order_id, vendor_name, estimated_time, contact_number, **kwargs
):
    """Extended template to match WhatsApp Business Manager requirements (5 parameters)"""
    subject = f"Order {order_id} is Being Prepared."
    message = (
        f"Hello, {username}! 🌟\n"
        f"Great news! Your order {order_id} is now being prepared by {vendor_name}. 🛍️\n"
        f"Estimated completion: {estimated_time}\n"
        f"We'll notify you once it's ready for delivery.\n"
        f"Need to make changes? Contact us immediately: {contact_number}"
    )
    return subject, message


def order_rejected_template(
    username, order_id, service_type, rejection_reason, refund_amount, **kwargs
):
    """Extended template to match WhatsApp Business Manager requirements (5 parameters)"""
    subject = f"Order {order_id} has been Rejected."
    message = (
        f"Hello, {username}! 🌟\n"
        f"Unfortunately, your {service_type} order {order_id} has been rejected. 🛍️\n"
        f"Reason: {rejection_reason}\n"
        f"Refund: ₹{refund_amount} (processed automatically)\n"
        f"Please contact customer support for more information or try ordering again."
    )
    return subject, message


def partner_order_accepted_template(
    partner_name, order_id, room_no, guest_name, **kwargs
):
    subject = f"Order {order_id} Accepted Successfully."
    message = (
        f"Hello, {partner_name}! 🌟\n"
        f"You have successfully accepted order {order_id} from {guest_name} in Room {room_no}. 🛍️\n"
        f"Please start preparing the order for delivery."
    )
    return subject, message


def partner_order_ongoing_template(
    partner_name, order_id, room_no, guest_name, estimated_completion, **kwargs
):
    """Extended template to match WhatsApp Business Manager requirements (5 parameters)"""
    subject = f"Order {order_id} Status Updated to Ongoing."
    message = (
        f"Hello, {partner_name}! 🌟\n"
        f"Order {order_id} from {guest_name} in Room {room_no} is now marked as ongoing. 🛍️\n"
        f"Estimated completion: {estimated_completion}\n"
        f"Please ensure timely delivery to the guest."
    )
    return subject, message


def partner_order_completed_template(
    partner_name, order_id, room_no, guest_name, **kwargs
):
    subject = f"Order {order_id} Completed Successfully."
    message = (
        f"Hello, {partner_name}! 🌟\n"
        f"Congratulations! Order {order_id} from {guest_name} in Room {room_no} has been completed successfully. 🛍️✨"
    )
    return subject, message


def partner_order_cancelled_template(
    partner_name, order_id, room_no, guest_name, **kwargs
):
    subject = f"Order {order_id} has been Cancelled."
    message = (
        f"Hello, {partner_name}! 🌟\n"
        f"Order {order_id} from {guest_name} in Room {room_no} has been cancelled. 🛍️"
    )
    return subject, message


def partner_order_rejected_template(
    partner_name,
    order_id,
    room_no,
    guest_name,
    rejection_reason,
    refund_amount,
    dashboard_link,
    **kwargs,
):
    """Extended template to match WhatsApp Business Manager requirements (7 parameters)"""
    subject = f"Order {order_id} has been Rejected."
    message = (
        f"Hello, {partner_name}! 🌟\n"
        f"You have rejected order {order_id} from {guest_name} in Room {room_no}. 🛍️\n"
        f"Reason: {rejection_reason}\n"
        f"Refund: ₹{refund_amount} (processed automatically)\n"
        f"The guest has been notified. Frequent rejections may affect your vendor rating.\n"
        f"Manage Orders: {dashboard_link}"
    )
    return subject, message


def precheckin_status_changed_template(
    guest_name, property_owner_name, status, room_number, **kwargs
):
    subject = f"Pre-Checkin Status Updated for {guest_name}."
    message = (
        f"Hello {property_owner_name}, 🌟\n"
        f"The pre-checkin status for {guest_name} in room(s) {room_number} has been updated to {status}. 🏨\n"
        f"Please take necessary actions as required."
    )
    return subject, message


def daily_summary_guest_template(
    username, total_orders, total_spent, property_name, most_ordered_service, **kwargs
):
    """Extended template to match WhatsApp Business Manager requirements (5 parameters)"""
    subject = f"Daily Summary - {property_name}"
    message = (
        f"Hello {username}! 🌟\n"
        f"Here's your activity summary for today at {property_name}:\n"
        f"📊 Total Orders: {total_orders}\n"
        f"💰 Total Spent: ₹{total_spent}\n"
        f"🎯 Most Ordered: {most_ordered_service}\n"
        f"Thank you for choosing us! 🏨✨"
    )
    return subject, message


def daily_summary_partner_template(
    partner_name, total_orders, total_revenue, property_name, **kwargs
):
    subject = f"Daily Business Summary - {property_name}"
    message = (
        f"Hello {partner_name}! 🌟\n"
        f"Here's your daily business summary for {property_name}:\n"
        f"📊 Total Orders: {total_orders}\n"
        f"💰 Total Revenue: ₹{total_revenue}\n"
        f"Keep up the great work! 🏨✨"
    )
    return subject, message


# New WhatsApp notification templates for complete flow


def signup_successful_template(partner_name, property_name, **kwargs):
    subject = f"Welcome to Nestafar! 🏨"
    message = (
        f"Hi {partner_name}!\n\n"
        f"Welcome to Nestafar! Your property {property_name} has been successfully registered on our platform.\n\n"
        f"To get started:\n"
        f"1. Complete your property setup\n"
        f"2. Upload high-quality photos\n"
        f"3. Set your pricing and availability\n"
        f"4. Add services and amenities\n\n"
        f"Our team will guide you through the onboarding process. Get ready to increase your revenue with our integrated service marketplace!\n\n"
        f"Start earning more today! 💰"
    )
    return subject, message


def precheckin_reminder_template(
    guest_name, property_name, checkin_date, hours_remaining, **kwargs
):
    subject = f"⏰ Pre Check-in Reminder - {hours_remaining} hours left"
    message = (
        f"Hi {guest_name}!\n\n"
        f"You have {hours_remaining} hours until your check-in at {property_name}.\n\n"
        f"Your pre check-in is still pending. Complete it now to:\n"
        f"✓ Skip the front desk queue\n"
        f"✓ Get instant room access\n"
        f"✓ Start enjoying services immediately\n\n"
        f"Check-in Date: {checkin_date}\n\n"
        f"Complete now to ensure a smooth arrival!"
    )
    return subject, message


def precheckin_cancellation_warning_template(
    guest_name, property_name, checkin_date, hours_remaining, **kwargs
):
    subject = f"🚨 URGENT: Complete Pre Check-in Now"
    message = (
        f"Hi {guest_name}!\n\n"
        f"FINAL REMINDER: Your check-in at {property_name} is in {hours_remaining} hours.\n\n"
        f"Your pre check-in will expire soon. Complete it immediately to avoid delays and potential booking cancellation.\n\n"
        f"Check-in Date: {checkin_date}\n\n"
        f"Complete NOW to secure your reservation!\n\n"
        f"Contact property directly if you need assistance."
    )
    return subject, message


def checkin_successful_template(
    guest_name, property_name, room_details, checkout_date, **kwargs
):
    subject = f"Welcome! 🎉 You're Checked In"
    message = (
        f"Hi {guest_name}!\n\n"
        f"Welcome to {property_name}! You're now checked in.\n\n"
        f"Room: {room_details}\n"
        f"Check-out: {checkout_date}\n\n"
        f"Access all services via QR codes around the property:\n"
        f"🍽️ Restaurant & Room Service\n"
        f"🧺 Laundry & Dry Cleaning\n"
        f"🚗 Transportation\n"
        f"🛍️ Local Shopping\n"
        f"🏛️ Tours & Experiences\n\n"
        f"Scan any QR code to order instantly. Enjoy your stay!"
    )
    return subject, message


def checkout_bills_template(
    guest_name, property_name, checkout_date, total_amount, **kwargs
):
    subject = f"Your Final Bill 💳"
    message = (
        f"Hi {guest_name}!\n\n"
        f"Thank you for staying at {property_name}!\n\n"
        f"Check-out Date: {checkout_date}\n"
        f"Total Amount: ₹{total_amount}\n\n"
        f"Your bill includes:\n"
        f"• Room charges\n"
        f"• Service orders\n"
        f"• Applicable taxes\n\n"
        f"Payment will be processed using your registered payment method.\n\n"
        f"We hope you had a wonderful stay!"
    )
    return subject, message


def review_request_template(guest_name, property_name, **kwargs):
    subject = f"How was your stay? ⭐"
    message = (
        f"Hi {guest_name}!\n\n"
        f"We hope you enjoyed your stay at {property_name}!\n\n"
        f"Your feedback helps us improve and helps other travelers choose the perfect accommodation.\n\n"
        f"Please take 2 minutes to share your experience:\n"
        f"⭐ Rate your stay\n"
        f"💭 Share what you loved\n"
        f"📝 Suggest improvements\n\n"
        f"Thank you for choosing Nestafar!"
    )
    return subject, message


def order_confirmed_template(
    guest_name,
    service_type,
    order_id,
    order_items,
    total_amount,
    delivery_time,
    **kwargs,
):
    subject = f"Order Confirmed ✅"
    message = (
        f"Hi {guest_name}!\n\n"
        f"Your {service_type} order has been confirmed!\n\n"
        f"Order ID: {order_id}\n"
        f"Items: {order_items}\n"
        f"Total: ₹{total_amount}\n"
        f"Delivery: {delivery_time}\n\n"
        f"The vendor is preparing your order. You'll receive updates on the progress.\n\n"
        f"Thank you for your order!"
    )
    return subject, message


def order_ready_template(
    guest_name, service_type, order_id, status, total_amount, instructions, **kwargs
):
    subject = f"Order Ready for Pickup/Delivery 🚀"
    message = (
        f"Hi {guest_name}!\n\n"
        f"Your {service_type} order is ready!\n\n"
        f"Order ID: {order_id}\n"
        f"Status: {status}\n"
        f"Total: ₹{total_amount}\n\n"
        f"{instructions}\n\n"
        f"Enjoy your order!"
    )
    return subject, message


def vendor_new_order_template(
    vendor_name,
    order_id,
    guest_name,
    property_name,
    order_items,
    total_amount,
    **kwargs,
):
    subject = f"🔔 New Order Received!"
    message = (
        f"Hi {vendor_name}!\n\n"
        f"You have a new order!\n\n"
        f"Order ID: {order_id}\n"
        f"Guest: {guest_name}\n"
        f"Property: {property_name}\n"
        f"Items: {order_items}\n"
        f"Total: ₹{total_amount}\n\n"
        f"Please accept or decline within 10 minutes to maintain your rating.\n\n"
        f"New business opportunity!"
    )
    return subject, message


def vendor_order_reminder_template(
    vendor_name, minutes_pending, order_id, total_amount, **kwargs
):
    subject = f"⏰ Order Pending - {minutes_pending} minutes"
    message = (
        f"Hi {vendor_name}!\n\n"
        f"REMINDER: You have a pending order for {minutes_pending} minutes.\n\n"
        f"Order ID: {order_id}\n"
        f"Total: ₹{total_amount}\n\n"
        f"Please respond immediately to avoid automatic cancellation and rating impact.\n\n"
        f"Time is running out!"
    )
    return subject, message


def dinner_reminder_template(guest_name, property_name, **kwargs):
    subject = f"🍽️ Dinner Time!"
    message = (
        f"Hi {guest_name}!\n\n"
        f"It's dinner time at {property_name}!\n\n"
        f"Don't miss out on delicious local cuisine. Order now from our partner restaurants:\n\n"
        f"🥘 Traditional dishes\n"
        f"🍕 International options\n"
        f"🥗 Healthy choices\n"
        f"🍰 Desserts & beverages\n\n"
        f"Scan the QR code in your room or common areas to explore our menu and order instantly.\n\n"
        f"Bon appétit! 🍴"
    )
    return subject, message


def weekly_report_template(
    partner_name,
    property_name,
    week_start,
    week_end,
    reservations,
    occupancy_rate,
    avg_orders,
    gmv,
    commission,
    recommendations,
    **kwargs,
):
    subject = f"📊 Your Weekly Business Report"
    message = (
        f"Hi {partner_name}!\n\n"
        f"Here's your {property_name} performance for {week_start} to {week_end}:\n\n"
        f"📈 Reservations: {reservations}\n"
        f"🏠 Occupancy Rate: {occupancy_rate}%\n"
        f"📦 Avg Orders/Guest: {avg_orders}\n"
        f"💰 Total GMV: ₹{gmv}\n"
        f"💵 Your Commission: ₹{commission}\n\n"
        f"Recommendations:\n{recommendations}\n\n"
        f"Keep up the great work! 🎉"
    )
    return subject, message


def service_hidden_notification_template(
    guest_name, service_name, property_name, **kwargs
):
    subject = f"Service Temporarily Unavailable ⚠️"
    message = (
        f"Hi {guest_name}!\n\n"
        f"We want to inform you that {service_name} service is temporarily unavailable at {property_name}.\n\n"
        f"This could be due to:\n"
        f"• High demand\n"
        f"• Maintenance\n"
        f"• Vendor unavailability\n\n"
        f"We're working to restore the service as soon as possible. Other services remain available through the QR codes around the property.\n\n"
        f"Thank you for your understanding! 🙏"
    )
    return subject, message


def service_restored_notification_template(
    guest_name, service_name, property_name, **kwargs
):
    subject = f"Service Restored ✅"
    message = (
        f"Hi {guest_name}!\n\n"
        f"Good news! {service_name} service is now available again at {property_name}.\n\n"
        f"You can now:\n"
        f"• Browse the full menu/catalog\n"
        f"• Place orders instantly\n"
        f"• Enjoy quick delivery\n\n"
        f"Scan the QR codes around the property to access this service.\n\n"
        f"Happy to serve you! 😊"
    )
    return subject, message


# Job management templates


def job_assigned_template(
    staff_name,
    job_id,
    job_type,
    room,
    pickup_location,
    dropoff_location,
    earnings,
    **kwargs,
):
    subject = f"New {job_type.title()} Job Assigned"
    details = []
    if room:
        details.append(f"Room: {room}")
    if pickup_location:
        details.append(f"Pickup: {pickup_location}")
    if dropoff_location:
        details.append(f"Drop-off: {dropoff_location}")
    if earnings:
        details.append(f"Earnings: ₹{earnings}")
    message = f"Hello {staff_name},\nYou have been assigned Job {job_id}.\n" + (
        "\n".join(details) if details else ""
    )
    return subject, message


def job_status_changed_template(staff_name, job_id, new_status, **kwargs):
    subject = f"Job {job_id} Status Updated"
    message = (
        f"Hello {staff_name},\nStatus updated to: {new_status.replace('_',' ').title()}"
    )
    return subject, message


def delivery_assigned_template(
    staff_name, job_id, pickup_location, dropoff_location, earnings, **kwargs
):
    subject = f"New Delivery Job Assigned - #{job_id}"
    details = [
        f"Hello {staff_name},",
        f"Delivery Job {job_id}:",
        f"Pickup: {pickup_location}" if pickup_location else None,
        f"Drop-off: {dropoff_location}" if dropoff_location else None,
        (f"Earnings: ₹{earnings}" if earnings is not None else None),
    ]
    message = "\n".join([d for d in details if d])
    return subject, message


# Additional templates for comprehensive testing
def welcome_message_template(
    guest_name, property_name, room_number, wifi_password, **kwargs
):
    subject = f"Welcome to {property_name}! 🏨"
    message = (
        f"Hi {guest_name}!\n\n"
        f"Welcome to {property_name}! We're delighted to have you stay with us.\n\n"
        f"🏠 Your Room: {room_number}\n"
        f"📶 WiFi Password: {wifi_password}\n\n"
        f"Need anything? Scan the QR codes around the property for:\n"
        f"• Room service & dining\n"
        f"• Housekeeping requests\n"
        f"• Local attractions\n"
        f"• Concierge services\n\n"
        f"Enjoy your stay! 🌟"
    )
    return subject, message


def checkout_reminder_template(
    guest_name, property_name, checkout_time, extension_available: bool, **kwargs
):
    subject = f"Checkout Reminder ⏰"
    message = (
        f"Hi {guest_name}!\n\n"
        f"This is a friendly reminder that checkout time at {property_name} is {checkout_time}.\n\n"
        f"✅ Please ensure all personal belongings are packed\n"
        f"✅ Room key cards are left in the room\n"
        f"✅ Any pending payments are settled\n\n"
    )

    if extension_available:
        message += f"Need more time? Late checkout may be available - please contact reception.\n\n"

    message += (
        f"Thank you for staying with us! We hope you had a wonderful experience. 🙏"
    )
    return subject, message


def checkout_successful_template(
    guest_name, property_name, total_amount, payment_method, **kwargs
):
    subject = f"Checkout Complete - Thank You! ✅"
    message = (
        f"Hi {guest_name}!\n\n"
        f"Your checkout from {property_name} has been completed successfully.\n\n"
        f"💰 Total Amount: {total_amount}\n"
        f"💳 Payment Method: {payment_method}\n\n"
        f"Thank you for choosing {property_name}! We hope you had an amazing stay.\n\n"
        f"We'd love to hear about your experience - a review would help us serve future guests better.\n\n"
        f"Safe travels and hope to see you again soon! 🌟"
    )
    return subject, message


def service_request_received_template(
    guest_name, service_name, estimated_time, property_name, **kwargs
):
    subject = f"Service Request Received ✅"
    message = (
        f"Hi {guest_name}!\n\n"
        f"We've received your request for {service_name} at {property_name}.\n\n"
        f"⏱️ Estimated Time: {estimated_time}\n\n"
        f"Our team is preparing to fulfill your request. You'll receive an update once the service is in progress.\n\n"
        f"Thank you for choosing our services! 😊"
    )
    return subject, message


def service_in_progress_template(
    guest_name, service_name, eta, property_name, **kwargs
):
    subject = f"Service In Progress 🔄"
    message = (
        f"Hi {guest_name}!\n\n"
        f"Good news! Your {service_name} request is now in progress at {property_name}.\n\n"
        f"🕐 Estimated Completion: {eta}\n\n"
        f"Our team is working on your request and will notify you once it's completed.\n\n"
        f"Thank you for your patience! 👍"
    )
    return subject, message


def service_completed_template(
    guest_name, service_name, completion_time, property_name, **kwargs
):
    subject = f"Service Completed ✅"
    message = (
        f"Hi {guest_name}!\n\n"
        f"Your {service_name} request has been completed at {property_name}!\n\n"
        f"⏰ Completed at: {completion_time}\n\n"
        f"We hope you're satisfied with the service. If you need anything else, feel free to scan the QR codes around the property.\n\n"
        f"Thank you for choosing our services! 🌟"
    )
    return subject, message


def food_order_placed_template(
    guest_name, property_name, order_items, total_amount, **kwargs
):
    subject = f"Food Order Placed 🍽️"
    message = (
        f"Hi {guest_name}!\n\n"
        f"Your food order has been placed at {property_name}!\n\n"
        f"📦 Items: {order_items}\n"
        f"💰 Total: {total_amount}\n\n"
        f"The restaurant has been notified and will confirm your order shortly. You'll receive updates on the preparation and delivery status.\n\n"
        f"Thank you for your order! 😋"
    )
    return subject, message


def food_order_confirmed_template(
    guest_name, property_name, order_items, delivery_time, **kwargs
):
    subject = f"Order Confirmed! 🎉"
    message = (
        f"Hi {guest_name}!\n\n"
        f"Great news! Your food order has been confirmed by the restaurant at {property_name}.\n\n"
        f"📦 Items: {order_items}\n"
        f"🕐 Estimated Delivery: {delivery_time}\n\n"
        f"Your delicious meal is being prepared. We'll notify you when it's ready for pickup or delivery.\n\n"
        f"Can't wait for you to enjoy! 🍴"
    )
    return subject, message


def food_order_ready_template(
    guest_name, property_name, order_items, total_amount, **kwargs
):
    subject = f"Your Order is Ready! 🚀"
    message = (
        f"Hi {guest_name}!\n\n"
        f"Your food order is ready at {property_name}!\n\n"
        f"📦 Items: {order_items}\n"
        f"💰 Total: {total_amount}\n\n"
        f"Your meal is hot and ready for delivery to your room. Our delivery team is on the way!\n\n"
        f"Enjoy your delicious meal! 😋"
    )
    return subject, message


def rating_request_template(guest_name, property_name, **kwargs):
    subject = f"How was your stay? ⭐"
    message = (
        f"Hi {guest_name}!\n\n"
        f"Thank you for choosing {property_name}! We hope you had a wonderful experience.\n\n"
        f"Your feedback is invaluable to us and helps other travelers make informed decisions.\n\n"
        f"Could you spare a moment to rate your stay and share your experience?\n\n"
        f"⭐⭐⭐⭐⭐ Rate us now!\n\n"
        f"Thank you for your time, and we look forward to hosting you again! 🙏"
    )
    return subject, message


def daily_summary_template(
    property_name,
    total_checkins,
    total_checkouts,
    occupancy_rate,
    total_revenue,
    **kwargs,
):
    subject = f"📊 Daily Summary for {property_name}"
    message = (
        f"Here's your daily performance summary for {property_name}:\n\n"
        f"📈 Today's Metrics:\n"
        f"🏠 Check-ins: {total_checkins}\n"
        f"🚪 Check-outs: {total_checkouts}\n"
        f"📊 Occupancy Rate: {occupancy_rate}\n"
        f"💰 Total Revenue: {total_revenue}\n\n"
        f"Keep up the great work! 🎉"
    )
    return subject, message


def weekly_summary_template(
    property_name, week_start, total_guests, average_occupancy, total_revenue, **kwargs
):
    subject = f"📊 Weekly Summary for {property_name}"
    message = (
        f"Here's your weekly performance summary for {property_name}:\n\n"
        f"📅 Week: {week_start}\n"
        f"👥 Total Guests: {total_guests}\n"
        f"📊 Average Occupancy: {average_occupancy}\n"
        f"💰 Total Revenue: {total_revenue}\n\n"
        f"Great progress this week! 🚀"
    )
    return subject, message


def monthly_summary_template(
    property_name, month, total_bookings, total_revenue, top_service, **kwargs
):
    subject = f"📊 Monthly Summary for {property_name}"
    message = (
        f"Here's your monthly performance summary for {property_name}:\n\n"
        f"📅 Month: {month}\n"
        f"📋 Total Bookings: {total_bookings}\n"
        f"💰 Total Revenue: {total_revenue}\n"
        f"🏆 Top Service: {top_service}\n\n"
        f"Fantastic month! Keep growing! 🌟"
    )
    return subject, message


def external_reservation_alert_template(
    guest_name,
    property_name,
    checkin_date,
    checkout_date,
    guest_count,
    room_type,
    booking_source,
    total_amount,
    external_booking_id,
    **kwargs,
):
    """External reservation alert template for property staff"""
    subject = f"🆕 New External Reservation Alert - {guest_name}"
    message = (
        f"🆕 New Reservation Alert! 📅\n\n"
        f"Guest: {guest_name}\n"
        f"Property: {property_name}\n"
        f"Check-in: {checkin_date}\n"
        f"Check-out: {checkout_date}\n"
        f"Guests: {guest_count}\n"
        f"Room Type: {room_type}\n"
        f"Booking Source: {booking_source}\n"
        f"Total Amount: ₹{total_amount}\n"
        f"Booking ID: {external_booking_id}\n\n"
        f"Please review and prepare for the upcoming guest arrival.\n\n"
        f"Need assistance? Contact support team."
    )
    return subject, message


# ============================================================================
# PAYMENT TEMPLATES
# ============================================================================

def payment_link_template(
    customer_name,
    property_name,
    amount,
    payment_url,
    reference_number,
    context_type,
    **kwargs,
):
    """Payment link template for checkout and precheckin payments"""
    context_name = "checkout" if context_type == 'checkout' else "advance payment"

    subject = f"Payment Request - {context_name.title()}"
    message = (
        f"🏨 *Payment Request*\n\n"
        f"Hi {customer_name}! 👋\n\n"
        f"Please complete your {context_name} payment:\n\n"
        f"💰 Amount: *₹{amount:,.2f}*\n"
        f"🏢 Property: {property_name}\n"
        f"📋 Reference: {reference_number}\n\n"
        f"👆 Click to pay securely: {payment_url}\n\n"
        f"💳 Safe & secure payment powered by Razorpay\n"
        f"🔒 Your payment details are protected\n\n"
        f"_Powered by Nestafar_"
    )
    return subject, message


def payment_success_template(
    customer_name,
    property_name,
    amount,
    payment_id,
    reference_number,
    payment_method,
    **kwargs,
):
    """Payment success confirmation template"""
    subject = "Payment Successful ✅"
    message = (
        f"✅ *Payment Successful!*\n\n"
        f"Hi {customer_name}! 🎉\n\n"
        f"Your payment has been processed successfully:\n\n"
        f"💰 Amount: *₹{amount:,.2f}*\n"
        f"🏢 Property: {property_name}\n"
        f"💳 Method: {payment_method}\n"
        f"📋 Reference: {reference_number}\n"
        f"🆔 Payment ID: {payment_id}\n\n"
        f"📧 Receipt has been sent to your email\n"
        f"📱 You can view details in your booking\n\n"
        f"Thank you for choosing Nestafar! 🌟\n\n"
        f"_Powered by Nestafar_"
    )
    return subject, message


def payment_failure_template(
    customer_name,
    property_name,
    amount,
    reference_number,
    error_reason,
    retry_url,
    **kwargs,
):
    """Payment failure notification template"""
    subject = "Payment Failed ❌"
    message = (
        f"❌ *Payment Failed*\n\n"
        f"Hi {customer_name},\n\n"
        f"Unfortunately, your payment could not be processed:\n\n"
        f"💰 Amount: ₹{amount:,.2f}\n"
        f"🏢 Property: {property_name}\n"
        f"📋 Reference: {reference_number}\n"
    )

    if error_reason:
        message += f"❗ Reason: {error_reason}\n"

    message += (
        f"\n🔄 Please try again: {retry_url}\n\n"
        f"💡 *Tips for successful payment:*\n"
        f"• Check your internet connection\n"
        f"• Ensure sufficient balance\n"
        f"• Verify card details\n\n"
        f"Need help? Contact our support team 📞\n\n"
        f"_Powered by Nestafar_"
    )
    return subject, message


def payment_reminder_template(
    customer_name,
    property_name,
    amount,
    payment_url,
    reference_number,
    hours_remaining,
    **kwargs,
):
    """Payment reminder template"""
    subject = "Payment Reminder ⏰"
    message = (
        f"⏰ *Payment Reminder*\n\n"
        f"Hi {customer_name}! 👋\n\n"
        f"Your payment is still pending:\n\n"
        f"💰 Amount: *₹{amount:,.2f}*\n"
        f"🏢 Property: {property_name}\n"
        f"📋 Reference: {reference_number}\n"
        f"⏳ Expires in: {hours_remaining} hours\n\n"
        f"👆 Complete payment: {payment_url}\n\n"
        f"⚡ Quick & secure payment\n"
        f"🔒 Protected by Razorpay\n\n"
        f"_Powered by Nestafar_"
    )
    return subject, message


def transfer_success_template(
    recipient_name,
    amount,
    payment_reference,
    transfer_id,
    settlement_date,
    recipient_type,
    **kwargs,
):
    """Transfer success notification for partners/vendors"""
    recipient_title = "Partner" if recipient_type == 'partner' else "Vendor"

    subject = f"Payment Received - ₹{amount:,.2f}"
    message = (
        f"💰 *Payment Received!*\n\n"
        f"Hi {recipient_name}! 🎉\n\n"
        f"You've received a payment:\n\n"
        f"💵 Amount: *₹{amount:,.2f}*\n"
        f"📋 Reference: {payment_reference}\n"
        f"🆔 Transfer ID: {transfer_id}\n"
        f"📅 Settlement: {settlement_date}\n"
        f"👤 Type: {recipient_title} Payment\n\n"
        f"💳 Funds will be credited to your registered account\n"
        f"📊 View details in your dashboard\n\n"
        f"Thank you for being part of Nestafar! 🌟\n\n"
        f"_Powered by Nestafar_"
    )
    return subject, message


def transfer_failure_template(
    recipient_name,
    amount,
    payment_reference,
    error_message,
    support_contact,
    recipient_type,
    **kwargs,
):
    """Transfer failure notification for partners/vendors"""
    recipient_title = "Partner" if recipient_type == 'partner' else "Vendor"

    subject = f"Transfer Failed - ₹{amount:,.2f}"
    message = (
        f"⚠️ *Transfer Failed*\n\n"
        f"Hi {recipient_name},\n\n"
        f"Unfortunately, your payment transfer failed:\n\n"
        f"💵 Amount: ₹{amount:,.2f}\n"
        f"📋 Reference: {payment_reference}\n"
        f"👤 Type: {recipient_title} Payment\n"
        f"❗ Error: {error_message}\n\n"
        f"🔄 We're automatically retrying the transfer\n"
        f"📞 Contact support if issue persists: {support_contact}\n\n"
        f"We apologize for the inconvenience.\n\n"
        f"_Powered by Nestafar_"
    )
    return subject, message


# ============================================================================
# ACCOUNT MANAGEMENT TEMPLATES
# ============================================================================

def account_activated_template(
    partner_name,
    account_id,
    activation_date,
    bank_account_details,
    dashboard_url,
    support_contact,
    **kwargs,
):
    """Template for account activation notification"""
    subject = "🎉 Your Razorpay Account is Activated!"
    message = (
        f"🎉 *Account Activated!*\n\n"
        f"Hi {partner_name}! 👋\n\n"
        f"Your Razorpay account has been successfully activated. "
        f"You can now receive payments directly to your bank account.\n\n"
        f"✅ Account Status: *Active*\n"
        f"🆔 Account ID: {account_id}\n"
        f"📅 Activated: {activation_date}\n"
        f"💳 Settlement: {bank_account_details}\n"
        f"🔗 Dashboard: {dashboard_url}\n\n"
        f"🚀 You're now ready to:\n"
        f"• Accept online payments\n"
        f"• Receive automatic settlements\n"
        f"• Track all transactions\n\n"
        f"📞 Need help? Contact: {support_contact}\n\n"
        f"Thank you for choosing Nestafar! 🌟\n\n"
        f"_Powered by Nestafar_"
    )
    return subject, message


def account_suspended_template(
    partner_name,
    account_id,
    suspension_reason,
    suspension_date,
    appeal_process,
    support_contact,
    **kwargs,
):
    """Template for account suspension notification"""
    subject = "⚠️ Your Razorpay Account is Suspended"
    message = (
        f"⚠️ *Account Suspended*\n\n"
        f"Hi {partner_name},\n\n"
        f"Your Razorpay account has been suspended.\n\n"
        f"🆔 Account ID: {account_id}\n"
        f"📅 Suspended: {suspension_date}\n"
        f"📋 Reason: {suspension_reason}\n\n"
        f"⚡ *Impact:*\n"
        f"• Payment acceptance is paused\n"
        f"• Settlements are on hold\n"
        f"• Account access restricted\n\n"
        f"🔄 *Next Steps:*\n"
        f"• Review suspension reason\n"
        f"• {appeal_process}\n"
        f"• Provide required documentation\n\n"
        f"📞 Support: {support_contact}\n"
        f"📧 Email: <EMAIL>\n\n"
        f"We're here to help resolve this quickly.\n\n"
        f"_Powered by Nestafar_"
    )
    return subject, message


def account_under_review_template(
    partner_name,
    account_id,
    review_reason,
    estimated_duration,
    review_date,
    support_contact,
    **kwargs,
):
    """Template for account under review notification"""
    subject = "📋 Your Razorpay Account is Under Review"
    message = (
        f"📋 *Account Under Review*\n\n"
        f"Hi {partner_name}! 👋\n\n"
        f"Your Razorpay account is currently under review. "
        f"This is a standard process to ensure account security.\n\n"
        f"🆔 Account ID: {account_id}\n"
        f"📅 Review Started: {review_date}\n"
        f"📝 Reason: {review_reason}\n"
        f"⏱️ Estimated Duration: {estimated_duration}\n\n"
        f"🔍 *During Review:*\n"
        f"• Account remains functional\n"
        f"• Payments can be accepted\n"
        f"• Settlements may be delayed\n\n"
        f"📧 We'll notify you once the review is complete\n"
        f"📞 Questions? Contact: {support_contact}\n\n"
        f"Thank you for your patience! 🙏\n\n"
        f"_Powered by Nestafar_"
    )
    return subject, message


def kyc_verified_template(
    partner_name,
    document_type,
    verification_date,
    account_status,
    dashboard_url,
    next_steps,
    **kwargs,
):
    """Template for KYC verification success"""
    subject = "✅ KYC Verification Successful"
    message = (
        f"✅ *KYC Verified!*\n\n"
        f"Hi {partner_name}! 🎉\n\n"
        f"Your KYC document has been successfully verified.\n\n"
        f"📄 Document: {document_type}\n"
        f"📅 Verified: {verification_date}\n"
        f"✅ Status: *Approved*\n"
        f"🏦 Account: {account_status}\n\n"
        f"🚀 *You can now:*\n"
        f"• Accept payments without limits\n"
        f"• Receive instant settlements\n"
        f"• Access full account features\n\n"
        f"🔗 Manage Account: {dashboard_url}\n"
        f"📈 Next: {next_steps}\n\n"
        f"Welcome to the Nestafar partner network! 🌟\n\n"
        f"_Powered by Nestafar_"
    )
    return subject, message


def kyc_rejected_template(
    partner_name,
    document_type,
    rejection_reason,
    resubmission_url,
    support_contact,
    deadline,
    **kwargs,
):
    """Template for KYC verification rejection"""
    subject = "❌ KYC Document Rejected"
    message = (
        f"❌ *KYC Document Rejected*\n\n"
        f"Hi {partner_name},\n\n"
        f"Your KYC document verification was unsuccessful.\n\n"
        f"📄 Document: {document_type}\n"
        f"❗ Reason: {rejection_reason}\n"
        f"⏰ Action Required: Within {deadline}\n\n"
        f"🔄 *To Resolve:*\n"
        f"• Upload a clear, valid document\n"
        f"• Ensure all details are visible\n"
        f"• Match registered information\n\n"
        f"🔗 Resubmit: {resubmission_url}\n"
        f"📞 Help: {support_contact}\n\n"
        f"⚠️ Account limitations may apply until verification\n\n"
        f"_Powered by Nestafar_"
    )
    return subject, message


def kyc_under_review_template(
    partner_name,
    document_type,
    submission_date,
    estimated_duration,
    reference_id,
    support_contact,
    **kwargs,
):
    """Template for KYC under review notification"""
    subject = "🔍 KYC Document Under Review"
    message = (
        f"🔍 *KYC Under Review*\n\n"
        f"Hi {partner_name}! 👋\n\n"
        f"Your KYC document is being reviewed by our verification team.\n\n"
        f"📄 Document: {document_type}\n"
        f"📅 Submitted: {submission_date}\n"
        f"🆔 Reference: {reference_id}\n"
        f"⏱️ Review Time: {estimated_duration}\n\n"
        f"📋 *Review Process:*\n"
        f"• Document authenticity check\n"
        f"• Information verification\n"
        f"• Compliance validation\n\n"
        f"📧 We'll notify you once complete\n"
        f"📞 Questions? Contact: {support_contact}\n\n"
        f"Thank you for your patience! 🙏\n\n"
        f"_Powered by Nestafar_"
    )
    return subject, message
