from pytest_bdd import scenario, given, then, when
from transport.models import *
from transport.tests import transportest


@scenario("features/transport_service.feature")
def test_manage_transport_services(client):
    pass


@given("I exist as a user")
def authenticate_user(client):
    pass


@when("I retrieve the list of transport services")
def get_transport_services(client):
    response = client.get("/api/transport-services/")
    response.json()  # trigger data parsing


@then("Then the response status code is 200")
def check_get_services_status_code(response):
    assert response.status_code == 200


@then("And the response data contains a list of transport services with basic details")
def check_get_services_response(response):
    data = response.json()
    assert isinstance(data, list)
    # TO Assert presence of basic service details based on  serializer (e.g., name, type)


@given("a transport service exists with name '<service_name>'")
def get_transport_service_by_name(
    service_name, service_factory=transportest.create_transport_service
):
    service = service_factory(name=service_name)
    return service


@when("I retrieve the details of the transport service")
def retrieve_transport_service_details(client, service):
    response = client.get(f"/api/transport-services/{service.id}/")
    response.json()  # trigger data parsing


@then("Then the response status code is 200")
def check_get_service_details_status_code(response):
    assert response.status_code == 200


@then(
    "And the response data contains the transport service details (name, partner, type)"
)
def check_get_service_details_response(response, service):
    data = response.json()
    assert data["id"] == service.id
    assert data["name"] == service.name
    assert (
        data["service_partner"]["type_of_service"] == "Transport"
    )  # Assuming partner details are nested
    # To Assert presence of other service details based on serializer (e.g., vehicle type)


# Add steps for filtering transport services (optional)
@when(
    "I retrieve the list of transport services filtered by '<filter_field>' with value '<filter_value>'"
)
def get_filtered_transport_services(client, filter_field, filter_value):
    url = f"/api/transport-services/?{filter_field}={filter_value}"
    response = client.get(url)
    response.json()  # trigger data parsing


@then("And the response data contains a filtered list of transport services")
def check_filtered_services_response(response):
    data = response.json()
    assert isinstance(data, list)
    # To add assertions based on the applied filter
    # To assert all prices are less than the filter value
