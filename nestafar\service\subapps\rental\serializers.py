from .models import (
    RentalService,
    RentalServiceItem,
    RentalOrderItem,
    RentalCart,
    RentalCartItems,
    RentalOrder,
)
from rest_framework.serializers import (
    ModelSerializer,
    SerializerMethodField,
    ValidationError,
)

from ...models import ServicePartner


class RentalServiceSerializer(ModelSerializer):
    n_items = SerializerMethodField(required=False)

    def get_n_items(self, obj):
        return obj.service_items.count()

    class Meta:
        model = RentalService
        fields = "__all__"


class RentalServiceItemSerializer(ModelSerializer):
    period = SerializerMethodField()
    minimum_period = SerializerMethodField()
    maximum_period = SerializerMethodField()
    type_of_rental = SerializerMethodField()

    def get_period(self, obj):
        return obj.service.period

    def get_minimum_period(self, obj):
        return obj.service.min_period

    def get_maximum_period(self, obj):
        return obj.service.max_period

    def get_type_of_rental(self, obj):
        return obj.service.type_of_rental

    def validate(self, attrs):
        addon = attrs.get("addon")
        if addon:
            for addon_item in addon.keys():
                try:
                    addon_price = float(addon[addon_item])
                    addon[addon_item] = addon_price
                except ValueError:
                    raise ValidationError("Invalid price for addon")
        else:
            attrs["addon"] = None
        return attrs

    class Meta:
        model = RentalServiceItem
        fields = "__all__"


class RentalOrderItemCreateSerializer(ModelSerializer):
    class Meta:
        model = RentalOrderItem
        fields = "__all__"


class RentalCartItemSerializer(ModelSerializer):
    name = SerializerMethodField(required=False)

    def get_name(self, obj):
        return obj.name if obj.name else obj.item.name

    class Meta:
        model = RentalCartItems
        exclude = ["cart"]


class RentalCartItemListSerializer(ModelSerializer):
    name = SerializerMethodField()
    deposit = SerializerMethodField(required=False)
    period = SerializerMethodField(required=False)

    def get_deposit(self, obj):
        return obj.item.deposit

    def get_name(self, obj):
        return obj.item.name

    def get_period(self, obj):
        return obj.item.service.period

    class Meta:
        model = RentalCartItems
        fields = "__all__"


class RentalCartSerializer(ModelSerializer):
    cart_items = RentalCartItemListSerializer(many=True)

    class Meta:
        model = RentalCart
        fields = "__all__"


class ServicePartnerNameSerializer(ModelSerializer):
    class Meta:
        model = ServicePartner
        fields = ["id", "name", "type_of_service", "phone_number"]


class RentalServiceItemCardSerializer(ModelSerializer):
    class Meta:
        model = RentalServiceItem
        fields = ["name", "deposit", "description", "price", "addon"]


class RentalOrderItemCardSerializer(ModelSerializer):
    item = RentalServiceItemCardSerializer()

    class Meta:
        model = RentalOrderItem
        fields = [
            "item",
            "quantity",
            "add_ons",
            "price",
            "pickup_date",
            "drop_date",
            "pickup_location",
            "no_of_periods",
        ]


class RentalOrderSerializer(ModelSerializer):
    order_items = RentalOrderItemCardSerializer(many=True)
    guest = SerializerMethodField(required=False)
    service_partner = ServicePartnerNameSerializer()
    service = SerializerMethodField(required=False)

    def get_guest(self, obj):
        return dict(
            id=obj.guest.id,
            phone_no=obj.guest.user.phone.as_e164,
            room_no=obj.guest.room.room_no,
            name=obj.guest.user.name,
        )

    def get_service(self, obj):
        return {"period": obj.service.period, "type": obj.service.type_of_rental}

    class Meta:
        model = RentalOrder
        fields = "__all__"
