"""
Checkout Payment Service

Service for integrating payment processing into the guest checkout flow.
Handles payment link creation for service orders and bill settlement.
"""

import logging
from decimal import Decimal
from typing import Dict, List
from django.db import transaction
from django.utils import timezone

from ..models import PaymentIntent
from ..constants import PaymentContext
from .razorpay_service import RazorpayService
from .payment_split_service import PaymentSplitService
from .payment_notification_service import PaymentNotificationService
from ..exceptions import PaymentException
from service import service_factory
from service.models import BaseCart
import datetime

logger = logging.getLogger(__name__)


class CheckoutPaymentService:
    """
    Service for handling checkout payment processing.
    
    Integrates with the existing checkout flow to create payment links
    for guest bills including service orders and room charges.
    """
    
    def __init__(self):
        self.razorpay_service = RazorpayService()
        self.split_service = PaymentSplitService()
        self.notification_service = PaymentNotificationService()
        self.logger = logger
    
    def create_checkout_payment_link(self, guest, total_amount: Decimal, 
                                   description: str = None) -> Dict:
        """
        Create payment link for guest checkout.
        
        Args:
            guest: Guest instance
            total_amount: Total bill amount
            description: Payment description
            
        Returns:
            Dictionary with payment link details
        """
        try:
            with transaction.atomic():
                # Get partner from guest's room property (if present)
                if not guest.room or not guest.room.property:
                    raise PaymentException("Guest must have an associated room and property")
                partner = getattr(guest.room.property, 'partner_profile', None)
                if not partner:
                    raise PaymentException("Property must have an associated partner profile")
                
                # Create payment intent
                payment_intent = PaymentIntent.objects.create(
                    context=PaymentContext.CHECKOUT,
                    partner=partner,
                    guest=guest,
                    total_amount=total_amount,
                    expires_at=timezone.now() + timezone.timedelta(hours=24),
                    metadata={
                        'guest_id': str(guest.id),
                        'room_id': str(guest.room.id),
                        'property_id': str(guest.room.property.id),
                        'checkout_initiated_at': timezone.now().isoformat(),
                    }
                )
                
                # Calculate payment splits
                split_data = self.split_service.calculate_splits(payment_intent)
                payment_intent.split_details = split_data
                payment_intent.save()
                
                # Create split records
                splits = self.split_service.create_payment_splits(payment_intent, split_data)
                
                # Prepare customer data
                customer_data = {
                    'name': getattr(guest.user, 'name', 'Guest'),
                    'email': getattr(guest.user, 'email', ''),
                    'contact': ''
                }
                if hasattr(guest.user, 'phone') and guest.user.phone:
                    customer_data['contact'] = str(getattr(guest.user.phone, 'national_number', ''))
                
                # Create payment description
                if not description:
                    description = f"Checkout payment for {guest.room.property.name} - Room {guest.room.room_no}"
                
                # Create Razorpay payment link
                payment_link_data = self.razorpay_service.create_payment_link(
                    amount=total_amount,
                    description=description,
                    customer_details=customer_data,
                    expire_by=payment_intent.expires_at,
                    notes={
                        'payment_intent_id': str(payment_intent.id),
                        'context': PaymentContext.CHECKOUT,
                        'guest_id': str(guest.id),
                        'room_number': guest.room.room_no,
                        'property_name': guest.room.property.name,
                    }
                )
                
                # Update payment intent with Razorpay details
                payment_intent.razorpay_payment_link_id = payment_link_data['id']
                payment_intent.save()
                
                # Send payment link notifications
                notifications_sent = self._send_checkout_payment_notifications(
                    payment_intent, payment_link_data['short_url']
                )
                
                self.logger.info(f"Created checkout payment link for guest {guest.id}")
                
                return {
                    'payment_intent_id': payment_intent.id,
                    'reference_number': payment_intent.reference_number,
                    'payment_link_id': payment_link_data['id'],
                    'payment_link_url': payment_link_data['short_url'],
                    'total_amount': total_amount,
                    'expires_at': payment_intent.expires_at,
                    'notifications_sent': notifications_sent,
                    'split_preview': {
                        'platform_commission': payment_intent.platform_commission,
                        'partner_amount': payment_intent.partner_amount,
                        'vendor_amount': payment_intent.vendor_amount,
                    }
                }
                
        except Exception as e:
            self.logger.error(f"Failed to create checkout payment link: {str(e)}")
            raise PaymentException(f"Failed to create checkout payment link: {str(e)}")
    
    def calculate_guest_bill(self, guest) -> Dict:
        """
        Calculate total bill for guest including all service orders and room charges.
        Uses the same logic as the running_bill API to ensure consistency.
        
        Args:
            guest: Guest instance
            
        Returns:
            Dictionary with bill breakdown
        """
        try:
            
            # Get all guests in the same room (for consistency with running_bill logic)
            guests = guest.__class__.objects.filter(
                room=guest.room, 
                checked_in=True, 
                checked_out=False
            )
            
            bill_breakdown = {
                'room_charges': Decimal('0.00'),
                'stay_details': {},
                'service_orders': {},
                'service_breakdown': [],
                'total_service_amount': Decimal('0.00'),
                'taxes': Decimal('0.00'),
                'total_amount': Decimal('0.00'),
            }
            
            total_bill = Decimal('0.00')
            
            # Calculate room charges (stay details) - same logic as running_bill
            room = guest.room
            if room.rate and room.rate > 0 and guests.exists():
                earliest_checkin_dt = min(
                    guests.values_list("check_in_date", flat=True)
                )
                stay_duration = datetime.date.today() - earliest_checkin_dt.date()
                # Ensure at least 1 day is charged even for same-day checkout
                billable_days = max(1, stay_duration.days)
                stay_total = round(room.rate * billable_days, 2)
                
                bill_breakdown['stay_details'] = {
                    "checkin_date": earliest_checkin_dt.isoformat(),
                    "duration": billable_days,
                    "rate": room.rate,
                    "total": stay_total,
                }
                bill_breakdown['room_charges'] = Decimal(str(stay_total))
                total_bill += Decimal(str(stay_total))
            
            # Calculate service orders using service_factory - same logic as running_bill
            for service_name, service_type in service_factory.url_mappings.items():
                orders = (
                    service_factory.service_cart_model.get(service_type)
                    .objects.filter(
                        guest__in=guests, 
                        status=BaseCart.CartStatus.COMPLETED.value
                    )
                    .order_by("-created_at")
                )
                
                total_values = orders.values_list("total", flat=True)
                service_total = sum(val for val in total_values if val is not None)
                service_total_decimal = Decimal(str(round(service_total, 2)))                
                total_bill += service_total_decimal
                
                # Serialize orders using the same serializer as running_bill
                serialized_orders = service_factory.service_cart_serializer.get(
                    service_type
                )(orders, many=True).data
                
                bill_breakdown['service_orders'][service_name] = serialized_orders
                
                # Add to service breakdown for easier processing
                if service_total > 0:
                    bill_breakdown['service_breakdown'].append({
                        'service_type': service_name.title(),
                        'service_count': orders.count(),
                        'total_amount': service_total_decimal,
                        'orders': serialized_orders,
                    })
            
            bill_breakdown['total_service_amount'] = total_bill - bill_breakdown['room_charges']
            
            # Calculate taxes (if applicable)
            # This can be customized based on business rules
            tax_rate = Decimal('0.00')  # No tax for now
            taxes = total_bill * tax_rate / 100
            bill_breakdown['taxes'] = taxes
            
            # Calculate final total
            bill_breakdown['total_amount'] = total_bill + taxes
            
            # Update room running total to match what running_bill does
            room.__class__.objects.filter(pk=room.pk).update(
                running_total=float(total_bill)
            )
            
            return bill_breakdown
            
        except Exception as e:
            self.logger.error(f"Failed to calculate guest bill: {str(e)}")
            raise PaymentException(f"Failed to calculate guest bill: {str(e)}")
    
    def process_checkout_payment_completion(self, payment_intent):
        """
        Process checkout payment completion.
        
        Args:
            payment_intent: PaymentIntent instance
        """
        try:
            with transaction.atomic():
                # Update guest checkout status
                guest = payment_intent.guest
                guest = payment_intent.guest
                if guest and not guest.checked_out and not guest.payment_completed:
                    # Mark guest as ready for checkout
                    guest.payment_completed = True
                    guest.save()                
                # Send completion notifications
                self._send_checkout_completion_notifications(payment_intent)
                
                # Trigger any post-payment checkout processes
                self._trigger_post_payment_processes(payment_intent)
                
                self.logger.info(f"Processed checkout payment completion for {payment_intent.id}")
                
        except Exception as e:
            self.logger.error(f"Failed to process checkout completion: {str(e)}")
            # Don't raise exception as payment is already completed
    
    def _get_order_items(self, order) -> List[Dict]:
        """
        Get order items for bill breakdown.
        
        Args:
            order: Service order instance
            
        Returns:
            List of order items
        """
        try:
            items = []
            
            # This is a simplified implementation
            # Each service type might have different item structures
            if hasattr(order, 'items'):
                for item in order.items.all():
                    items.append({
                        'name': getattr(item, 'name', 'Item'),
                        'quantity': getattr(item, 'quantity', 1),
                        'price': getattr(item, 'price', 0),
                    })
            else:
                # Fallback for orders without detailed items
                items.append({
                    'name': f"{order.__class__.__name__.replace('Order', '')} Service",
                    'quantity': 1,
                    'price': order.total_amount,
                })
            
            return items
            
        except Exception as e:
            self.logger.error(f"Failed to get order items: {str(e)}")
            return []
    
    def _send_checkout_payment_notifications(self, payment_intent, payment_url: str) -> Dict:
        """
        Send checkout payment notifications.

        Args:
            payment_intent: PaymentIntent instance
            payment_url: Payment link URL

        Returns:
            Dictionary with notification results
        """
        try:
            # Send payment link notification using existing notification system
            result = self.notification_service.send_payment_link_notification(
                payment_intent, payment_url
            )

            return {
                'whatsapp': result
            }

        except Exception as e:
            self.logger.error(f"Failed to send checkout notifications: {str(e)}")
            return {'error': str(e)}
    
    def _send_checkout_completion_notifications(self, payment_intent):
        """
        Send checkout completion notifications.
        
        Args:
            payment_intent: PaymentIntent instance
        """
        try:
            # Send success notification to guest
            self.notification_service.send_payment_success_notification(payment_intent)
            
            # Send notification to partner about successful checkout
            # This can be implemented based on business requirements
            
        except Exception as e:
            self.logger.error(f"Failed to send completion notifications: {str(e)}")
    
    def _trigger_post_payment_processes(self, payment_intent):
        """
        Trigger post-payment processes.
        
        Args:
            payment_intent: PaymentIntent instance
        """
        try:
            # Clear room running total
            if payment_intent.guest and payment_intent.guest.room:
                room = payment_intent.guest.room
                room.running_total = 0
                room.save()
            
            # Mark service orders as paid
            self._mark_service_orders_as_paid(payment_intent.guest)
            
            # Any other post-payment processes can be added here
            
        except Exception as e:
            self.logger.error(f"Failed to trigger post-payment processes: {str(e)}")
    
    def _mark_service_orders_as_paid(self, guest):
        """
        Mark guest's service orders as paid.
        
        Args:
            guest: Guest instance
        """
        try:
            # Import service order models
            from service.subapps.food.models import FoodOrder
            from service.subapps.laundry.models import LaundryOrder
            from service.subapps.rental.models import RentalOrder
            from service.subapps.transport.models import TransportOrder
            from service.subapps.shop.models import ShopOrder
            from service.subapps.tourism.models import TourismOrder
            
            order_models = [
                FoodOrder, LaundryOrder, RentalOrder,
                TransportOrder, ShopOrder, TourismOrder
            ]
            
            for order_model in order_models:
                orders = order_model.objects.filter(
                    guest=guest,
                    status='completed',
                ).exclude(
                    payment_status='paid'
                )
                
                for order in orders:
                    order.payment_status = 'paid'
                    order.save()
            
        except Exception as e:
            self.logger.error(f"Failed to mark service orders as paid: {str(e)}")
