from django.urls import path, include
from rest_framework import routers
from .views.property import PropertyModelViewSet, StayAmenitiesView, RoomAmenitiesView
from .views.checkin import (
    InitiateCheckin,
    Checkin,
    CompleteCheckin,
    CheckoutView,
    RequestCheckin,
)
from .views.room import RoomModelViewSet
from .views.internal import PropertyDataVerification
from .views.guest import GuestHistoryView


router = routers.DefaultRouter()
router.register(r"property", PropertyModelViewSet, basename="property")
router.register(r"room", RoomModelViewSet, basename="room")

urlpatterns = [
    path("", include(router.urls)),
    path("initiate-checkin/", InitiateCheckin.as_view(), name="initiate-checkin"),
    path("checkin/", Checkin.as_view(), name="checkin"),
    path("complete-checkin/", CompleteCheckin.as_view(), name="complete-checkin"),
    path("checkout/", CheckoutView.as_view(), name="checkout"),
    path("request-checkin/", RequestCheckin.as_view(), name="request-checkin"),
    path(
        "internal/data-verification/<pk>/",
        PropertyDataVerification.as_view(),
        name="data-verification",
    ),
    path("guest/history/", GuestHistoryView.as_view(), name="guest-history"),
    path("amenities/", StayAmenitiesView.as_view(), name="stay-amenities"),
    path("amenities/room/", RoomAmenitiesView.as_view(), name="room-amenities"),
]
