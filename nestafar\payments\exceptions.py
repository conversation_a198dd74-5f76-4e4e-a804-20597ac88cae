"""
Payment Exceptions

Custom exceptions for payment processing and Razorpay integration.
"""


class PaymentException(Exception):
    """Base exception for payment-related errors"""

    def __init__(self, message, error_code=None, details=None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class RazorpayException(PaymentException):
    """Exception for Razorpay API errors"""

    def __init__(self, message, error_code=None, status_code=None, response_data=None):
        self.status_code = status_code
        self.response_data = response_data or {}
        super().__init__(message, error_code, self.response_data)


class PaymentSplitException(PaymentException):
    """Exception for payment split calculation errors"""

    pass


class WebhookException(PaymentException):
    """Exception for webhook processing errors"""

    pass


class PaymentValidationException(PaymentException):
    """Exception for payment validation errors"""

    pass


class PaymentProcessingException(PaymentException):
    """Exception for payment processing errors"""

    pass


class PaymentLinkException(PaymentException):
    """Exception for payment link creation/management errors"""

    pass


class TransferException(PaymentException):
    """Exception for transfer processing errors"""

    pass


class ConfigurationException(PaymentException):
    """Exception for payment configuration errors"""

    pass


class SignatureVerificationException(WebhookException):
    """Exception for webhook signature verification failures"""

    pass


class DuplicateWebhookException(WebhookException):
    """Exception for duplicate webhook processing attempts"""

    pass


class InsufficientBalanceException(TransferException):
    """Exception for insufficient balance errors"""

    pass


class AccountNotVerifiedException(TransferException):
    """Exception for unverified account errors"""

    pass
