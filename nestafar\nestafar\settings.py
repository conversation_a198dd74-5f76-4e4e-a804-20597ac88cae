import os
from pathlib import Path
from dotenv import load_dotenv
import os
import dj_database_url
from datetime import timedelta
from firebase_admin import initialize_app
import platform
import warnings

# Suppress pkg_resources deprecation warning from third-party packages
warnings.filterwarnings("ignore", message="pkg_resources is deprecated", category=UserWarning)

load_dotenv()
MODE = os.getenv("DJANGO_ENV", "local")
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Detect if we're running on Windows
IS_WINDOWS = platform.system().lower() == 'windows'

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-u*73boz$y)mm10cokvyzzw70#67^i@5i4^ek57!$5te@her8w7"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ["0.0.0.0", "*", "localhost"]
CSRF_TRUSTED_ORIGINS = [
    "https://*",
    "https://guestdev.nestafar.com",
    "https://partnerdev.nestafar.com",
    
]

CORS_ALLOWED_ORIGINS = [
    "https://guestdev.nestafar.com",
    "https://partnerdev.nestafar.com",
    "https://*.nestafar.com",
    "http://localhost:8080",
]
CORS_ALLOW_CREDENTIALS = True

CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]
# CORS_ORIGIN_ALLOW_ALL = True
# CORS_ALLOW_CREDENTIALS = False


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "phonenumber_field",
    "celery",
    "django_celery_beat",
    "django_celery_results",
    "django_extensions",
    # "fcm_django",
    "corsheaders",
    "service",
    "stay",
    "core",
    "service.subapps.food",
    "service.subapps.shop",
    "service.subapps.tourism",
    "service.subapps.transport",
    "service.subapps.rental",
    "service.subapps.laundry",
    "rest_framework_simplejwt",
    "rest_framework_simplejwt.token_blacklist",
    "geo",
    "notification",
    "booking",
    "pms",
    "payments",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "core.middlewares.jwt.JWTAuthenticationMiddleware",
    "core.middlewares.property.PropertyMiddleware",
    "nestafar.logger.DjangoRequestLogger",
]
if MODE != "dev":
    MIDDLEWARE.append("whitenoise.middleware.WhiteNoiseMiddleware")

ROOT_URLCONF = "nestafar.urls"

# Authentication settings

AUTHENTICATION_BACKENDS = [
    "core.auth.OtpAuthBackend",
]
LOGIN_URL = "/core/login/"

AUTH_USER_MODEL = "core.User"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "nestafar.wsgi.application"

# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases

if MODE == "test":
    DATABASES = {
        "default": {
            "ENGINE": "django_cockroachdb",
            "NAME": "backenddb",
            "HOST": "crdb",
            "PORT": "26257",
        },
    }
if MODE == "prod":
    DATABASES = {
        "default": dj_database_url.config(
            default=os.getenv("PROD_DATABASE_URL"), engine="django_cockroachdb"
        )
    }

if MODE == "dev":
    DATABASES = {
        "default": dj_database_url.config(
            default=os.getenv("DEV_DATABASE_URL"), engine="django_cockroachdb"
        )
    }

if MODE == "local":
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": BASE_DIR / "db.sqlite3",
        }
    }

if MODE != "dev":
    STORAGES = {
        "default": {
            "BACKEND": "storages.backends.s3.S3Storage",
        },
        "staticfiles": {
            "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
        },
    }
else:
    STORAGES = {
        "default": {
            "BACKEND": "storages.backends.s3.S3Storage",
        },
        "staticfiles": {
            "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
        },
    }
    # DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

# Comment this when not using local DB

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = "en-us"

CELERY_TIMEZONE = "Asia/Kolkata"
TIME_ZONE = "Asia/Kolkata"
CELERY_ENABLE_UTC = True

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.0/howto/static-files/
# if MODE != 'dev' :
#     STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"

STATIC_URL = "/static/"
STATIC_ROOT = "static"

# STATICFILES_DIRS = (os.path.join(BASE_DIR, "static"),)
# Default primary key field type
# https://docs.djangoproject.com/en/4.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.AutoField"


# AWS S3
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = os.getenv("AWS_STORAGE_BUCKET_NAME")
AWS_S3_SIGNATURE_NAME = ("s3v4",)
AWS_S3_REGION_NAME = os.getenv("AWS_S3_REGION_NAME")
AWS_S3_FILE_OVERWRITE = False
AWS_DEFAULT_ACL = None
AWS_S3_VERITY = True
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10mb file limit

# Phone number field settings
PHONENUMBER_DEFAULT_REGION = "IN"
PHONENUMBER_DB_FORMAT = "E164"
PHONENUMBER_DEFAULT_FORMAT = "E164"

# CELERY SETTINGS
REDIS_HOST = "redis"
if MODE == "dev" or MODE == "local":
    REDIS_HOST = "localhost"
if MODE == "prod":
    REDIS_HOST = os.getenv("REDIS_HOST")
if MODE == "test":
    REDIS_HOST = "redis"

CELERY_BROKER_URL = "redis://" + REDIS_HOST
result_backend = "redis://" + REDIS_HOST

# CELERY BEAT SCHEDULER

CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True


# REDIS CACHE

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://" + REDIS_HOST,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    }
}

# Google Maps settings
GOOGLE_MAPS_API_KEY = os.getenv("GOOGLE_MAPS_API_KEY")

# Fast 2 SMS Settings
FAST2SMS_API_KEY = os.getenv("FAST2SMS_API_KEY")
FAST2SMS_API_ENDPOINT = "https://www.fast2sms.com/dev/bulkV2"

from django.core.exceptions import ImproperlyConfigured

# WhatsApp Business API Settings
WHATSAPP_ACCESS_TOKEN = os.getenv("WHATSAPP_ACCESS_TOKEN")
WHATSAPP_PHONE_NUMBER_ID = os.getenv("WHATSAPP_PHONE_NUMBER_ID")
WHATSAPP_BUSINESS_ACCOUNT_ID = os.getenv("WHATSAPP_BUSINESS_ACCOUNT_ID")
WHATSAPP_WEBHOOK_VERIFY_TOKEN = os.getenv("WHATSAPP_WEBHOOK_VERIFY_TOKEN")
WHATSAPP_API_VERSION = os.getenv("WHATSAPP_API_VERSION", "v22.0")

# sanity-check mandatory env vars in production-like modes
if MODE in {"prod", "test"}:
    missing = [
        name
        for name, value in [
            ("WHATSAPP_ACCESS_TOKEN", WHATSAPP_ACCESS_TOKEN),
            ("WHATSAPP_PHONE_NUMBER_ID", WHATSAPP_PHONE_NUMBER_ID),
            ("WHATSAPP_BUSINESS_ACCOUNT_ID", WHATSAPP_BUSINESS_ACCOUNT_ID),
            ("WHATSAPP_WEBHOOK_VERIFY_TOKEN", WHATSAPP_WEBHOOK_VERIFY_TOKEN),
        ]
        if not value
    ]
    if missing:
        raise ImproperlyConfigured(
            f"Missing required WhatsApp Business API environment variables: {', '.join(missing)}"
        )

# trailing slash keeps `urljoin` and manual f-strings predictable
WHATSAPP_API_BASE_URL = f"https://graph.facebook.com/{WHATSAPP_API_VERSION}/"

# Rest Framework settings : https://www.django-rest-framework.org/api-guide/settings/

REST_FRAMEWORK = {
    # Use Django's standard `django.contrib.auth` permissions,
    # or allow read-only access for unauthenticated users.
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.DjangoModelPermissionsOrAnonReadOnly"
    ],
    "DEFAULT_FILTER_BACKENDS": [
        "django_filters.rest_framework.DjangoFilterBackend",
        "rest_framework.filters.SearchFilter",
        "rest_framework.filters.OrderingFilter",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ),
}

# JWT Settings
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=7),  # 1 week
    "REFRESH_TOKEN_LIFETIME": timedelta(days=14),  # 2 week
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
}
# LOGGING_FILES_DIR= os.path.join(BASE_DIR, "logs")
LOGGING_FILES_DIR = "logs"
# Ensure logs directory exists for file handlers in all contexts (Django, Celery)
LOGGING_DIR = os.path.join(BASE_DIR, LOGGING_FILES_DIR)
os.makedirs(LOGGING_DIR, exist_ok=True)

# ---------------------------------------------------------------------------
# Logging Configuration
# ---------------------------------------------------------------------------
# Goal:
#  * In development (DEBUG=True) show ALL log levels (DEBUG and above) on the
#    Django runserver console and Celery worker terminal.
#  * Persist structured JSON logs to rotating files (metric/audit/error) and a
#    timed rotating file for celery-specific logs.
#  * Avoid duplicate handlers when modules are reloaded (e.g. Celery child
#    processes, autoreload) by using the central Django LOGGING dict instead of
#    ad‑hoc handler attachment in application code.
#  * Keep existing file names so downstream tooling is unaffected.

LOG_LEVEL = "DEBUG" if DEBUG else "INFO"

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,  # let Django/Celery base loggers coexist
    "formatters": {
        # Human friendly formatter for interactive dev terminals
        "console": {
            "format": "[%(asctime)s] %(levelname)s %(name)s:%(lineno)d | %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        # JSON formatter for structured log ingestion
        "json": {
            "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
            # Keep it concise; message field will contain dicts we pass
            "fmt": "%(asctime)s %(levelname)s %(name)s %(message)s",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": LOG_LEVEL,
            "formatter": "console",
            "filters": ["project_only"],
        },
        "metric_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "INFO",
            "filename": os.path.join(BASE_DIR, LOGGING_FILES_DIR, "metric.log"),
            "maxBytes": 10 * 1024 * 1024,
            "backupCount": 5,
            "formatter": "json",
        },
        "audit_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "INFO",
            "filename": os.path.join(BASE_DIR, LOGGING_FILES_DIR, "audit.log"),
            "maxBytes": 10 * 1024 * 1024,
            "backupCount": 5,
            "formatter": "json",
        },
        "error_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "ERROR",
            "filename": os.path.join(BASE_DIR, LOGGING_FILES_DIR, "error.log"),
            "maxBytes": 10 * 1024 * 1024,
            "backupCount": 5,
            "formatter": "json",
        },
        "celery_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "maxBytes": 10 * 1024 * 1024,  # 10MB per file
            "backupCount": 7,
            "level": LOG_LEVEL,
            "filename": os.path.join(BASE_DIR, LOGGING_FILES_DIR, "celery.log"),
            "formatter": "json",
        },
    },
    "loggers": {
        # Root: DEBUG so project modules (e.g. core.views) inherit full verbosity;
        # console noise still curtailed by the project_only filter.
        "": {
            "handlers": ["console"],
            "level": LOG_LEVEL,
        },
        # Silence Django's own info/debug unless explicitly raised.
        "django": {
            "handlers": ["console"],
            "level": "WARNING",
            "propagate": False,
        },
        # Our structured domain loggers
        "metrics": {
            "handlers": ["metric_file", "console"],
            "level": "INFO",
            "propagate": False,
        },
        "audit": {
            "handlers": ["audit_file", "console"],
            "level": "INFO",
            "propagate": False,
        },
        "error": {
            "handlers": ["error_file", "console"],
            "level": "ERROR",
            "propagate": False,
        },
        # Celery library + our task modules typically use logger = get_task_logger(__name__)
        # Those propagate to root; we also provide a dedicated celery logger for
        # any explicit 'celery' named logs with file persistence.
        "celery": {
            "handlers": ["celery_file", "console"],
            "level": "INFO",
            "propagate": False,
        },
        # Celery task logger (rarely used directly); also file-only
        "celery.task": {
            "handlers": ["celery_file"],
            "level": "INFO",
            "propagate": False,
        },
    },
    "filters": {
        "project_only": {
            "()": "nestafar.logger.ProjectConsoleFilter",
            # Optional: configure via env PROJECT_LOG_PREFIXES, fallback inside class
        }
    },
}

try:
    from .local_settings import *
except ImportError:
    pass

CDN_HOST = os.getenv("CDN_DOMAIN")

if CDN_HOST:
    AWS_S3_CUSTOM_DOMAIN = CDN_HOST


# Optional ONLY IF you have initialized a firebase app already:
# Visit https://firebase.google.com/docs/admin/setup/#python
# for more options for the following:
# Store an environment variable called GOOGLE_APPLICATION_CREDENTIALS
# which is a path that point to a json file with your credentials.
# Additional arguments are available: credentials, options, name
FIREBASE_APP = initialize_app()
# To learn more, visit the docs here:
# https://cloud.google.com/docs/authentication/getting-started>

FCM_DJANGO_SETTINGS = {
    # an instance of firebase_admin.App to be used as default for all fcm-django requests
    # default: None (the default Firebase app)
    # an instance of firebase_admin.App to be used as default for all fcm-django requests
    # default: None (the default Firebase app)
    "DEFAULT_FIREBASE_APP": "Nestafar",
    # default: _('FCM Django')
    # default: _('FCM Django')
    "APP_VERBOSE_NAME": "_(FCM Nestafar)",
    # true if you want to have only one active device per registered user at a time
    # default: False
    # true if you want to have only one active device per registered user at a time
    # default: False
    "ONE_DEVICE_PER_USER": True,
    # devices to which notifications cannot be sent,
    # are deleted upon receiving error response from FCM
    # default: False
    # devices to which notifications cannot be sent,
    # are deleted upon receiving error response from FCM
    # default: False
    "DELETE_INACTIVE_DEVICES": True,
}

# Email Configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.getenv('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.getenv('EMAIL_PORT', '587'))
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD')
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '<EMAIL>')


# Core API config (used by pms.services.aiosell)
AIOSELL_SETTINGS = {
    "API_BASE_URL": "https://live.aiosell.com/api/v2/cm",
    "TIMEOUT": 30,  # seconds
}

# Celery task routing (dedicated aiosell queue)
AIOSELL_TASK_ROUTES = {
    "pms.tasks.aiosell_tasks.sync_calendar_to_aiosell": {"queue": "aiosell"},
    "pms.tasks.aiosell_tasks.sync_room_block_to_aiosell": {"queue": "aiosell"},
    "pms.tasks.aiosell_tasks.sync_room_type_to_aiosell": {"queue": "aiosell"},
    "pms.tasks.aiosell_tasks.sync_rate_plan_to_aiosell": {"queue": "aiosell"},
    "pms.tasks.aiosell_tasks.sync_hotel_initial_data_to_aiosell": {"queue": "aiosell"},
    "pms.tasks.aiosell_tasks.bulk_sync_calendar_entries_to_aiosell": {
        "queue": "aiosell"
    },
    "pms.tasks.aiosell_tasks.retry_failed_aiosell_syncs": {"queue": "aiosell"},
    "pms.tasks.aiosell_tasks.cleanup_old_sync_logs": {"queue": "aiosell"},
    "pms.tasks.aiosell_tasks.sync_availability_to_aiosell": {"queue": "aiosell"},
}

# Expose to Celery (merge-friendly if extended later)
CELERY_TASK_ROUTES = {**AIOSELL_TASK_ROUTES}

# Periodic tasks (seconds-based schedule); extend if needed
CELERY_BEAT_SCHEDULE = {
    "aiosell-retry-failed-syncs": {
        "task": "pms.tasks.aiosell_tasks.retry_failed_aiosell_syncs",
        "schedule": 2 * 60 * 60,  # every 2 hours
    },
    "aiosell-cleanup-old-sync-logs": {
        "task": "pms.tasks.aiosell_tasks.cleanup_old_sync_logs",
        "schedule": 24 * 60 * 60,  # daily
    },
}
# RAZORPAY PAYMENT GATEWAY CONFIGURATION

# Razorpay API Configuration
RAZORPAY_SETTINGS = {
    # API Credentials (from environment variables)
    "KEY_ID": os.getenv("RAZORPAY_KEY_ID"),
    "KEY_SECRET": os.getenv("RAZORPAY_KEY_SECRET"),
    # Environment Configuration
    "ENVIRONMENT": os.getenv("RAZORPAY_ENVIRONMENT", "test"),  # 'test' or 'live'
    # API URLs
    "BASE_URL": {
        "test": "https://api.razorpay.com/v1",
        "live": "https://api.razorpay.com/v1",
    },
    # Webhook Configuration
    "WEBHOOK_SECRET": os.getenv("RAZORPAY_WEBHOOK_SECRET"),
    "WEBHOOK_URL": os.getenv("RAZORPAY_WEBHOOK_URL", "/payments/webhooks/razorpay/"),
    # Payment Link Configuration
    "PAYMENT_LINK_SETTINGS": {
        "expire_by_hours": 24,  # Payment links expire after 24 hours
        "send_sms": True,
        "send_email": False,
        "reminder_enable": True,
    },
    # Route (Split Payments) Configuration
    "ROUTE_SETTINGS": {
        "default_currency": "INR",
        "auto_capture": True,
        "settlement_schedule": "immediate",  # 'immediate' or 'scheduled'
    },
    # Default Commission Configuration
    "COMMISSION_SETTINGS": {
        "default_platform_rate": 5.00,  # 5% default platform commission rate
        "min_commission_rate": 0.00,
        "max_commission_rate": 100.00,
    },
    # API Timeouts and Retry Configuration
    "API_SETTINGS": {
        "timeout": 30,  # seconds
        "max_retries": 3,
        "retry_delay": 5,  # seconds
    },
    # Webhook Processing Configuration
    "WEBHOOK_SETTINGS": {
        "max_retry_attempts": 3,
        "retry_delay_seconds": 5,
        "processing_timeout": 30,
        "signature_verification": True,
    },
    # Amount Limits (in rupees)
    "AMOUNT_LIMITS": {
        "min_payment_amount": 1.00,  # ₹1.00
        "max_payment_amount": 100000.00,  # ₹1,00,000
    },
    # Logging Configuration
    "LOGGING": {
        "log_api_requests": True,
        "log_webhook_events": True,
        "log_sensitive_data": False,  # Never log sensitive data in any environment    
        "log_payment_links": False,  # Never log payment links in any environment    
        "log_payment_intents": False,  # Never log payment intents in any environment    
        "log_payment_transfers": False,  # Never log payment transfers in any environment    
    },    
}

# Sanity-check Razorpay credentials in production-like modes
if MODE in {"prod", "test"}:
    missing = [
        name
        for name, value in [
            ("RAZORPAY_KEY_ID", RAZORPAY_SETTINGS.get("KEY_ID")),
            ("RAZORPAY_KEY_SECRET", RAZORPAY_SETTINGS.get("KEY_SECRET")),
        ]
        if not value
    ]
    if missing:
        raise ImproperlyConfigured(
            f"Missing required Razorpay configuration for mode '{MODE}': {', '.join(missing)}. "
            "Set RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET environment variables or configure RAZORPAY_SETTINGS."
        )

# Razorpay Route Task Configuration for Celery
RAZORPAY_TASK_ROUTES = {
    "payments.tasks.process_payment_webhook": {"queue": "payments"},
    "payments.tasks.create_payment_transfers": {"queue": "payments"},
    "payments.tasks.retry_failed_transfers": {"queue": "payments"},
    "payments.tasks.send_payment_notifications": {"queue": "notifications"},
    "payments.tasks.reconcile_payments": {"queue": "payments"},
}

# Add Razorpay tasks to Celery routes
CELERY_TASK_ROUTES.update(RAZORPAY_TASK_ROUTES)

# Add Razorpay periodic tasks
RAZORPAY_BEAT_SCHEDULE = {
    "retry-failed-payment-transfers": {
        "task": "payments.tasks.retry_failed_transfers",
        "schedule": 30 * 60,  # every 30 minutes
    },
    "reconcile-payments-daily": {
        "task": "payments.tasks.reconcile_payments",
        "schedule": 24 * 60 * 60,  # daily
    },
    "cleanup-old-webhook-events": {
        "task": "payments.tasks.cleanup_old_webhook_events",
        "schedule": 7 * 24 * 60 * 60,  # weekly
    },
}

# Add Razorpay tasks to Celery beat schedule
CELERY_BEAT_SCHEDULE.update(RAZORPAY_BEAT_SCHEDULE)

# Payment Gateway Selection (for future multi-gateway support)
DEFAULT_PAYMENT_GATEWAY = "razorpay"

# Security Configuration for Payments
# NOTE: Webhook processing relies on X-Razorpay-Signature verification as primary security.
# IP allowlisting is an additional layer but should not be the sole security mechanism.
PAYMENT_SECURITY_SETTINGS = {
    "require_https_webhooks": not DEBUG,  # Require HTTPS for webhooks in production, not required in development mode
    "webhook_ip_whitelist": [
        # Official Razorpay webhook IP addresses
        # Source: https://razorpay.com/docs/security/whitelists/
        # last_verified: 2025-09-16
        "***********",
        "**********", 
        "*************",
    ],
    "max_webhook_age_seconds": int(os.environ.get('RAZORPAY_WEBHOOK_MAX_AGE', 600)),  # 10 minutes default, configurable via RAZORPAY_WEBHOOK_MAX_AGE env var
}
