from typing import Optional, TYPE_CHECKING
from booking.models import PreCheckin, PreCheckinGuest
from pms.models import RoomBlock
from django.db import transaction
from decimal import Decimal, InvalidOperation
from datetime import timedelta
import logging
from notification.tasks import send_notification
from booking.tasks import distribute_availability_after_booking

if TYPE_CHECKING:
    from booking.models import Reservation
logger = logging.getLogger(__name__)


def determine_payment_status(paid: float, total: float) -> str:
    """Return normalized payment status based on amounts.

    Rules:
      - unpaid: paid <= 0
      - completed: total > 0 and paid >= total (overpayment counts as completed)
      - partial: otherwise
    """
    try:
        # Keep amounts non-negative and use Decimal for money-safe comparisons
        paid_val = max(
            Decimal("0"),
            (paid if isinstance(paid, Decimal) else Decimal(str(paid or 0))),
        )
        total_val = max(
            Decimal("0"),
            (total if isinstance(total, Decimal) else Decimal(str(total or 0))),
        )
    except (InvalidOperation, TypeError, ValueError):
        return "unpaid"

    if paid_val <= 0:
        return "unpaid"
    if total_val > 0 and paid_val >= total_val:
        return "completed"
    return "partial"


def trigger_precheckin_and_block(reservation: "Reservation") -> Optional[PreCheckin]:
    """
    Create a PreCheckin and a temporary RoomBlock for a new reservation.
    Also enqueue availability distribution.
    Idempotent: if a PreCheckin already exists for this reservation, returns it.
    """
    # Idempotency: return existing PreCheckin if it already exists
    existing_precheckin = reservation.precheckins.first()
    if existing_precheckin:
        return existing_precheckin
    try:
        with transaction.atomic():
            if not reservation.check_in or not reservation.check_out:
                raise ValueError(
                    f"Invalid check-in/check-out dates for reservation {reservation.id}"
                )

            if reservation.check_out < reservation.check_in:
                logger.warning(
                    f"Check-out before check-in for reservation {reservation.id}, using 1 day"
                )
                stay_days = 1
            else:
                # For same-day bookings (check_out == check_in), treat as 1 day minimum
                stay_days = max(1, (reservation.check_out - reservation.check_in).days)

            # Effective end date as original check_in + stay_days
            effective_end_date = reservation.check_in + timedelta(days=stay_days)

            number_of_rooms = 1
            if reservation.room_details:
                try:
                    number_of_rooms = max(1, len(reservation.room_details))
                except Exception as e:
                    logger.error(
                        f"Failed to determine number of rooms for reservation {reservation.id}: {e}"
                    )

            # Normalize monetary values to Decimal
            try:
                total_dec = Decimal(str(reservation.total or 0))
            except (InvalidOperation, TypeError, ValueError):
                total_dec = Decimal("0")

            try:
                paid_dec = Decimal(str(reservation.paid or 0))
            except (InvalidOperation, TypeError, ValueError):
                paid_dec = Decimal("0")

            pc = PreCheckin.objects.create(
                property=reservation.property,
                reservation=reservation,
                number_of_rooms=number_of_rooms,
                stay_duration=stay_days,
                expected_checkin=reservation.check_in,
                total_amount=float(total_dec),
                amount_paid=float(paid_dec),
                pending_balance=float(max(Decimal("0"), total_dec - paid_dec)),
                payment_status=determine_payment_status(paid_dec, total_dec),
                status="pending",
            )

            # Create PreCheckinGuest object for the reservation user
            try:
                PreCheckinGuest.objects.create(
                    pre_checkin=pc,
                    user=reservation.user,
                    is_primary=True,
                    age=18,  # Default age, can be updated later
                )
                logger.info(f"Created PreCheckinGuest for user {reservation.user.id} and precheckin {pc.id}")
            except Exception as guest_exc:
                logger.error(f"Failed to create PreCheckinGuest for reservation {reservation.id}: {guest_exc}")

            # Create a reservation RoomBlock (idempotent & concurrency-safe)
            # Align with RoomBlock model fields: hotel, room, reservation, blocked_from/blocked_until, reason
            try:
                # Quick existence check (covered by unique_together constraint added in migration)
                if not RoomBlock.objects.filter(
                    hotel=reservation.property,
                    reservation=reservation,
                    reason="reservation",
                ).exists():
                    # Pick a representative room if available (first room of property) to anchor the block
                    representative_room = getattr(
                        reservation.property, "property_rooms", None
                    )
                    representative_room = (
                        representative_room.first() if representative_room else None
                    )
                    if representative_room is None:
                        logger.warning(
                            f"No rooms found to attach RoomBlock for reservation {reservation.id}; skipping block creation"
                        )
                    else:
                        RoomBlock.objects.create(
                            hotel=reservation.property,
                            room=representative_room,
                            reservation=reservation,
                            blocked_from=reservation.check_in,
                            blocked_until=effective_end_date,
                            reason="reservation",
                        )
            except Exception as rb_exc:
                logger.error(
                    f"RoomBlock creation failed for reservation {reservation.id}: {rb_exc}"
                )

    except Exception as e:
        logger.error(
            f"Failed to create PreCheckin or RoomBlock for reservation {reservation.id}: {e}"
        )
        raise

    # Enqueue availability distribution after commit
    try:
        transaction.on_commit(
            lambda: distribute_availability_after_booking.delay(str(reservation.id))  # type: ignore
        )
    except Exception:
        logger.error(
            f"Failed to register availability distribution hook for reservation {reservation.id}"
        )

    # Send notification to guest to complete pre-checkin
    try:
        user = reservation.user
        if user and user.id:
            send_notification.delay(
                user_id=str(user.id),
                event="PRECHECKIN_CREATED",
                data={
                    "guest_name": getattr(user, "name", "Guest"),
                    "property_owner_name": getattr(
                        reservation.property, "name", "Property"
                    ),
                    "expected_date": reservation.check_in.strftime("%d %b %Y"),
                    "room_number": "N/A",
                    "precheckin_link": "https://guest.nestafar.com",
                },
            )
    except Exception as e:
        logger.error(
            f"Failed to send notification for reservation {reservation.id}: {e}"
        )
        pass
    return pc
