"""
Razorpay Helper Functions

Utility functions for Razorpay-specific operations and data formatting.
"""

import hashlib
import hmac
import logging
from decimal import Decimal
from typing import Dict, Optional
import datetime as _datetime
from django.utils import timezone

logger = logging.getLogger(__name__)


def verify_webhook_signature(payload: str, signature: str, secret: str) -> bool:
    """
    Verify Razorpay webhook signature.

    Args:
        payload: Raw webhook payload string
        signature: Signature from X-Razorpay-Signature header
        secret: Webhook secret from Razorpay dashboard

    Returns:
        True if signature is valid, False otherwise
    """
    try:
        expected_signature = hmac.new(
            secret.encode("utf-8"), payload.encode("utf-8"), hashlib.sha256
        ).hexdigest()

        return hmac.compare_digest(expected_signature, signature)
    except Exception as e:
        logger.error(f"Signature verification error: {str(e)}")
        return False


def format_razorpay_amount(amount: Decimal) -> int:
    """
    Convert amount from rupees to paise for Razorpay API.

    Args:
        amount: Amount in rupees (Decimal)

    Returns:
        Amount in paise (int)
    """
    return int(amount * 100)


def format_amount_from_razorpay(amount: int) -> Decimal:
    """
    Convert amount from paise to rupees from Razorpay API.

    Args:
        amount: Amount in paise (int)

    Returns:
        Amount in rupees (Decimal)
    """
    return Decimal(amount) / 100


def parse_razorpay_response(response_data: Dict) -> Dict:
    """
    Parse and normalize Razorpay API response.

    Args:
        response_data: Raw response from Razorpay API

    Returns:
        Normalized response data
    """
    if not isinstance(response_data, dict):
        return {"error": "Invalid response format"}

    # Extract common fields
    parsed = {
        "id": response_data.get("id"),
        "entity": response_data.get("entity"),
        "status": response_data.get("status"),
        "created_at": response_data.get("created_at"),
        "updated_at": response_data.get("updated_at"),
    }

    # Handle amount fields
    if "amount" in response_data:
        try:
            raw_amount = response_data["amount"]
            amount_paise = int(raw_amount)
            if amount_paise < 0:
                msg = f"Negative amount in response: {amount_paise}"
                logger.error(msg + f" | raw_amount={raw_amount}")
                parsed["amount_error"] = msg
            else:
                parsed["amount_paise"] = amount_paise
                parsed["amount_rupees"] = format_amount_from_razorpay(amount_paise)
        except (ValueError, TypeError) as e:
            raw_amount = response_data.get('amount')
            logger.error(f"Invalid amount in response: {raw_amount} | error: {e}")
            parsed["amount_error"] = f"Invalid amount: {e}"

    # Include all original data
    parsed["raw_data"] = response_data

    return parsed


def extract_customer_info(payment_data: Dict) -> Dict:
    """
    Extract customer information from Razorpay payment data.

    Args:
        payment_data: Payment data from Razorpay

    Returns:
        Customer information dictionary
    """
    customer_info = {}

    # Extract from payment data
    if "email" in payment_data:
        customer_info["email"] = payment_data["email"]
    if "contact" in payment_data:
        customer_info["contact"] = payment_data["contact"]

    # Extract from notes
    notes = payment_data.get("notes", {})
    if "customer_name" in notes:
        customer_info["name"] = notes["customer_name"]
    if "customer_email" in notes:
        customer_info["email"] = notes["customer_email"]
    if "customer_phone" in notes:
        customer_info["contact"] = notes["customer_phone"]

    return customer_info


def build_payment_link_payload(
    amount: Decimal, description: str, customer: Dict, **kwargs
) -> Dict:
    """
    Build payment link creation payload for Razorpay API.

    Args:
        amount: Payment amount in rupees
        description: Payment description
        customer: Customer details
        **kwargs: Additional parameters

    Returns:
        Payment link payload
    """
    payload = {
        "amount": format_razorpay_amount(amount),
        "currency": "INR",
        "description": description,
        "customer": {
            "name": customer.get("name", ""),
            "email": customer.get("email", ""),
            "contact": customer.get("contact", ""),
        },
    }

    # Add optional fields
    if "callback_url" in kwargs:
        payload["callback_url"] = kwargs["callback_url"]

    if "expire_by" in kwargs:
        expire_by = kwargs.get("expire_by")
        # Allow None to skip setting expire_by
        if isinstance(expire_by, _datetime.datetime):
            # If naive datetime, treat it as UTC
            dt = expire_by
            if dt.tzinfo is None:
                try:
                    # python's datetime.timezone is available
                    dt = dt.replace(tzinfo=_datetime.timezone.utc)
                except Exception:
                    # fallback: assume naive is UTC
                    dt = dt
            payload["expire_by"] = int(dt.timestamp())
        elif isinstance(expire_by, (int, float)):
            # Accept both seconds and milliseconds epoch. If large value, assume milliseconds
            ts = float(expire_by)
            # Millisecond epoch detection heuristics: > 1e11 (~year 5138) or >1e12
            if abs(ts) > 1e11:
                ts = ts / 1000.0
            payload["expire_by"] = int(ts)
        else:
            raise TypeError(
                f"expire_by must be datetime, int, float or None, got {type(expire_by)}"
            )

    if "notes" in kwargs:
        payload["notes"] = kwargs["notes"]

    # Notification settings
    payload["notify"] = {
        "sms": kwargs.get("send_sms", True),
        "email": kwargs.get("send_email", False),
    }

    payload["reminder_enable"] = kwargs.get("reminder_enable", True)

    return payload


def build_transfer_payload(
    account_id: str, amount: Decimal, currency: str = "INR", **kwargs
) -> Dict:
    """
    Build transfer payload for Razorpay Route API.

    Args:
        account_id: Razorpay linked account ID
        amount: Transfer amount in rupees
        currency: Currency code
        **kwargs: Additional parameters

    Returns:
        Transfer payload
    """
    payload = {
        "account": account_id,
        "amount": format_razorpay_amount(amount),
        "currency": currency,
    }

    # Add optional fields
    if "notes" in kwargs:
        payload["notes"] = kwargs["notes"]

    if "linked_account_notes" in kwargs:
        payload["linked_account_notes"] = kwargs["linked_account_notes"]

    if "on_hold" in kwargs:
        payload["on_hold"] = kwargs["on_hold"]

    if "on_hold_until" in kwargs:
        on_hold_until = kwargs["on_hold_until"]
        # Accept datetime-like objects or numeric timestamps (seconds or milliseconds)
        if hasattr(on_hold_until, "timestamp"):
            dt = on_hold_until
            # If it's a datetime, treat naive datetimes as UTC
            if isinstance(dt, _datetime.datetime) and dt.tzinfo is None:
                try:
                    dt = dt.replace(tzinfo=_datetime.timezone.utc)
                except Exception:
                    # fallback: leave as-is
                    dt = dt
            try:
                payload["on_hold_until"] = int(dt.timestamp())
            except Exception as e:
                logger.error(
                    "Failed to derive timestamp from on_hold_until=%r (type=%s): %s",
                    on_hold_until,
                    type(on_hold_until),
                    e,
                )
                raise ValueError("on_hold_until must be a datetime object") from e
        elif isinstance(on_hold_until, (int, float)):
            ts = float(on_hold_until)
            if abs(ts) > 1e11:
                ts = ts / 1000.0
            payload["on_hold_until"] = int(ts)
        else:
            logger.error(
                "on_hold_until must be datetime or numeric timestamp, got %r (type=%s)",
                on_hold_until,
                type(on_hold_until),
            )
            raise ValueError("on_hold_until must be a datetime object or int/float timestamp")
    return payload


def extract_error_details(error_response: Dict) -> Dict:
    """
    Extract error details from Razorpay error response.

    Args:
        error_response: Error response from Razorpay API

    Returns:
        Structured error details
    """
    error_details = {
        "code": None,
        "description": None,
        "field": None,
        "reason": None,
        "step": None,
        "source": None,
    }

    if "error" in error_response:
        error = error_response["error"]
        error_details.update(
            {
                "code": error.get("code"),
                "description": error.get("description"),
                "field": error.get("field"),
                "reason": error.get("reason"),
                "step": error.get("step"),
                "source": error.get("source"),
            }
        )

    return error_details


def is_webhook_event_supported(event_type: str) -> bool:
    """
    Check if webhook event type is supported.

    Args:
        event_type: Webhook event type

    Returns:
        True if event is supported
    """
    from ..constants import RazorpayWebhookEvents

    return event_type in RazorpayWebhookEvents.SUPPORTED_EVENTS


def get_webhook_entity_type(webhook_data: Dict) -> Optional[str]:
    """
    Extract entity type from webhook data.

    Args:
        webhook_data: Webhook payload

    Returns:
        Entity type (payment, transfer, etc.) or None
    """
    payload = webhook_data.get("payload", {})
    if not payload:
        return None

    # Get the first key in payload (should be entity type)
    entity_types = list(payload.keys())
    return entity_types[0] if entity_types else None


def get_webhook_entity_id(webhook_data: Dict) -> Optional[str]:
    """
    Extract entity ID from webhook data.

    Args:
        webhook_data: Webhook payload

    Returns:
        Entity ID or None
    """
    entity_type = get_webhook_entity_type(webhook_data)
    if not entity_type:
        return None

    entity_data = webhook_data.get("payload", {}).get(entity_type, {})
    return entity_data.get("id")


def format_webhook_notes(payment_intent_id: str, context: str, **kwargs) -> Dict:
    """
    Format notes for webhook tracking.

    Args:
        payment_intent_id: Payment intent UUID
        context: Payment context
        **kwargs: Additional note fields

    Returns:
        Formatted notes dictionary
    """
    notes = {
        "payment_intent_id": str(payment_intent_id),
        "context": context,
        "platform": "nestafar",
        "created_at": timezone.now().isoformat(),
    }

    notes.update(kwargs)
    return notes


def sanitize_customer_data(customer_data: Dict) -> Dict:
    """
    Sanitize customer data for Razorpay API.

    Args:
        customer_data: Raw customer data

    Returns:
        Sanitized customer data
    """
    sanitized = {}

    # Name (required)
    name = customer_data.get("name", "").strip()
    if name:
        sanitized["name"] = name[:100]  # Razorpay limit

    # Email (optional)
    email = customer_data.get("email", "").strip()
    if email and "@" in email:
        sanitized["email"] = email[:100]

    # Contact (optional)
    contact = customer_data.get("contact", "").strip()
    if contact:
        # Remove non-numeric characters
        contact = "".join(filter(str.isdigit, contact))
        if len(contact) >= 10:
            sanitized["contact"] = contact[:15]  # Razorpay limit

    return sanitized
