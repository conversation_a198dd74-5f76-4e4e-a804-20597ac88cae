from typing import Optional, Dict, Any, List
import logging
from django.conf import settings
from django.core.cache import cache
from googlemaps import Client
from .models import Location, LocationMetadata, AdministrationArea, City, State, Country

# Configure logger
logger = logging.getLogger(__name__)


class GoogleMapsService:
    def __init__(self):
        self.client = Client(key=settings.GOOGLE_MAPS_API_KEY)
        self.cache_timeout = 60 * 60 * 24 * 7  # 7 days
        logger.info("GoogleMapsService initialized with API key")

    def get_place_details(self, place_id: str) -> Optional[Dict[str, Any]]:
        """First check if details exist in search cache before making API call"""
        logger.debug(f"Fetching place details for place_id: {place_id}")

        # Check if this place's details were cached during a recent search
        search_cache_key = f"place_search_details_{place_id}"
        cached_search_details = cache.get(search_cache_key)
        if cached_search_details:
            logger.debug(
                f"Found place details in search cache for place_id: {place_id}"
            )
            return cached_search_details

        # If not in search cache, check regular cache
        cache_key = f"place_details_{place_id}"
        cached_result = cache.get(cache_key)
        if cached_result:
            logger.debug(
                f"Found place details in regular cache for place_id: {place_id}"
            )
            return cached_result

        # If not in any cache, fetch from API
        try:
            logger.info(
                f"Making API call to fetch place details for place_id: {place_id}"
            )
            result = self.client.place(place_id)
            if result.get("status") == "OK":
                logger.debug(
                    f"Successfully fetched place details for place_id: {place_id}"
                )
                cache.set(cache_key, result, self.cache_timeout)
                return result
            logger.warning(
                f"Failed to fetch place details. Status: {result.get('status')} for place_id: {place_id}"
            )
            return None
        except Exception as e:
            logger.error(
                f"Error fetching place details for place_id: {place_id}", exc_info=True
            )
            return None

    def search_place(
        self, query: str, location_bias: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        logger.debug(
            f"Searching for place with query: {query}, location_bias: {location_bias}"
        )
        cache_key = f"google_search_{query}"
        cached_result = cache.get(cache_key)
        if cached_result:
            logger.debug(f"Found search results in cache for query: {query}")
            return cached_result

        try:
            logger.info(f"Making API call to search places with query: {query}")
            result = self.client.places(
                query, location=location_bias, region="in"  # Bias towards India
            )
            if result.get("status") == "OK":
                logger.debug(f"Successfully fetched search results for query: {query}")
                # Cache the overall search result
                cache.set(cache_key, result, self.cache_timeout)

                # Also cache individual place details from the search results
                for place in result.get("results", []):
                    if place_id := place.get("place_id"):
                        logger.debug(
                            f"Caching individual place details for place_id: {place_id}"
                        )
                        # Create a structured place details object similar to what the Place API returns
                        detailed_place = {
                            "result": {
                                **place,
                                "address_components": place.get(
                                    "address_components", []
                                ),
                                "formatted_address": place.get("formatted_address", ""),
                                "geometry": place.get("geometry", {}),
                                "name": place.get("name", ""),
                                "place_id": place_id,
                                "types": place.get("types", []),
                                "business_status": place.get("business_status"),
                                "rating": place.get("rating", 0),
                            },
                            "status": "OK",
                        }
                        # Cache individual place details
                        search_cache_key = f"place_search_details_{place_id}"
                        cache.set(search_cache_key, detailed_place, self.cache_timeout)

                return result
            logger.warning(
                f"Failed to fetch search results. Status: {result.get('status')} for query: {query}"
            )
            return None
        except Exception as e:
            logger.error(
                f"Error searching for places with query: {query}", exc_info=True
            )
            return None


class LocationService:
    def __init__(self):
        self.maps_service = GoogleMapsService()
        logger.info("LocationService initialized")

    def process_address_components(
        self, components: List[Dict[str, Any]]
    ) -> Optional[AdministrationArea]:
        """Process Google Maps address components into our model structure"""
        logger.debug("Processing address components")
        country = state = city = area = None
        pincode = None
        area_name = None

        try:
            for component in components:
                types = component.get("types", [])

                if "country" in types:
                    country, _ = Country.objects.get_or_create(
                        code=component["short_name"],
                        defaults={"name": component["long_name"]},
                    )
                    logger.debug(f"Processed country: {country.name}")
                elif "administrative_area_level_1" in types:
                    if country:
                        state = State.get_or_create_state(
                            country=country,
                            name=component["long_name"],
                            code=component["short_name"],
                        )
                        logger.debug(f"Processed state: {state.name}")
                elif "locality" in types:
                    if state:
                        city, _ = City.objects.get_or_create(
                            state=state, name=component["long_name"]
                        )
                        logger.debug(f"Processed city: {city.name}")
                elif "postal_code" in types:
                    pincode = component["long_name"]
                    logger.debug(f"Processed pincode: {pincode}")
                elif "sublocality_level_1" in types:
                    area_name = component["long_name"]
                    logger.debug(f"Processed area name: {area_name}")

            if city and pincode:
                area = AdministrationArea.get_or_create_area(
                    city=city,
                    pincode=pincode,
                    name=area_name if area_name else city.name,
                )
                logger.debug(f"Created/retrieved administration area: {area.name}")

            return area
        except Exception as e:
            logger.error("Error processing address components", exc_info=True)
            return None

    def _parse_place_details(self, place_details: Dict[str, Any]) -> Dict[str, Any]:
        logger.debug("Parsing place details")
        result = place_details.get("result", {})
        geometry = result.get("geometry", {}).get("location", {})
        editorial_summary = result.get("editorial_summary", {})

        parsed_data = {
            "name": result.get("name", ""),
            "address": result.get("formatted_address", ""),
            "description": editorial_summary.get("overview", ""),
            "latitude": round(geometry.get("lat", 0), 6),
            "longitude": round(geometry.get("lng", 0), 6),
            "place_id": result.get("place_id"),
            "timezone": f"UTC{result.get('utc_offset', 0)/60:+g}",
            "location_type": self._determine_location_type(result),
            "is_verified": (
                True if result.get("business_status") == "OPERATIONAL" else False
            ),
        }
        logger.debug(f"Parsed place details for: {parsed_data['name']}")
        return parsed_data

    def _determine_location_type(self, place_details: Dict[str, Any]) -> str:
        types = place_details.get("types", [])

        type_mapping = {
            "lodging": Location.LocationType.HOTEL,
            "restaurant": Location.LocationType.RESTAURANT,
            "bus_station": Location.LocationType.TRANSPORT,
            "train_station": Location.LocationType.TRANSPORT,
            "airport": Location.LocationType.TRANSPORT,
            "tourist_attraction": Location.LocationType.TOURIST,
        }

        for place_type in types:
            if place_type in type_mapping:
                mapped_type = type_mapping[place_type]
                logger.debug(
                    f"Determined location type: {mapped_type} from Google type: {place_type}"
                )
                return mapped_type

        logger.debug("Could not determine specific location type, using OTHER")
        return Location.LocationType.OTHER

    def _create_location_metadata(
        self, location: Location, place_details: Dict[str, Any]
    ) -> None:
        logger.debug(f"Creating metadata for location: {location.name}")
        result = place_details.get("result", {})

        rating_mapping = {
            0: LocationMetadata.Rating.POOR,
            1: LocationMetadata.Rating.POOR,
            2: LocationMetadata.Rating.FAIR,
            3: LocationMetadata.Rating.GOOD,
            4: LocationMetadata.Rating.VERY_GOOD,
            5: LocationMetadata.Rating.EXCELLENT,
        }

        google_rating = result.get("rating", 0)
        rating = rating_mapping.get(int(google_rating), LocationMetadata.Rating.FAIR)

        amenities = {
            "wheelchair_accessible": result.get(
                "wheelchair_accessible_entrance", False
            ),
            "opening_hours": result.get("current_opening_hours", {}).get(
                "weekday_text", []
            ),
            "phone": result.get("formatted_phone_number"),
            "website": result.get("website"),
            "photos": [
                photo.get("photo_reference") for photo in result.get("photos", [])
            ],
        }

        try:
            LocationMetadata.objects.create(
                location=location, overall_rating=rating, amenities=amenities
            )
            logger.debug(f"Successfully created metadata for location: {location.name}")
        except Exception as e:
            logger.error(
                f"Error creating metadata for location: {location.name}", exc_info=True
            )

    def get_or_create_location(self, place_id: str) -> Optional[Location]:
        logger.info(f"Getting or creating location for place_id: {place_id}")

        # Check if location exists in database
        location = Location.objects.filter(place_id=place_id).first()
        if location:
            logger.debug(f"Found existing location in database: {location.name}")
            return location

        # Try to get place details from search cache first
        search_cache_key = f"place_search_details_{place_id}"
        place_details = cache.get(search_cache_key)

        # If not in search cache, try regular cache or API
        if not place_details:
            logger.debug(
                f"Place details not found in search cache for place_id: {place_id}"
            )
            place_details = self.maps_service.get_place_details(place_id)

        if not place_details:
            logger.warning(f"Could not fetch place details for place_id: {place_id}")
            return None

        # Process and save location data
        try:
            administrative_area = self.process_address_components(
                place_details.get("result", {}).get("address_components", [])
            )

            location_data = self._parse_place_details(place_details)
            location_data["administrative_area"] = administrative_area

            # Create the location using the processed data
            location = Location.objects.create(**location_data)
            logger.info(f"Successfully created new location: {location.name}")

            # Create additional metadata for the location
            self._create_location_metadata(location, place_details)
            return location
        except Exception as e:
            logger.error(
                f"Error creating location for place_id: {place_id}", exc_info=True
            )
            return None
