from django.apps import apps
from stay.models import Guest
from .jwt import ExemptedPaths, ExemptedPathsWithRequests


class PropertyMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        try:
            if request.path in ExemptedPaths:
                response = self.get_response(request)
                return response
            if request.path in ExemptedPathsWithRequests.keys():
                if request.method == ExemptedPathsWithRequests[request.path]:
                    response = self.get_response(request)
                    return response

            property_header = request.headers.get("X-PROPERTY-ID")
            if getattr(request, "property", None):
                response = self.get_response(request)
                return response
            elif property_header:
                Property = apps.get_model("stay", "Property")
                _property = Property.objects.get(pk=property_header)
                setattr(request, "property", _property)
            elif (
                getattr(request, "user", False)
                and not request.user.is_anonymous
                and request.user.is_partner
            ):
                active_property = request.user.partner_profile.active_property
                if (
                    not active_property
                    and request.user.partner_profile.properties.exists()
                ):
                    active_property = request.user.partner_profile.properties.first()
                    request.user.partner_profile.active_property = active_property
                    request.user.partner_profile.save()
                if active_property:
                    setattr(request, "property", active_property)
            elif (
                getattr(request, "user", False)
                and not request.user.is_anonymous
                and not request.user.is_partner
            ):
                guest = Guest.objects.filter(
                    user=request.user, checked_in=True, checked_out=False
                ).last()
                if guest:
                    setattr(request, "property", guest.room.property)
                    setattr(request, "guest", guest)
        except Exception as e:
            print(e)
        return self.get_response(request)
