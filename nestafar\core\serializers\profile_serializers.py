import email
from ..models import User, PartnerProfile
from rest_framework import serializers
from stay.models import Guest
from stay.serializers import GuestSerializer


class UserProfileSerializer(serializers.ModelSerializer):
    trips = serializers.SerializerMethodField()
    guest = serializers.SerializerMethodField()
    features = serializers.SerializerMethodField()
    helpline = serializers.SerializerMethodField()

    def get_helpline(self, obj):
        guest = Guest.objects.filter(
            user=obj, checked_in=True, checked_out=False
        ).last()
        if guest:
            phones = guest.room.property.staffs.values_list("user__phone", flat=True)
            phones = [phone.as_e164 for phone in phones]
            return phones

    def get_guest(self, obj):
        guest = Guest.objects.filter(
            user=obj, checked_in=True, checked_out=False
        ).last()
        if not guest:
            return None
        return GuestSerializer(guest).data

    def get_trips(self, obj):
        guest_entries = Guest.objects.filter(user=obj, checked_out=True)
        trips = []
        for entry in guest_entries:
            trips.append(
                {
                    "property": entry.room.property.name,
                    "total_spends": entry.total_spends,
                    "total_orders": entry.total_orders,
                }
            )

        return trips

    def get_features(self, obj):
        if obj.is_partner:
            profile = obj.partner_profile
            primitives = (bool, str, int, float, type(None))
            features = {
                k: v for k, v in profile.__dict__.items() if isinstance(v, primitives)
            }
            del features["id"]
            return features
        else:
            guest = Guest.objects.filter(
                user=obj, checked_in=True, checked_out=False
            ).last()
            if guest:
                property = guest.room.property
                perms = {
                    "has_food": False,
                    "has_rental": False,
                    "has_transport": False,
                    "has_laundry": False,
                    "has_shop": False,
                    "has_tourism": False,
                }
                for staff in property.staffs.all():
                    perms["has_food"] |= staff.has_food
                    perms["has_rental"] |= staff.has_rental
                    perms["has_transport"] |= staff.has_transport
                    perms["has_laundry"] |= staff.has_laundry
                    perms["has_shop"] |= staff.has_shop
                    perms["has_tourism"] |= staff.has_tourism
                return perms

    class Meta:
        model = User
        exclude = ["password"]


class PartnerProfileSerializer(serializers.ModelSerializer):
    properties = serializers.SerializerMethodField(read_only=True)
    name = serializers.CharField(source="user.name", read_only=True)
    phone = serializers.CharField(source="user.phone", read_only=True)
    email = serializers.CharField(source="user.email", read_only=True)
    created_at = serializers.DateTimeField(source="user.created_at", read_only=True)

    def get_properties(self, obj):
        return obj.properties.values_list("id", flat=True)

    class Meta:
        model = PartnerProfile
        read_only_fields = ["properties", "created_at", "name", "phone", "email"]
        fields = "__all__"
