"""
Payment Services Tests

Tests for payment system services including RazorpayService,
PaymentSplitService, and integration services.
"""

from decimal import Decimal
from unittest.mock import patch, Mock
import hmac
import hashlib
from django.test import TestCase

from ..services.razorpay_service import RazorpayService
from ..services.payment_split_service import PaymentSplitService
from ..services.checkout_payment_service import CheckoutPaymentService
from ..services.precheckin_payment_service import PrecheckinPaymentService
from ..constants import PaymentContext
from ..exceptions import PaymentException, RazorpayException
from .test_base import PaymentTestBase, MockRazorpayService


class RazorpayServiceTest(PaymentTestBase):
    """Tests for RazorpayService"""
    
    def setUp(self):
        super().setUp()
        self.razorpay_service = RazorpayService()
        self.mock_razorpay = MockRazorpayService()
    
    @patch('payments.services.razorpay_service.razorpay.Client')
    def test_create_payment_link(self, mock_client):
        """Test creating payment link"""
        # Mock Razorpay client response
        mock_payment_link = {
            'id': 'plink_test123',
            'short_url': 'https://rzp.io/l/plink_test123',
            'amount': 100000,
            'status': 'created'
        }
        mock_client.return_value.payment_link.create.return_value = mock_payment_link
        
        # Create payment link
        result = self.razorpay_service.create_payment_link(
            amount=Decimal('1000.00'),
            description='Test payment',
            customer_details={
                'name': 'Test Customer',
                'email': '<EMAIL>',
                'contact': '+919876543210'
            }
        )
        
        self.assertEqual(result['id'], 'plink_test123')
        self.assertEqual(result['short_url'], 'https://rzp.io/l/plink_test123')
        mock_client.return_value.payment_link.create.assert_called_once()
    
    @patch('payments.services.razorpay_service.razorpay.Client')
    def test_create_payment_link_error(self, mock_client):
        """Test payment link creation error handling"""
        # Mock Razorpay client error
        mock_client.return_value.payment_link.create.side_effect = Exception('API Error')
        
        with self.assertRaises(RazorpayException):
            self.razorpay_service.create_payment_link(
                amount=Decimal('1000.00'),
                description='Test payment',
                customer_details={'name': 'Test Customer'}
            )
    
    @patch('payments.services.razorpay_service.razorpay.Client')
    def test_create_transfer(self, mock_client):
        """Test creating transfers"""
        # Mock Razorpay client response
        mock_transfers = [
            {
                'id': 'trf_test123',
                'amount': 50000,
                'status': 'processed'
            }
        ]
        mock_client.return_value.payment.transfer.return_value = mock_transfers
        
        # Create transfers
        transfers = [
            {
                'account': 'acc_test123',
                'amount': 50000,
                'currency': 'INR'
            }
        ]
        
        result = self.razorpay_service.create_transfer('pay_test123', transfers)
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]['id'], 'trf_test123')
        mock_client.return_value.payment.transfer.assert_called_once()
    
    def test_verify_webhook_signature(self):
        """Test webhook signature verification using real HMAC implementation"""
        payload = '{"event": "payment.captured"}'

        # Ensure payment_config has a known webhook secret for the test
        from payments.services.payment_config_service import payment_config
        test_secret = 'test_webhook_secret_123'
        webhook_settings = {'signature_verification': True}
        with patch.dict(payment_config._razorpay_settings, {'WEBHOOK_SECRET': test_secret, 'WEBHOOK_SETTINGS': webhook_settings}):
            # Construct a valid HMAC SHA256 signature matching the service implementation
            valid_signature = hmac.new(
                test_secret.encode('utf-8'), payload.encode('utf-8'), hashlib.sha256
            ).hexdigest()

            # The real implementation should return True for a valid signature
            self.assertTrue(self.razorpay_service.verify_webhook_signature(payload, valid_signature))

            # And should return False for an invalid signature
            self.assertFalse(self.razorpay_service.verify_webhook_signature(payload, 'invalid_signature'))
class PaymentSplitServiceTest(PaymentTestBase):
    """Tests for PaymentSplitService"""
    
    def setUp(self):
        super().setUp()
        self.split_service = PaymentSplitService()
    
    def test_calculate_checkout_splits(self):
        """Test calculating splits for checkout payment"""
        payment_intent = self.create_payment_intent(
            context=PaymentContext.CHECKOUT,
            amount=Decimal('1000.00')
        )
        
        split_data = self.split_service.calculate_splits(payment_intent)
        
        # Verify split calculation
        self.assertIn('splits', split_data)
        self.assertIn('summary', split_data)
        
        summary = split_data['summary']
        self.assertEqual(summary['total_amount'], Decimal('1000.00'))
        self.assertEqual(summary['platform_commission'], Decimal('50.00'))  # 5% of 1000
        self.assertGreater(summary['partner_amount'], Decimal('0'))
    
    def test_calculate_precheckin_splits(self):
        """Test calculating splits for precheckin payment"""
        payment_intent = self.create_payment_intent(
            context=PaymentContext.PRECHECKIN,
            amount=Decimal('1000.00'),
            guest=None  # Remove guest for precheckin
        )
        
        split_data = self.split_service.calculate_splits(payment_intent)
        
        # Verify precheckin split (all goes to partner)
        summary = split_data['summary']
        self.assertEqual(summary['total_amount'], Decimal('1000.00'))
        self.assertEqual(summary['platform_commission'], Decimal('0.00'))  # No commission for precheckin
        self.assertEqual(summary['partner_amount'], Decimal('1000.00'))  # All to partner
        self.assertEqual(summary['vendor_amount'], Decimal('0.00'))  # No vendors for precheckin
    
    def test_create_payment_splits(self):
        """Test creating payment split records"""
        payment_intent = self.create_payment_intent()
        split_data = self.split_service.calculate_splits(payment_intent)
        
        splits = self.split_service.create_payment_splits(payment_intent, split_data)
        
        self.assertGreater(len(splits), 0)
        
        # Verify splits were created in database
        db_splits = payment_intent.splits.all()
        self.assertEqual(len(splits), db_splits.count())
        
        # Verify split amounts sum to total
        total_split_amount = sum(split.amount for split in db_splits)
        self.assertEqual(total_split_amount, payment_intent.total_amount)


class CheckoutPaymentServiceTest(PaymentTestBase):
    """Tests for CheckoutPaymentService"""
    
    def setUp(self):
        super().setUp()
        self.checkout_service = CheckoutPaymentService()
    
    @patch('payments.services.checkout_payment_service.RazorpayService')
    @patch('payments.services.checkout_payment_service.PaymentNotificationService')
    def test_create_checkout_payment_link(self, mock_notification, mock_razorpay):
        """Test creating checkout payment link"""
        # Mock services
        mock_razorpay.return_value.create_payment_link.return_value = {
            'id': 'plink_test123',
            'short_url': 'https://rzp.io/l/plink_test123'
        }
        mock_notification.return_value.send_payment_link_sms.return_value = {'sent': True}
        mock_notification.return_value.send_payment_link_whatsapp.return_value = {'sent': True}
        
        # Create payment link
        result = self.checkout_service.create_checkout_payment_link(
            guest=self.guest,
            total_amount=Decimal('1000.00'),
            description='Test checkout payment'
        )
        
        # Verify result
        self.assertIn('payment_intent_id', result)
        self.assertIn('payment_link_url', result)
        self.assertEqual(result['total_amount'], Decimal('1000.00'))
        
        # Verify payment intent was created
        payment_intent = self.guest.payment_intents.first()
        self.assertIsNotNone(payment_intent)
        self.assertEqual(payment_intent.context, PaymentContext.CHECKOUT)
    
    def test_calculate_guest_bill(self):
        """Test calculating guest bill"""
        # This test would be more comprehensive with actual service orders
        bill_breakdown = self.checkout_service.calculate_guest_bill(self.guest)
        
        self.assertIn('room_charges', bill_breakdown)
        self.assertIn('service_orders', bill_breakdown)
        self.assertIn('total_amount', bill_breakdown)
        self.assertIsInstance(bill_breakdown['total_amount'], Decimal)
        
        # Verify total_amount is the sum of all charges
        expected_total = (
            bill_breakdown.get('room_charges', Decimal('0')) +
            sum(order.get('amount', Decimal('0')) for order in bill_breakdown.get('service_orders', []))
        )
        self.assertEqual(bill_breakdown['total_amount'], expected_total)
        
        # Verify amounts are non-negative
        self.assertGreaterEqual(bill_breakdown['total_amount'], Decimal('0'))


class PrecheckinPaymentServiceTest(PaymentTestBase):
    """Tests for PrecheckinPaymentService"""
    
    def setUp(self):
        super().setUp()
        self.precheckin_service = PrecheckinPaymentService()
    
    @patch('payments.services.precheckin_payment_service.RazorpayService')
    @patch('payments.services.precheckin_payment_service.PaymentNotificationService')
    def test_create_precheckin_payment_link(self, mock_notification, mock_razorpay):
        """Test creating precheckin payment link"""
        # Mock services
        mock_razorpay.return_value.create_payment_link.return_value = {
            'id': 'plink_test456',
            'short_url': 'https://rzp.io/l/plink_test456'
        }
        mock_notification.return_value.send_payment_link_sms.return_value = {'sent': True}
        mock_notification.return_value.send_payment_link_whatsapp.return_value = {'sent': True}
        
        # Create payment link
        result = self.precheckin_service.create_precheckin_payment_link(
            precheckin=self.precheckin,
            upfront_amount=Decimal('400.00'),
            description='Test precheckin payment'
        )
        
        # Verify result
        self.assertIn('payment_intent_id', result)
        self.assertIn('payment_link_url', result)
        self.assertEqual(result['total_amount'], Decimal('400.00'))
        
        # Verify payment intent was created
        payment_intent = self.precheckin.payment_intents.first()
        self.assertIsNotNone(payment_intent)
        self.assertEqual(payment_intent.context, PaymentContext.PRECHECKIN)
    
    def test_calculate_upfront_amount(self):
        """Test calculating upfront amount"""
        # Test default percentage (20%)
        upfront_amount = self.precheckin_service.calculate_upfront_amount(self.precheckin)
        expected_amount = Decimal('400.00')  # 20% of 2000
        self.assertEqual(upfront_amount, expected_amount)
        
        # Test custom percentage
        upfront_amount = self.precheckin_service.calculate_upfront_amount(
            self.precheckin, 
            percentage=Decimal('30.00')
        )
        expected_amount = Decimal('600.00')  # 30% of 2000
        self.assertEqual(upfront_amount, expected_amount)
        
        # Test minimum amount enforcement
        small_precheckin = self.precheckin
        small_precheckin.total_amount = Decimal('50.00')
        
        upfront_amount = self.precheckin_service.calculate_upfront_amount(small_precheckin)
        self.assertEqual(upfront_amount, Decimal('100.00'))  # Minimum amount
    
    def test_process_precheckin_payment_completion(self):
        """Test processing precheckin payment completion"""
        payment_intent = self.create_payment_intent(
            context=PaymentContext.PRECHECKIN,
            amount=Decimal('400.00'),
            guest=None
        )

        payment_intent.mark_as_paid()

        # Process completion
        self.precheckin_service.process_precheckin_payment_completion(payment_intent)

        # Verify precheckin was updated
        self.precheckin.refresh_from_db()
        self.assertEqual(self.precheckin.amount_paid, 400.00)
        self.assertEqual(self.precheckin.pending_balance, 1600.00)  # 2000 - 400
        self.assertEqual(self.precheckin.payment_status, 'partial')


class PaymentServiceIntegrationTest(PaymentTestBase):
    """Integration tests for payment services"""
    
    def test_end_to_end_checkout_flow(self):
        """Test complete checkout payment flow"""
        checkout_service = CheckoutPaymentService()
        
        with patch('payments.services.checkout_payment_service.RazorpayService') as mock_razorpay:
            with patch('payments.services.checkout_payment_service.PaymentNotificationService') as mock_notification:
                # Mock service responses
                mock_razorpay.return_value.create_payment_link.return_value = {
                    'id': 'plink_integration_test',
                    'short_url': 'https://rzp.io/l/plink_integration_test'
                }
                mock_notification.return_value.send_payment_link_sms.return_value = {'sent': True}
                mock_notification.return_value.send_payment_link_whatsapp.return_value = {'sent': True}
                
                # Create payment link
                result = checkout_service.create_checkout_payment_link(
                    guest=self.guest,
                    total_amount=Decimal('1500.00')
                )
                
                # Verify payment intent was created with splits
                payment_intent_id = result['payment_intent_id']
                payment_intent = self.guest.payment_intents.get(id=payment_intent_id)
                
                self.assertEqual(payment_intent.total_amount, Decimal('1500.00'))
                self.assertGreater(payment_intent.splits.count(), 0)
                
                # Simulate payment completion
                payment_intent.mark_as_paid(razorpay_payment_id='pay_integration_test')
                
                # Process completion
                checkout_service.process_checkout_payment_completion(payment_intent)
                
                # Verify guest payment_completed flag was set
                self.guest.refresh_from_db()
                self.assertTrue(self.guest.payment_completed)
    
    def test_end_to_end_precheckin_flow(self):
        """Test complete precheckin payment flow"""
        precheckin_service = PrecheckinPaymentService()
        
        with patch('payments.services.precheckin_payment_service.RazorpayService') as mock_razorpay:
            with patch('payments.services.precheckin_payment_service.PaymentNotificationService') as mock_notification:
                # Mock service responses
                mock_razorpay.return_value.create_payment_link.return_value = {
                    'id': 'plink_precheckin_test',
                    'short_url': 'https://rzp.io/l/plink_precheckin_test'
                }
                mock_notification.return_value.send_payment_link_sms.return_value = {'sent': True}
                mock_notification.return_value.send_payment_link_whatsapp.return_value = {'sent': True}
                
                # Calculate upfront amount
                upfront_amount = precheckin_service.calculate_upfront_amount(self.precheckin)
                
                # Create payment link
                result = precheckin_service.create_precheckin_payment_link(
                    precheckin=self.precheckin,
                    upfront_amount=upfront_amount
                )
                
                # Verify payment intent was created
                payment_intent_id = result['payment_intent_id']
                payment_intent = self.precheckin.payment_intents.get(id=payment_intent_id)
                
                self.assertEqual(payment_intent.context, PaymentContext.PRECHECKIN)
                self.assertEqual(payment_intent.total_amount, upfront_amount)
                
                # Simulate payment completion
                payment_intent.mark_as_paid(razorpay_payment_id='pay_precheckin_test')
                
                # Process completion
                precheckin_service.process_precheckin_payment_completion(payment_intent)
                
                # Verify precheckin was updated
                self.precheckin.refresh_from_db()
                self.assertGreater(self.precheckin.amount_paid, 0)
                self.assertLess(self.precheckin.pending_balance, self.precheckin.total_amount)
