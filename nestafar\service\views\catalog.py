from rest_framework.views import APIView
from core.permissions import PropertyPermission, ServicePermission
from service.service_factory import *
from nestafar.responses import SuccessR<PERSON>ponse, BadRequestResponse
from service.serializers import *


class CatalogView(APIView):
    permission_classes = [PropertyPermission, ServicePermission]

    def get(self, request, service_type=None):
        try:
            partner_type = url_mappings.get(service_type)
            if hasattr(self.request, "property"):
                property_partners = self.request.property.property_partner.filter(
                    partner__type_of_service=partner_type
                )
                catalogs = []
                for property_partner in property_partners:
                    items = property_partner.partner.service_item.objects.filter(
                        service__partner=property_partner.partner, is_active=True
                    )
                    property_partner_data = PropertyPartnerSerializer(
                        property_partner
                    ).data
                    items = [
                        item.get_catalog_item(property_partner.commission)
                        for item in items
                    ]

                    items_data = service_item_list_serializer.get(partner_type)(
                        items, many=True
                    ).data
                    property_partner_data["catalog"] = items_data
                    catalogs.append(property_partner_data)
                return SuccessResponse(catalogs)
            else:
                return BadRequestResponse(message="Property not found")
        except Exception as e:
            return BadRequestResponse(str(e))
