import uuid
from django.db import models


class Staff(models.Model):
    """Internal property staff or gig workers mapped to a core.User"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(
        "core.User", on_delete=models.CASCADE, related_name="staff"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return getattr(self.user, "name", str(self.id))


class StaffProfile(models.Model):
    class StaffRole(models.TextChoices):
        HOUSEKEEPING = "housekeeping", "Housekeeping"
        DELIVERY = "delivery", "Delivery"
        MAINTENANCE = "maintenance", "Maintenance"
        FRONT_DESK = "frontdesk", "Front Desk"

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name="profiles")
    property = models.ForeignKey(
        "stay.Property", on_delete=models.CASCADE, related_name="staff_profiles"
    )
    role = models.CharField(max_length=32, choices=StaffRole.choices)
    contact_email = models.EmailField(blank=True, null=True)
    alt_phone = models.CharField(max_length=32, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    is_gig_worker = models.BooleanField(
        default=False, help_text="True for on-demand delivery workers"
    )
    earnings_rate = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Per-job or hourly rate",
    )
    meta = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("staff", "property")
        indexes = [
            models.Index(fields=["property", "role", "is_active"]),
        ]

    def __str__(self) -> str:
        return f"{self.staff.user.name} @ {self.property.name} ({self.role})"
