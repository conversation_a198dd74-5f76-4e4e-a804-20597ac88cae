"""
Payment Tasks

Celery tasks for asynchronous payment processing including
webhook processing, transfer retries, and reconciliation.
"""

import logging
from celery import shared_task
from .models import PaymentSplit
from .constants import TransferStatus, PaymentStatus
from django.utils import timezone
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def process_webhook_event(self, webhook_event_id):
    """
    Process a webhook event asynchronously.
    
    Args:
        webhook_event_id: PaymentWebhookEvent ID
    """
    try:
        from .models import PaymentWebhookEvent
        from .webhooks.payment_webhook_processor import PaymentWebhookProcessor
        from .webhooks.transfer_webhook_processor import TransferWebhookProcessor
        
        # Get webhook event
        webhook_event = PaymentWebhookEvent.objects.get(id=webhook_event_id)
        
        logger.info(f"Processing webhook event {webhook_event.event_id} "
                   f"(type: {webhook_event.event_type})")
        
        # Process based on entity type
        if webhook_event.is_payment_event():
            processor = PaymentWebhookProcessor()
            processor.process_event(webhook_event)
        elif webhook_event.is_transfer_event():
            processor = TransferWebhookProcessor()
            processor.process_event(webhook_event)
        else:
            logger.warning(f"No processor for entity type: {webhook_event.entity_type}")
            webhook_event.mark_processing_error("No processor available")
            return
        
        logger.info(f"Successfully processed webhook event {webhook_event.event_id}")
        
    except PaymentWebhookEvent.DoesNotExist:
        logger.error(f"Webhook event {webhook_event_id} not found")
    except Exception as e:
        logger.error(f"Error processing webhook event {webhook_event_id}: {str(e)}")
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            retry_delay = 60 * (2 ** self.request.retries)  # 1min, 2min, 4min
            raise self.retry(countdown=retry_delay, exc=e)
        else:
            # Mark as failed after max retries
            try:
                webhook_event = PaymentWebhookEvent.objects.get(id=webhook_event_id)
                webhook_event.mark_processing_error(f"Max retries exceeded: {str(e)}")
            except Exception as e:
                logger.error(f"Failed to mark webhook event as failed: {str(e)}")
                pass

@shared_task(bind=True, max_retries=3)
def create_payment_transfers(self, payment_intent_id):
    """
    Create Razorpay Route transfers for a payment.
    
    Args:
        payment_intent_id: PaymentIntent ID
    """
    try:
        from .models import PaymentIntent, PaymentSplit
        from .services import RazorpayService
        
        # Get payment intent
        payment_intent = PaymentIntent.objects.get(id=payment_intent_id)
        
        if not payment_intent.is_paid():
            logger.warning(f"Payment {payment_intent_id} is not paid yet")
            return
        
        # Get splits that need transfers
        splits_for_transfer = payment_intent.splits.filter(
            recipient_type__in=['partner', 'vendor'],
            razorpay_account_id__isnull=False,
            status='pending'
        )
        
        if not splits_for_transfer.exists():
            logger.info(f"No transfers needed for payment {payment_intent_id}")
            return
        
        # Create transfers
        razorpay_service = RazorpayService()
        transfers = []
        # Build transfers while preserving the exact split order mapping
        splits = list(splits_for_transfer)
        split_order = []
        for split in splits:
            payload = split.get_transfer_payload()
            if payload:
                # Attach deterministic idempotency token per split to avoid duplicate payouts on retries
                # Use split id (UUID) as the reference_id which Razorpay supports for deduplication
                payload['reference_id'] = str(split.id)
                transfers.append(payload)
                split_order.append(split)
            else:
                logger.warning(f"Skipping split {split.id}: empty transfer payload")

        if transfers:
            transfer_results = razorpay_service.create_transfer(
                payment_intent.razorpay_payment_id,
                transfers
            )

            # Update split records in the same order requests were sent
            if not isinstance(transfer_results, (list, tuple)) or not transfer_results:
                logger.error(
                    f"Unexpected transfer response for payment {payment_intent_id}: {transfer_results!r}"
                )
                raise ValueError("Unexpected transfer response type or empty result")
            if len(transfer_results) != len(transfers):
                logger.error(
                    f"Mismatch in transfer results: requested={len(transfers)}, received={len(transfer_results)}"
                )
                if len(transfer_results) < len(transfers):
                    # Mark any splits beyond the returned results as failed
                    for split in split_order[len(transfer_results):]:
                        split.mark_as_failed(error_message="No transfer result returned (API mismatch)")
            # Process each returned transfer, guarding against missing 'id'
            for split, transfer_result in zip(split_order, transfer_results):
                tr_id = transfer_result.get('id') if isinstance(transfer_result, dict) else None
                if tr_id:
                    split.mark_as_initiated(tr_id)
                else:
                    split.mark_as_failed(error_message="Missing transfer id in provider response")

            logger.info(f"Created {len(transfer_results)} transfers for payment {payment_intent_id}")
        
    except PaymentIntent.DoesNotExist:
        logger.error(f"Payment intent {payment_intent_id} not found")
    except Exception as e:
        logger.error(f"Error creating transfers for payment {payment_intent_id}: {str(e)}")
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            retry_delay = 300 * (2 ** self.request.retries)  # 5min, 10min, 20min
            raise self.retry(countdown=retry_delay, exc=e)


@shared_task(bind=True, max_retries=5)
def retry_failed_transfer(self, payment_split_id):
    """
    Retry a failed transfer.
    
    Args:
        payment_split_id: PaymentSplit ID
    """
    try:
        from .models import PaymentSplit
        from .services import RazorpayService
        
        # Get payment split
        payment_split = PaymentSplit.objects.get(id=payment_split_id)
        
        if not payment_split.can_retry():
            logger.warning(f"Split {payment_split_id} cannot be retried")
            return
        
        # Check if payment is still valid
        if not payment_split.payment_intent.is_paid():
            logger.warning(f"Payment {payment_split.payment_intent.id} is not paid")
            return
        
        # Retry transfer
        razorpay_service = RazorpayService()
        transfer_payload = payment_split.get_transfer_payload()
        
        if not transfer_payload:
            logger.error(f"No valid transfer payload for split {payment_split_id}")
            payment_split.mark_as_failed(error_message="No valid transfer payload")
            return
        
        # Create transfer
        transfer_results = razorpay_service.create_transfer(
            payment_split.payment_intent.razorpay_payment_id,
            [transfer_payload]
        )

        if transfer_results and isinstance(transfer_results, (list, tuple)) and len(transfer_results) > 0:
            transfer_result = transfer_results[0]
            if isinstance(transfer_result, dict) and 'id' in transfer_result:
                payment_split.mark_as_initiated(transfer_result['id'])
                logger.info(f"Successfully retried transfer for split {payment_split_id}")
            else:
                payment_split.mark_as_failed(error_message="Invalid transfer response format")
                logger.error(f"Invalid transfer response for split {payment_split_id}: {transfer_result}")
        else:
            payment_split.mark_as_failed(error_message="Empty transfer response")
            logger.error(f"Empty transfer response for split {payment_split_id}")            
        
    except PaymentSplit.DoesNotExist:
        logger.error(f"Payment split {payment_split_id} not found")
    except Exception as e:
        logger.error(f"Error retrying transfer for split {payment_split_id}: {str(e)}")
        
        # Update retry count and mark as failed if max retries exceeded
        try:
            payment_split = PaymentSplit.objects.get(id=payment_split_id)
            if self.request.retries >= self.max_retries:
                payment_split.mark_as_failed(error_message=f"Max retries exceeded: {str(e)}")
            else:
                # Retry with exponential backoff
                retry_delay = 300 * (2 ** self.request.retries)  # 5min, 10min, 20min, 40min, 80min
                raise self.retry(countdown=retry_delay, exc=e)
        except PaymentSplit.DoesNotExist:
            pass


@shared_task
def retry_failed_transfers():
    """
    Periodic task to retry failed transfers.
    
    This task runs periodically to retry transfers that failed
    due to temporary issues.
    """
    try:
        
        # Get failed transfers that can be retried
        failed_splits = PaymentSplit.objects.filter(
            status=TransferStatus.FAILED,
            retry_count__lt=3,  # Max 3 retry attempts
            payment_intent__status=PaymentStatus.COMPLETED  # Only for completed payments
        ).select_related('payment_intent')
        
        retry_count = 0
        
        for split in failed_splits:
            try:
                # Schedule retry
                retry_failed_transfer.delay(split.id)
                retry_count += 1
            except Exception as e:
                logger.error(f"Error scheduling retry for split {split.id}: {str(e)}")
        
        logger.info(f"Scheduled {retry_count} transfer retries")
        
    except Exception as e:
        logger.error(f"Error in retry_failed_transfers task: {str(e)}")


@shared_task
def reconcile_payments():
    """
    Periodic task to reconcile payments with Razorpay.
    
    This task checks for discrepancies between local payment
    status and Razorpay's records.
    """
    try:
        from .models import PaymentIntent
        from .services import RazorpayService
        from .constants import PaymentStatus
        
        # Get payments that might need reconciliation
        # (pending payments older than 1 hour)
        cutoff_time = timezone.now() - timedelta(hours=1)
        pending_payments = PaymentIntent.objects.filter(
            status=PaymentStatus.PENDING,
            created_at__lt=cutoff_time,
            razorpay_payment_link_id__isnull=False
        )
        
        razorpay_service = RazorpayService()
        reconciled_count = 0
        
        for payment_intent in pending_payments:
            try:
                # Check payment link status
                link_data = razorpay_service.get_payment_link(
                    payment_intent.razorpay_payment_link_id
                )
                
                # Update status if payment was completed
                if link_data.get('status') == 'paid' and not payment_intent.is_paid():
                    payments = link_data.get('payments', [])
                    if payments:
                        payment_data = payments[0]
                        payment_intent.mark_as_paid(
                            razorpay_payment_id=payment_data.get('id'),
                            payment_method=payment_data.get('method')
                        )
                        
                        # Trigger transfer creation
                        create_payment_transfers.delay(payment_intent.id)
                        
                        reconciled_count += 1
                        logger.info(f"Reconciled payment {payment_intent.id}")
                
            except Exception as e:
                logger.error(f"Error reconciling payment {payment_intent.id}: {str(e)}")
        
        logger.info(f"Reconciled {reconciled_count} payments")
        
    except Exception as e:
        logger.error(f"Error in reconcile_payments task: {str(e)}")


@shared_task
def send_payment_notifications(payment_intent_id, notification_type):
    """
    Send payment-related notifications.
    
    Args:
        payment_intent_id: PaymentIntent ID
        notification_type: Type of notification to send
    """
    try:
        from .models import PaymentIntent
        from .services import PaymentNotificationService
        
        payment_intent = PaymentIntent.objects.get(id=payment_intent_id)
        notification_service = PaymentNotificationService()
        
        if notification_type == 'payment_success':
            notification_service.send_payment_success_notification(payment_intent)
        elif notification_type == 'payment_failure':
            notification_service.send_payment_failure_notification(payment_intent)
        elif notification_type == 'payment_reminder':
            notification_service.send_payment_reminder_notification(payment_intent)
        else:
            logger.warning(f"Unknown notification type: {notification_type}")
        
    except PaymentIntent.DoesNotExist:
        logger.error(f"Payment intent {payment_intent_id} not found")
    except Exception as e:
        logger.error(f"Error sending notification: {str(e)}")


@shared_task
def cleanup_old_webhook_events():
    """
    Periodic task to clean up old webhook events.
    
    Removes webhook events older than 30 days to prevent
    database bloat while keeping recent events for debugging.
    """
    try:
        from .models import PaymentWebhookEvent
        
        # Delete events older than 30 days
        cutoff_date = timezone.now() - timedelta(days=30)
        deleted_count, _ = PaymentWebhookEvent.objects.filter(
            received_at__lt=cutoff_date
        ).delete()
        
        logger.info(f"Cleaned up {deleted_count} old webhook events")
        
    except Exception as e:
        logger.error(f"Error cleaning up webhook events: {str(e)}")


@shared_task
def generate_payment_reports():
    """
    Periodic task to generate payment reports and statistics.
    
    This task can be extended to generate various payment
    reports for business intelligence and monitoring.
    """
    try:
        from .models import PaymentIntent, PaymentSplit
        from .constants import PaymentStatus, TransferStatus

        # Calculate daily statistics
        today = timezone.now().date()
        # combine date with minimum time (00:00:00) to get start of day as naive datetime
        naive_today_start = datetime.combine(today, datetime.min.time())
        # convert to aware datetime in the current timezone
        today_start = timezone.make_aware(naive_today_start)

        # Payment statistics
        total_payments = PaymentIntent.objects.filter(created_at__gte=today_start).count()
        completed_payments = PaymentIntent.objects.filter(
            created_at__gte=today_start,
            status=PaymentStatus.COMPLETED
        ).count()
        
        # Transfer statistics
        total_transfers = PaymentSplit.objects.filter(created_at__gte=today_start).count()
        completed_transfers = PaymentSplit.objects.filter(
            created_at__gte=today_start,
            status=TransferStatus.PROCESSED
        ).count()
        
        # Calculate success rates
        payment_success_rate = (completed_payments / total_payments * 100) if total_payments > 0 else 0
        transfer_success_rate = (completed_transfers / total_transfers * 100) if total_transfers > 0 else 0
        
        logger.info(f"Daily payment stats - Total: {total_payments}, "
                   f"Completed: {completed_payments}, Success Rate: {payment_success_rate:.2f}%")
        logger.info(f"Daily transfer stats - Total: {total_transfers}, "
                   f"Completed: {completed_transfers}, Success Rate: {transfer_success_rate:.2f}%")
        
        # Here you could save these statistics to a reporting table
        # or send them to an external monitoring service
        
    except Exception as e:
        logger.error(f"Error generating payment reports: {str(e)}")
