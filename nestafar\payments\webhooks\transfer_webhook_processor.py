"""
Transfer Webhook Processor

Processes transfer-related webhook events from Razorpay Route including
transfer processing, failures, and reversals.
"""

import logging
from django.db import transaction
from django.utils import timezone

from ..models import PaymentSplit, PaymentIntent
from ..constants import TransferStatus, RazorpayWebhookEvents
from ..exceptions import WebhookException, TransferException

logger = logging.getLogger(__name__)


class TransferWebhookProcessor:
    """
    Processor for transfer-related webhook events.
    
    Handles transfer lifecycle events and updates split
    records with current transfer status.
    """
    
    def process_event(self, webhook_event):
        """
        Process a transfer webhook event.
        
        Args:
            webhook_event: PaymentWebhookEvent instance
        """
        try:
            event_type = webhook_event.event_type
            
            logger.info(f"Processing transfer webhook event: {event_type} "
                       f"for entity {webhook_event.entity_id}")
            
            # Route to appropriate handler
            if event_type == RazorpayWebhookEvents.TRANSFER_PROCESSED:
                self._handle_transfer_processed(webhook_event)
            elif event_type == RazorpayWebhookEvents.TRANSFER_FAILED:
                self._handle_transfer_failed(webhook_event)
            elif event_type == RazorpayWebhookEvents.TRANSFER_REVERSED:
                self._handle_transfer_reversed(webhook_event)
            else:
                logger.warning(f"No handler for transfer event type: {event_type}")
                webhook_event.mark_processing_error(f"No handler for event type: {event_type}")
                return
            
            # Mark as processed
            webhook_event.mark_as_processed()
            logger.info(f"Successfully processed transfer webhook event {webhook_event.event_id}")
            
        except Exception as e:
            logger.error(f"Error processing transfer webhook {webhook_event.event_id}: {str(e)}")
            webhook_event.mark_processing_error(str(e))
            raise WebhookException(f"Transfer webhook processing failed: {str(e)}")
    
    def _handle_transfer_processed(self, webhook_event):
        """
        Handle transfer.processed event.
        
        This event indicates that a transfer has been successfully
        processed and funds have been transferred to the recipient.
        """
        transfer_data = webhook_event.get_transfer_data()
        transfer_id = transfer_data.get('id')
        
        if not transfer_id:
            raise WebhookException("No transfer ID in webhook data")
        
        try:
            with transaction.atomic():
                # Find payment split by transfer ID
                payment_split = PaymentSplit.objects.select_for_update().get(
                    razorpay_transfer_id=transfer_id
                )
                
                # Skip if already processed
                if payment_split.is_completed():
                    logger.info(f"Transfer {transfer_id} already processed")
                    return
                
                # Update split status
                settlement_id = transfer_data.get('settlement_id')
                payment_split.mark_as_completed(settlement_id=settlement_id)
                
                # Update metadata with transfer details
                new_meta = {
                    **(payment_split.metadata or {}),
                    'transfer_processed_at': timezone.now().isoformat(),
                    'settlement_id': settlement_id,
                    'transfer_amount': transfer_data.get('amount'),
                    'transfer_currency': transfer_data.get('currency'),
                }
                payment_split.metadata = new_meta
                payment_split.save()
                
                logger.info(f"Marked transfer {transfer_id} as completed for split {payment_split.id}")
                
                # Check if all transfers are completed
                self._check_all_transfers_completed(payment_split.payment_intent)
                
        except PaymentSplit.DoesNotExist:
            logger.warning(f"No payment split found for transfer {transfer_id}")
        except Exception as e:
            logger.error(f"Error handling transfer processed: {str(e)}")
            raise TransferException(f"Failed to process transfer completion: {str(e)}")
    
    def _handle_transfer_failed(self, webhook_event):
        """
        Handle transfer.failed event.
        
        This event indicates that a transfer has failed and
        may need to be retried or handled manually.
        """
        transfer_data = webhook_event.get_transfer_data()
        transfer_id = transfer_data.get('id')
        
        if not transfer_id:
            raise WebhookException("No transfer ID in webhook data")
        
        try:
            with transaction.atomic():
                # Find payment split by transfer ID
                payment_split = PaymentSplit.objects.select_for_update().get(
                    razorpay_transfer_id=transfer_id
                )                
                # Extract error information
                error_code = transfer_data.get('error', {}).get('code', 'TRANSFER_FAILED')
                error_description = transfer_data.get('error', {}).get('description', 'Transfer failed')
                
                # Update split status
                payment_split.mark_as_failed(
                    error_code=error_code,
                    error_message=error_description
                )
                
                # Update metadata with failure details
                new_meta = {
                    **(payment_split.metadata or {}),
                    'transfer_failed_at': timezone.now().isoformat(),
                    'failure_reason': error_description,
                    'error_code': error_code,
                    'retry_count': payment_split.retry_count,
                }
                payment_split.metadata = new_meta
                payment_split.save()
                
                logger.warning(f"Transfer {transfer_id} failed for split {payment_split.id}: {error_description}")
                
                # Schedule retry if eligible
                self._schedule_transfer_retry(payment_split)
                
                # Send failure notifications
                self._send_transfer_failure_notifications(payment_split, error_description)
                
        except PaymentSplit.DoesNotExist:
            logger.warning(f"No payment split found for failed transfer {transfer_id}")
        except Exception as e:
            logger.error(f"Error handling transfer failure: {str(e)}")
            raise TransferException(f"Failed to process transfer failure: {str(e)}")
    
    def _handle_transfer_reversed(self, webhook_event):
        """
        Handle transfer.reversed event.
        
        This event indicates that a previously successful transfer
        has been reversed (refunded back to the source).
        """
        transfer_data = webhook_event.get_transfer_data()
        transfer_id = transfer_data.get('id')
        
        if not transfer_id:
            raise WebhookException("No transfer ID in webhook data")
        
        try:
            with transaction.atomic():
                # Find payment split by transfer ID
                payment_split = PaymentSplit.objects.select_for_update().get(
                    razorpay_transfer_id=transfer_id
                )
                
                # Update split status to reversed
                payment_split.mark_as_reversed()
                
                # Update metadata with reversal details
                reversal_amount = transfer_data.get('amount_reversed', 0)
                new_meta = {
                    **(payment_split.metadata or {}),
                    'transfer_reversed_at': timezone.now().isoformat(),
                    'reversal_amount': reversal_amount,
                    'reversal_reason': transfer_data.get('notes', {}).get('reason', 'Transfer reversed'),
                }
                payment_split.metadata = new_meta
                payment_split.save()
                
                logger.info(f"Transfer {transfer_id} reversed for split {payment_split.id}")
                
                # Send reversal notifications
                self._send_transfer_reversal_notifications(payment_split, reversal_amount)
                
        except PaymentSplit.DoesNotExist:
            logger.warning(f"No payment split found for reversed transfer {transfer_id}")
        except Exception as e:
            logger.error(f"Error handling transfer reversal: {str(e)}")
            raise TransferException(f"Failed to process transfer reversal: {str(e)}")
    
    def _check_all_transfers_completed(self, payment_intent):
        """
        Check if all transfers for a payment are completed.
        
        Args:
            payment_intent: PaymentIntent instance
        """
        try:
            # Get all splits that require transfers
            transfer_splits = payment_intent.splits.filter(
                recipient_type__in=['partner', 'vendor'],
                razorpay_account_id__isnull=False
            )
            
            # Check if all are completed
            completed_transfers = transfer_splits.filter(status=TransferStatus.PROCESSED).count()
            total_transfers = transfer_splits.count()
            
            if completed_transfers == total_transfers and total_transfers > 0:
                logger.info(f"All transfers completed for payment {payment_intent.id}")
                
                # Send completion notifications
                self._send_all_transfers_completed_notifications(payment_intent)
                
                # Update payment intent metadata
                new_meta = {
                    **(payment_intent.metadata or {}),
                    'all_transfers_completed_at': timezone.now().isoformat(),
                    'total_transfers': total_transfers,
                }
                payment_intent.metadata = new_meta
                payment_intent.save()
            
        except Exception as e:
            logger.error(f"Error checking transfer completion: {str(e)}")
            # Don't raise exception - this is not critical
    
    def _schedule_transfer_retry(self, payment_split):
        """
        Schedule retry for failed transfer if eligible.
        
        Args:
            payment_split: PaymentSplit instance
        """
        try:
            if payment_split.can_retry():
                # Import here to avoid circular imports
                from ..tasks import retry_failed_transfer
                
                # Schedule retry with exponential backoff
                # Cap the exponent to prevent overflow
                retry_delay = min(300 * (2 ** min(payment_split.retry_count, 10)), 3600)  # Max 1 hour
                
                retry_failed_transfer.apply_async(
                    args=[payment_split.id],
                    countdown=retry_delay
                )
                
                logger.info(f"Scheduled retry for transfer {payment_split.razorpay_transfer_id} "
                          f"in {retry_delay} seconds")
            else:
                logger.warning(f"Transfer {payment_split.razorpay_transfer_id} "
                             f"exceeded max retry attempts")
                
        except Exception as e:
            logger.error(f"Error scheduling transfer retry: {str(e)}")
    
    def _send_transfer_failure_notifications(self, payment_split, error_message):
        """
        Send notifications for transfer failures.
        
        Args:
            payment_split: PaymentSplit instance
            error_message: Error message from Razorpay
        """
        try:
            # Import here to avoid circular imports
            from ..services import PaymentNotificationService
            
            notification_service = PaymentNotificationService()
            notification_service.send_transfer_failure_notification(
                payment_split, 
                error_message
            )
            
        except Exception as e:
            logger.error(f"Error sending transfer failure notifications: {str(e)}")
            # Don't raise exception - notifications are not critical
    
    def _send_transfer_reversal_notifications(self, payment_split, reversal_amount):
        """
        Send notifications for transfer reversals.
        
        Args:
            payment_split: PaymentSplit instance
            reversal_amount: Amount that was reversed
        """
        try:
            # Import here to avoid circular imports
            from ..services import PaymentNotificationService
            
            notification_service = PaymentNotificationService()
            notification_service.send_transfer_reversal_notification(
                payment_split, 
                reversal_amount
            )
            
        except Exception as e:
            logger.error(f"Error sending transfer reversal notifications: {str(e)}")
            # Don't raise exception - notifications are not critical
    
    def _send_all_transfers_completed_notifications(self, payment_intent):
        """
        Send notifications when all transfers are completed.
        
        Args:
            payment_intent: PaymentIntent instance
        """
        try:
            # Import here to avoid circular imports
            from ..services import PaymentNotificationService
            
            notification_service = PaymentNotificationService()
            notification_service.send_all_transfers_completed_notification(payment_intent)
            
        except Exception as e:
            logger.error(f"Error sending completion notifications: {str(e)}")
            # Don't raise exception - notifications are not critical
