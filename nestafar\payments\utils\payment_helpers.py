"""
Payment Helper Functions

Common utility functions for payment processing and calculations.
"""

import uuid
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List, Optional
from django.utils import timezone


def calculate_commission(amount: Decimal, rate: Decimal) -> Decimal:
    """
    Calculate commission amount from total amount and rate.

    Args:
        amount: Total amount
        rate: Commission rate as percentage (e.g., 5.00 for 5%)

    Returns:
        Commission amount rounded to 2 decimal places
    """
    if not amount or not rate:
        return Decimal("0.00")

    commission = (amount * rate) / Decimal("100")
    return commission.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)


def format_amount(amount: Decimal, currency: str = "INR") -> str:
    """
    Format amount for display.

    Args:
        amount: Amount to format
        currency: Currency code

    Returns:
        Formatted amount string
    """
    if currency == "INR":
        return f"₹{amount:,.2f}"
    return f"{amount:,.2f} {currency}"


def generate_payment_reference() -> str:
    """
    Generate a unique payment reference number.

    Returns:
        Unique reference number
    """
    timestamp = timezone.now().strftime("%Y%m%d%H%M%S")
    random_part = str(uuid.uuid4()).replace("-", "")[:8].upper()
    return f"PAY{timestamp}{random_part}"


def validate_split_total(
    splits: List[Dict], total_amount: Decimal, tolerance: Decimal = Decimal("0.01")
) -> bool:
    """
    Validate that split amounts sum up to total amount.

    Args:
        splits: List of split dictionaries with 'amount' key
        total_amount: Expected total amount
        tolerance: Acceptable difference tolerance

    Returns:
        True if splits are valid
    """
    split_total = sum(Decimal(str(split.get("amount", 0))) for split in splits)
    difference = abs(split_total - total_amount)
    return difference <= tolerance


def distribute_rounding_difference(
    splits: List[Dict], total_amount: Decimal
) -> List[Dict]:
    """
    Distribute rounding differences across splits to match total amount.

    Args:
        splits: List of split dictionaries
        total_amount: Target total amount

    Returns:
        Adjusted splits list
    """
    if not splits:
        return splits

    split_total = sum(Decimal(str(split.get("amount", 0))) for split in splits)
    difference = total_amount - split_total

    if difference == 0:
        return splits

    # Distribute difference to the largest split
    largest_split_idx = max(
        range(len(splits)), key=lambda i: Decimal(str(splits[i].get("amount", 0)))
    )

    splits[largest_split_idx]["amount"] = (
        Decimal(str(splits[largest_split_idx]["amount"])) + difference
    )

    return splits


def calculate_percentage_split(amount: Decimal, percentage: Decimal) -> Decimal:
    """
    Calculate split amount based on percentage.

    Args:
        amount: Total amount
        percentage: Percentage (0-100)

    Returns:
        Split amount
    """
    if not amount or not percentage:
        return Decimal("0.00")

    split_amount = (amount * percentage) / Decimal("100")
    return split_amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)


def get_payment_context_display(context: str) -> str:
    """
    Get display name for payment context.

    Args:
        context: Payment context code

    Returns:
        Human-readable context name
    """
    context_map = {
        "checkout": "Guest Checkout",
        "precheckin": "Precheckin Payment",
        "service_order": "Service Order",
        "booking": "Booking Payment",
    }
    return context_map.get(context, context.title())


def calculate_tax_amount(base_amount: Decimal, tax_rate: Decimal) -> Decimal:
    """
    Calculate tax amount from base amount and tax rate.

    Args:
        base_amount: Base amount before tax
        tax_rate: Tax rate as percentage

    Returns:
        Tax amount
    """
    if not base_amount or not tax_rate:
        return Decimal("0.00")

    tax_amount = (base_amount * tax_rate) / Decimal("100")
    return tax_amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)


def calculate_amount_with_tax(base_amount: Decimal, tax_rate: Decimal) -> Decimal:
    """
    Calculate total amount including tax.

    Args:
        base_amount: Base amount before tax
        tax_rate: Tax rate as percentage

    Returns:
        Total amount including tax
    """
    tax_amount = calculate_tax_amount(base_amount, tax_rate)
    return base_amount + tax_amount


def split_amount_proportionally(
    total_amount: Decimal, proportions: List[Decimal]
) -> List[Decimal]:
    """
    Split amount proportionally based on given proportions.

    Args:
        total_amount: Total amount to split
        proportions: List of proportions (should sum to 1.0)

    Returns:
        List of split amounts
    """
    if not proportions:
        return []

    # Normalize proportions to sum to 1.0
    proportion_sum = sum(proportions)
    if proportion_sum == 0:
        return [Decimal("0.00")] * len(proportions)

    normalized_proportions = [p / proportion_sum for p in proportions]

    # Calculate splits
    splits = []
    remaining_amount = total_amount

    for i, proportion in enumerate(normalized_proportions):
        if i == len(normalized_proportions) - 1:
            # Last split gets remaining amount to handle rounding
            splits.append(remaining_amount)
        else:
            split_amount = (total_amount * proportion).quantize(
                Decimal("0.01"), rounding=ROUND_HALF_UP
            )
            splits.append(split_amount)
            remaining_amount -= split_amount

    return splits


def create_split_summary(splits: List[Dict]) -> Dict:
    """
    Create a summary of payment splits.

    Args:
        splits: List of split dictionaries

    Returns:
        Split summary dictionary
    """
    if not splits:
        return {
            "total_amount": Decimal("0.00"),
            "split_count": 0,
            "recipients": [],
        }

    total_amount = sum(Decimal(str(split.get("amount", 0))) for split in splits)
    recipients = [split.get("recipient_name", "Unknown") for split in splits]

    return {
        "total_amount": total_amount,
        "split_count": len(splits),
        "recipients": recipients,
        "splits": splits,
    }


def find_payment_intent_by_identifier(identifier):
    """
    Find PaymentIntent by identifier using the lookup order:
    1. id (UUID)
    2. reference_number
    3. razorpay_payment_id
    4. razorpay_payment_link_id

    Returns PaymentIntent instance or None if not found.
    """
    from ..models import PaymentIntent

    try:
        return PaymentIntent.objects.get(id=identifier)
    except (PaymentIntent.DoesNotExist, ValueError):
        pass

    try:
        return PaymentIntent.objects.get(reference_number=identifier)
    except PaymentIntent.DoesNotExist:
        pass

    try:
        return PaymentIntent.objects.get(razorpay_payment_id=identifier)
    except PaymentIntent.DoesNotExist:
        pass

    try:
        return PaymentIntent.objects.get(razorpay_payment_link_id=identifier)
    except PaymentIntent.DoesNotExist:
        pass

    return None


def validate_minimum_split_amount(
    splits: List[Dict], min_amount: Decimal = Decimal("1.00")
) -> List[str]:
    """
    Validate that all splits meet minimum amount requirement.

    Args:
        splits: List of split dictionaries
        min_amount: Minimum allowed split amount

    Returns:
        List of validation errors
    """
    errors = []

    for i, split in enumerate(splits):
        amount = Decimal(str(split.get("amount", 0)))
        if amount < min_amount:
            recipient = split.get("recipient_name", f"Split {i+1}")
            errors.append(
                f"{recipient}: Amount ₹{amount} is below minimum ₹{min_amount}"
            )

    return errors


def calculate_net_amount(gross_amount: Decimal, deductions: List[Decimal]) -> Decimal:
    """
    Calculate net amount after deductions.

    Args:
        gross_amount: Gross amount before deductions
        deductions: List of deduction amounts

    Returns:
        Net amount after deductions
    """
    total_deductions = sum(deductions)
    return max(Decimal("0.00"), gross_amount - total_deductions)
