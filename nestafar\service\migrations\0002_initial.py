# Generated by Django 4.2.7 on 2025-10-02 05:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('geo', '0001_initial'),
        ('stay', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('service', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='staffprofile',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_profiles', to='stay.property'),
        ),
        migrations.AddField(
            model_name='staffprofile',
            name='staff',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='profiles', to='service.staff'),
        ),
        migrations.AddField(
            model_name='staff',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='staff', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='servicepartner',
            name='location',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='geo.location'),
        ),
        migrations.AddField(
            model_name='jobstatuslog',
            name='changed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='jobstatuslog',
            name='job',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_logs', to='service.job'),
        ),
        migrations.AddField(
            model_name='job',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='jobs', to='stay.property'),
        ),
        migrations.AddField(
            model_name='job',
            name='staff',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='jobs', to='service.staffprofile'),
        ),
        migrations.AddIndex(
            model_name='staffprofile',
            index=models.Index(fields=['property', 'role', 'is_active'], name='service_sta_propert_245c51_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='staffprofile',
            unique_together={('staff', 'property')},
        ),
        migrations.AddConstraint(
            model_name='servicepartner',
            constraint=models.UniqueConstraint(condition=models.Q(models.Q(('razorpay_linked_account_id', None), _negated=True), models.Q(('razorpay_linked_account_id', ''), _negated=True)), fields=('razorpay_linked_account_id',), name='unique_razorpay_linked_account'),
        ),
        migrations.AddConstraint(
            model_name='servicepartner',
            constraint=models.CheckConstraint(check=models.Q(models.Q(('account_status', 'verified'), _negated=True), models.Q(('razorpay_linked_account_id__isnull', False), models.Q(('razorpay_linked_account_id', ''), _negated=True), ('razorpay_account_verified', True), ('kyc_completed', True)), _connector='OR'), name='check_verified_requires_account_and_kyc'),
        ),
        migrations.AddIndex(
            model_name='jobstatuslog',
            index=models.Index(fields=['job', 'created_at'], name='service_job_job_id_8980ff_idx'),
        ),
        migrations.AddIndex(
            model_name='job',
            index=models.Index(fields=['property', 'status'], name='service_job_propert_a1468d_idx'),
        ),
        migrations.AddIndex(
            model_name='job',
            index=models.Index(fields=['property', 'type'], name='service_job_propert_e47fbc_idx'),
        ),
        migrations.AddIndex(
            model_name='job',
            index=models.Index(fields=['property', 'created_at'], name='service_job_propert_d166e3_idx'),
        ),
    ]
