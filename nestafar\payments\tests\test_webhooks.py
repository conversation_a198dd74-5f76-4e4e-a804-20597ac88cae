"""
Webhook Tests

Tests for webhook processing including payment and transfer
webhook processors.
"""

from decimal import Decimal
from unittest.mock import patch, Mock
from django.test import TestCase
from django.utils import timezone

from ..models import PaymentIntent, PaymentSplit, PaymentWebhookEvent
from ..constants import PaymentContext, PaymentStatus, TransferStatus, RazorpayWebhookEvents
from ..webhooks.payment_webhook_processor import PaymentWebhookProcessor
from ..webhooks.transfer_webhook_processor import TransferWebhookProcessor
from ..exceptions import WebhookException
from .test_base import PaymentTestBase


class PaymentWebhookProcessorTest(PaymentTestBase):
    """Tests for PaymentWebhookProcessor"""
    
    def setUp(self):
        super().setUp()
        self.processor = PaymentWebhookProcessor()
    
    def test_process_payment_captured_event(self):
        """Test processing payment.captured webhook event"""
        # Create payment intent
        payment_intent = self.create_payment_intent()
        
        # Create webhook event
        webhook_event = self.create_webhook_event(
            event_type=RazorpayWebhookEvents.PAYMENT_CAPTURED,
            entity_id='pay_captured_test',
            raw_payload={
                'event': RazorpayWebhookEvents.PAYMENT_CAPTURED,
                'payload': {
                    'payment': {
                        'entity': {
                            'id': 'pay_captured_test',
                            'status': 'captured',
                            'amount': 100000,  # 1000.00 in paise
                            'method': 'card',
                            'notes': {
                                'payment_intent_id': str(payment_intent.id)
                            }
                        }
                    }
                }
            }
        )
        
        # Process webhook
        self.processor.process_event(webhook_event)
        
        # Verify payment intent was updated
        payment_intent.refresh_from_db()
        self.assertEqual(payment_intent.status, PaymentStatus.COMPLETED)
        self.assertEqual(payment_intent.razorpay_payment_id, 'pay_captured_test')
        self.assertEqual(payment_intent.payment_method, 'card')
        self.assertIsNotNone(payment_intent.paid_at)
        
        # Verify webhook was marked as processed
        webhook_event.refresh_from_db()
        self.assertTrue(webhook_event.processed)
    
    def test_process_payment_failed_event(self):
        """Test processing payment.failed webhook event"""
        # Create payment intent
        payment_intent = self.create_payment_intent()
        
        # Create webhook event
        webhook_event = self.create_webhook_event(
            event_type=RazorpayWebhookEvents.PAYMENT_FAILED,
            entity_id='pay_failed_test',
            raw_payload={
                'event': RazorpayWebhookEvents.PAYMENT_FAILED,
                'payload': {
                    'payment': {
                        'entity': {
                            'id': 'pay_failed_test',
                            'status': 'failed',
                            'amount': 100000,
                            'error_code': 'BAD_REQUEST_ERROR',
                            'error_description': 'Payment failed due to insufficient funds',
                            'notes': {
                                'payment_intent_id': str(payment_intent.id)
                            }
                        }
                    }
                }
            }
        )
        
        # Process webhook
        self.processor.process_event(webhook_event)
        
        # Verify payment intent was updated
        payment_intent.refresh_from_db()
        self.assertEqual(payment_intent.status, PaymentStatus.FAILED)
        self.assertIsNotNone(payment_intent.failed_at)
        
        # Verify webhook was marked as processed
        webhook_event.refresh_from_db()
        self.assertTrue(webhook_event.processed)
    
    def test_process_payment_link_paid_event(self):
        """Test processing payment_link.paid webhook event"""
        # Create payment intent
        payment_intent = self.create_payment_intent()
        payment_intent.razorpay_payment_link_id = 'plink_paid_test'
        payment_intent.save()
        
        # Create webhook event
        webhook_event = self.create_webhook_event(
            event_type=RazorpayWebhookEvents.PAYMENT_LINK_PAID,
            entity_id='plink_paid_test',
            raw_payload={
                'event': RazorpayWebhookEvents.PAYMENT_LINK_PAID,
                'payload': {
                    'payment_link': {
                        'entity': {
                            'id': 'plink_paid_test',
                            'status': 'paid',
                            'payments': [
                                {
                                    'id': 'pay_link_payment_test',
                                    'status': 'captured',
                                    'method': 'upi'
                                }
                            ]
                        }
                    }
                }
            }
        )
        
        # Process webhook
        self.processor.process_event(webhook_event)
        
        # Verify payment intent was updated
        payment_intent.refresh_from_db()
        self.assertEqual(payment_intent.status, PaymentStatus.COMPLETED)
        self.assertEqual(payment_intent.razorpay_payment_id, 'pay_link_payment_test')
        self.assertEqual(payment_intent.payment_method, 'upi')
    
    def test_process_payment_without_payment_intent(self):
        """Test processing payment webhook without matching payment intent"""
        # Create webhook event without corresponding payment intent
        webhook_event = self.create_webhook_event(
            event_type=RazorpayWebhookEvents.PAYMENT_CAPTURED,
            entity_id='pay_orphan_test',
            raw_payload={
                'event': RazorpayWebhookEvents.PAYMENT_CAPTURED,
                'payload': {
                    'payment': {
                        'entity': {
                            'id': 'pay_orphan_test',
                            'status': 'captured',
                            'notes': {
                                'payment_intent_id': 'non-existent-id'
                            }
                        }
                    }
                }
            }
        )
        
        # Process webhook - should not raise exception
        self.processor.process_event(webhook_event)
        
        # Verify webhook was marked as processed (with warning logged)
        webhook_event.refresh_from_db()
        self.assertTrue(webhook_event.processed)
    
    @patch('payments.webhooks.payment_webhook_processor.logger')
    def test_process_event_exception_handling(self, mock_logger):
        """Test webhook processor exception handling"""
        # Create webhook event
        webhook_event = self.create_webhook_event(
            event_type=RazorpayWebhookEvents.PAYMENT_CAPTURED
        )
        
        # Mock an exception during processing
        with patch.object(self.processor, '_handle_payment_captured', side_effect=Exception('Test error')):
            with self.assertRaises(WebhookException):
                self.processor.process_event(webhook_event)
        
        # Verify error was logged and webhook marked with error
        self.assertTrue(mock_logger.exception.called or mock_logger.error.called)
        webhook_event.refresh_from_db()
        self.assertFalse(webhook_event.processed)
        self.assertIsNotNone(webhook_event.processing_error)

class TransferWebhookProcessorTest(PaymentTestBase):
    """Tests for TransferWebhookProcessor"""
    
    def setUp(self):
        super().setUp()
        self.processor = TransferWebhookProcessor()
    
    def test_process_transfer_processed_event(self):
        """Test processing transfer.processed webhook event"""
        # Create payment intent and split
        payment_intent = self.create_payment_intent()
        payment_split = self.create_payment_split(payment_intent)
        payment_split.razorpay_transfer_id = 'trf_processed_test'
        payment_split.status = TransferStatus.PROCESSING
        payment_split.save()
        
        # Create webhook event
        webhook_event = self.create_webhook_event(
            event_type=RazorpayWebhookEvents.TRANSFER_PROCESSED,
            entity_id='trf_processed_test',
            raw_payload={
                'event': RazorpayWebhookEvents.TRANSFER_PROCESSED,
                'payload': {
                    'transfer': {
                        'entity': {
                            'id': 'trf_processed_test',
                            'status': 'processed',
                            'amount': 95000,  # 950.00 in paise
                            'currency': 'INR',
                            'settlement_id': 'setl_test123'
                        }
                    }
                }
            }
        )
        webhook_event.entity_type = 'transfer'
        webhook_event.save()
        
        # Process webhook
        self.processor.process_event(webhook_event)
        
        # Verify payment split was updated
        payment_split.refresh_from_db()
        self.assertEqual(payment_split.status, TransferStatus.PROCESSED)
        self.assertIsNotNone(payment_split.transfer_completed_at)
        
        # Verify webhook was marked as processed
        webhook_event.refresh_from_db()
        self.assertTrue(webhook_event.processed)
    
    def test_process_transfer_failed_event(self):
        """Test processing transfer.failed webhook event"""
        # Create payment intent and split
        payment_intent = self.create_payment_intent()
        payment_split = self.create_payment_split(payment_intent)
        payment_split.razorpay_transfer_id = 'trf_failed_test'
        payment_split.status = TransferStatus.PROCESSING
        payment_split.save()
        
        # Create webhook event
        webhook_event = self.create_webhook_event(
            event_type=RazorpayWebhookEvents.TRANSFER_FAILED,
            entity_id='trf_failed_test',
            raw_payload={
                'event': RazorpayWebhookEvents.TRANSFER_FAILED,
                'payload': {
                    'transfer': {
                        'entity': {
                            'id': 'trf_failed_test',
                            'status': 'failed',
                            'error': {
                                'code': 'BAD_REQUEST_ERROR',
                                'description': 'Invalid account details'
                            }
                        }
                    }
                }
            }
        )
        webhook_event.entity_type = 'transfer'
        webhook_event.save()
        
        # Process webhook
        with patch('payments.webhooks.transfer_webhook_processor.retry_failed_transfer') as mock_retry:
            self.processor.process_event(webhook_event)
        
        # Verify payment split was updated
        payment_split.refresh_from_db()
        self.assertEqual(payment_split.status, TransferStatus.FAILED)
        self.assertEqual(payment_split.error_code, 'BAD_REQUEST_ERROR')
        self.assertEqual(payment_split.error_message, 'Invalid account details')
        self.assertEqual(payment_split.retry_count, 1)
        
        # Verify retry was scheduled
        mock_retry.apply_async.assert_called_once()
    
    def test_process_transfer_reversed_event(self):
        """Test processing transfer.reversed webhook event"""
        # Create payment intent and split
        payment_intent = self.create_payment_intent()
        payment_split = self.create_payment_split(payment_intent)
        payment_split.razorpay_transfer_id = 'trf_reversed_test'
        payment_split.status = TransferStatus.PROCESSED
        payment_split.save()
        
        # Create webhook event
        webhook_event = self.create_webhook_event(
            event_type=RazorpayWebhookEvents.TRANSFER_REVERSED,
            entity_id='trf_reversed_test',
            raw_payload={
                'event': RazorpayWebhookEvents.TRANSFER_REVERSED,
                'payload': {
                    'transfer': {
                        'entity': {
                            'id': 'trf_reversed_test',
                            'status': 'reversed',
                            'amount_reversed': 95000,
                            'notes': {
                                'reason': 'Customer dispute'
                            }
                        }
                    }
                }
            }
        )
        webhook_event.entity_type = 'transfer'
        webhook_event.save()
        
        # Process webhook
        self.processor.process_event(webhook_event)
        
        # Verify payment split was updated
        payment_split.refresh_from_db()
        self.assertEqual(payment_split.status, TransferStatus.REVERSED)
        
        # Verify metadata was updated
        self.assertIn('transfer_reversed_at', payment_split.metadata)
        self.assertIn('reversal_amount', payment_split.metadata)
    
    def test_process_transfer_without_payment_split(self):
        """Test processing transfer webhook without matching payment split"""
        # Create webhook event without corresponding payment split
        webhook_event = self.create_webhook_event(
            event_type=RazorpayWebhookEvents.TRANSFER_PROCESSED,
            entity_id='trf_orphan_test',
            raw_payload={
                'event': RazorpayWebhookEvents.TRANSFER_PROCESSED,
                'payload': {
                    'transfer': {
                        'entity': {
                            'id': 'trf_orphan_test',
                            'status': 'processed'
                        }
                    }
                }
            }
        )
        webhook_event.entity_type = 'transfer'
        webhook_event.save()
        
        # Process webhook - should not raise exception
        self.processor.process_event(webhook_event)
        
        # Verify webhook was marked as processed (with warning logged)
        webhook_event.refresh_from_db()
        self.assertTrue(webhook_event.processed)
    
    def test_check_all_transfers_completed(self):
        """Test checking if all transfers are completed"""
        # Create payment intent with multiple splits
        payment_intent = self.create_payment_intent(amount=Decimal('2000.00'))
        
        # Create partner split
        partner_split = self.create_payment_split(
            payment_intent,
            recipient_type='partner',
            amount=Decimal('1900.00')
        )
        
        # Create vendor split
        vendor_split = self.create_payment_split(
            payment_intent,
            recipient_type='vendor',
            amount=Decimal('100.00')
        )
        
        # Mark both as completed
        partner_split.status = TransferStatus.PROCESSED
        partner_split.save()
        vendor_split.status = TransferStatus.PROCESSED
        vendor_split.save()
        
        # Test the method
        with patch.object(self.processor, '_send_all_transfers_completed_notifications') as mock_notify:
            self.processor._check_all_transfers_completed(payment_intent)
            mock_notify.assert_called_once_with(payment_intent)
        
        # Verify payment intent metadata was updated
        payment_intent.refresh_from_db()
        self.assertIn('all_transfers_completed_at', payment_intent.metadata)


class WebhookIntegrationTest(PaymentTestBase):
    """Integration tests for webhook processing"""
    
    def test_end_to_end_payment_webhook_flow(self):
        """Test complete payment webhook processing flow"""
        # Create payment intent with splits
        payment_intent = self.create_payment_intent(amount=Decimal('1000.00'))
        
        # Calculate and create splits
        from ..services.payment_split_service import PaymentSplitService
        split_service = PaymentSplitService()
        split_data = split_service.calculate_splits(payment_intent)
        splits = split_service.create_payment_splits(payment_intent, split_data)
        
        # Create payment captured webhook
        webhook_event = self.create_webhook_event(
            event_type=RazorpayWebhookEvents.PAYMENT_CAPTURED,
            entity_id='pay_integration_test',
            raw_payload={
                'event': RazorpayWebhookEvents.PAYMENT_CAPTURED,
                'payload': {
                    'payment': {
                        'entity': {
                            'id': 'pay_integration_test',
                            'status': 'captured',
                            'amount': 100000,
                            'method': 'card',
                            'notes': {
                                'payment_intent_id': str(payment_intent.id)
                            }
                        }
                    }
                }
            }
        )
        
        # Process payment webhook
        payment_processor = PaymentWebhookProcessor()
        
        with patch('payments.webhooks.payment_webhook_processor.create_payment_transfers') as mock_transfer_task:
            payment_processor.process_event(webhook_event)

            # Verify payment was marked as paid
            payment_intent.refresh_from_db()
            self.assertTrue(payment_intent.is_paid())

            # Verify transfer creation was scheduled
            mock_transfer_task.delay.assert_called_once_with(payment_intent.id)    
    def test_end_to_end_transfer_webhook_flow(self):
        """Test complete transfer webhook processing flow"""
        # Create payment intent and split
        payment_intent = self.create_payment_intent()
        payment_intent.mark_as_paid()
        
        payment_split = self.create_payment_split(payment_intent)
        payment_split.razorpay_transfer_id = 'trf_integration_test'
        payment_split.status = TransferStatus.PROCESSING
        payment_split.save()
        
        # Create transfer processed webhook
        webhook_event = self.create_webhook_event(
            event_type=RazorpayWebhookEvents.TRANSFER_PROCESSED,
            entity_id='trf_integration_test',
            raw_payload={
                'event': RazorpayWebhookEvents.TRANSFER_PROCESSED,
                'payload': {
                    'transfer': {
                        'entity': {
                            'id': 'trf_integration_test',
                            'status': 'processed',
                            'amount': 95000,
                            'settlement_id': 'setl_integration_test'
                        }
                    }
                }
            }
        )
        webhook_event.entity_type = 'transfer'
        webhook_event.save()
        
        # Process transfer webhook
        transfer_processor = TransferWebhookProcessor()
        
        with patch('payments.services.payment_notification_service.PaymentNotificationService') as mock_notification:
            transfer_processor.process_event(webhook_event)
            
            # Verify split was marked as completed
            payment_split.refresh_from_db()
            self.assertTrue(payment_split.is_completed())
            
            # Verify webhook was processed
            webhook_event.refresh_from_db()
            self.assertTrue(webhook_event.processed)
