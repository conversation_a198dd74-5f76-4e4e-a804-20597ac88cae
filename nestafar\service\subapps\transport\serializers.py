from rest_framework.fields import Serial<PERSON><PERSON><PERSON>od<PERSON>ield
from rest_framework.serializers import ModelSerializer, ValidationError
from .models import *
from ...models import ServicePartner
from geo.serializers import LocationSerializer


class TransportServiceSerializer(ModelSerializer):
    n_items = SerializerMethodField(required=False)

    def get_n_items(self, obj):
        return obj.service_items.count()

    class Meta:
        model = TransportService
        fields = "__all__"


class TransportServiceItemSerializer(ModelSerializer):
    def validate(self, attrs):
        addon = attrs.get("addon")
        if addon:
            for addon_item in addon.keys():
                try:
                    addon_price = float(addon[addon_item])
                    addon[addon_item] = addon_price
                except ValueError:
                    raise ValidationError("Invalid price for addon")
        else:
            attrs["addon"] = None
        return attrs

    class Meta:
        model = TransportServiceItem
        fields = "__all__"


class TransportServiceItemLocationSerializer(TransportServiceItemSerializer):
    stop_location = LocationSerializer()
    vehicle_type = SerializerMethodField(required=False)

    def get_vehicle_type(self, obj):
        return obj.service.vehicle_type

    class Meta:
        model = TransportServiceItem
        fields = "__all__"


class TransportOrderItemSerializer(ModelSerializer):
    item = TransportServiceItemSerializer()

    class Meta:
        model = TransportOrderItem
        fields = "__all__"


class ServicePartnerNameSerializer(ModelSerializer):
    class Meta:
        model = ServicePartner
        fields = ["id", "name", "type_of_service", "phone_number"]


class TransportOrderSerializer(ModelSerializer):
    order_items = TransportOrderItemSerializer(many=True)
    service_partner = ServicePartnerNameSerializer()
    guest = SerializerMethodField(required=False)

    def get_guest(self, obj):
        return {
            "id": obj.guest.id,
            "phone_no": obj.guest.user.phone.as_e164,
            "room_no": obj.guest.room.room_no,
            "name": obj.guest.user.name,
        }

    class Meta:
        model = TransportOrder
        fields = "__all__"


class TransportCartItemSerializer(ModelSerializer):
    name = SerializerMethodField(required=False)

    def get_name(self, obj):
        return obj.name if obj.name else obj.item.name

    class Meta:
        model = TransportCartItems
        exclude = ["cart"]


class TransportCartSerializer(ModelSerializer):
    cart_items = TransportCartItemSerializer(many=True)

    class Meta:
        model = TransportCart
        fields = "__all__"
